
import axios from "axios";
import React, { useState } from "react";
import { toast } from "react-toastify";

const EmbeddedExperts = () => {
    const [embeddingLoading, setEmbeddingLoading] = useState(false);

    const handleAddEmbeddings = async () => {
        try {
          setEmbeddingLoading(true);
          
          const response = await axios.post(
            `${process.env.NEXT_PUBLIC_EMBEDDED_EXPERTS}`,
          );
        console.log(response, "response")
          if (response.data.status === "Embeddings computed successfully") {
            toast.success('Embeddings generated successfully!');
          } else {
            toast.error('Something went wrong while generating embeddings.');
          }
        } catch (error) {
          console.error('Error generating embeddings:', error);
          toast.error('Failed to generate embeddings.');
        } finally {
          setEmbeddingLoading(false);
        }
      };
  return (
    <div className="">
      <h5 className="mb-2 admin-add-blog-list-header fw-semibold">
        Add Embeddings
      </h5>
      <div className="row">
        <div className="col-sm-4">
          <div className="mb-3">
            <button
              type="button"
              className="btn purple-button"
              onClick={handleAddEmbeddings}
              disabled={embeddingLoading}
            >
              {embeddingLoading ? "Processing..." : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmbeddedExperts;
