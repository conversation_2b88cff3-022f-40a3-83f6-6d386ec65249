import React, { useState, useEffect, useRef } from "react";
import dynamic from "next/dynamic";
import { Modal, Button, Form } from "react-bootstrap";
import { toast } from "react-toastify";
import { MdCancel } from "react-icons/md";
import ModalImage from "react-modal-image";
import { debounce } from "lodash";
import { useSession } from "next-auth/react";

// Updated getCategoryIdByName function
const getCategoryIdByName = (categories, categoryName) => {
  if (!categoryName) {
    return null;
  }

  const filteredCategories = categories?.filter(
    (cat) => cat?.Category === categoryName
  );

  return filteredCategories.length > 0 ? filteredCategories[0].id : null;
};

// getCategoryNameById function remains unchanged
const getCategoryNameById = (categories, id) => {
  const category = categories.find((cat) => cat.id === parseInt(id, 10));
  return category ? category.Category : "Unknown";
};

// Dynamically import ReactQuill
const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
import "react-quill/dist/quill.snow.css";

import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";

// Define the AdminBlogEditModal component
const AdminBlogEditModal = ({
  openBlogEditModal,
  handleClose,
  blogDetails,
  fetchBlogs,
}) => {
  const [loading, setLoading] = useState(false);
  const [blogId, setBlogId] = useState(null);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [formData, setFormData] = useState({
    title: "",
    blogBody: "",
    blogBannerImage: null,
    blogFeatureImage: null,
    blogImages: [],
    category: "",
    description: "",
  });
  const [blogFileName, setBlogFileName] = useState("");
  const bannerImageInputRef = useRef(null);
  const featureImageInputRef = useRef(null);
  const imageInputRef = useRef(null);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;

  useEffect(() => {
    if (admin_id) {
      const fetchCategories = async () => {
        try {
          const response = await axiosAuth.get(
            `${process.env.NEXT_PUBLIC_GET_POST_BLOG_CATEGORY}?user_id=${admin_id}`
          );
          if (response.status === 200 || response.statusText === "OK") {
            setCategories(response?.data);
          } else {
            toast.error("Failed to fetch categories");
          }
        } catch (error) {
          console.error("Fetch categories error:", error);
          toast.error("An error occurred while fetching categories");
        }
      };

      fetchCategories();
    }
  }, [admin_id, axiosAuth]);

  useEffect(() => {
    if (blogDetails) {
      setBlogId(blogDetails?.id);
      setFormData({
        title: blogDetails?.BlogTitle || "Data not found",
        blogBody: blogDetails?.BlogBody || "Data not found",
        blogBannerImage: blogDetails?.BlogBannerImage || null,
        blogFeatureImage: blogDetails?.BlogFeatureImage || null,
        blogImages: blogDetails?.BlogImages ? [...blogDetails?.BlogImages] : [],
        category: blogDetails?.BlogCategoryVal,
        description: blogDetails?.BlogSummary || "",
      });
      setBlogFileName(blogDetails?.BlogImages ? "" : "");
      setSelectedCategory(blogDetails?.BlogCategoryVal);
    } else {
      setFormData({
        title: "",
        blogBody: "",
        blogBannerImage: null,
        blogFeatureImage: null,
        blogImages: [],
        category: "",
        description: "",
      });
      setBlogFileName("");
      setSelectedCategory("");
    }
  }, [blogDetails]);

  const resetForm = () => {
    setFormData({
      title: "",
      blogBody: "",
      blogBannerImage: null,
      blogFeatureImage: null,
      blogImages: [],
      category: "",
      description: "",
    });
    handleClose();
    setBlogFileName("");
    setSelectedCategory("");
  };

  const handleImageChange = (e, imageType) => {
    const file = e.target.files[0];
    setFormData((prevData) => ({
      ...prevData,
      [imageType]: file,
    }));
  };

  const handleBlogFile = (e) => {
    const files = Array.from(e.target.files);
    const fileNames = files.map((file) => file.name);
    setBlogFileName(fileNames.join(", "));
    setFormData((prevData) => ({
      ...prevData,
      blogImages: [...prevData.blogImages, ...files],
    }));
  };

  const handleRemoveImage = (index) => {
    setFormData((prevData) => {
      const updatedImages = [...prevData.blogImages];
      updatedImages.splice(index, 1);
      return {
        ...prevData,
        blogImages: updatedImages,
      };
    });
  };

  const handleRemoveBannerImage = () => {
    setFormData((prevData) => ({
      ...prevData,
      blogBannerImage: null,
    }));
  };

  const handleRemoveFeatureImage = () => {
    setFormData((prevData) => ({
      ...prevData,
      blogFeatureImage: null,
    }));
  };

  const debouncedEditorChange = React.useCallback(
    debounce((content) => {
      setFormData((prevData) => ({
        ...prevData,
        blogBody: content,
      }));
    }, 500),
    []
  );

  const handleEditorChange = (content) => {
    debouncedEditorChange(content);
  };

  const handleBlogUpdate = async () => {
    if (!formData.title || !formData.blogBody || !selectedCategory) {
      toast.error("All fields are required", {
        position: "top-center",
        autoClose: 3000,
      });
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append("BlogTitle", formData?.title);
    formDataToSend.append("BlogBody", formData?.blogBody);
    let categoryID = getCategoryIdByName(categories, selectedCategory);

    if (categoryID) {
      formDataToSend.append("BlogCategoryVal", categoryID);
    } else {
      toast.error("Invalid category selected");
      return;
    }

    formDataToSend.append("BlogSummary", formData?.description);
    if (formData?.blogBannerImage) {
      formDataToSend.append("BlogBannerImage", formData?.blogBannerImage);
    }
    if (formData?.blogFeatureImage) {
      formDataToSend.append("BlogFeatureImage", formData?.blogFeatureImage);
    }
    if (formData?.blogImages.length > 0) {
      formData.blogImages.forEach((image, index) => {
        formDataToSend.append(`BlogImages[${index}]`, image);
      });
    }

    const apiEndpoint = `${process.env.NEXT_PUBLIC_DOCTOR_UPDATE_BLOG_API_ENDPOINT}${blogId}/`;

    try {
      setLoading(true); // Start loading indicator
      const response = await axiosAuth.put(apiEndpoint, formDataToSend);
      if (response?.statusText === "OK") {
        toast.success("Blog updated successfully!", {
          position: "top-center",
          autoClose: 5000,
        });
        fetchBlogs();
        handleClose();
      } else {
        toast.error("Failed to update Blog", {
          position: "top-center",
          autoClose: 5000,
        });
        handleClose();
      }
    } catch (error) {
      console.error("An error occurred:", error);
      toast.error("An error occurred while updating the blog");
      handleClose();
    } finally {
      setLoading(false); // End loading indicator
    }
  };

  const triggerFileInput = (inputRef) => {
    if (inputRef.current) {
      inputRef.current.click();
    }
  };

  return (
    <Modal
      show={openBlogEditModal}
      onHide={handleClose}
      centered
      size="xl"
      scrollable
    >
      <Modal.Header>
        <Modal.Title className="fs-5 mx-3 mt-2 upload-box">
          Edit Blog
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="modal-1-box">
          <div className="form-group">
            <div className="input-group input-group-border mb-2">
              <span className="input-group-text label-col">Blog Title</span>
              <input
                type="text"
                className="form-control custom-input"
                id="title"
                name="title"
                placeholder="Enter Blog Title"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    title: e.target.value,
                  }))
                }
                required
              />
            </div>
            <div className="input-group input-group-border mb-2">
              <span className="input-group-text label-col">Category</span>
              <Form.Select
                id="category"
                name="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                required
              >
                <option value="">Select Category</option>
                {Array.isArray(categories) &&
                  categories?.map((category) => (
                    <option key={category.id} value={category.Category}>
                      {category.Category}
                    </option>
                  ))}
              </Form.Select>
            </div>
            <div className="input-group input-group-border mb-2">
              <span className="input-group-text label-col">Description</span>
              <input
                type="text"
                className="form-control custom-input"
                id="description"
                name="description"
                placeholder="Enter Description"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    description: e.target.value,
                  }))
                }
              />
            </div>

            <div className="input-group input-group-border mb-2">
              <span className="input-group-text label-col">Blog Body</span>
              <div style={{ width: "100%", overflow: "auto" }}>
                {typeof window !== "undefined" && (
                  <ReactQuill
                    modules={{
                      toolbar: {
                        container: [
                          [
                            { header: [1, 2, 3, 4, 5, 6, false] }, // Header options
                            { font: [] }, // Font options
                            { size: [] }, // Font size options
                          ],
                          [{ size: [] }],
                          [
                            "bold",
                            "italic",
                            "underline",
                            "strike",
                            "blockquote",
                          ],
                          [
                            { list: "ordered" },
                            { list: "bullet" },
                            { indent: "-1" },
                            { indent: "+1" },
                          ],
                          ["link"],
                          ["clean"],
                        ],
                      },
                    }}
                    formats={[
                      "header",
                      "font",
                      "size", // Font size formatting
                      "bold",
                      "italic",
                      "underline",
                      "strike",
                      "blockquote",
                      "list",
                      "bullet",
                      "indent",
                      "link",
                    ]}
                    theme="snow"
                    value={formData.blogBody}
                    onChange={handleEditorChange}
                    placeholder="Enter your Blog Summary"
                  />
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <div className="input-group input-group-border mb-2">
                  <span className="input-group-text label-col">
                    Blog Banner Image
                  </span>
                  <div className="d-flex flex-column w-100">
                    <input
                      type="file"
                      accept="image/*"
                      className="form-control d-none"
                      ref={bannerImageInputRef}
                      onChange={(e) => handleImageChange(e, "blogBannerImage")}
                    />
                    {formData.blogBannerImage && (
                      <div className="position-relative">
                        <MdCancel
                          size={30}
                          color="red"
                          className="position-absolute"
                          style={{
                            top: "-10px",
                            right: "10px",
                            cursor: "pointer",
                          }}
                          onClick={handleRemoveBannerImage}
                        />
                        <ModalImage
                          className="blog-images-small-size"
                          small={
                            formData.blogBannerImage instanceof File
                              ? URL.createObjectURL(formData.blogBannerImage)
                              : formData.blogBannerImage
                          }
                          large={
                            formData.blogBannerImage instanceof File
                              ? URL.createObjectURL(formData.blogBannerImage)
                              : formData.blogBannerImage
                          }
                          alt="Blog Banner"
                        />
                      </div>
                    )}
                    <Button
                      variant="outline-primary"
                      onClick={() => triggerFileInput(bannerImageInputRef)}
                    >
                      Change Image
                    </Button>
                  </div>
                </div>
              </div>

              <div className="col-md-6">
                <div className="input-group input-group-border mb-2">
                  <span className="input-group-text label-col">
                    Blog Feature Image
                  </span>
                  <div className="d-flex flex-column w-100">
                    <input
                      type="file"
                      accept="image/*"
                      className="form-control d-none"
                      ref={featureImageInputRef}
                      onChange={(e) => handleImageChange(e, "blogFeatureImage")}
                    />
                    {formData.blogFeatureImage && (
                      <div className="position-relative">
                        <MdCancel
                          size={30}
                          color="red"
                          className="position-absolute"
                          style={{
                            top: "-10px",
                            right: "10px",
                            cursor: "pointer",
                          }}
                          onClick={handleRemoveFeatureImage}
                        />
                        <ModalImage
                          className="blog-images-small-size"
                          small={
                            formData.blogFeatureImage instanceof File
                              ? URL.createObjectURL(formData.blogFeatureImage)
                              : formData.blogFeatureImage
                          }
                          large={
                            formData.blogFeatureImage instanceof File
                              ? URL.createObjectURL(formData.blogFeatureImage)
                              : formData.blogFeatureImage
                          }
                          alt="Blog Feature"
                        />
                      </div>
                    )}
                    <Button
                      variant="outline-primary"
                      onClick={() => triggerFileInput(featureImageInputRef)}
                    >
                      Change Image
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="input-group input-group-border mb-2">
              <span className="input-group-text label-col">Blog Images</span>
              <div className="d-flex flex-column w-100">
                <input
                  type="file"
                  accept="image/*"
                  className="form-control d-none"
                  ref={imageInputRef}
                  onChange={handleBlogFile}
                  multiple
                />
                <div className="d-flex flex-wrap">
                  {formData?.blogImages?.map((image, index) => (
                    <div key={index} className="position-relative m-2">
                      <MdCancel
                        size={30}
                        color="red"
                        className="position-absolute"
                        style={{
                          top: "-10px",
                          right: "10px",
                          cursor: "pointer",
                        }}
                        onClick={() => handleRemoveImage(index)}
                      />
                      <ModalImage
                        className="blog-images-small-size"
                        small={
                          image instanceof File
                            ? URL.createObjectURL(image)
                            : image
                        }
                        large={
                          image instanceof File
                            ? URL.createObjectURL(image)
                            : image
                        }
                        alt={`Blog Image ${index + 1}`}
                      />
                    </div>
                  ))}
                </div>

                <Button
                  variant="outline-primary"
                  onClick={() => triggerFileInput(imageInputRef)}
                >
                  Add Images
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={resetForm}>
          Close
        </Button>
        <Button variant="primary" onClick={handleBlogUpdate} disabled={loading}>
          {loading ? "Updating..." : "Update Blog"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AdminBlogEditModal;
