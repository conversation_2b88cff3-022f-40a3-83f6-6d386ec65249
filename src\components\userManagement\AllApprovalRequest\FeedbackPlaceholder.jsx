import React from "react";
import { Placeholder } from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";

const FeedbackPlaceholder = () => {
  const feedbacksPlaceholder = Array.from({ length: 8 }).map((_, index) => (
    <div key={index} className="col-sm-6 p-2">
      <div className="introvideo-bg p-3">
        <div className="row">
          <div className="col-sm-auto mb-2">
            <Placeholder as="div" animation="glow">
              <Placeholder
                style={{ width: "50px", height: "50px", borderRadius: "50%" }}
              />
            </Placeholder>
          </div>
          <div className="col-sm-auto p-0">
            <Placeholder as="div" animation="glow">
              <Placeholder
                className="allApproval-expert-name mb-0"
                style={{ width: "100px", height: "20px" }}
              />
            </Placeholder>
          </div>
        </div>
        <div className="row">
          <Placeholder as="div" animation="glow">
            <Placeholder
              className="allApproval-expert-para"
              style={{ width: "100%", height: "60px" }}
            />
          </Placeholder>
        </div>
      </div>
    </div>
  ));

  return <div className="row">{feedbacksPlaceholder}</div>;
};

export default FeedbackPlaceholder;
