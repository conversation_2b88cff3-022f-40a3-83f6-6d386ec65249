import React, { useEffect, useState } from "react";
import Image from "next/image";
import Modal from "react-bootstrap/Modal";
import "./prescription.css";
import img1 from "../../../public/assets/adminloginlogo.png";
import { calculateAge } from "../../utils/helperfunction";

const PrescriptionModalResInf = ({
  selectedPresciption,
  prescriptionData,
  showResearcherInfluencerPrescriptionModal,
  setShowResearcherInfluencerPrescriptionModal,
}) => {
  const id = selectedPresciption?.prescriptions[0];


  const {
    prescription_data = {},
    patient_details = {},
    doctor_details = {},
    appointment_details = {},
  } = prescriptionData || {};


  // const iv_medication = prescription_data?.iv_medication;

  return (
    <>
      <Modal
        show={showResearcherInfluencerPrescriptionModal}
        onHide={() => setShowResearcherInfluencerPrescriptionModal(false)}
        dialogClassName="custom-modal-width"
      >
        <Modal.Header className="custom-modal-header ">
          <div className="row">
            <div className="col-sm-9">
              <p className="purple-content mb-1 fw-semibold">
                {prescriptionData?.doctor_details?.name}
              </p>

              <p className="purple-content fs-6 mb-0">
                Dept. of {doctor_details?.doctor_other_details?.Dept}
              </p>
            </div>

            <div className="col-sm-3">
              <Image className="headerImage1" src={img1} alt="header" />
            </div>
          </div>

          <div className="row">
            <div className="col-sm-9">
              <p className="black-content mb-1">MD license: 12-136547</p>

              <p className="black-content mb-1">
                Doctor ID: {prescriptionData?.doctor_details?.id}
              </p>

              <p className="black-content fw-medium mb-0">24th June, 2023</p>
            </div>

            <div className="col-sm-3">
              <p className="purple-content mb-1 fw-semibold">
                {doctor_details?.doctor_other_details?.PractisingHospital}
              </p>

              <p className="black-content mb-0">
                <a className="link-opacity-100" href="#">
                  www.cancerhospital.com
                </a>
              </p>
            </div>
          </div>
        </Modal.Header>
        <Modal.Body>
          <form className="row g-3">
            <div className="col-md-2 mt-0">
              <label
                htmlFor="inputId"
                className="form-label purple-content-2 fw-medium mb-1"
              >
                Id
              </label>
              <input
                type="text"
                className="form-control custom-form-control"
                id="inputId"
                value={prescriptionData?.patient_details?.id}
                readOnly
              />
            </div>
            <div className="col-md-3 mt-0">
              <label
                htmlFor="inputName"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Name
              </label>
              <input
                type="name"
                className="form-control custom-form-control"
                id="inputName"
                value={prescriptionData?.patient_details?.name}
                readOnly
              />
            </div>
            <div className="col-md-3 mt-0">
              <label
                htmlFor="inputEmail"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Consultation Id
              </label>
              <input
                type="mail"
                className="form-control custom-form-control"
                id="inputEmail"
                value={prescriptionData?.appointment_details?.id}
                readOnly
              />
            </div>
            <div className="col-md-2 mt-0">
              <label
                htmlFor="inputEmail"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Sex
              </label>
              <input
                type="mail"
                className="form-control custom-form-control"
                id="inputEmail"
                value="M"
                readOnly
              />
            </div>
            <div className="col-md-2 mt-0">
              <label
                htmlFor="inputEmail"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Age
              </label>
              <input
                type="mail"
                className="form-control custom-form-control"
                id="inputEmail"
                value={calculateAge(patient_details?.date_of_birth)}
                readOnly
              />
            </div>

            <div className="input-group mb-0">
              <span
                className="input-group-text purple-content"
                id="basic-addon1"
              >
                Current Diagnosis
              </span>
              <input
                type="text"
                className="form-control custom-form-control"
                aria-label="Username"
                aria-describedby="basic-addon1"
                value={prescription_data?.CurrentDiagnosis}
                readOnly
              />
            </div>

            <div className="form-floating">
              <textarea
                className="form-control"
                placeholder="Consultation Summary "
                id="floatingTextarea"
                style={{ height: "130px" }}
                readOnly
                value={prescription_data?.SpecialInstructions}
              />
              <label htmlFor="floatingTextarea" className="purple-content">
                Consultation Summary
              </label>
            </div>
            <div className="form-floating">
              <textarea
                className="form-control"
                placeholder="Remarks "
                id="floatingTextarea"
                style={{ height: "130px" }}
                readOnly
                value={prescription_data?.SpecialInstructions}
              />
              <label htmlFor="floatingTextarea" className="purple-content">
                Remarks
              </label>
            </div>
            <div className="form-floating">
              <textarea
                className="form-control"
                placeholder="Leave a comment here"
                id="floatingTextarea"
                style={{ height: "130px" }}
                readOnly
                value={prescription_data?.SpecialInstructions}
              />
              <label htmlFor="floatingTextarea" className="purple-content">
                Special Instructions
              </label>
            </div>

            <div className="input-group mb-0 mt-2">
              <span
                className="input-group-text purple-content"
                id="basic-addon1"
              >
                Follow-up Appointment
              </span>
              <input
                type="text"
                className="form-control custom-form-control"
                aria-label="Username"
                aria-describedby="basic-addon1"
                value={prescription_data?.FollowUp}
                readOnly
              />
            </div>

            <div className="col-sm-3 offset-sm-9 border">
              <p className="purple-signature">Digital Signature</p>
              {selectedPresciption?.doctor_Signature && (
                <Image
                  src={selectedPresciption.doctor_Signature}
                  alt="researcher Sign"
                  className="ms-5 mb-2"
                  width={100}
                  height={50}
                />
              )}
              {/* <img
              // src={img2}
              src={prescription_data?.DoctorSignature}
              alt=""
              className="ms-5 mb-2"
            /> */}
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default PrescriptionModalResInf;
