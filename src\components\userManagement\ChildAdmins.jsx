"use client";
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import Image from "next/image";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import Link from "next/link";
import ChildAdminFilters from "./patients-data/childAdminListComps/ChildAdminFilters";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import dummyProfile from "../../../public/images/dummy-avatar.jpg";
import Loading from "../Loading/PageLoading/Loading";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import { capitalizeFullName, highlightText } from "../../utils/helperfunction";
import _ from "lodash";
import CustomPagination from "../CustomPagination/CustomPagination";
import NoDataFound from "../noDataFound/NoDataFound";

ChartJS.register(ArcElement, Tooltip, Legend);
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);
const headers = [
  "Child Admin Name",
  "Contact",
  "Date of Joining",
  "Designation",
  "Status",
];

function getColor(status) {
  switch (status) {
    case "pending":
      return "orange";
    case "Approval_requested":
      return "blue";
    case "Approved":
      return "rgb(0, 204, 74)";
    case "Deactivated":
      return "gray";
    case "Rejected":
      return "red";
    case "self_deactivation":
      return "red";
    default:
      return "black";
  }
}
const ChildAdminsComp = () => {
  const [childAdminList, setChildAdminList] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [designation, setDesignation] = useState("");
  const [loading, setLoading] = useState(true);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const { data: session } = useSession();
  const [totalChildAdmin, setTotalChildAdmin] = useState(0);
  const { userPermissions } = useAdminContext();
  const axiosAuth = useAxiosAuth();
  const userId = session && session?.user?.id;
  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);

  const canDeactivateUser = userPermissions?.includes("cu_app.deactivate_user");
  const fetchedDataRef = useRef({});

  const fetchChildAdminsList = useCallback(
    async (query) => {
      try {
        let url = `${process.env.NEXT_PUBLIC_GET_ALL_CHILD_ADMINS}?user_id=${userId}`;

        if (startDate && endDate && userId) {
          url += `&start_date=${startDate}&end_date=${endDate}&page=${current_page}`;
        } else if (designation && userId) {
          url += `&designation=${designation}&page=${current_page}`;
        } else if (query && userId) {
          url += `&name=${query}&page=${current_page}`;
        } else if (!startDate && !endDate && !designation && !query) {
          url += `&page=${current_page}`;
        }
        // Check if data for this URL is already fetched
        if (fetchedDataRef.current[url]) {
          const cachedData = fetchedDataRef.current[url];
          setTotalChildAdmin(cachedData?.total_child_admin || 0);
          setChildAdminList(cachedData?.child_admin_data);
          setTotalPages(cachedData?.total_pages);
        } else {
          const response = await axiosAuth.get(url);
          fetchedDataRef.current[url] = response.data; // Store fetched data in useRef
          setTotalChildAdmin(response.data?.total_child_admin || 0);

          // setChildAdminList(response?.data?.child_admin_data?.slice().reverse());
          setChildAdminList(response?.data?.child_admin_data);
          setTotalPages(response?.data?.total_pages);
        }
      } catch (err) {
        console.log("Error in getting the Child Admins List", err); // Corrected the error message
      } finally {
        setLoading(false);
      }
    },
    [axiosAuth, userId, designation, startDate, endDate, current_page]
  ); // Added missing dependencies

  const debouncedFetchChildAdminsList = useMemo(() => {
    return _.debounce((query) => {
      fetchChildAdminsList(query);
    }, 500);
  }, [fetchChildAdminsList]);

  useEffect(() => {
    debouncedFetchChildAdminsList(searchQuery);
    return () => {
      debouncedFetchChildAdminsList.cancel();
    };
  }, [searchQuery, debouncedFetchChildAdminsList]);


  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearchQuery(value);
    setCurrent_Page(1);
  };
  const handleDesignationChange = (event) => {
    const value = event.target.value;
    setDesignation(value);
    setCurrent_Page(1);
  };

  const handleDateChange = (event, dateType) => {
    if (dateType === "startDate") {
      setStartDate(event.target.value);
    } else if (dateType === "endDate") {
      setEndDate(event.target.value);
    }
  };

  // const filteredChildAdminList = childAdminList.filter((admin) =>
  //   admin.name.toLowerCase().includes(searchQuery.toLowerCase())
  // );

  const handleClearSearch = () => {
    setDesignation("");
    setCurrent_Page(1);
    // fetchChildAdminsList("");
  };
  const handleClearDesignationSearch = () => {
    setDesignation("");
    setCurrent_Page(1);
  };

  return (
    <div>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="bg-color">
          <div className=" overflow-hidden">
            <div className="user-management-scroll overflow-auto">
              <div className="row">
                <div className="col-sm-12">
                  <div className="row">
                    <ChildAdminFilters
                      handleDateChange={handleDateChange}
                      startDate={startDate}
                      endDate={endDate}
                      searchQuery={searchQuery}
                      setDesignation={setDesignation}
                      totalChildAdmin={totalChildAdmin}
                      handleSearchChange={handleSearchChange}
                      handleDesignationChange={handleDesignationChange}
                      loading={loading}
                      handleClearSearch={handleClearSearch}
                      designation={designation}
                      handleClearDesignationSearch={
                        handleClearDesignationSearch
                      }
                      setCurrent_Page={setCurrent_Page}
                    />
                  </div>

                  <table className="table mt-2">
                    <thead className="custom-border">
                      <tr className="custom-name">
                        {headers.map((header) => (
                          <th
                            key={header}
                            scope="col"
                            className="fw-light text-center child-admin-list-heading"
                            style={{ fontSize: "14px" }}
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="custom-border">
                      {childAdminList &&
                      Array.isArray(childAdminList) &&
                      childAdminList?.length === 0 ? (
                        <tr>
                          <td colSpan="5" className="text-center">
                            <div
                              className="d-flex flex-column align-items-center justify-content-center"
                              style={{ minHeight: "428px" }}
                            >
                              <NoDataFound />
                            </div>
                          </td>
                        </tr>
                      ) : (
                        childAdminList &&
                        Array.isArray(childAdminList) &&
                        childAdminList?.map((admin, index) => {
                          return (
                            <tr
                              key={index}
                              className="custom-row align-baseline"
                            >
                              <td className="purple-content text-capitalize">
                                <Link
                                  href={`/usermanagement/childAdmin/${admin?.admin_details?.id}/${admin?.admin_details?.email}/${admin?.admin_details?.approval}`}
                                  className="child-admin-name"
                                >
                                  {admin?.admin_other_details?.profile_photo ? (
                                    <Image
                                      src={`${admin?.admin_other_details?.profile_photo}`}
                                      alt={`Dr ${admin?.admin_details?.name}`}
                                      width={100}
                                      height={100}
                                      className="expert_image"
                                    />
                                  ) : (
                                    <Image
                                      src={dummyProfile}
                                      alt={`Dr ${admin?.admin_details?.name}`}
                                      width={35}
                                      height={35}
                                      className="expert_image"
                                    />
                                  )}
                                  {highlightText(
                                    admin?.admin_details?.name,
                                    searchQuery
                                  )}
                                </Link>
                              </td>
                              <td className="custom-font text-center">
                                {admin?.admin_details?.phone}
                              </td>
                              <td className="text-center">
                                {
                                  admin?.admin_details?.DateOfRegistration?.split(
                                    "T"
                                  )[0]
                                }
                              </td>
                              <td className="text-center">
                                {capitalizeFullName(
                                  admin?.admin_other_details?.Designation
                                ) || "No Designation Assigned"}
                              </td>
                              {/* <td className="text-center green-text">
                            {admin?.admin_details?.approval}
                          </td> */}
                              <td
                                className="text-center fw-semibold"
                                style={{
                                  color: getColor(
                                    admin?.admin_details?.approval
                                  ),
                                }}
                              >
                                {admin?.admin_details?.approval === "pending"
                                  ? "Pending"
                                  : admin?.admin_details?.approval ===
                                    "Approval_requested"
                                  ? "Approval Requested"
                                  : admin?.admin_details?.approval ===
                                    "Approved"
                                  ? "Approved"
                                  : admin?.admin_details?.approval ===
                                    "Deactivated"
                                  ? "Deactivated"
                                  : admin?.admin_details?.approval ===
                                    "Rejected"
                                  ? "Rejected"
                                  : admin?.admin_details?.approval ===
                                    "self_deactivation"
                                  ? "Self Deactivated"
                                  : admin?.admin_details?.approval}
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                  {totalPages > 1 && (
                    <div className="d-flex justify-content-center align-items-center">
                      <div className="d-none d-xl-block">
                        <CustomPagination
                          total_pages={totalPages}
                          current_page={current_page}
                          setCurrent_Page={setCurrent_Page}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChildAdminsComp;
