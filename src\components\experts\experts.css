.form-control.custom-form-control {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  font-size: 12px;
}

/* filter style */
.filterwidth {
  height: 40px !important;
  margin-left: auto;
  font-size: 14px;
  border: none;
  box-shadow: 0 3px 6px #00000029;
  border-radius: 3px;
}

.loader-container1 {
  align-items: center;
  justify-content: center;
  margin-top: 40%;
}
.orange-search-btn {
  background-color: #ff971a;
  color: white;
  padding: 10px;
  padding-right: 40px;
  padding-left: 40px;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
}
.orange-search-btn:hover {
  background-color: #ff971a;
  color: white;
  padding: 10px;
  padding-right: 40px;
  padding-left: 40px;
  font-size: 14px;
}
.custom-shadow {
  /* background: #FFFFFF 0% 0% no-repeat padding-box; */
  box-shadow: 0px 3px 6px #00000012;
  border: 1px solid #ffffff;
  border-radius: 5px;
  opacity: 1;
}
.side-border {
  border-right: 2px solid #f6f6f6;
  opacity: 1;
}
.single-line {
  white-space: nowrap; /* Prevents text wrapping */
  overflow: hidden; /* Ensures no overflow issues */
  text-overflow: ellipsis; /* Adds "..." if the text is too long */
}
.custom-shadow2 {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  opacity: 1;
}
.expert-purple-content {
  letter-spacing: 0px;
  color: #8107d1;
  font-weight: 400;
  font-size: 12px;
}
span.progress-label {
  letter-spacing: -0.28px;
  color: #b3b8bd;
}
button.btn.btn-primary.purple-btn.custom-btn-width {
  width: 90%;
}

/* *****************************Appeoval Component************************************ */
.card.custom-card {
  border: none;
}

.card-body {
  padding: 10px;
}
.steps {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;
}

.step {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px;
}

.step-icon-wrap {
  width: 40px;
  height: 40px;
  background-color: #d1d1d1;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.step-title {
  font-size: 11px;
  margin: 0;
  letter-spacing: 0px;
  color: #333333;
  opacity: 1;
  font-weight: 700;
}

.completed .step-icon-wrap {
  background-color: #8107d1;
}
.line {
  position: absolute;
  height: 2px;
  background-color: #ddd;
}

.line:first-child {
  left: 50%;
  right: 100%;
}

.line:last-child {
  right: 50%;
  left: 100%;
}
td.bg-custom {
  background-color: #f6f6f6 !important;
  border: 1px solid #e3e3e3;
  opacity: 1;
}

.custom-font {
  cursor: pointer;
}
.expertise_count {
  color: #8107d1;
  /* font-size: 10px; */
  font-weight: bold;
}
#tooltip {
  background-color: white;
  color: black;
}
.expert_image {
  border-radius: 50%;
  margin-right: 5%;
}

.custom-row {
  padding-top: 20px;
}

.nodata-approvals {
  width: 300px;
  height: 100%;
  object-fit: contain;
}
.calender-filter-container {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #dee2e6;
  border-radius: 3px;
}
span.date-filter-expert {
  background: #ffffff;
  border-radius: 3px;
  /* padding: 13px 6px 7px; */
  padding: 9px;
  display: inline-flex;
  font-size: 13px;
  width: 100%;
  justify-content: space-between;
}
.calendar-icon-expert {
  color: #8107d1;
}
.cross-icon-expert {
  margin-left: 205px;
  margin-top: -3px;
  position: absolute;
}
.cross-icon-calendar {
  color: #ff7700;
  z-index: 99;
  cursor: pointer;
}
.calender-icon-calendar {
  color: #8107d1;
}
.expert-search-bar {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  font-size: 13px;
  color: #5b5b5b;
  height: 40px;
  border: 1px solid #dee2e6;
}
.cancel-expert-search-btn {
  position: absolute;
  bottom: 8px;
  right: 43px;
  font-size: 15px;
  font-weight: 600;
}
.no-data-found-margin{
  margin-top: 15%;
}

