"use client";
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import Image from "next/image";
import profile from "../../../../public/images/dummy-avatar.jpg";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import { useSession } from "next-auth/react";
// import { Tooltip } from 'react-tooltip';
import Loading from "../../../components/Loading/PageLoading/Loading.js";
import { convertDateFormat } from "./db";
import Cookies from "js-cookie";
import Link from "next/link";
import PagePagination from "../../PagePagination/PagePagination.jsx";
import Placeholder from "react-bootstrap/Placeholder";
import dynamic from "next/dynamic";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth.js";
import PatientDashboardStatistics from "./PatientDashboardStatistics";
import {
  formatDateToYMD,
  highlightInteger,
  highlightText,
} from "../../../utils/helperfunction";
import PatientsListFilters from "./PatientsListFilters";
import PatientsLineGraph from "./PatientsLineGraph";
import PatientTableHead from "./PatientTableHead";

import _ from "lodash";
import CustomPagination from "../../CustomPagination/CustomPagination";
import NoDataFound from "@/components/noDataFound/NoDataFound";

ChartJS.register(ArcElement, Tooltip, Legend);
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 7 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow" className="w-100">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

const PatientsAllData = () => {
  const [expertSelect, setExpertSelected] = useState();
  const [status, setStatus] = useState();
  const [allPatientDeatils, setAllPatientDeatils] = useState([]);
  const [loading, setLoading] = useState(true);
  const [contentLoading, setContentLoading] = useState(true);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [searchQuery, setSearchQuery] = useState("");

  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const { data: session } = useSession();
  const adminId = session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const fetchedDataRef = useRef({});

  const fetchData = useCallback(
    async (query) => {
      try {
        let url = `${process.env.NEXT_PUBLIC_GET_ALL_PATIENTS_BY_TYPE}?user_id=${adminId}`;

        if (startDate && endDate && adminId) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}&page=${current_page}`;
        } else if (expertSelect && adminId) {
          url += `&role=${expertSelect}&page=${current_page}`;
        } else if (status && adminId) {
          url += `&status=${status}&page=${current_page}`;
        } else if (query && adminId) {
          url += `&name=${query}`;
        } else if (
          !startDate &&
          !endDate &&
          !expertSelect &&
          !status &&
          !query
        ) {
          url += `&page=${current_page}`;
        }
        // Check if data for this URL is already fetched
        if (fetchedDataRef.current[url]) {
          const cachedData = fetchedDataRef.current[url];
          setAllPatientDeatils(cachedData?.patient_data);
          setTotalPages(cachedData?.total_pages);
        } else {
          const data = await axiosAuth.get(url);
          fetchedDataRef.current[url] = data?.data; // Store fetched data in useRef
          if (data?.data) {
            setLoading(false);
            setContentLoading(false);
          }
          setAllPatientDeatils(data?.data?.patient_data);
          setTotalPages(data?.data?.total_pages);
        }
      } catch (error) {
        console.error(error);
        setContentLoading(false);
      } finally {
        setLoading(false);
        setContentLoading(false);
      }
    },
    [startDate, endDate, adminId, expertSelect, axiosAuth, status, current_page]
  );
  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      fetchData(query);
    }, 500);
  }, [fetchData]);
  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  return (
    <>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="bg-color">
          <div className=" overflow-hidden">
            <div className="user-management-scroll overflow-auto">
              <div className="row">
                <div className="col-sm-5">
                  <PatientDashboardStatistics />
                  <PatientsLineGraph />
                </div>
                {/* *********************************************Total Expert Appointments********************************************** */}
                <div className="col-sm-7">
                  <PatientsListFilters
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    fetchData={fetchData}
                    adminId={adminId}
                    startDate={startDate}
                    endDate={endDate}
                    expertSelect={expertSelect}
                    setExpertSelected={setExpertSelected}
                    setStatus={setStatus}
                    status={status}
                    setStartDate={setStartDate}
                    setEndDate={setEndDate}
                    setCurrent_Page={setCurrent_Page}
                  />

                  <table className="table mt-2">
                    <PatientTableHead />
                    <tbody className="custom-border">
                      {loading ? (
                        renderPlaceholders()
                      ) : (
                        <>
                          {allPatientDeatils?.length > 0 ? (
                            <>
                              {allPatientDeatils &&
                                allPatientDeatils?.map((patient, index) => {
                                 
                                  return (
                                    <tr key={index} className="custom-row">
                                      <td className="custom-font text-center text-capitalize">
                                        {highlightInteger(
                                          patient["patient details"]?.id,
                                          searchQuery
                                        )}
                                      </td>
                                      <td className="purple-content d-flex align-items-center">
                                        {patient?.other_details
                                          ?.ProfilePhoto ? (
                                          <>
                                            <Image
                                              src={
                                                patient?.other_details
                                                  ?.ProfilePhoto
                                              }
                                              alt={`Dr`}
                                              width={35}
                                              height={35}
                                              className="expert_image"
                                            />
                                          </>
                                        ) : (
                                          <>
                                            <Image
                                              src={profile}
                                              alt={`Dr`}
                                              width={35}
                                              height={35}
                                              className="expert_image"
                                            />
                                          </>
                                        )}
                                        &nbsp;
                                        <Link
                                          href={`/usermanagement/patients/${patient["patient details"]?.id}/${patient["patient details"]?.email}/${patient["patient details"]?.approval}/${patient["patient details"]?.name}`}
                                          className="main-purple-content text-decoration-none text-capitalize"
                                        >
                                          {highlightText(
                                            patient["patient details"]?.name,
                                            searchQuery
                                          )}
                                        </Link>
                                      </td>
                                      <td className=" custom-font  text-center">
                                        {convertDateFormat(
                                          patient["patient details"]
                                            ?.DateOfRegistration
                                        )}
                                      </td>
                                      <td
                                        className={`text-center text-capitalize ${
                                          patient["patient details"]
                                            ?.approval === "Approved"
                                            ? "text-success"
                                            : patient["patient details"]
                                                ?.approval === "Deactivated"
                                            ? "text-warning"
                                            : patient["patient details"]
                                                ?.approval === "Deleted" ||
                                              "self_deleted"
                                            ? "text-danger"
                                            : ""
                                        }`}
                                        style={{ fontSize: "12px" }}
                                      >
                                        {patient["patient details"]
                                          ?.approval === "Approved"
                                          ? "Active"
                                          : patient["patient details"]
                                              ?.approval}
                                      </td>
                                    </tr>
                                  );
                                })}
                            </>
                          ) : (
                            <tr>
                              <td colSpan="5 my-5">
                                <NoDataFound />
                              </td>
                            </tr>
                          )}
                        </>
                      )}
                    </tbody>
                  </table>
                  {totalPages > 1 && (
                    <div className="d-flex justify-content-center align-items-center">
                      <div className="d-none d-xl-block">
                        <CustomPagination
                          total_pages={totalPages}
                          current_page={current_page}
                          setCurrent_Page={setCurrent_Page}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default dynamic(() => Promise.resolve(PatientsAllData), { ssr: false });
