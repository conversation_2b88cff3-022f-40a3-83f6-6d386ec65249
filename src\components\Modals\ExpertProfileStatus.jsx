import { usePathname } from "next/navigation";
import React, { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";

const getOptions = (pathname, rejectionCategories) => {
  if (pathname.startsWith("/usermanagement/patients/")) {
    return rejectionCategories.map((category, index) => (
      <option key={index} value={category.Content}>
        {category.Content}
      </option>
    ));
  } else if (pathname.startsWith("/usermanagement/experts/")) {
    return rejectionCategories.map((category, index) => (
      <option key={index} value={category.Content}>
        {category.Content}
      </option>
    ));
  }
  return null;
};

const ExpertProfileStatus = ({
  selectedAction,
  setSelectedAction,
  showModal,
  setShowModal,
  setDeactivationReason,
  setRejectionReason,
  handleProfileApprovals,
  deactivationReason,
  rejectionReason,
  setReasonCategory,
  reasonCategory,
}) => {
  const pathname = usePathname();
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const [rejectionCategories, setRejectionCategories] = useState([]);

  const fetchRejectionCategories = useCallback(async () => {
    try {
      let category = "";
      if (pathname.startsWith("/usermanagement/patients/")) {
        if (selectedAction === "Deactivate Profile") {
          category = "Patient Deactivation";
        } else if (selectedAction === "Reject Profile") {
          category = "Patient Rejection";
        }
      } else if (pathname.startsWith("/usermanagement/experts/")) {
        if (selectedAction === "Deactivate Profile") {
          category = "Expert Deactivation";
        } else if (selectedAction === "Reject Profile") {
          category = "Expert Rejection";
        }
      }

      if (category) {
        const rejectCategoryResponse = await axiosAuth.get(
          `${
            process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE
          }all/?Category=${encodeURIComponent(category)}&user_id=${user_id}`
        );
        setRejectionCategories(rejectCategoryResponse?.data || []);
      }
    } catch (error) {
      console.error("Error fetching rejection categories", error);
      toast.error(`Failed to fetch rejection categories.`, {
        autoClose: 3000,
        position: "top-center",
      });
    }
  }, [axiosAuth, pathname, selectedAction, user_id]);

  useEffect(() => {
    fetchRejectionCategories();
  }, [fetchRejectionCategories]);

  const handleConfirmation = async () => {
    let action;
    let reason;

    if (deactivationReason) {
      action = "Deactivated";
      reason = deactivationReason;
    } else if (rejectionReason) {
      action = "Rejected";
      reason = rejectionReason;
    }
    // else {
    //   return;
    // }

    if (!reasonCategory) {
      toast.error(`Please select the reason Category`);
      return;
    }
    if (!reason) {
      toast.error(`Please provide a reason`);
      return;
    }

    try {
      // Assuming handleProfileApprovals handles both rejection and deactivation
      await handleProfileApprovals(action, reason);
      setDeactivationReason("");
      setRejectionReason("");
      setShowModal(false);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        contentLabel={`${selectedAction} Confirmation`}
        className="custom-deactivate-width"
        size="lg"
        centered
        backdrop="static"
      >
        <div className="modal-content modal-content-deactivate-modal">
          <Modal.Header>
            <Modal.Title className="custom-title-2">
              {`${selectedAction} `}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p className="">
              Are you sure you want to {selectedAction?.toLowerCase()}?
            </p>
            <div className="mb-3">
              <label htmlFor="reasonSelect" className="form-label">
                Select a reason:
              </label>
              {selectedAction === "Reject Profile" && (
                <>
                  <select
                    className="form-select"
                    id="categorySelect"
                    value={reasonCategory}
                    onChange={(e) => setReasonCategory(e.target.value)}
                  >
                    <option value="">Select a reason category</option>
                    {getOptions(pathname, rejectionCategories)}
                  </select>
                </>
              )}
              {selectedAction === "Deactivate Profile" && (
                <select
                  className="form-select"
                  id="categorySelect"
                  value={reasonCategory}
                  onChange={(e) => setReasonCategory(e.target.value)}
                >
                  <option value="">Select a reason category</option>
                  {getOptions(pathname, rejectionCategories)}
                </select>
              )}
            </div>

            <textarea
              className="custom-textarea"
              placeholder={`Enter reason for ${selectedAction?.toLowerCase()}...`}
              rows={5}
              value={
                selectedAction === "Deactivate Profile"
                  ? deactivationReason
                  : rejectionReason
              }
              onChange={(e) => {
                if (selectedAction === "Deactivate Profile") {
                  setDeactivationReason(e.target.value);
                } else if (selectedAction === "Reject Profile") {
                  setRejectionReason(e.target.value);
                }
              }}
            />
          </Modal.Body>
          <Modal.Footer>
            <Button
              className="btn-cancel-deact border-0"
              variant={"danger"}
              onClick={() => {
                setShowModal(false);
                setSelectedAction("Profile Actions");
              }}
            >
              Cancel
            </Button>
            <Button
              variant={
                selectedAction === "Deactivate Profile" ? "info" : "danger"
              }
              className="btn-deact border-0"
              onClick={handleConfirmation}
            >
              {`${selectedAction}`}
            </Button>
          </Modal.Footer>
        </div>
      </Modal>
    </>
  );
};

export default ExpertProfileStatus;
