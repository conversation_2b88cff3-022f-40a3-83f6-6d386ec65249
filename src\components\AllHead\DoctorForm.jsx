import React from "react";
import {Row, Col, Form } from 'react-bootstrap';
import { MdOutlineAccessTime } from "react-icons/md";
 const DoctorForm =() =>{
    return(
        <>
        {/* ---------------Doctor Form----------------- */}
        <Form>
            <Row>
                      <Col md={12} >
                      <Form.Group controlId="name">
                        <Form.Label className='label-edit mt-4'>Name</Form.Label>
                        {/* Add patient-specific form fields here */}
                        <Form.Control type="text" className='input-form-modal'/>
                        </Form.Group>
                      </Col>
                      <Col md={6} >
                      <Form.Group controlId="phoneNo">
                        <Form.Label className='label-edit mt-2'>Phone Number</Form.Label>
                        <Form.Control type="text" className='input-form-modal'/>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                      <Form.Group controlId="Email">
                        <Form.Label className='label-edit mt-2'>Email</Form.Label>
                        <Form.Control type="email" className='input-form-modal'/>
                        </Form.Group>
                      </Col>
                      <Col md={6} >
                      <Form.Group controlId="TimeZone">
                        <Form.Label className='label-edit mt-2'>Timezone</Form.Label>
                        <MdOutlineAccessTime className='timezone-icon' />
                        <Form.Control type="text" className='input-form-modal' />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                      <Form.Group controlId="gender">
                        <Form.Label className='label-edit mt-2'>Gender</Form.Label>
                        <Form.Control as="select" className='input-form-modal' placeholder="Select gender">
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                          <option value="other">Other</option>
                        </Form.Control>
                        </Form.Group>
                      </Col>
                      <Col md={6} >
                      <Form.Group controlId="racticingHospital">
                        <Form.Label className='label-edit mt-2'>Practicing Hospital</Form.Label>
                        <Form.Control type="text" className='input-form-modal'/>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                      <Form.Group controlId="Department">
                        <Form.Label className='label-edit mt-2'>Department</Form.Label>
                        <Form.Control type="text" className='input-form-modal'/>
                        </Form.Group>
                      </Col>
                      <Col md={12} >

                        <Form.Group controlId="textArea">
                          <Form.Label className='label-edit mt-2'>Address</Form.Label>
                          <Form.Control
                            as="textarea"
                            className="input-form-modal"
                            rows={5} // Adjust the number of rows as needed
                            
                            
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6} >
                      <Form.Group controlId="membership">
                        <Form.Label className='label-edit mt-3'>Member Code</Form.Label>
                        <Form.Control type="text" className='input-form-modal'  />
                        </Form.Group>
                      </Col>
                      <Col md={4}className="offset-2">
                      <button type="button" className='btn purple-modal-btn' >Submit</button>
                      </Col>
                    </Row>
                    </Form>
        </>
    )
 }
 export default DoctorForm;