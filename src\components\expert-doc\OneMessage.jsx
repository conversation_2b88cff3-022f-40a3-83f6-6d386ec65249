import React, { useRef, useState } from "react";
import { Form, InputGroup, Button } from "react-bootstrap";
import { ImAttachment } from "react-icons/im";
import { HiFlag, HiOutlineFlag } from "react-icons/hi2";
const OneMessage = () => {
  const fileInputRef = useRef(null);
  const [selectedFileName, setSelectedFileName] = useState(null);

  const handleAttachmentClick = () => {
    // Programmatically trigger the file input click
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    // Handle the selected file
    const selectedFile = event.target.files[0];

    // Update state to display the selected file name
    setSelectedFileName(selectedFile ? selectedFile.name : null);

    // Add your file upload logic here
  };
  const [flagFilled, setFlagFilled] = useState(false);

  const handleFlagClick = () => {
    setFlagFilled((prevFlagFilled) => !prevFlagFilled);
  };
  return (
    <>
      <div className="col-sm">
        <div className="queiries-lines">
          <span className="article-query">
            Article on Advanced Treatment of cancer cells
          </span>
          <span className="flag-advance">
            {" "}
            {flagFilled ? (
              <HiFlag color="red" onClick={handleFlagClick} />
            ) : (
              <HiOutlineFlag color="gray" onClick={handleFlagClick} />
            )}
          </span>
          <p className="article-lines">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua. Bibendum
            est ultricies integer quis. Iaculis urna id volutpat lacus laoreet.
            Mauris vitae ultricies leo integer malesuada. Ac odio tempor orci
            dapibus ultrices in. Egestas diam in arcu cursus euismod. Dictum
            fusce ut placerat orci nulla. Tincidunt ornare massa eget egestas
            purus viverra accumsan in nisl. <br />
            <br />
            Tempor id eu nisl nunc mi ipsum faucibus. Fusce id velit ut tortor
            pretium. Massa ultricies mi quis hendrerit dolor magna eget. Nullam
            eget felis eget nunc lobortis. Faucibus ornare suspendisse sed nisi.
            Sagittis eu volutpat odio facilisis mauris sit amet massa.
            <br />
            <br />
            Erat velit scelerisque in dictum non consectetur a erat. Amet nulla
            facilisi morbi tempus iaculis urna. Egestas purus viverra accumsan
            in nisl. Feugiat in ante metus dictum at tempor commodo. Convallis
            tellus id interdum velit laoreet. Proin sagittis nisl rhoncus mattis
            rhoncus urna neque viverra. Viverra aliquet eget sit amet tellus
            cras adipiscing enim eu. Ut faucibus pulvinar elementum integer enim
            neque volutpat ac
          </p>
          <Form>
            <Form.Group controlId="exampleForm.ControlTextarea1">
              <Form.Control as="textarea" className="text-advance" rows={8} />
            </Form.Group>
            <div className="attach-button">
              <label htmlFor="fileInput" className="label-attach">
                <ImAttachment
                  className="icon-attach"
                  onClick={handleAttachmentClick}
                  style={{ color: "#808080" }}
                />
                {/* Hidden file input */}
                <input
                  id="fileInput"
                  ref={fileInputRef}
                  type="file"
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                />
                Attach
              </label>

              {/* Display the selected file name */}
              {selectedFileName && <p>Selected File: {selectedFileName}</p>}
              <Button className="btn btn-reply-msg" type="button">
                Replay
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </>
  );
};

export default OneMessage;
