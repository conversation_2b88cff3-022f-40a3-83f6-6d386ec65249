"use client";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import firebaseApp from "@/utils/firebase";

export const AdminDetailsContext = createContext();

export default function AdminProvider({ children }) {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState({});
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const axiosAuth = useAxiosAuth();
  const { data: session, status } = useSession();
  const [currentToken, setCurrentToken] = useState();
  const [notificationPermissionStatus, setNotificationPermissionStatus] =
    useState("");

  const userId = useMemo(() => session?.user?.id, [session]);
  const emailAddress = useMemo(() => session?.user?.email, [session]);

  const getPatientData = useCallback(async () => {
    try {
      if (emailAddress) {
        setLoading(true);
        const resp = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_USER_DATA}${emailAddress}/`
        );
        setUserData(resp?.data?.user_data);
        const phoneVerified =
          resp?.data?.user_data?.admin_other_details?.PhoneVerified;

        if (phoneVerified === true) {
          setIsPhoneVerified(true);
        } else if (phoneVerified === false) {
          setIsPhoneVerified(false);
        }
      }
    } catch (err) {
      console.log("Error in getting user Data", err);
    } finally {
      setLoading(false);
    }
  }, [emailAddress, axiosAuth]);

  useEffect(() => {
    if (emailAddress) {
      getPatientData();
    }
  }, [getPatientData, emailAddress]);

  const newRefreshToken = useMemo(() => {
    return session?.user?.refresh_token || null;
  }, [session]);

  const newAccessToken = useMemo(() => {
    return session?.user?.access_token || null;
  }, [session]);

  useEffect(() => {
    if (userId) {
      Cookies.set("user_id", userId);
    }
  }, [userId]);

  const isAdminChildAdmin = useMemo(
    () => userData?.role === "child_admin",
    [userData]
  );
  const isAdmin = useMemo(() => userData?.role === "admin", [userData]);

  const userPermissions = useMemo(
    () => userData?.permissions || [],
    [userData]
  );

  const retrieveToken = useCallback(async () => {
    if (typeof window === "undefined" || !("serviceWorker" in navigator))
      return;

    try {
      const messaging = getMessaging(firebaseApp);
      const permission = await Notification.requestPermission();
      setNotificationPermissionStatus(permission);

      if (permission !== "granted") {
        console.warn("Notification permission not granted.");
        return;
      }

      const currentToken = await getToken(messaging, {
        vapidKey: process.env.NEXT_PUBLIC_VAPID_KEY,
      });

      if (!currentToken) {
        return;
      }

      setCurrentToken(currentToken);

      // Check if token is already stored to prevent duplicate API calls
      const storedToken = localStorage.getItem("fcm_token");
      if (storedToken === currentToken) {
        return;
      }

      if (session) {
        try {
          const response = await axiosAuth.post(
            `${process.env.NEXT_PUBLIC_FIREBASE_SETUP_DEVICE_API}`,
            {
              registration_id: currentToken,
              type: "web",
            }
          );
          localStorage.setItem("fcm_token", currentToken);
        } catch (error) {
          console.error("Error registering device on server:", error);
        }
      }
    } catch (error) {
      console.error("An error occurred while retrieving token:", error);
    }
  }, [session, axiosAuth]);

  useEffect(() => {
    retrieveToken();
  }, [retrieveToken]);

  useEffect(() => {
    if (!currentToken) return;

    const messaging = getMessaging(firebaseApp);
    onMessage(messaging, (payload) => {
      const { notification, fcmOptions } = payload;

      if (Notification?.permission === "granted") {
        const notificationOptions = {
          title: notification.title,
          body: notification.body,
          icon: "/image/CU_logo.png",
        };

        const notificationInstance = new Notification(
          notificationOptions.title,
          notificationOptions
        );
        notificationInstance.onclick = () => {
          if (fcmOptions?.link) window.open(fcmOptions.link, "_blank");
        };
      }
    });
  }, [currentToken]);



  return (
    <AdminDetailsContext.Provider
      value={{
        session,
        isAdminChildAdmin,
        userPermissions,
        isPhoneVerified,
        loading,
        getPatientData,
        newRefreshToken,
        userId,
        newAccessToken,
        isAdmin,
        notificationPermissionStatus,
      }}
    >
      {children}
    </AdminDetailsContext.Provider>
  );
}

export function useAdminContext() {
  return useContext(AdminDetailsContext);
}
