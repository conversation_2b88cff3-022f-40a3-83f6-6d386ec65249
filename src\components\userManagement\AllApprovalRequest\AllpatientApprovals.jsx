import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import Link from "next/link";
import { capitalizeFullName } from "../../../utils/helperfunction";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import CustomPagination from "../../../components/CustomPagination/CustomPagination.jsx";
import AppointmentApprovalReq from "./AppointmentApprovalReq";
import { Button, Form, Modal } from "react-bootstrap";
import { toast } from "react-toastify";
import PatientApprovalPlaceholder from "./PatientApprovalPlaceholder";
import NoDataFound from "@/components/noDataFound/NoDataFound";

const AllpatientApprovals = ({ userId }) => {
  const [singleApproval, setSingleApproval] = useState({});
  const [loading, setLoading] = useState(true);
  const [initialLoading, setInitialLoading] = useState(true);
  const [patientApprovals, setpatientApprovals] = useState([]);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reason, setReason] = useState("");
  const [rejectReasonModal, setRejectReasonModal] = useState(false);
  const [current_page, setCurrent_Page] = useState(1);
  const [reasonCategory, setReasonCategory] = useState("");
  const [rejectionCategories, setRejectionCategories] = useState();

  const axiosAuth = useAxiosAuth();

  const fetchAllPatientApprovals = useCallback(
    async (pageNumber) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_ALL_PATIENTS_BY_TYPE}?status=Approval_requested&user_id=${userId}`;

        if (pageNumber) {
          url += `&page=${pageNumber}`;
        }
        const response = await axiosAuth.get(url);
        setpatientApprovals(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [userId, axiosAuth]
  );

  useEffect(() => {
    fetchAllPatientApprovals(current_page);
  }, [fetchAllPatientApprovals, current_page]);

  const handleProfileApprovals = async (id, status) => {
    try {
      let body = { approval: status };
      if (status === "Rejected") {
        body.StatusChangeReason = reason;
        body.ReasonType = "Rejection";
        body.ReasonCategory = reasonCategory;
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${id}/?user_id=${userId}`,
        body
      );
      if (response?.data?.data?.approval === "Approved") {
        toast.success(`The Profile has been Approved successfully`);
      } else if (response?.data?.data?.approval === "Deactivated") {
        toast.success(`The Profile has been Rejected successfully`);
      } else {
        toast.error(response?.data?.data);
      }
      fetchAllPatientApprovals(1);
      setShowReasonModal(false);
      setRejectReasonModal(false);
      setReason("");
    } catch (error) {
      console.error(error);
    }
  };

  const handleOpenModal = (patient) => {
    setSingleApproval(patient);
    setShowReasonModal(true);
  };

  const handleReject = async () => {
    try {
      setRejectReasonModal(true);
    } catch (error) {
      console.error("Error in handling rejection: ", error);
    }
  };
  const fetchRejectionCategories = useCallback(async () => {
    try {
      const rejectCategoryResponse = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}all/?Category=Patient Rejection&user_id=${userId}`
      );
      setRejectionCategories(rejectCategoryResponse?.data || []);
    } catch (err) {
      console.log("error in fetching the categories", err);
    }
  }, [axiosAuth, userId]);

  useEffect(() => {
    fetchRejectionCategories();
  }, [fetchRejectionCategories]);

  return (
    <div className=" overflow-hidden">
      <div className="user-management-scroll overflow-auto">
        <div className="row">
          <div className="col-sm-6 gx-2">
            <p className="allApproval-subHeading mt-2">
              Patient Profile Approval
            </p>

            <table className="table mt-2">
              <thead className="allApproval-patient-head">
                <tr className="">
                  <th scope="col" className="text-center allApproval-heading">
                    Patient Name
                  </th>
                  <th scope="col" className="text-center allApproval-heading">
                    Date of Application
                  </th>
                  <th scope="col" className="text-center allApproval-heading">
                    Status
                  </th>
                  <th scope="col" className="text-center allApproval-heading">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="allApproval-patient-head">
                {patientApprovals &&
                patientApprovals?.patient_data?.length === 0 ? (
                  <tr>
                    <td colSpan="4">
                      <div className=" mt-5">
                        <NoDataFound />
                      </div>
                    </td>
                  </tr>
                ) : loading ? (
                  <PatientApprovalPlaceholder />
                ) : (
                  patientApprovals?.patient_data?.map((patient, index) => (
                    <tr key={index} className="allApproval-patient-custom-row">
                      <td className="text-center allApproval-patient-content d-flex align-items-center pr-4">
                        {patient?.other_details?.ProfilePhoto ? (
                          <>
                            <Image
                              // src={patient?.profile_photo}
                              src={patient?.other_details?.ProfilePhoto}
                              alt={`Dr ${patient?.["patient details"]?.name}`}
                              width={35}
                              height={35}
                              className="expert_image"
                            />
                          </>
                        ) : (
                          <>
                            <Image
                              src={dummyProfile}
                              alt={`Dr ${patient?.["patient details"]?.name}`}
                              width={35}
                              height={35}
                              className="expert_image"
                            />
                          </>
                        )}
                        <Link
                          href=""
                          // href={`/usermanagement/experts/experts-doctor/${expert.id}/${expert.email}/${expert.approval}`}
                          className="text-decoration-none"
                        >
                          <span className="allApproval-patient-content">
                            {capitalizeFullName(
                              patient?.["patient details"]?.name
                            )}
                          </span>
                        </Link>
                      </td>

                      <td className="text-center">
                        {
                          patient?.[
                            "patient details"
                          ]?.DateOfRegistration?.split("T")[0]
                        }
                      </td>
                      <td
                        className="text-center"
                        style={{
                          color: "orange",
                        }}
                      >
                        {patient?.["patient details"]?.approval}
                      </td>
                      <td
                        className="text-center fw-semibold"
                        style={{ color: "#8107d1", cursor: "pointer" }}
                        onClick={() => {
                          handleOpenModal(patient);
                        }}
                      >
                        View
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            <div className="d-flex justify-content-center align-items-center">
              <div className="d-none d-xl-block">
                {patientApprovals?.total_pages !== 1 && (
                  <CustomPagination
                    total_pages={patientApprovals?.total_pages}
                    current_page={current_page}
                    setCurrent_Page={setCurrent_Page}
                  />
                )}
              </div>
            </div>
          </div>

          <AppointmentApprovalReq userId={userId} />
          <Modal
            show={showReasonModal}
            onHide={() => {
              setShowReasonModal(false);
            }}
            centered
          >
            <Modal.Header>
              <Modal.Title className="allApproval-subHeading">
                Reason for Appointment Cancellation
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <p>
                {singleApproval?.approval_status_reason?.reactivation_reason}
              </p>
            </Modal.Body>

            <Modal.Footer>
              <button
                type="button"
                className="btn allApproval-reject-btn"
                onClick={handleReject}
              >
                Reject
              </button>

              <button
                onClick={() =>
                  handleProfileApprovals(
                    singleApproval?.["patient details"]?.id,
                    "Approved"
                  )
                }
                type="button"
                className="btn allApproval-approve-btn ms-2"
              >
                Approve
              </button>
            </Modal.Footer>
          </Modal>

          {/* //reason modal */}
          <Modal
            show={rejectReasonModal}
            onHide={() => setRejectReasonModal(false)}
            centered
            size="lg"
          >
            <Modal.Header closeButton>
              <Modal.Title style={{ color: "#8107d1" }}>
                Enter Rejection Reason
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form.Group controlId="reasonCategory" className="my-3">
                <Form.Label>Reason Category</Form.Label>
                <Form.Select
                  value={reasonCategory}
                  onChange={(e) => setReasonCategory(e.target.value)}
                >
                  <option value="">Select category...</option>
                  {rejectionCategories?.map((category) => (
                    <option key={category.id} value={category.Content}>
                      {category.Content}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
              <Form.Group controlId="rejectionReason">
                <Form.Control
                  as="textarea"
                  rows={6}
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Enter reason for rejection..."
                />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="secondary"
                onClick={() => setRejectReasonModal(false)}
              >
                Cancel
              </Button>
              <Button
                style={{ backgroundColor: "#8107d1" }}
                onClick={() =>
                  handleProfileApprovals(
                    singleApproval?.["patient details"]?.id,
                    "Rejected"
                  )
                }
              >
                Submit
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default AllpatientApprovals;
