import React from "react";
import Image from "next/image";

import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { capitalizeFullName } from "../../../utils/helperfunction";

const AppointmentPreview = ({
  onClick,
  expertsList,
  setSelectedExpertId,
  setExpertTimeZone,
  setExpertName,
}) => {
  return (
    <>
      <div className="row mt-4">
        <div className="col-sm-4">
          <button
            type="button"
            className="btn btn-preview"
            onClick={() => onClick("doctor")}
          >
            Doctor
          </button>
        </div>
        <div className="col-sm-4 gx-0">
          <button
            type="button"
            className="btn btn-preview"
            onClick={() => onClick("researcher")}
          >
            Researchers
          </button>
        </div>{" "}
        <div className="col-sm-4">
          <button
            type="button"
            className="btn btn-preview"
            onClick={() => onClick("influencer")}
          >
            Health Guides
          </button>
        </div>
      </div>
      <div className="row">
        <div className="col-sm-12 mt-3">
          <div className="preview-content">
            {expertsList &&
              expertsList?.map((pair, index) => (
                <div
                  key={index}
                  className="inner-content mt-2"
                  onClick={() => {
                    setExpertTimeZone(pair.TimeZone);
                    setSelectedExpertId(pair.id);
                    setExpertName(pair.name);
                  }}
                >
                  <div className="d-flex col-8">
                    <Image
                      src={pair.profile_photo || dummyProfile}
                      className="profile-img-expert"
                      alt={`${pair.name}'s profile`}
                      width={40}
                      height={40}
                    />
                    <div>
                      <p className="mb-0 card-name">{pair.name}</p>
                      <p className="mb-0 expert-type">
                        {capitalizeFullName(pair.user_role)}
                      </p>
                    </div>
                  </div>
                  {/* <p className="">with</p>
                <div className="d-flex">
                  <Image
                    src={pair.patient.image}
                    className="profile-img-expert"
                  />
                  <div>
                    <p className="mb-0 card-name">{pair.patient.name}</p>
                    <p className="mb-0 type-patient">{pair.patient.type}</p>
                  </div>
                </div> */}
                  <div className="d-flex col-4">
                    {/* <Image
                    src={pair.patient.image}
                    className="profile-img-expert"
                  /> */}
                    <div className="active-approval">
                      <p className="mb-0 card-name">{pair?.approval}</p>
                      <p className="mb-0 type-patient">
                        Status :{" "}
                        {pair?.is_active === true ? "Active" : "In Active"}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default AppointmentPreview;
