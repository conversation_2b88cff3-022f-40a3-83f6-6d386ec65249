import React from "react";
import { Modal } from "react-bootstrap";
import "./rejectionmodal.css";
const RejectionModal = ({
  RejectModalAndDetails,
  setRejectModalAndDetails,
}) => {
  return (
    <div>
      <Modal
        show={RejectModalAndDetails?.rejectShowModal}
        onHide={() => setRejectModalAndDetails({ rejectShowModal: false })}
        centered
        size="lg"
        backdrop="static"
      >
        <Modal.Header closeButton>
          <Modal.Title className="rejectReason-model-section">
            <div className="rejectReason-title">
              Reason for {RejectModalAndDetails.type} profile.
            </div>
            <div className="rejectReason-requested-date">
              Requested Date :
              <span className="rejectReason-requested-date-value">
                {RejectModalAndDetails?.rejectedDate?.split("T")[0]}
              </span>
            </div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>{RejectModalAndDetails?.rejectedReason}</Modal.Body>
      </Modal>
    </div>
  );
};

export default RejectionModal;
