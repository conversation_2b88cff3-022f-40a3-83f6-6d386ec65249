import Image from "next/image";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import useAxiosAuth from "../../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import {
  capitalizeFullName,
  highlightText,
} from "../../../../utils/helperfunction";
import dummyProfile from "../../../../../public/images/dummy-avatar.jpg";
import _ from "lodash";
import AllBlogsFilter from "../../BlogsContent/AllBlogsFilter";
import CustomPagination from "../../../CustomPagination/CustomPagination";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import Swal from "sweetalert2";
import axios from "axios";
import NoDataFound from "../../../noDataFound/NoDataFound";

const AskRuchikaExperts = () => {
  const [expertsData, setExpertsData] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [review, setReview] = useState("");
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(true);
  const [initialLoading, setInitialLoading] = useState(true);
  const [deletingExpertId, setDeletingExpertId] = useState(null);
  const [selectedExpert, setSelectedExpert] = useState({});
  const [current_page, setCurrent_Page] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [experience, setExperience] = useState();

  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = Cookies.get("userId") || session?.user.id;

  const fetchExpertsData = useCallback(
    async (query) => {
      try {
        setLoading(true);
        if (current_page === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_EXPERTS_BY_FILTER}?page=${current_page}`;
        if (query) {
          url += `&name=${query}`;
        }
        if (experience) {
          url += `&exp=${experience}`;
        }
        const response = await axios.get(url);
        setExpertsData(response?.data);
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      } catch (error) {
        console.error("Error fetching Experts: ", error);
      } finally {
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      }
    },
    [current_page, experience]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      fetchExpertsData(query);
    }, 500);
  }, [fetchExpertsData]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  const handleAddRanking = async (id) => {
    const body = {
      ExpertId: id,
    };
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to add this expert in the recommended experts?",
      // icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });
    if (result.isConfirmed) {
      try {
        const response = await axiosAuth.post(
          `${process.env.NEXT_PUBLIC_CREATE_RECOMMENDED_EXPERTS}?user_id=${admin_id}`,
          body
        );
        const responseData = response?.data;

        if (
          responseData ===
          "Successfully added the expert to ask ruchika expert_data"
        ) {
          toast.success(
            `${"Successfully added the expert to recommended expert"}`,
            {
              position: "top-center",
              theme: "colored",
              autoClose: 2500,
            }
          );
        } else if (responseData === "Expert has been already selected.") {
          toast.error(
            "The expert has already been assigned to recommended experts",
            {
              position: "top-center",
              theme: "colored",
              autoClose: 2500,
            }
          );
        }
        fetchExpertsData();
      } catch (err) {
        toast.error("Error in adding this recommended expert", {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      } finally {
      }
    }
  };

  const handleDeleteRanking = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this recommended expert?",
      // icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });
    if (result.isConfirmed) {
      try {
        setDeletingExpertId(id);
        setDeleteLoading(true);
        const response = await axiosAuth.delete(
          `${process.env.NEXT_PUBLIC_UPDATE_RECOMMENDED_EXPERTS}${id}/?user_id=${admin_id}`
          // { data: body }
        );
        if (response?.status === 204) {
          toast.success(
            `Successfully deleted the expert from the recommended experts`
          );
        }
        setDeleteLoading(false);
        await fetchExpertsData();
      } catch (err) {
        toast.error(`Error in deleting the rank`);
      } finally {
        setDeleteLoading(false);
        setDeletingExpertId(null);
      }
    }
  };

  const handleOpenRankingModal = (item) => {
    setSelectedExpert(item);
    setShowModal(true);
  };


  return (
    <>
      <div className="bg-color mb-4 p-2">
        <h5 className=" admin-add-blog-list-header fw-semibold">
          Add Recommended experts for Ask Ruchika
        </h5>
        <div className="row">
          <AllBlogsFilter
            setSearchQuery={setSearchQuery}
            setExperience={setExperience}
            experience={experience}
            searchQuery={searchQuery}
            loading={loading}
            setReview={setReview}
            topAuthors={true}
            review={review}
            isTopExperts={true}
          />
        </div>

        <div className="row">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr className="text-center">
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Sl No
                  </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Expert ID
                  </th>
                  <th colSpan={2} scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Select Recommended Expert
                  </th>
                  <th scope="col" style={{  fontSize: "14px" }} className="fw-semibold">Expert Profile</th>
                  <th scope="col" style={{  fontSize: "14px" }} className="fw-semibold">Expert Role</th>
                  <th scope="col" style={{ width: "150px", fontSize: "14px" }} className="fw-semibold">
                    Reviews
                  </th>
                  <th scope="col" style={{ width: "150px", fontSize: "14px" }} className="fw-semibold">
                    Remove
                  </th>
                </tr>
              </thead>
              <tbody>
                {expertsData?.experts_data?.length > 0 ? (
                  expertsData?.experts_data?.map((item, index) => (
                    <tr key={index}>
                      <td scope="row" className="col custom-font ">{index + 1}</td>
                      <td className="col custom-font">{item?.expert_details?.id}</td>
                      <td
                        colSpan={2}
                        className="col custom-font text-center items-center"
                      >
                        <div className=" d-flex align-items-center">
                          <input
                            className=" form-check-input  blogs-content-input"
                            type="checkbox"
                            name="category"
                            id="expertCategory"
                            value="Expert Rejection"
                            checked={item?.expert_recommended === true}
                            onChange={() =>
                              handleAddRanking(item?.expert_details?.id)
                            }
                          />
                          <div className=" ms-2 ">
                            {item?.expert_recommended === true && (
                              <span className="badge badge-success bg-success">
                                Recommended
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="text-start purple-content pr-4">
                        {item?.expert_other_details?.ProfilePhoto ? (
                          <Image
                            src={item?.expert_other_details?.ProfilePhoto}
                            alt={item?.expert_details?.name}
                            width={35}
                            height={35}
                            className="expert_image"
                          />
                        ) : (
                          <Image
                            src={dummyProfile}
                            alt={item?.expert_details?.name}
                            width={35}
                            height={35}
                            className="expert_image"
                          />
                        )}
                        <span>
                          {item?.expert_details?.prefix}{" "}
                          {highlightText(
                            capitalizeFullName(item?.expert_details?.name),
                            searchQuery
                          )}
                        </span>
                      </td>
                      <td className="custom-font">
                        {capitalizeFullName(
                          item?.expert_role ? item?.expert_role : "Expert Role"
                        )}
                      </td>
                      <td className="custom-font">{item?.rating}</td>
                      <td >
                        <button
                          style={{ color: "red", fontSize: "15px" }}
                          className=" border-0 bg-white delete-ranking-exp-btn custom-font fw-semibold" 
                          onClick={() =>
                            handleDeleteRanking(item?.expert_details?.id)
                            //handleDeleteRanking
                            //handleDeleteRanking
                          }
                          disabled={
                            deletingExpertId === item?.expert_details?.id ||
                            item?.expert_recommended === false
                          }
                        >
                          {deletingExpertId === item?.id
                            ? "Removing..."
                            : "Remove"}
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="10">
                      <NoDataFound />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {expertsData?.total_pages !== 1 && (
          <div className="d-flex justify-content-center align-items-center mt-3">
            <div className="d-none d-xl-block">
              <CustomPagination
                total_pages={expertsData?.total_pages}
                current_page={current_page}
                setCurrent_Page={setCurrent_Page}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default AskRuchikaExperts;
