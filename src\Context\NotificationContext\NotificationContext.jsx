"use client";

import { useSession } from "next-auth/react";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";

const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const [notifications, setNotifications] = useState([]);
  const [totalNotifications, setTotalNotifications] = useState(0);
  const [notificationLoading, setNotificationLoading] = useState(true);
  const [patientData, setPatientData] = useState("");
  const [patientLoading, setPatientLoading] = useState(true);

  const userId = useMemo(() => session?.user?.id, [session]);
  const emailAddress = useMemo(() => session?.user?.email, [session]);

  const getPatientData = useCallback(async () => {
    try {
      if (emailAddress && userId) {
        setPatientLoading(true);
        const resp = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_USER_DATA}${emailAddress}/`
        );
        setPatientData(resp?.data?.user_data);
      }
    } catch (err) {
      console.log("error in getting user data ", err);
    } finally {
      setPatientLoading(false);
    }
  }, [userId, emailAddress, axiosAuth]);
  useEffect(() => {
    getPatientData();
  }, [getPatientData]);

  const fetchNotifications = useCallback(async () => {
    try {
      setNotificationLoading(true);
      if (userId) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_ALL_NOTIFICATIONS}${userId}/`
        );
        setNotifications(response?.data);
        setTotalNotifications(response?.data?.length); // Update total count
        setNotificationLoading(false);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setNotificationLoading(false);
    }
  }, [axiosAuth, userId]);

  // Update notification status to 'read' when clicked
  const updateNotificationStatus = async (id) => {
    try {
      const resp = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_READ_NOTIFICATIONS}${id}/`
      );
      fetchNotifications();
    } catch (error) {
      console.error("Error updating notification status:", error);
    }
  };

  // Fetch notifications when component mounts
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        totalNotifications,
        notificationLoading,
        fetchNotifications,
        updateNotificationStatus,
        patientData,
        patientLoading,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => useContext(NotificationContext);
