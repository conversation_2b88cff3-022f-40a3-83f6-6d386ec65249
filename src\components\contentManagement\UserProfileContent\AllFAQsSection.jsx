import React, { useState, useEffect, useCallback } from "react";
import { MdEdit, MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "react-bootstrap";
import Swal from "sweetalert2";
import Loader from "../../loader/Loader";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import ContentManPlaceholder from "../UserProfileContent/ContentManPlaceholder";

const AllFAQsSection = () => {
  const [showFAQEditModal, setShowFAQEditModal] = useState(false);
  const [selectedFAQ, setSelectedFAQ] = useState(null);
  const [listOfFAQs, setListOfFAQs] = useState(null);
  const [newFAQ, setNewFAQ] = useState({ question: "", answer: "" });
  const [formData, setFormData] = useState({
    id: "",
    question: "",
    answer: "",
  });
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getListOfFAQs = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_FAQS}?user_id=${user_id}`
        );
        setListOfFAQs(response?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  useEffect(() => {
    getListOfFAQs();
  }, [user_id, getListOfFAQs, axiosAuth]);

  const handleAddFAQ = async () => {
    if (!newFAQ.question || !newFAQ.answer) {
      toast.error("Both question and answer are required!");
      return;
    }

    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_GET_FAQS}?user_id=${user_id}`,
        {
          question: newFAQ.question,
          answer: newFAQ.answer,
        }
      );
      setLoading(false);
      if (response?.data) {
        toast.success(`FAQ Added Successfully`);
        getListOfFAQs();
      }
      setNewFAQ({ question: "", answer: "" });
    } catch (error) {
      console.log("Error in adding the FAQ", error);
      toast.error(`Something Went Wrong!`);
    }
  };

  const handleEditClick = (faq) => {
    setSelectedFAQ(faq);
    setFormData({ id: faq.id, question: faq.question, answer: faq.answer });
    setShowFAQEditModal(true);
  };

  const handleClose = () => {
    setShowFAQEditModal(false);
    setSelectedFAQ(null);
  };

  const handleDeleteClick = async (faqId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_CRUD_FAQS}${faqId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`FAQ Deleted Successfully`);
            getListOfFAQs();
          }
        } catch (error) {
          console.log("Error in deleting the FAQ", error);
          toast.error(`Something Went Wrong!`);
        }
      }
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CRUD_FAQS}${formData.id}/?user_id=${user_id}`,
        {
          question: formData.question,
          answer: formData.answer,
        }
      );
      if (response?.data) {
        toast.success(`FAQ Edited Successfully`);
        getListOfFAQs();
      }
      setFormData({ id: "", question: "", answer: "" });
    } catch (error) {
      console.log("Error in editing the FAQ", error);
      toast.error(`Something Went Wrong!`);
    }
    handleClose();
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <>
      <div className="row">
        <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
          Add FAQs
        </h5>
        <div className="col-sm-4">
          <div className="mb-3">
            <label htmlFor="question" className="form-label custom-label">
              FAQ Question
            </label>
            <input
              type="text"
              className="form-control custom-form-control"
              id="question"
              value={newFAQ.question}
              onChange={(e) =>
                setNewFAQ({ ...newFAQ, question: e.target.value })
              }
              placeholder="Enter FAQ Question"
            />
          </div>
          <div className="mb-3">
            <label htmlFor="answer" className="form-label custom-label">
              FAQ Answer
            </label>
            <textarea
              className="form-control custom-form-control"
              id="answer"
              value={newFAQ.answer}
              onChange={(e) => setNewFAQ({ ...newFAQ, answer: e.target.value })}
              placeholder="Enter FAQ Answer"
              rows="4"
            />
          </div>
          <button
            disabled={loading}
            type="submit"
            onClick={handleAddFAQ}
            className="btn purple-button"
          >
            {loading ? "Adding FAQ" : "Add FAQ"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th
                    scope="col"
                    className="text-center"
                    style={{ width: "60px" }}
                  >
                    Slno
                  </th>
                  <th scope="col">FAQ Question</th>
                  <th scope="col">FAQ Answer</th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              <tbody>
                {listOfFAQs &&
                  Array.isArray(listOfFAQs) &&
                  listOfFAQs.map((item, index) => (
                    <tr key={item.id}>
                      <th scope="row" className="text-center custom-font-size">
                        {index + 1}
                      </th>
                      <td className="custom-font-size">{item.question}</td>
                      <td className="custom-font-size">{item.answer}</td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm purple-button"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit className="custom-font-size" />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item.id)}
                        >
                          <MdDelete className="custom-font-size" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Modal show={showFAQEditModal} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit FAQ</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="formFAQQuestion">
            <Form.Label>FAQ Question</Form.Label>
            <Form.Control
              type="text"
              name="question"
              value={formData.question}
              onChange={handleChange}
            />
          </Form.Group>
          <Form.Group controlId="formFAQAnswer" className="mt-3">
            <Form.Label>FAQ Answer</Form.Label>
            <Form.Control
              as="textarea"
              name="answer"
              value={formData.answer}
              onChange={handleChange}
              rows="4"
            />
          </Form.Group>
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            onClick={handleSubmit}
          >
            Update
          </Button>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default AllFAQsSection;
