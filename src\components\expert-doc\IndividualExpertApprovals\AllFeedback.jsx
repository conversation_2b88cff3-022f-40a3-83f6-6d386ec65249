import Image from "next/image";
import React, { useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { MdReviews } from "react-icons/md";

const AllFeedback = ({
  UnApprovedFeedback,
  unApprovedFeedbackLoading,
  setShowFeedbackModal,
}) => {
  const [singleFeedback, setSingleFeedback] = useState([]);
  const handleFeedbackApprovals = (item) => {
    setSingleFeedback(item);
    setShowFeedbackModal(true);
  };
  return (
    <div className="">
      <p className="fw-bold grey-text">Feedback</p>
      {unApprovedFeedbackLoading === false ? (
        <>
          {UnApprovedFeedback &&
          Array.isArray(UnApprovedFeedback) &&
          UnApprovedFeedback?.length === 0 ? (
            <div className="fs-4 d-flex align-items-center justify-content-center no-articles-found">
              <PiFolderNotchOpenFill color={"#8107d1"} size={30} />
              &nbsp; No Records Found
            </div>
          ) : (
            // <div style={{ maxHeight: "125px", overflowY: "auto" }}>
            Array.isArray(UnApprovedFeedback) &&
            UnApprovedFeedback?.map((item, index) => (
              // <div key={index} className="bg-color mb-2">
              //   <div className="row bg-grey upload-reviews">
              //     <div className="col-sm-1 fb-doctor-pic">
              //       {item.doctor_photo ? (
              //         <Image
              //           src={item.doctor_photo || profile}
              //           alt="feedback"
              //           height={35}
              //           width={35}
              //           style={{ borderRadius: "50%" }}
              //           onError={(e) => {
              //             e.target.src = "/images/profile.png"; // Set a fallback image when an error occurs
              //           }}
              //         />
              //       ) : (
              //         <Image
              //           src="/images/profile.png" // Fallback image
              //           alt="fallback"
              //           height={35}
              //           width={35}
              //           className="testimonial_image"
              //         />
              //       )}
              //     </div>
              //     <div className="col-sm-2">
              //       <p className="fw-semibold mb-0">
              //         {/* {formatDate(item.CurrentTime)} */}
              //       </p>
              //       <p className="btn-transparent custom-transperent-btn">
              //         {/* {timeDifference(item.CurrentTime)} */}
              //       </p>
              //     </div>
              //     <div className="col-sm-8 mb-0">
              //       <p>{item.FeedbackCategory}</p>
              //     </div>
              //     <div className="col-sm-1">
              //       <button
              //         type="button"
              //         className="btn btn-yellow"
              //         onClick={() => handleFeedbackApprovals(item)}
              //       >
              //         Review
              //       </button>
              //     </div>
              //   </div>
              // </div>

              <div key={index} className="col-sm-6 p-2">
                <div className="introvideo-bg p-3">
                  <div className="row ">
                    {/* <p className="allApproval-heading mb-3">{video.title}</p> */}
                  </div>
                  <div className="row">
                    <div className="col-sm-auto">
                      {item.doctor_photo ? (
                        <Image
                          src={item.doctor_photo || profile}
                          width={50}
                          height={50}
                          alt="profile-image"
                          className="allApproval-image"
                        />
                      ) : (
                        <Image
                          src="/images/profile.png"
                          alt="fallback"
                          height={50}
                          width={50}
                          className="testimonial_image"
                        />
                      )}
                    </div>
                    {/* <div className="col-sm-auto p-0">
                        <p className="allApproval-expert-name mb-0">
                          {video.name}
                        </p>
                        <p className="allApproval-expert-role">{video.role}</p>
                      </div> */}
                    <div className="col-sm-2">
                      <p className="fw-semibold mb-0">
                        {/* {formatDate(item.CurrentTime)} */}
                      </p>
                      <p className="btn-transparent custom-transperent-btn">
                        {/* {timeDifference(item.CurrentTime)} */}
                      </p>
                    </div>
                  </div>
                  <div className="row">
                    <p className="allApproval-expert-para">
                      <p>{item.FeedbackCategory}</p>
                    </p>
                    <div className="modal-footer">
                      <button
                        type="button"
                        className="btn allApproval-reject-btn"
                        // onClick={handleRejectFeedback}
                      >
                        Reject
                      </button>

                      <button
                        type="button"
                        className="btn allApproval-approve-btn ms-2"
                      >
                        Approve
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
            // </div>
          )}
        </>
      ) : (
        renderPlaceholders()
      )}
    </div>
  );
};

export default AllFeedback;
