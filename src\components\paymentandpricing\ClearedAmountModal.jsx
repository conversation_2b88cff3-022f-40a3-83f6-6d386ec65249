import React from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

const ClearedAmountModal = ({ show, handleClose, clearedAmount }) => (
  <Modal show={show} onHide={handleClose}>
    <Modal.Header closeButton>
      <Modal.Title>Cleared Amount</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <p>The cleared amount is: ${clearedAmount}</p>
    </Modal.Body>
    <Modal.Footer>
      <Button variant="secondary" onClick={handleClose}>
        Close
      </Button>
    </Modal.Footer>
  </Modal>
);

export default ClearedAmountModal;
