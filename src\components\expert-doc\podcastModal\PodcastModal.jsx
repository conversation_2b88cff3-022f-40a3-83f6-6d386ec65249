/* eslint-disable @next/next/no-img-element */
import Image from "next/image";
import React, { useState } from "react";
import Modal from "react-modal";
import { IoClose } from "react-icons/io5";
import RejectReasonModal from "../RejectReasonModal";
import { debounce } from "lodash";
import { capitalizeFullName } from "../../../utils/helperfunction";

const PodcastModal = ({
  showPodcastModal,
  setshowPodcastModal,
  singlePodcast,
  doctorsData,
}) => {
  //   const article = singleArticle?.blog_details;
  const [showReasonModal, setShowReasonModal] = useState(false);
  const handleClose = () => setshowPodcastModal(!showPodcastModal);
  const handleShowReasonModal = () => {
    setShowReasonModal(true);
  };
  const hideReasonModal = () => {
    setShowReasonModal(false);
  };
  const handleConsentApprovals = async (status, reason) => {
    let blog_status;

    if (status === "reject") {
      blog_status = 0;
    } else if (status === "approve") {
      blog_status = 1;
    }
    try {
      let dataToSend = { Status: blog_status };

      // Include reason only if status is reject
      if (status === "reject") {
        dataToSend.Reason = reason;
      }
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_GET_DOCTORS_CONSENT_FORM}${doctorsData?.id}/?user_id=${admin_id}`,
        dataToSend
      );
      if (response.data?.message?.Status === 1) {
        toast.success(`Consent Form Approved Successfully`);
        setshowConsentFormModal(false);
      } else if (response.data?.message?.Status === 0) {
        toast.success(`Consent Form Rejected Successfully`);
        setshowConsentFormModal(false);
      }
      fetchDoctorConsent();
    } catch (error) {
      console.error(error);
    }
  };
  const debouncedHandleConsentForm = debounce(handleConsentApprovals, 1000);

  return (
    <div>
      <Modal
        isOpen={showPodcastModal}
        onRequestClose={() => setshowPodcastModal(!showPodcastModal)}
        className=" article-modal-cont"
        // contentLabel="Example Modal"
      >
        <div className="modal-content custom-modal-content-article">
          <div className="d-flex justify-content-between ">
            <div style={{ color: "#8107D1" }}>
              <h2>Podcast no - {singlePodcast?.id}</h2>
            </div>
            <div>
              <IoClose
                onClick={handleClose}
                color="#8107D1"
                size={25}
                cursor={"pointer"}
              />
            </div>
          </div>
          <div
            className="modal-body-custom"
            style={{ maxHeight: "400px", overflowY: "auto" }}
          >
            {/* <h5>Podcast </h5> */}
            <p className=" text-capitalize">
              Doctor - {capitalizeFullName(doctorsData?.name)}
            </p>
            <h5>Podcast Topic : {singlePodcast?.PodcastTopic}</h5>
            <h5>Podcast Url : {singlePodcast?.PodcastURL}</h5>
            <div
              className=" d-flex px-2"
              style={{ color: "#8107D1", fontWeight: "bold" }}
            >
              <p className=" ms-auto">
                12 days ago
                {/* {convertDateFormat(article?.BlogDateTime.split("T")[0])} */}
              </p>
            </div>
          </div>
          {/* {showButtons && (
            <div className=" mt-5 d-flex align-items-center ms-auto me-3">
              <div className="">
                <button
                  type="button"
                  className="btn bg-danger text-white rounded-1"
                  onClick={()=>handleShowReasonModal()}
                >
                  Reject
                </button>
              </div>
              <div className="">
                <button
                  type="button"
                  className="btn btn-green ms-5 py-2"
                  onClick={() =>
                    debouncedHandleArticleApprovals(
                      "approve",
                      singleArticle.blog_details.id,
                      singleArticle.blog_details.BlogTitle
                    )
                  }
                >
                  Approve
                </button>
              </div>
            </div>
          )} */}
        </div>
      </Modal>

      {showReasonModal && (
        <RejectReasonModal
          show={handleShowReasonModal}
          onHide={hideReasonModal}
          debouncedHandleArticleApprovals={debouncedHandleArticleApprovals}
          singleArticle={singleArticle}
          type="article"
        />
      )}
    </div>
  );
};

export default PodcastModal;
