import Image from "next/image";
import React from "react";
import { Placeholder } from "react-bootstrap";
import profile from "../../../public/assets/sampleprofilepay.png";
import { PiCircleFill } from "react-icons/pi";
import nodataFound from "../../../public/images/nodata.png";
import NoDataFound from "../noDataFound/NoDataFound";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 6 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index}>
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "40px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const PaymentHistoryBody = ({
  allPayments,
  initialLoading,
  loading,
  handleViewInvoiceModal,
}) => {
  if (initialLoading) {
    return renderPlaceholders("initialLoad");
  }

  return (
    <tbody className="table-body">
      {allPayments?.items?.length > 0 ? (
        allPayments?.items?.map((item, index) => (
          <tr key={index}>
            <td className="patient-name-payment text-capitalize">
              {item?.invoice?.customer_name}
            </td>
            <td>{item?.AppointmentId}</td>
            <td>
              <PiCircleFill
                className={`${item?.payment_status}-indication mx-2`}
              />
              {item?.payment_status === "failed" && (
                <span className="canceled">Failed</span>
              )}
              {item?.payment_status === "succeeded" && (
                <span className="proceed">Success</span>
              )}
            </td>
            <td>$ {item?.amount?.toFixed(2)}</td>
            <td>{item?.date?.split("T")[0]}</td>

            <td>
              <button
                type="button"
                className="btn btn-purple-refund"
                onClick={() => handleViewInvoiceModal(item)}
              >
                View Invoice
              </button>
            </td>
          </tr>
        ))
      ) : (
        <tr>
          <td colSpan="10">
            <NoDataFound />
          </td>
        </tr>
      )}
      {loading && renderPlaceholders("load")}
    </tbody>
  );
};

export default PaymentHistoryBody;
