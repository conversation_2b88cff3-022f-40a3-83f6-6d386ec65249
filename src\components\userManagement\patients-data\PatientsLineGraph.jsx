import React, { useCallback, useEffect, useState } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { formatDateToYMD } from "@/utils/helperfunction";
import { Placeholder } from "react-bootstrap";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const PatientsLineGraph = () => {
  const { data: session } = useSession();
  const adminId = session?.user?.id;
  const [allPatientDetails, setAllPatientDetails] = useState([]);
  const axiosAuth = useAxiosAuth();
  const [loading, setLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  const currentYear = new Date().getFullYear();
  const startOfYear = new Date(currentYear, 0, 1); // January 1st of the current year
  const endOfYear = new Date(currentYear, 11, 31);

  const [startDate, setStartDate] = useState(startOfYear);
  const [endDate, setEndDate] = useState(endOfYear);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      let url = `${process.env.NEXT_PUBLIC_GET_ALL_PATIENTS_BY_TYPE}?user_id=${adminId}`;
      if (startDate && endDate && adminId) {
        const startFormatted = formatDateToYMD(startDate);
        const endFormatted = formatDateToYMD(endDate);
        url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
      }
      const data = await axiosAuth.get(url);
      if (data?.data) {
        setLoading(false);
        setAllPatientDetails(data?.data?.patient_data);
      }
      setLoading(false);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [adminId, axiosAuth, endDate, startDate]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const monthCounts = {};

  // Iterate over each patient's details
  allPatientDetails?.forEach((patient) => {
    const DateOfRegistration = patient?.["patient details"]?.DateOfRegistration;

    if (DateOfRegistration) {
      const month = new Date(DateOfRegistration)?.getMonth();
      monthCounts[month] = (monthCounts[month] || 0) + 1;
    }
  });

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const labels = months;
  const data = {
    labels: labels,
    datasets: [
      {
        tension: 0.4,
        label: "Number of Patients",
        data: labels.map((month, index) => monthCounts[index] || 0),
        borderColor: "#8107d1",
        backgroundColor: "white",
        borderWidth: 2,
        pointBackgroundColor: "white",
        pointBorderColor: "#8107d1",
        pointRadius: 3,
        pointHoverRadius: 4,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        // position: "top",
        display: false,
      },
      // title: {
      //   display: true,
      //   text: "Patient Registration Rate by Month",
      // },
      tooltip: {
        // backgroundColor: "rgba(255, 255, 255, 0.8)",
        // borderColor: "#8107d1",
        borderWidth: 1,
        // titleColor: "#333",
        // bodyColor: "#333",
        displayColors: false,
      },
    },

    scales: {
      x: {
        title: {
          // display: true,
          // text: "Month",
          // color: "#8107d1",
          font: {
            size: 13,
            weight: 600,
          },
        },
        ticks: {
          // color: "#8107d1",
          font: {
            size: 13,
            weight: 600,
          },
        },
      },

      y: {
        title: {
          display: true,
          font: {
            size: 13,
            weight: 600,
          },
          text: "Number of Patients",
        },

        ticks: {
          callback: function (value) {
            return value >= 0 ? value.toFixed(0) : null;
          },
          stepSize: 5,
          suggestedMin: 0,
        },
      },
    },
  };

  const onDateSelect = (date) => {
    const year = date.getFullYear(); // Get the selected year
    setSelectedYear(year);
    const start = new Date(`${year}-01-01`);
    const end = new Date(`${year}-12-31`);
    setStartDate(start);
    setEndDate(end);
  };
  return (
    <div>
      <div className="d-flex mt-3">
        <div className=" me-auto patient-line-graph-heading">
          Track Patient Growth Yearly
        </div>
        <div className="col-3">
          <DatePicker
            className=" date-picker-patients-list"
            style={{ border: "0px" }}
            selected={new Date(`${selectedYear}`)}
            onChange={(date) => onDateSelect(date)}
            showYearPicker
            dateFormat="yyyy"
          />
        </div>
      </div>
      {loading ? (
        <div className="loading-chart">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              size={"lg"}
              style={{ height: "300px", borderRadius: "5px" }}
            />
          </Placeholder>
        </div>
      ) : (
        <Line options={options} data={data} />
      )}
    </div>
  );
};

export default PatientsLineGraph;
