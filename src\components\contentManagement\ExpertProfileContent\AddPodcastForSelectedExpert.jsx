import Image from "next/image";
import React, { useCallback, useContext, useEffect, useState } from "react";
import { Button, Form, InputGroup, Modal } from "react-bootstrap";
import ReactPlayer from "react-player";
import { capitalizeFullName, isValidURL } from "../../../utils/helperfunction";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { AdminDetailsContext } from "../../../Context/AdminContext/AdminContext";
import { GiDuration } from "react-icons/gi";
import { toast } from "react-toastify";
import { AiFillYoutube } from "react-icons/ai";
import { IoIosAddCircleOutline } from "react-icons/io";
import { MdDelete } from "react-icons/md";
import { SiApplepodcasts } from "react-icons/si";
import { FaSpotify } from "react-icons/fa";
import Link from "next/link";

const getCategoryIdByName = (categories, categoryName) => {
  if (!categoryName) {
    return null;
  }

  const filteredCategories = categories?.filter(
    (cat) => cat?.Category === categoryName
  );

  return filteredCategories.length > 0 ? filteredCategories[0].id : null;
};

const AddPodcastForSelectedExpert = ({
  addPodcastModal,
  setAddPodcastModal,
  selectedDoctor,
  setSelectedDoctor,
  from,
  fetchAllPodcasts = () => {},
}) => {
  const initialFormData = {
    PodcastTopic: selectedDoctor?.PodcastTopic || "",
    YoutubePodcastURL: "",
    PodcastDescription: selectedDoctor?.PodcastDescription || "",
    PodcastCategory: selectedDoctor?.PodcastCategoryVal || "",
    YoutubeThumbnailImage: null,
    YoutubePodcastTranscription: "",
    YoutubePodcastDuration: "",
    ApplePodcastURL: "",
    SpotifyPodcastURL: "",
    ExpertId: selectedDoctor?.ExpertId,
  };

  const [categories, setCategories] = useState({});
  const [podcastUploading, setPodcastUploading] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [YoutubeThumbnailPreview, setYoutubeThumbnailPreview] = useState(null);
  const [formData, setFormData] = useState(initialFormData);
  const [timestamps, setTimestamps] = useState([{ time: "", title: "" }]);
  const [helpfullLinks, setHelpfullLinks] = useState([{ title: "", url: "" }]);
  const axiosAuth = useAxiosAuth();
  const { session } = useContext(AdminDetailsContext);
  const admin_id = session?.user.id;

  const fetchCategories = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_POST_PODCAST_CATEGORY}`
      );
      if (response.status === 200) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error("Error fetching categories from the API: ", error);
    }
  }, [axiosAuth]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleThumbnailChange = (e) => {
    const file = e.target.files[0];

    if (file && file.type.startsWith("image/")) {
      setFormData((prevData) => ({
        ...prevData,
        YoutubeThumbnailImage: file,
      }));
      setYoutubeThumbnailPreview(URL.createObjectURL(file));
    } else {
      toast.error("Please select a valid image file");
    }
  };

  const handleAddPodcast = async () => {
    if (!selectedDoctor) {
      toast.error("Please select the Expert to add Podcast");
      return;
    }

    // Optional: Uncomment this part if you want to validate word count
    const wordCount =
      formData.PodcastDescription &&
      formData.PodcastDescription?.trim()?.split(/\s+/)?.length;

    if (wordCount < 50 || wordCount > 100) {
      toast.error("Description should be between 50 and 100 words");
      return;
    }

    // Check for required fields
    if (
      !formData.PodcastTopic ||
      !formData.YoutubePodcastURL ||
      !formData.PodcastDescription ||
      !formData.PodcastCategory ||
      !formData.YoutubeThumbnailImage ||
      !formData.YoutubePodcastDuration
    ) {
      const message = !formData.PodcastTopic
        ? "Podcast Topic"
        : !formData.YoutubePodcastURL
        ? "Youtube URL"
        : !formData.PodcastDescription
        ? "Podcast Description"
        : !formData.PodcastCategory
        ? "Podcast Category"
        : !formData.YoutubePodcastDuration
        ? "Youtube Duration"
        : !formData.YoutubePodcastTranscription
        ? "YouTube Transcription"
        : !timestamps
        ? "Youtube Timestamps"
        : "Youtube Thumbnail Image";
      toast.error(`${message} is required`);
      return;
    }

    // Create platform object
    let platform = {
      youtube: {
        url: formData.YoutubePodcastURL,
        timestamps: timestamps,
        transcript: formData.YoutubePodcastTranscription,
        thumbnail: formData.YoutubeThumbnailImage,
        duration: formData.YoutubePodcastDuration,
      },
      apple_podcast: {
        url: formData.ApplePodcastURL,
      },
      spotify: {
        url: formData.SpotifyPodcastURL,
      },
    };

    setPodcastUploading(true);

    const dataToSend = new FormData();
    dataToSend.append("PodcastTopic", formData.PodcastTopic);
    // dataToSend.append("ExpertId", selectedDoctor.id);
    dataToSend.append("PodcastDescription", formData.PodcastDescription);
    if (formData.YoutubeThumbnailImage) {
      dataToSend.append("ThumbnailImage", formData.YoutubeThumbnailImage);
    }

    // Append platform as JSON string or individual properties
    if (platform) dataToSend.append("Platforms", JSON.stringify(platform)); // Or append each field individually

    const filteredLinks = helpfullLinks?.filter(
      (link) => link.title && link.url
    );

    if (filteredLinks && filteredLinks?.length > 0) {
      dataToSend.append("HelpfulLinks", JSON.stringify(filteredLinks));
    }
    if (formData.ExpertId || selectedDoctor?.id)
      dataToSend.append("ExpertId", formData.ExpertId || selectedDoctor?.id);
    let categoryID = getCategoryIdByName(categories, formData.PodcastCategory);
    if (categoryID) {
      dataToSend.append("PodcastCategoryVal", categoryID);
    } else {
      toast.error("Invalid category selected");
      return;
    }

    let apiEndpoint;
    let method;
    if (from === "allApprovalsPodcast") {
      apiEndpoint = `${process.env.NEXT_PUBLIC_PUBLISH_EXPERTS_PODCAST_REQUEST}${selectedDoctor.id}/?user_id=${admin_id}`;
      method = "put";
    } else {
      apiEndpoint = `${process.env.NEXT_PUBLIC_GET_EXPERTS_PODCAST_REQUEST}${selectedDoctor.id}/?user_id=${admin_id}`;
      method = "post";
    }

    try {
      const response = await axiosAuth[method](apiEndpoint, dataToSend);

      if (response.status === 201 || response.status === 200) {
        toast.success("Podcast Uploaded successfully!");
      } else {
        toast.error("Failed to Upload Podcast");
        console.error("Failed to Upload Podcast");
      }
      setPodcastUploading(true);
    } catch (error) {
      console.error("An error occurred:", error);
      toast.error("An error occurred while uploading the podcast");
    } finally {
      setPodcastUploading(false);
      handleCancelPodcast();
      // setAddPodcastModal(false);
      if (from === "allApprovalsPodcast") {
        fetchAllPodcasts();
      }
    }
  };

  const handleCancelPodcast = () => {
    setSelectedDoctor(null);
    setFormData(initialFormData);
    setAddPodcastModal(false);
    setYoutubeThumbnailPreview(null);
    setTimestamps([{ time: "", title: "" }]); // Reset timestamps
    setHelpfullLinks([{ title: "", url: "" }]);
  };

  // Function to handle adding a new timestamp
  const handleAddTimestamp = () => {
    setTimestamps((prevTimestamps) => [
      ...prevTimestamps,
      { time: "", title: "" },
    ]);
  };

  const handleAddHelpfullLinks = () => {
    setHelpfullLinks((prevHelpfullLink) => [
      ...prevHelpfullLink,
      { title: "", url: "" },
    ]);
  };

  // Function to handle timestamp changes
  const handleTimestampChange = (index, field, value) => {
    const updatedTimestamps = [...timestamps];
    updatedTimestamps[index][field] = value;
    setTimestamps(updatedTimestamps);
  };

  const handleHelpfullLink = (index, field, value) => {
    const updatedHelpfulLinks = [...helpfullLinks];
    updatedHelpfulLinks[index][field] = value;
    setHelpfullLinks(updatedHelpfulLinks);
  };
  return (
    <div>
      <Modal
        show={addPodcastModal}
        onHide={() => {
          setAddPodcastModal(false);
          handleCancelPodcast();
        }}
        size="xl"
        centered
        scrollable
      >
        <Modal.Header>
          <Modal.Title>Add Podcast</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="row">
            <div className="mb-3">
              {from === "allApprovalsPodcast" && (
                <div className="row form-label custom-label">
                  <div className="col-sm-auto">
                    <span>Upload Podcast for Selected Expert</span>
                  </div>
                </div>
              )}
              <div className="cancellation-desclaimer-section">
                <strong>Disclaimer:</strong> Podcasts can be uploaded for
                individual experts.
              </div>
            </div>
            {selectedDoctor && from === "allApprovalsPodcast" && (
              <div>
                <p>
                  You have selected{" "}
                  <strong>{capitalizeFullName(selectedDoctor.role)}</strong>
                  -&nbsp;
                  <strong>
                    {capitalizeFullName(
                      selectedDoctor.name || selectedDoctor.expert_name
                    )}
                  </strong>{" "}
                  with an ID {selectedDoctor.ExpertId || selectedDoctor.id}
                </p>
              </div>
            )}

            <InputGroup className="mb-3">
              <InputGroup.Text id="basic-addon1">Podcast Topic</InputGroup.Text>
              <Form.Control
                placeholder="Enter Podcast title"
                aria-label="PodcastTopic"
                aria-describedby="basic-addon1"
                value={formData.PodcastTopic}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    PodcastTopic: e.target.value,
                  }))
                }
                required
              />
            </InputGroup>

            <div className="mt-2">
              <label
                htmlFor="PodcastCategory"
                className="form-label custom-label"
              >
                Podcast Category
              </label>
              <select
                className="form-select custom-input"
                id="PodcastCategory"
                value={formData.PodcastCategory}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    PodcastCategory: e.target.value,
                  }))
                }
              >
                <option value="">Select Podcast Category</option>
                {categories &&
                  Array.isArray(categories) &&
                  categories?.map((category) => (
                    <option key={category.id} value={category.Category}>
                      {category.Category}
                    </option>
                  ))}
              </select>
            </div>
            <div className="mt-2">
              <label
                htmlFor="PodcastDescription"
                className="form-label custom-label"
              >
                Podcast Description
              </label>
              <textarea
                className="form-control custom-input"
                id="PodcastDescription"
                rows="5"
                value={formData.PodcastDescription}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    PodcastDescription: e.target.value,
                  }))
                }
                required
              />
            </div>
            <div className="border mt-2 rounded">
              <InputGroup className="mb-3 mt-4">
                <InputGroup.Text id="basic-addon1">
                  <AiFillYoutube color="#FF0000" className="fs-3" />
                  &nbsp; Youtube Podcast URL
                </InputGroup.Text>
                <Form.Control
                  placeholder="Enter Youtube Podcast URL"
                  type="url"
                  aria-label="YoutubePodcastURL"
                  aria-describedby="basic-addon1"
                  value={formData.YoutubePodcastURL}
                  onChange={(e) =>
                    setFormData((prevData) => ({
                      ...prevData,
                      YoutubePodcastURL: e.target.value,
                    }))
                  }
                  required
                />
              </InputGroup>
              <InputGroup className="mb-3 mt-4">
                <InputGroup.Text id="basic-addon1">
                  <GiDuration color="#FF0000" className="fs-3" />
                  &nbsp; Youtube Podcast Duration
                </InputGroup.Text>
                <Form.Control
                  placeholder="Enter Youtube Podcast Duration"
                  type="text"
                  aria-label="YoutubePodcastDuration"
                  aria-describedby="basic-addon1"
                  value={formData.YoutubePodcastDuration}
                  onChange={(e) =>
                    setFormData((prevData) => ({
                      ...prevData,
                      YoutubePodcastDuration: e.target.value,
                    }))
                  }
                  required
                />
              </InputGroup>
              <div className="mt-2">
                <label
                  htmlFor="YoutubeThumbnailImage"
                  className="form-label custom-label"
                >
                  Thumbnail Image
                </label>
                <input
                  type="file"
                  className="form-control custom-input"
                  accept="image/*"
                  onChange={handleThumbnailChange}
                />
                {YoutubeThumbnailPreview && (
                  <div className="thumbnail-preview">
                    <Image
                      src={YoutubeThumbnailPreview}
                      alt="Thumbnail Preview"
                      width={100}
                      height={100}
                    />
                  </div>
                )}
              </div>

              <div className="mt-4">
                <label
                  htmlFor="YoutubePodcastTranscription"
                  className="form-label custom-label d-flex justify-content-between align-items-center"
                >
                  Podcast Transcription
                  <Link
                    href={"https://youtubetotranscript.com/"}
                    className="text-black text-decoration-none "
                    target="_blank"
                  >
                    Get Transcript
                  </Link>
                </label>
                <textarea
                  className="form-control custom-input"
                  id="YoutubePodcastTranscription"
                  rows="5"
                  value={formData.YoutubePodcastTranscription}
                  onChange={(e) =>
                    setFormData((prevData) => ({
                      ...prevData,
                      YoutubePodcastTranscription: e.target.value,
                    }))
                  }
                />
              </div>

              {/* Timestamps and Titles Section */}
              <div className="timestamps-section mt-4 mb-4">
                <label htmlFor="timestamps" className="form-label custom-label">
                  Podcast Timestamps
                </label>
                <div
                  className="timestamps-container"
                  style={{
                    maxHeight: timestamps.length > 5 ? "200px" : "auto",
                    overflowY: timestamps.length > 5 ? "scroll" : "visible",
                  }}
                >
                  {timestamps.map((timestamp, index) => (
                    <div key={index} className="d-flex mb-2">
                      <InputGroup>
                        <InputGroup.Text id="basic-addon1">
                          Timestamp
                        </InputGroup.Text>
                        <Form.Control
                          placeholder="Timestamp (e.g., 01:00)"
                          aria-label="helpful title"
                          aria-describedby="basic-addon1"
                          className="form-control"
                          type="text"
                          value={timestamp.time}
                          onChange={(e) =>
                            handleTimestampChange(index, "time", e.target.value)
                          }
                          required
                        />
                      </InputGroup>

                      <InputGroup>
                        <InputGroup.Text id="basic-addon1">
                          Text
                        </InputGroup.Text>
                        <Form.Control
                          aria-label="timestamp text"
                          aria-describedby="basic-addon1"
                          type="text"
                          placeholder="enter the text"
                          value={timestamp.title}
                          onChange={(e) =>
                            handleTimestampChange(
                              index,
                              "title",
                              e.target.value
                            )
                          }
                          required
                        />
                      </InputGroup>

                      <div className="d-flex justify-content-between align-items-center">
                        <IoIosAddCircleOutline
                          className="fs-3"
                          onClick={handleAddTimestamp}
                        />

                        {timestamps.length > 1 && (
                          <MdDelete
                            className="fs-3"
                            onClick={() => {
                              if (timestamps.length > 1) {
                                const newTimestamps = [...timestamps];
                                newTimestamps.splice(index, 1);
                                setTimestamps(newTimestamps);
                              }
                            }}
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <InputGroup className="mt-4">
              <InputGroup.Text id="basic-addon1">
                <SiApplepodcasts color="#8800D5" className="fs-4" />
                &nbsp; Apple Podcast URL
              </InputGroup.Text>
              <Form.Control
                placeholder="Enter Spotify URL"
                aria-label="ApplePodcastURL"
                aria-describedby="basic-addon1"
                type="url"
                value={formData.ApplePodcastURL}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    ApplePodcastURL: e.target.value,
                  }))
                }
                required
              />
            </InputGroup>

            <InputGroup className="mt-4">
              <InputGroup.Text id="basic-addon1">
                <FaSpotify color="#1DB954" className="fs-4" />
                &nbsp; Spotify Podcast URL
              </InputGroup.Text>
              <Form.Control
                placeholder="Enter Spotify URL"
                aria-label="SpotifyPodcastURL"
                aria-describedby="basic-addon1"
                type="url"
                value={formData.SpotifyPodcastURL}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    SpotifyPodcastURL: e.target.value,
                  }))
                }
                required
              />
            </InputGroup>

            <div className="timestamps-section mt-4 mb-4">
              <label
                htmlFor="helpfullLinks"
                className="form-label custom-label"
              >
                Helpful Links
              </label>
              <div
                style={{
                  maxHeight: helpfullLinks.length > 5 ? "200px" : "auto",
                  overflowY: helpfullLinks.length > 5 ? "scroll" : "visible",
                }}
              >
                {helpfullLinks.map((helpfulLink, index) => (
                  <div key={index} className="d-flex mb-2">
                    <InputGroup>
                      <InputGroup.Text id="basic-addon1">Title</InputGroup.Text>
                      <Form.Control
                        placeholder="enter the title..."
                        aria-label="helpful title"
                        aria-describedby="basic-addon1"
                        type="text"
                        value={helpfulLink.title}
                        onChange={(e) =>
                          handleHelpfullLink(index, "title", e.target.value)
                        }
                        required
                      />
                    </InputGroup>
                    <InputGroup>
                      <InputGroup.Text id="basic-addon1">URL</InputGroup.Text>
                      <Form.Control
                        aria-label="helpful link"
                        aria-describedby="basic-addon1"
                        type="text"
                        placeholder="enter the url..."
                        value={helpfulLink.url}
                        onChange={(e) =>
                          handleHelpfullLink(index, "url", e.target.value)
                        }
                        required
                      />
                    </InputGroup>

                    <div className="d-flex justify-content-between align-items-center">
                      <IoIosAddCircleOutline
                        className="fs-3"
                        onClick={handleAddHelpfullLinks}
                      />
                      {helpfullLinks?.length > 1 && (
                        <MdDelete
                          className="fs-3"
                          onClick={() => {
                            const newHelpfulLink = [...helpfullLinks];
                            newHelpfulLink.splice(index, 1);
                            setHelpfullLinks(newHelpfulLink);
                          }}
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* ------------------------------------Podcast Preview Modal------------------------------------------- */}
            <Modal
              show={showPreviewModal}
              onHide={() => setShowPreviewModal(false)}
              size="xl"
            >
              <Modal.Header closeButton>
                <Modal.Title>Podcast Preview</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <h4 className="custom-purple-text-color">
                  {formData.PodcastTopic}
                </h4>
                <div className="d-flex justify-content-center align-items-center">
                  {/* <ReactPlayer
                    url={formData.YoutubePodcastURL}
                    light={formData?.YoutubeThumbnailImage || ""}
                  /> */}
                  <div className="video-container">
                    <ReactPlayer
                      url={formData.YoutubePodcastURL}
                      light={
                        YoutubeThumbnailPreview ||
                        formData?.YoutubeThumbnailImage ||
                        ""
                      }
                      playing={false} // Ensures it shows the thumbnail before playing
                      controls
                    />
                  </div>
                </div>
                <div>
                  <p className="custom-purple-text-color">Podcast Category</p>
                  <p>{formData?.PodcastCategory}</p>
                </div>
                <div>
                  <p className="custom-purple-text-color">
                    Podcast Description
                  </p>
                  <p>{formData?.PodcastDescription || "No data available"}</p>
                </div>
                <div>
                  {Array.isArray(timestamps) && timestamps.length > 0 ? (
                    <>
                      <p className="custom-purple-text-color">
                        Podcast TimeStamps
                      </p>
                      {timestamps.map((timestamp, index) => {
                        // Validate if timestamp has both time and title
                        if (!timestamp?.time || !timestamp?.title) return null; // Skip invalid entries
                        return (
                          <div key={index}>
                            <strong>{timestamp.time}</strong>: {timestamp.title}
                          </div>
                        );
                      })}
                    </>
                  ) : null}{" "}
                </div>
                <p className="custom-purple-text-color">
                  Podcast Transcription
                </p>
                <div
                  style={{
                    maxHeight: "200px", // Adjust height as needed
                    overflowY: "auto",
                    border: "1px solid #ddd",
                    padding: "10px",
                    borderRadius: "5px",
                    backgroundColor: "#f9f9f9",
                  }}
                >
                  <p>
                    {formData?.YoutubePodcastTranscription ||
                      "No data available"}
                  </p>
                </div>
                <div className="mt-3">
                  <p className="custom-purple-text-color">Other Platforms</p>
                  {formData?.ApplePodcastURL ? (
                    <a
                      href={formData.ApplePodcastURL}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-decoration-none"
                    >
                      <SiApplepodcasts
                        style={{
                          color: "#B150E2",
                          fontSize: "20px",
                        }}
                      />{" "}
                      Listen on Apple Podcast
                    </a>
                  ) : (
                    <p>No link available</p>
                  )}
                </div>
                <div>
                  {/* <p className="custom-purple-text-color">Spotify Podcast</p> */}
                  {formData?.SpotifyPodcastURL ? (
                    <a
                      href={formData.SpotifyPodcastURL}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-decoration-none"
                    >
                      <FaSpotify
                        style={{
                          color: "#1ed761",
                          fontSize: "20px",
                        }}
                      />{" "}
                      Listen on Spotify
                    </a>
                  ) : (
                    <p>No link available</p>
                  )}
                </div>
                <div className=" mt-3">
                  <p className="custom-purple-text-color">Helpful Links</p>
                  {helpfullLinks.length > 0 ? (
                    <ul className="list-unstyled">
                      {helpfullLinks.map((helpfulLink, index) => (
                        <li key={index} className="mb-2">
                          <a
                            href={helpfulLink.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="helpful-link"
                          >
                            🔗 {helpfulLink.title || "Untitled"}
                          </a>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p>No helpful links available</p>
                  )}
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  className="bg-danger border-0"
                  onClick={() => setShowPreviewModal(false)}
                >
                  Close
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between align-items-center">
          <Button className="bg-danger border-0" onClick={handleCancelPodcast}>
            Cancel
          </Button>
          {isValidURL(formData.YoutubePodcastURL) && (
            <Button
              className="custom-purple-button"
              onClick={() => setShowPreviewModal(true)}
            >
              Preview Podcast
            </Button>
          )}
          <Button
            className="custom-purple-button"
            onClick={handleAddPodcast}
            disabled={podcastUploading}
          >
            {podcastUploading ? "Uploading..." : "Add Podcast"}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AddPodcastForSelectedExpert;
