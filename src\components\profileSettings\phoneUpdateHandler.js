"use client";

import { isValidPhoneNumber } from "libphonenumber-js";
import PhoneInput from "react-phone-input-2";
import Swal from "sweetalert2";

export const handlePhoneUpdate = async ({
  tempPhone,
  setTempPhone,
  tempCountryCode,
  setTempCountryCode,
  axiosAuth,
  setFormData,
}) => {
  let updatedPhone = "";
  let updatedCountryCode = "";
  let selectedCountry = null;

  // Step 1: Ask for new phone number
  const { value: phoneConfirmed } = await Swal.fire({
    title: "Update Phone Number",
    html: `
          <div id="swal-phone-input-container"></div>
          <div id="phone-error" style="color: red; font-size: 0.9em; margin-top: 6px;"></div>
      `,
    allowOutsideClick: false,

    didOpen: () => {
      if (typeof window !== "undefined") {
        const container = document.getElementById("swal-phone-input-container");
        if (container) {
          const root = document.createElement("div");
          container.appendChild(root);

          const inputComponent = (
            <PhoneInput
              country={tempCountryCode.replace("+", "").toLowerCase()}
              value={`${tempCountryCode}${tempPhone}`}
              onChange={(value, country, e, formattedValue) => {
                updatedPhone = formattedValue
                  .replace(`+${country.dialCode}`, "")
                  .trim();
                updatedCountryCode = `+${country.dialCode}`;
                selectedCountry = country;
              }}
              inputProps={{
                id: "swal-phone-input-hidden",
                autoFocus: true,
              }}
              inputStyle={{
                height: "40px",
                width: "100%",
                border: "1px solid #5b5b5b",
                background: "white",
                outline: "none",
                boxShadow: "none",
              }}
              buttonStyle={{
                border: "1px solid #5b5b5b",
                borderRight: "1px",
                background: "white",
                width: "10%",
              }}
            />
          );

          import("react-dom/client").then((ReactDOMClient) => {
            const rootContainer = ReactDOMClient.createRoot(root);
            rootContainer.render(inputComponent);
          });
        }
      }
    },
    showCancelButton: true,
    confirmButtonText: "Send OTP",
    confirmButtonColor: "#9426b2",
    cancelButtonColor: "red",
    preConfirm: () => {
      const fullPhone = `${updatedCountryCode || tempCountryCode}${
        updatedPhone || tempPhone
      }`;
      const isValid = isValidPhoneNumber(fullPhone);
      if (typeof window !== "undefined") {
        const errorDiv = document.getElementById("phone-error");
        if (!isValid) {
          if (errorDiv) {
            errorDiv.innerText = `Invalid phone number for ${
              selectedCountry?.name || "selected country"
            }`;
          }
          return false;
        }

        if (errorDiv) {
          errorDiv.innerText = "";
        }
      }

      return true;
    },
  });

  if (phoneConfirmed) {
    try {
      const sendOtpApiUrl = process.env.NEXT_PUBLIC_UPDATE_PHONE_OTP;
      const requestData = {
        phone: updatedPhone || tempPhone,
        country_code: updatedCountryCode || tempCountryCode,
      };

      const sendOtpResponse = await axiosAuth.post(sendOtpApiUrl, requestData);
      const otpMessage = sendOtpResponse?.data?.message;

      if (otpMessage === "OTP sent successfully") {
        // OTP SWAL
        await Swal.fire({
          title: "Enter OTP",

          text: "We have sent an OTP to your phone number. Please enter the OTP.",
          html: `
                <p style="margin-bottom: 12px;">We have sent an OTP to your phone number. Please enter the OTP.</p>
                <div class="form-group">
                    <input 
                    type="text" 
                    id="otp-input" 
                    class="form-control" 
                    placeholder="Enter OTP" 
                    style="
                        height: 40px; 
                        width: 100%; 
                        border: 1px solid #5b5b5b; 
                        background: white; 
                        opacity: 1;
                        padding: 8px;
                        font-size: 14px;
                        border-radius: 4px;
                    "
                    onfocus="this.style.outline='none'; this.style.boxShadow='none';"
                    />
                    <div 
                    id="otp-error" 
                    style="color: red; margin-top: 5px; font-size: 0.9em;"
                    ></div>
                </div>
            `,

          focusConfirm: false,
          showCancelButton: true,
          confirmButtonText: "Verify",
          confirmButtonColor: "#9426b2",
          cancelButtonColor: "red",
          allowOutsideClick: false,
          //   preConfirm: async () => {
          //     if (typeof window === "undefined") return false;
          //     const otpVal = document.getElementById("otp-input").value.trim();

          //     if (!otpVal || otpVal.length < 4) {
          //       document.getElementById("otp-error").innerText =
          //         "Please enter a valid OTP.";
          //       return false;
          //     }

          //     try {
          //       const verifyOtpApiUrl = process.env.NEXT_PUBLIC_VERIFY_PHONE_OTP;
          //       const verifyOtpRequestData = {
          //         otp: otpVal,
          //       };

          //       const verifyOtpResponse = await axiosAuth.post(
          //         verifyOtpApiUrl,
          //         verifyOtpRequestData
          //       );
          //       console.log("verifyOtpResponse", verifyOtpResponse);

          //       const verifyMessage = verifyOtpResponse?.data?.message;

          //       console.log("verifyMessage", verifyMessage);

          //       if (verifyMessage === "Phone number updated successfully.") {
          //         Swal.fire("Success", verifyMessage, "success");
          //         setTempPhone(updatedPhone);
          //         setTempCountryCode(updatedCountryCode);
          //         if (setFormData) {
          //           setFormData((prev) => ({
          //             ...prev,
          //             phone: updatedPhone,
          //             country_code: updatedCountryCode,
          //           }));
          //         }
          //       } else {
          //         document.getElementById("otp-error").innerText =
          //           verifyMessage || "OTP verification failed.";
          //         return false;
          //       }
          //     } catch (err) {
          //       console.log("error message", err);
          //       let errorMessage =
          //         err?.response?.data?.message || "Verification failed ";

          //       document.getElementById("otp-error").innerText = errorMessage;

          //       return false;
          //     }
          //   },
          preConfirm: async () => {
            if (typeof window === "undefined") return false;
            const otpVal = document.getElementById("otp-input").value.trim();

            if (!otpVal || otpVal.length < 4) {
              document.getElementById("otp-error").innerText =
                "Please enter a valid OTP.";
              return false;
            }

            try {
              const verifyOtpApiUrl = process.env.NEXT_PUBLIC_VERIFY_PHONE_OTP;
              const verifyOtpRequestData = {
                otp: otpVal,
              };

              const verifyOtpResponse = await axiosAuth.post(
                verifyOtpApiUrl,
                verifyOtpRequestData
              );

              const verifyMessage = verifyOtpResponse.data?.message;

              if (verifyMessage === "Phone number updated successfully.") {
                Swal.fire("Success", verifyMessage, "success");
                setTempPhone(updatedPhone);
                setTempCountryCode(updatedCountryCode);
                if (setFormData) {
                  setFormData((prev) => ({
                    ...prev,
                    phone: updatedPhone,
                    country_code: updatedCountryCode,
                  }));
                }
                return true;
              } else {
                const errorMessage =
                  verifyMessage || "OTP verification failed.";
                document.getElementById("otp-error").innerText = errorMessage;
                return false;
              }
            } catch (err) {
              // Handle Axios error response
              const errorMessage =
                err.response?.data?.message ||
                "OTP verification failed. Please try again.";

              // Special handling for expired OTP
              const displayMessage = errorMessage.includes("expired")
                ? "The OTP has expired. Please request a new one."
                : errorMessage;

              document.getElementById("otp-error").innerText = displayMessage;
              return false;
            }
          },
        });
      } else {
        Swal.fire("Error", otpMessage || "Could not send OTP", "error");
      }
    } catch (error) {
      Swal.fire(
        "Error",
        error.response?.data?.message || "Something went wrong",
        "error"
      );
    }
  }
};
