.ldsRoller {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 10%;
  right: 0;
  margin: auto;
  width: 50%; /* Set the initial width as a percentage */
  height: 80px;
  z-index: 9999;
}

/* ... (rest of your CSS) */

.ldsRoller div {
  animation: ldsRoller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  transform-origin: 40px 40px;
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #bd2fe4;
  margin: -4px 0 0 -4px;
}

.ldsRoller div:nth-child(1) {
  animation-delay: -0.036s;
}
.ldsRoller div:nth-child(1):after {
  top: 63px;
  left: 63px;
}
.ldsRoller div:nth-child(2) {
  animation-delay: -0.072s;
}
.ldsRoller div:nth-child(2):after {
  top: 68px;
  left: 56px;
}
.ldsRoller div:nth-child(3) {
  animation-delay: -0.108s;
}
.ldsRoller div:nth-child(3):after {
  top: 71px;
  left: 48px;
}

@keyframes ldsRoller {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
