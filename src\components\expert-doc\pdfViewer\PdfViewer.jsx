"use client";

import React from "react";
import { useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./PdfViewer.css";
import Image from "next/image";
import { getFileExtension } from "@/utils/helperfunction";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs`;

const options = {
  cMapUrl: "/cmaps/",
  standardFontDataUrl: "/standard_fonts/",
};

const Loader = () => {
  return (
    <div style={{ color: "#8107d1" }} className="spinner-border" role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  );
};

const PdfViewer = ({ document }) => {
  const [numPages, setNumPages] = useState();
  const [containerWidth, setContainerWidth] = useState();

  const [_, link] = document;

  const fileExtension = getFileExtension(link);

  function onDocumentLoadSuccess({ numPages: nextNumPages }) {
    setNumPages(nextNumPages);
  }
 

  return (
    <>
      {link &&
        (fileExtension == "pdf" ? (
          <div className="Example">
            <div className="Example__container">
              <div className="Example__container__document"></div>
              <Document
                file={link?.split("?")[0]}
                onLoadSuccess={onDocumentLoadSuccess}
                options={options}
                loading={<Loader />}
              >
                {Array.from(new Array(numPages), (_el, index) => (
                  <Page
                    key={`page_${index + 1}`}
                    pageNumber={index + 1}
                    width={containerWidth ? Math.min(containerWidth, 800) : 800}
                  />
                ))}
              </Document>
            </div>
          </div>
        ) : ["png", "jpg", "jpeg", "jfif"].includes(fileExtension) ? (
          <div className="">
            <Image
              src={link?.split("?")[0]}
              style={{ objectFit: "contain", width: "100%" }}
              alt="carrier-img"
              width={400}
              height={200}
            />
          </div>
        ) : (
          <p className="text-center py-5">Unsupported file format</p>
        ))}
    </>
  );
};

export default PdfViewer;
