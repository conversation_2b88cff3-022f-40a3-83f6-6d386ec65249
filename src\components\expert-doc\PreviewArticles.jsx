import React from "react";
import { AiFillEye } from "react-icons/ai";
import ModalImage from "react-modal-image";
import { formatCustomDate, timeDifference } from "../../utils/helperfunction";

const PreviewArticles = ({ selectedItem = {} }) => {
  const {
    BlogTitle,
    BlogBody,
    BlogImages,
    BlogViews,
    BlogDateTime,
  } = selectedItem?.blog_details;
  return (
    <>
      <div className="row">
        <div className="d-flex justify-content-between align-items-center">
          <p className="m-3 introPreview-heading">Blog Preview </p>
          <p className="float-end me-4 mt-3 d-flex justify-content-center align-items-center fw-bold">
            <AiFillEye color="#8107d1" className="fs-5 " /> &nbsp;
            {BlogViews}
            &nbsp; Views
          </p>
        </div>
        <div className="me-3  d-flex justify-content-end align-items-center blog-date">
          Date : &nbsp;
          <span className="uploads-blog-formatedate">
            {BlogDateTime && formatCustomDate(BlogDateTime)}
          </span>
          &nbsp;
          <span className="uploads-blog-time-difference">
            ({BlogDateTime && timeDifference(BlogDateTime)})
          </span>
        </div>
        <div className="col-sm-10 mx-auto">
          <p className="fw-semibold mt-4">{BlogTitle}</p>
          <p className="">Description</p>
          <p
            className=""
            dangerouslySetInnerHTML={{
              __html: BlogBody,
            }}
          ></p>
        </div>
        <div className="article-image-container">
          {BlogImages &&
            String(BlogImages)
              .split(",")
              .map((img, index) => {
                const imageUrl = `${img.trim()}`;
                return (
                  <div className="image-wrapper" key={index}>
                    <ModalImage
                      small={imageUrl}
                      className={"custom-small-image"}
                      large={imageUrl}
                      showRotate={true}
                      alt={`art-${index}`}
                    />
                  </div>
                );
              })}
        </div>
      </div>
    </>
  );
};

export default PreviewArticles;
