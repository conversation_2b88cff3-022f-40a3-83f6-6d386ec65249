"use client";
import React, { useState, useEffect } from "react";
import PriceAndSubscription from "./PricingAndSubscrip";
import PaymentHead from "./PaymentHead";
import PaymentHistory from "./PaymentHistory";
import FeeManagement from "./FeeManagement";
import ExpertPaymentsTable from "./ExpertPaymentsTable";
import PaymentandPricingToggle2 from "./PaymentandPricingToggle2";

const PaymentPriceToggle = () => {
  const [activeTab, setActiveTab] = useState("payment");
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);
  const [paymentTab, setPaymentTab] = useState(null);

  useEffect(() => {
    setActiveTab("payment");
  }, []);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    if (tab === "payment") {
      setShowPaymentOptions(true);
    } else {
      setShowPaymentOptions(false);
    }
  };

  const handlePaymentTabChange = (tab) => {
    setPaymentTab(tab);
  };

  return (
    <>
      <PaymentHead heading="Payments and Pricing" />
      <div className="d-inline-flex gap-1 buttons-row mb-0 gx-5">
        <button
          className={`btn grey-btn ${
            activeTab === "payment" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("payment")}
        >
          Payments History
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "fee" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("fee")}
        >
          Fee Management
        </button>
      </div>

      {activeTab === "payment" && <PaymentandPricingToggle2 />}
      {activeTab === "fee" && <FeeManagement />}
    </>
  );
};

export default PaymentPriceToggle;
