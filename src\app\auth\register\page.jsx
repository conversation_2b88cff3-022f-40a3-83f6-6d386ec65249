
import React from 'react'

const page = () => {
  return (
    <div>page</div>
  )
}

export default page




// 'use client';

// import Loader from '@/components/loader/Loader';
// import RegistrationForm from '@/components/register/RegistrationForm';
// import { useSession } from 'next-auth/react';
// import { useRouter } from 'next/navigation';
// import React, { useEffect } from 'react';

// const Register = () => {
//   const { data: session, status } = useSession();
//   const router = useRouter();

//   useEffect(() => {
//     if (status === 'authenticated') {
//       router.replace('/');
//     }
//   }, [status, router]);

//   if (status === 'loading') {
//     return (
//       <div>
//         <Loader />
//       </div>
//     );
//   }

//   if (status !== 'authenticated') {
//     return (
//       <div className="">
//         <RegistrationForm />
//       </div>
//     );
//   }

//   return null;
// };

// export default Register;
