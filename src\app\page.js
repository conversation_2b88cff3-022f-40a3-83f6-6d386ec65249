"use client";
import { Suspense, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import Home from "../components/administratorDasboard/Home";
import "react-datepicker/dist/react-datepicker.css";
import Navbar from "../components/navbar/Navbar";
import AdminDashboard from "../components/administratorDasboard/AdminDashboard";
import withAuth from "../withAuth/withAuth";

const Homepage = () => {
  return (
    <div className="container-fluid">
    <div className="row ">
      <div className="col-sm-1 navbar-parent">
        <Navbar />
      </div>
      <div className="col-sm-11">
        <Suspense>
          <AdminDashboard />
        </Suspense>
      </div>
    </div>
    </div>
  );
};

export default withAuth(Homepage);
