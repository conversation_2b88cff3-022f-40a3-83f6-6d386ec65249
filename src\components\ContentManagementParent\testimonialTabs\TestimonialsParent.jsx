import React, { useCallback, useEffect, useMemo, useState } from "react";
import "../BlogsContent/blogs-content.css";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import Swal from "sweetalert2";
import { toast } from "react-toastify";
import "react-tooltip/dist/react-tooltip.css";
import axios from "axios";
import _ from "lodash";
import AllBlogsFilter from "../BlogsContent/AllBlogsFilter";
import useAxiosAuth from "@/lib/hooks/useAxiosAuth";
import TestimonialsMainList from "./TestimonialsMainList";
import ExpertTestimonialsList from "./ExpertTestimonialsList";

const TestimonialsParent = () => {
  const [loading, setLoading] = useState(true);
  const [current_page, setCurrent_Page] = useState(1);
  const [current_pagePatient, setCurrent_PagePatient] = useState(1);
  const [testimonials, setTestimonials] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [rankingLoading, setRankingLoading] = useState(false);
  const [expertTestimonials, setExpertTestimonials] = useState([]);
  const [patientSearchQuery, setpatientSearchQuery] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();

  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const axiosAuth = useAxiosAuth();

  const fetchAllTestimonials = useCallback(
    async (query) => {
      try {
        // setLoading(true);
        if (current_pagePatient === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_TESTIMONIALS}?page=${current_pagePatient}`;
        if (query) {
          url += `&search=${query}`;
        }
        const response = await axios.get(url);
        setTestimonials(response?.data);
        setLoading(false);
        if (current_pagePatient === 1) setInitialLoading(false);
      } catch (error) {
        console.error("Error fetching patient testimonials: ", error);
      } finally {
        setLoading(false);
        if (current_pagePatient === 1) setInitialLoading(false);
      }
    },
    [current_pagePatient]
  );
  const fetchExpertTestimonials = useCallback(
    async (query) => {
      try {
        // setLoading(true);
        if (current_page === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_EXPERT_TESTIMONIALS}?page=${current_page}`;
        if (query) {
          url += `&search=${query}`;
        }
        const response = await axios.get(url);
        setExpertTestimonials(response?.data);
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      } catch (error) {
        console.error("Error fetching expert testimonials: ", error);
      } finally {
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      }
    },
    [current_page]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      fetchExpertTestimonials(query);
    }, 500);
  }, [fetchExpertTestimonials]);

  const debouncedFetchDataPatient = useMemo(() => {
    return _.debounce((query) => {
      fetchAllTestimonials(query);
    }, 500);
  }, [fetchAllTestimonials]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  useEffect(() => {
    debouncedFetchDataPatient(patientSearchQuery);
  }, [patientSearchQuery, debouncedFetchDataPatient]);

  const handleDeleteRanking = async (id, category) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete the ranking for this blog?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });
    if (result.isConfirmed) {
      try {
        setDeleteLoading(true);
        const response = await axiosAuth.delete(
          `${process.env.NEXT_PUBLIC_DELETE_TESTIMONIALS_RANK}${id}/?user_id=${userId}`
        );
        if (response?.data === "Deleted successfully.") {
          toast.success(`The Rank has been successfully deleted`, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        }
        setDeleteLoading(false);
        {
          category === "expert"
            ? fetchExpertTestimonials()
            : fetchAllTestimonials();
        }
      } catch (err) {
        toast.error(`Error in deleting the rank`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
    }
  };
  const handleAddRanking = async (id, category, ExpertId) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You want to add this testimonial?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      const body = {
        ranking: true,
        category: category === "expert" ? "ExpertReviews" : "Testimonials",
        c_id: id,
      };
      if (category === "expert") {
        body.ExpertId = ExpertId;
      }

      try {
        setRankingLoading(true);
        const response = await axiosAuth.post(
          `${process.env.NEXT_PUBLIC_ADD_TESTIMONIALS_RANK}?user_id=${userId}`,
          body
        );

        if (response?.data === "Successfully selected the content.") {
          toast.success(`Testimonial selected successfully.`, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        } else if (
          response?.data === "This content has been already selected."
        ) {
          toast.error("This testimonial has already been selected.", {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        } else {
          toast.info(response?.data, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        }
        {
          category === "expert"
            ? fetchExpertTestimonials()
            : fetchAllTestimonials();
        }
        setRankingLoading(false);
      } catch (err) {
        toast.error(`Error selecting this testimonial`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
        setRankingLoading(false); // Make sure to stop the loading state if there is an error
      }
    }
  };

  return (
    <>
      <div
        style={{ maxHeight: "600px", padding: "12px" }}
        className=" overflow-y-auto"
      >
        <div className="bg-color p-2">
          <h5 className="mb-3 admin-add-blog-list-header fw-semibold">
            Patient Testimonials
          </h5>
          <div className="row">
            <AllBlogsFilter
              startDate={startDate}
              endDate={endDate}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              setSearchQuery={setpatientSearchQuery}
              searchQuery={patientSearchQuery}
              loading={loading}
              isTestimonial={true}
            />
          </div>
          <TestimonialsMainList
            handleDeleteRanking={handleDeleteRanking}
            testimonials={testimonials}
            loading={loading}
            initialLoading={initialLoading}
            current_pagePatient={current_pagePatient}
            setCurrent_PagePatient={setCurrent_PagePatient}
            fetchAllTestimonials={fetchAllTestimonials}
            searchQuery={patientSearchQuery}
            handleAddRanking={handleAddRanking}
          />
        </div>
        <div className="bg-color p-2">
          <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
            Expert Testimonials
          </h5>
          <div className="row">
            <AllBlogsFilter
              startDate={startDate}
              endDate={endDate}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              setSearchQuery={setSearchQuery}
              searchQuery={searchQuery}
              loading={loading}
              isTestimonial={true}
            />
          </div>
          <ExpertTestimonialsList
            handleDeleteRanking={handleDeleteRanking}
            expertTestimonials={expertTestimonials}
            loading={loading}
            initialLoading={initialLoading}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
            fetchAllTestimonials={fetchAllTestimonials}
            searchQuery={searchQuery}
            handleAddRanking={handleAddRanking}
          />
        </div>
      </div>
    </>
  );
};

export default TestimonialsParent;
