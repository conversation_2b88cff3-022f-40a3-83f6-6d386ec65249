import React from "react";
import { Placeholder } from "react-bootstrap";

const HomeStatistics = ({ user_information, loading }) => {
  const {
    total_active_users = 0,
    total_active_patients = 0,
    total_active_experts = 0,
    total_active_admin = 0,
  } = user_information;
  const StatisticPlaceholder = () => (
    <Placeholder as="p" animation="glow">
      <Placeholder
        xs={12}
        size={"lg"}
        style={{ height: "50px", borderRadius: "5px" }}
      />
    </Placeholder>
  );
  // const loading = true
  return (
    <div className="row">
      <div className="col-sm-3">
        <div className="custom-border">
          <p className="text-center sub-heading">Total Active Users</p>
          {loading ? (
            <StatisticPlaceholder />
          ) : (
            <p className="text-center purple-num mb-2">{total_active_users}</p>
          )}
        </div>
      </div>
      <div className="col-sm-3">
        <div className="custom-border">
          <p className="text-center sub-heading">Experts</p>
          {loading ? (
            <StatisticPlaceholder />
          ) : (
            <p className="text-center purple-num mb-2">
              {total_active_experts}
            </p>
          )}{" "}
        </div>
      </div>
      <div className="col-sm-3">
        <div className="custom-border">
          <p className="text-center sub-heading ">Patients</p>
          {loading ? (
            <StatisticPlaceholder />
          ) : (
            <p className="text-center purple-num mb-2">
              {total_active_patients}
            </p>
          )}{" "}
        </div>
      </div>
      <div className="col-sm-3">
        <div className="custom-border">
          <p className="text-center sub-heading ">Child Admin</p>
          {loading ? (
            <StatisticPlaceholder />
          ) : (
            <p className="text-center purple-num mb-2">{total_active_admin}</p>
          )}{" "}
        </div>
      </div>
    </div>
  );
};

export default HomeStatistics;
