import { useState } from "react";
import { Button, Form, Modal } from "react-bootstrap";

const RejectReasonModal = ({
  show,
  onHide,
  debouncedHandleArticleApprovals,
  singleArticle,
  type,
  fetchData,
}) => {
  const [reason, setReason] = useState("");


  const handleReject = async () => {
    try {
      if (type === "article") {
        await debouncedHandleArticleApprovals(
          "reject",
          singleArticle.blog_details.id,
          singleArticle.blog_details.BlogTitle,
          reason
        );
      } else if (type === "review") {
        const words = singleArticle.Review.split(" ");
        const reviewSlice = words.slice(0, 4).join(" ");
        await debouncedHandleArticleApprovals(
          "reject",
          singleArticle.id,
          reviewSlice,
          reason
        );
      } else {
        await debouncedHandleArticleApprovals("reject", reason);
      }
      await fetchData();
      onHide(); // Close the modal after the operation is complete
    } catch (error) {
      console.error("Error in handling rejection: ", error);
    }
  };

  return (
    <Modal show={show} onHide={onHide} centered size="lg">
      <Modal.Header closeButton>
        <Modal.Title style={{ color: "#8107d1" }}>
          Enter Rejection Reason
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form.Group controlId="rejectionReason">
          <Form.Control
            as="textarea"
            rows={6}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter reason for rejection..."
          />
        </Form.Group>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button
          
          style={{ backgroundColor: "#8107d1" }}
          onClick={handleReject}
        >
          Save
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default RejectReasonModal;
