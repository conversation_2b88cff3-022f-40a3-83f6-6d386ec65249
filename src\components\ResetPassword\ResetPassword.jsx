"use client";
import React, { useState, useCallback, useEffect } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation"; // Import useSearchParams
import { FaEye, FaEyeSlash } from "react-icons/fa"; // Import icons
import adminLogo from "../../../public/assets/adminloginlogo.png";
import ellipseadmin from "../../../public/assets/ellipseadmin.png";
import ellipsetop from "../../../public/assets/ellipse-top.png";
import RightLogin from "../Login/rightLogin";
import axios from "axios";
import { toast } from "react-toastify";
import Loading from "../Loading/PageLoading/Loading";
import { Spinner } from "react-bootstrap";

const ResetPassword = () => {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [loading, setLoading] = useState(true);
  const params = useSearchParams();
  const verificationCode = params.get("verify_code");
  const email = params.get("email");

  useEffect(() => {
    if (!email && !verificationCode) {
      if (typeof window !== "undefined") {
        window.location.href = "/auth/login";
      }
      setLoading(true);
      return;
    } else {
      setLoading(false);
    }
    // setLoading(false);
  }, [email, verificationCode]);

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();

      if (newPassword !== confirmPassword) {
        toast.error("Passwords do not match!", {
          autoClose: 3000,
        });
        setNewPassword("");
        setConfirmPassword("");
        return;
      }

      try {
        setProcessing(true);
        const response = await axios.put(
          `${process.env.NEXT_PUBLIC_ADMIN_VERIFY_FORGOT_PASSWORD}${email}/`,
          {
            code: verificationCode,
            pw: newPassword,
          }
        );
        if (response.status === 200) {
          toast.success(
            "Password reset successful. You will be redirected to the login page.",
            {
              autoClose: 3000,
              position: "top-center",
            }
          );
          setNewPassword("");
          setConfirmPassword("");
          if (typeof window !== "undefined") {
            setTimeout(() => {
              window.location.href = "/auth/login";
            }, 3000);
          }
        } else {
          toast.error("Password reset failed", {
            autoClose: 3000,
            position: "top-center",
          });
        }
      } catch (error) {
        console.error("Error during password reset:", error);
        toast.error("Error during password reset.", {
          autoClose: 3000,
          position: "top-center",
        });
      } finally {
        setProcessing(false);
      }
    },
    [newPassword, confirmPassword, email, verificationCode]
  );

  return (
    <>
      {loading ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="row">
          <div className="col-sm-6 gx-0">
            <Image src={adminLogo} className="admin-logo ms-2" alt="" />
            <Image src={ellipsetop} className="ellipse-top" alt="" />
            <h5 className="login-heading">Reset Password</h5>
            <div className="login-form">
              <form>
                <div className="row">
                  <div className="col-sm-12">
                    <div className="form-group mt-2">
                      <label
                        htmlFor="newPassword"
                        className="admin-login-label mb-1"
                      >
                        Enter New Password:
                      </label>
                      <div className="input-group position-relative">
                        <input
                          type={showNewPassword ? "text" : "password"}
                          className="form-control form-login-fields"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          placeholder="Enter new password"
                          required
                        />
                        <span
                          className="show-hide-password-section"
                          onMouseDown={() => {
                            setShowNewPassword(true);
                          }}
                          onMouseUp={() => {
                            setShowNewPassword(false);
                          }}
                          onTouchStart={() => {
                            setShowNewPassword(true);
                          }}
                          onTouchEnd={() => {
                            setShowNewPassword(false);
                          }}
                        >
                          {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                        </span>
                      </div>
                    </div>
                    <div className="form-group mt-5">
                      <label
                        htmlFor="confirmPassword"
                        className="admin-login-label mb-1"
                      >
                        Confirm Password:
                      </label>
                      <div className="input-group position-relative">
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          className="form-control form-login-fields"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          placeholder="Confirm password"
                          required
                          onKeyDown={handleKeyPress}
                        />
                        <span
                          className="show-hide-password-section"
                          onMouseDown={() => {
                            setShowConfirmPassword(true);
                          }}
                          onMouseUp={() => {
                            setShowConfirmPassword(false);
                          }}
                          onTouchStart={() => {
                            setShowConfirmPassword(true);
                          }}
                          onTouchEnd={() => {
                            setShowConfirmPassword(false);
                          }}
                        >
                          {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                        </span>
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={handleSubmit}
                    className="admin-submit mt-5"
                    disabled={processing}
                  >
                    {processing ? (
                      <>
                        <div className="d-flex justify-content-center align-items-center">
                          <div>
                            <Spinner animation="border" size="sm" />
                          </div>
                          &nbsp; &nbsp;
                          <div className="fs-5">Submitting...</div>
                        </div>
                      </>
                    ) : (
                      <div className="d-flex justify-content-center align-items-center">
                        <p className="fs-5 mt-2">Confirm</p>
                      </div>
                    )}
                  </button>
                </div>
              </form>
            </div>
            <Image src={ellipseadmin} className="ellipse-down" alt="" />
          </div>

          <div className="col-sm-6 gx-0">
            <RightLogin />
          </div>
        </div>
      )}
    </>
  );
};

export default ResetPassword;
