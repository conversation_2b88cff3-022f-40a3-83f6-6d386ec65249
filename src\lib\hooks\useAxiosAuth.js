import { useEffect, useCallback, useState, useMemo } from "react";
import { useRefreshToken } from "./useRefreshToken";
import { axiosAuth } from "../axios";
import Cookies from "js-cookie";
import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
const useAxiosAuth = () => {
  const refreshToken = useRefreshToken();
  const [error, setError] = useState(false);

  const { data: session, update } = useSession(); // get update from useSession
  const [url, setUrl] = useState("");
  const router = useRouter();

  let requestAttempted = false;

  useEffect(() => {
    if (typeof window !== "undefined") {
      const newUrl = new URL(window.location.href);
      const pathWithParams = `${newUrl.pathname}${newUrl.search}`;
      setUrl(pathWithParams);
    }
  }, [router]);

  const newAccessToken = useMemo(() => {
    if (session?.user?.access_token) {
      return session?.user?.access_token;
    }
    return null;
  }, [session]);

  const handleSignOut = async () => {
    try {
      await signOut({ redirect: false, callbackUrl: "/" });
      // toast.success("Logged Out Successfully", {
      //   autoClose: 3000,
      //   theme: "colored",
      //   position: "top-center",
      // });

      Cookies.remove("user_id");
      setTimeout(() => {
        router.push(`/auth/login?returnUrl=${url}`);
      }, 2000);
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };
  const handle401Error = useCallback(
    async (error) => {
      const prevRequest = error?.config;
      if (
        error?.response?.status === 401 &&
        error?.response?.data === "Invalid token" &&
        !requestAttempted
      ) {
        requestAttempted = true;
        try {
          let refreshTokenCall = await refreshToken();

          let newAccessTokenResponse = refreshTokenCall;

          if (newAccessTokenResponse == "Refesh token expired") {
            handleSignOut();
            return;
          }
          prevRequest.headers[
            "Authorization"
          ] = `Bearer ${newAccessTokenResponse}`;
          if (session) {
            let updatedUser = { ...session.user };
            updatedUser.access_token = newAccessTokenResponse; // check if session.user exists

            await update({
              ...session,
              user: updatedUser,
            });
          }
          return axiosAuth(prevRequest);
        } catch (refreshError) {
          // console.error("Error during token refresh:", refreshError);
          handleSignOut();
          return Promise.reject(error);
        }
      }
      return Promise.reject(error);
    },
    [refreshToken, session, update] // add update to dependencies
  );

  useEffect(() => {
    const requestInterceptor = axiosAuth.interceptors.request.use(
      (config) => {
        if (!config.headers["Authorization"] && session) {
          config.headers["Authorization"] = `Bearer ${
            newAccessToken || session?.user?.access_token
          }`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // const responseInterceptor = axiosAuth.interceptors.response.use(
    //   (response) => response,
    //   async (error) => {
    //     setError(true);
    //     try {
    //       console.log(error, "This responseInterceptor");
    //       await handle401Error(error);
    //     } catch (error) {
    //       console.error("Error in responseInterceptor:", error);
    //     }
    //   }
    // );
    const responseInterceptor = axiosAuth.interceptors.response.use(
      (response) => response,
      async (error) => {
        setError(true);
        await handle401Error(error);
      }
    );

    return () => {
      axiosAuth.interceptors.request.eject(requestInterceptor);
      axiosAuth.interceptors.response.eject(responseInterceptor);
    };
  }, [handle401Error, newAccessToken, session]);

  return axiosAuth;
};

export default useAxiosAuth;
