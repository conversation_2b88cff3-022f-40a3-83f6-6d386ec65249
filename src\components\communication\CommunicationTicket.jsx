"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import SingleMessage from "../communication/SingleMessage";
import { HiFlag, HiOutlineFlag } from "react-icons/hi2";
import Image from "next/image";
import profileImg from "../../../public/images/profile.png";
import { IoMdRefresh } from "react-icons/io";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import {
  capitalizeFullName,
  convert_time,
  truncateTitle,
} from "../../utils/helperfunction";
import { Placeholder } from "react-bootstrap";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import CustomPagination from "../CustomPagination/CustomPagination";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};
const CommunicationTicket = ({ data: initialData, loading, activeTab }) => {
  const [isUrgentClicked, setIsUrgentClicked] = useState(false);
  const [rotation, setRotation] = useState(0);
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [filteredTickets, setFilteredTickets] = useState(
    initialData?.ticket_details || []
  );
  const [totalQueries, setTotalQueries] = useState(
    initialData?.ticket_details?.length || 0
  );
  const [showSingleMessage, setShowSingleMessage] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterLoading, setFilterLoading] = useState(false);
  const [selectedTicketData, setSelectedTicketData] = useState(null);
  const [error, setError] = useState(false);
  const { data: session } = useSession();
  const admin_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const fetchedDataRef = useRef({});
  const isFetchingRef = useRef(false);

  const handleTicketClick = (tickets) => {
    setSelectedTicketData(tickets);
    setShowSingleMessage(true);
  };

  useEffect(() => {
    const dataArray = Array.isArray(initialData?.ticket_details)
      ? initialData?.ticket_details
      : [];
    setFilteredTickets(dataArray);

    if (dataArray.length > 0) {
      const lastElement = dataArray[dataArray.length - 1];
      setTotalQueries(lastElement);
    } else {
      setTotalQueries(null);
    }
  }, [initialData]);

  const fetchTicketData = useCallback(async () => {
    if (isFetchingRef.current || !admin_id) return;

    const controller = new AbortController(); // Handle cancellations
    const signal = controller.signal;

    isFetchingRef.current = true;
    setFilterLoading(true);
    try {
      const url = `${process.env.NEXT_PUBLIC_TICKETS_BASED_BY_USER_ROLE}${activeTab}/${selectedStatus}/?user_id=${admin_id}&page=${current_page}&per_page=10`;

      // Check for cached data
      if (fetchedDataRef.current[url]) {
        const cachedData = fetchedDataRef.current[url];
        setFilteredTickets(cachedData?.ticket_details || []);
        setTotalPages(cachedData?.total_pages || 1);
      } else {
        const response = await axiosAuth.get(url, { signal }); // Attach the abort signal
        if (response.status === 200) {
          fetchedDataRef.current[url] = response.data; // Cache response data
          setFilteredTickets(response?.data?.ticket_details.slice(0, -1) || []);
          setTotalPages(response.data?.total_pages || 1);
        } else {
          console.error("Error fetching tickets: ", response.statusText);
        }
      }
    } catch (error) {
      if (error.name !== "AbortError") {
        console.error("Error fetching tickets: ", error);
      } else {
        console.log("Fetch aborted");
      }
      setError(true);
    } finally {
      isFetchingRef.current = false;
      setFilterLoading(false);
    }
  }, [admin_id, axiosAuth, selectedStatus, activeTab, current_page]);

  useEffect(() => {
    const delay = setTimeout(() => {
      fetchTicketData();
    }, 300); // Add debounce delay (e.g., 300ms)

    return () => clearTimeout(delay); // Cleanup for debounce
  }, [fetchTicketData]);

  const handleShowAllTickets = () => {
    setShowSingleMessage(false);
    setSelectedTicketData(null);
    setCurrent_Page(1);
  };
  const handleSearch = () => {
    let processedQuery = searchQuery.trim();
    // Remove '#' if present
    processedQuery = processedQuery.replace("#", "");
    // Extract numeric portion
    const ticketId = processedQuery.match(/\d+/);

    if (ticketId) {
      const filteredData = filteredTickets?.filter((ticketGroup) => {
        const ticketDetailsId = ticketGroup[0]?.ticket_details.id;

        return ticketDetailsId == ticketId;
      });
      setFilteredTickets(filteredData);
    } else {
      // If no numeric portion found, reset to show all tickets
      setFilteredTickets(initialData?.ticket_details);
    }
  };
  const handleRefresh = () => {
    setFilteredTickets(initialData?.ticket_details);
    setRotation((prevRotation) => prevRotation + 360);
    setSearchQuery("");
  };

  return (
    <>
      <div className="bg-color">
        {/* <div className="user-management-scroll overflow-auto"> */}
        <div className="row">
          <div className="col-sm-auto">
            <div className="queries-background">
              <p className="mt-3 fw-medium ms-1">Inbox</p>
              <div
                className={`box-color query-border-1 mt-2 ${
                  isUrgentClicked ? "query-border-1" : ""
                }`}
                onClick={() => {
                  setSelectedStatus("all");
                  handleShowAllTickets();
                }}
              >
                All Tickets
                <span className="queries-no-color-1">
                  {totalQueries?.total_tickets}
                </span>
              </div>
              <div
                className={`box-color query-border-2 mt-2 ${
                  isUrgentClicked ? "query-border-2" : ""
                }`}
                onClick={() => {
                  setSelectedStatus("Open");
                  handleShowAllTickets();
                }}
              >
                Open
                <span className="queries-no-color-2">
                  {totalQueries?.total_open_ticket}
                </span>
              </div>
              <div
                className={`box-color query-border-3 mt-2 ${
                  isUrgentClicked ? "query-border-3" : ""
                }`}
                onClick={() => {
                  setSelectedStatus("On Hold");
                  handleShowAllTickets();
                }}
              >
                On Hold
                <span className="queries-no-color-3">
                  {totalQueries?.total_on_hold_ticket}
                </span>
              </div>
              <div
                className={`box-color query-border-4 mt-2 ${
                  isUrgentClicked ? "query-border-4" : ""
                }`}
                onClick={() => {
                  setSelectedStatus("Escalated");
                  handleShowAllTickets();
                }}
              >
                Escalated
                <span className="queries-no-color-4">
                  {totalQueries?.total_escalated_ticket}
                </span>
              </div>
              <div
                className={`box-color query-border-5 mt-2 ${
                  isUrgentClicked ? "query-border-5" : ""
                }`}
                onClick={() => {
                  setSelectedStatus("Closed");
                  handleShowAllTickets();
                }}
              >
                Closed
                <span className="queries-no-color-5">
                  {totalQueries?.total_closed_ticket}
                </span>
              </div>
            </div>
          </div>

          <div className={isUrgentClicked ? "hello-col" : "col-sm"}>
            {selectedTicketData !== null && showSingleMessage ? (
              <SingleMessage
                ticketId={selectedTicketData[0]?.ticket_details?.id}
                handleShowAllTickets={handleShowAllTickets}
                time={selectedTicketData[1]?.thread_data[0]?.CreatedTime}
                title={selectedTicketData[0]?.ticket_details?.subject}
              />
            ) : (
              <>
                <div className="queiries-lines" style={{ height: "615px" }}>
                  <div className="float-start tickets_title">
                    {capitalizeFullName(activeTab)} Tickets
                  </div>
                  <div className="col-sm-6  offset-sm-6 ml-auto d-flex justify-content-around align-items-center">
                    &nbsp;&nbsp;
                    <div className=" offset-sm-4 input-group mb-3 ">
                      <input
                        type="text"
                        className="form-control custom-form-control"
                        placeholder="Search for Ticket ID"
                        aria-label="Search for Ticket ID"
                        aria-describedby="button-addon2"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                      <button
                        className="btn orange-btn-communication"
                        type="button"
                        id="button-addon2"
                        onClick={handleSearch}
                      >
                        Search
                      </button>
                    </div>
                    &nbsp;&nbsp;
                    <div
                      className="d-flex justify-content-between align-items-center mb-3"
                      onClick={handleRefresh}
                    >
                      <div className="custom_refresh">Refresh</div>
                      <div>
                        {" "}
                        <IoMdRefresh
                          className="rotating-refresh-icon"
                          style={{ transform: `rotate(${rotation}deg)` }}
                        />
                      </div>
                    </div>
                  </div>
                  {loading || filterLoading ? (
                    renderPlaceholders()
                  ) : filteredTickets?.length === 0 ? (
                    <div className="fs-4 d-flex align-items-center justify-content-center no-Patient-tickets-found">
                      <PiFolderNotchOpenFill className="PiFolderNotchOpenFill_icon" />
                      &nbsp; No Records Found
                    </div>
                  ) : (
                    <>
                      <div>
                        {filteredTickets &&
                          Array.isArray(filteredTickets) &&
                          filteredTickets?.map((ticketGroup, index) => {
                            const ticket = ticketGroup[0]?.ticket_details;
                            const threadData = ticketGroup[1]?.thread_data;
                            return (
                              ticket?.id && (
                                <div
                                  key={index}
                                  className={`line-color`}
                                  onClick={() => handleTicketClick(ticketGroup)}
                                >
                                  <span className="remove-btn-style">
                                    {index + 1}
                                  </span>
                                  <span className="urgent-query">
                                    <span className="ticket-heading">
                                      Ticket Id{" "}
                                    </span>
                                    &nbsp;- {ticket?.id}
                                  </span>
                                  <span className="line-of-query ">
                                    {truncateTitle(ticket?.subject, 50)}
                                  </span>
                                  <span className="query-time">
                                    {convert_time(ticket?.CreatedTime)}
                                  </span>
                                  <span className="query-status px-auto">
                                    <span
                                      className={`query-status-text ${
                                        ticket?.status === "Open"
                                          ? "bg-success"
                                          : ticket?.status === "Closed"
                                          ? "bg-danger"
                                          : ticket?.status === "Escalated"
                                          ? "bg-info"
                                          : ticket?.status === "On Hold"
                                          ? "bg-warning"
                                          : ""
                                      }`}
                                    >
                                      {ticket?.status}
                                    </span>
                                  </span>
                                </div>
                              )
                            );
                          })}
                      </div>

                      {totalPages > 1 && (
                        <div className="d-flex justify-content-center align-items-center mt-2">
                          <div className="d-none d-xl-block">
                            <CustomPagination
                              total_pages={totalPages}
                              current_page={current_page}
                              setCurrent_Page={setCurrent_Page}
                            />
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
        {/* </div> */}
      </div>
    </>
  );
};

export default CommunicationTicket;
