import React from "react";
import { Placeholder } from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";

const PatientApprovalPlaceholder = () => {
  const placeholders = Array.from({ length: 10 }).map((_, index) => (
    <tr key={index} className="allApproval-patient-custom-row">
      <td className="text-center d-flex align-items-center pr-4">
        <Placeholder
          as="div"
          animation="glow"
          className="d-flex align-items-center"
        >
          <Placeholder
            style={{
              width: "35px",
              height: "35px",
              borderRadius: "50%",
              marginRight: "10px",
            }}
            className="expert_image"
          />
          <Placeholder
            className="allApproval-patient-content"
            style={{ width: "150px" }}
          />
        </Placeholder>
      </td>
      <td className="text-center">
        <Placeholder as="div" animation="glow">
          <Placeholder style={{ width: "100px" }} />
        </Placeholder>
      </td>
      <td className="text-center">
        <Placeholder as="div" animation="glow">
          <Placeholder style={{ width: "180px" }} />
        </Placeholder>
      </td>
      <td className="text-center fw-semibold">
        <Placeholder as="div" animation="glow">
          <Placeholder style={{ width: "100px" }} />
        </Placeholder>
      </td>
    </tr>
  ));

  return <>{placeholders}</>;
};

export default PatientApprovalPlaceholder;
