button.btn.btn-preview {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  color: #8107d1;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
}
button.btn.btn-preview:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.inner-content {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  border-radius: 5px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 15px 0px 15px 0px;
  cursor: pointer;
}



.preview-content {
  background: #fbfbfb;
  border-radius: 3px;
  width: 100%;
  min-height: 692px;
  height: 100%;
  /* display: flex;
    justify-content: center;
    align-items: center;
    text-align: center; */
  padding: 10px;
}

.nopreview {
  color: #dfdfdf;
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  /* Optionally, you can use text-align for horizontal centering */
}

.card-name {
  letter-spacing: 0px;
  color: #414146;
  font-size: 14px;
  font-weight: 600;
}

.expert-type {
  letter-spacing: 0px;
  color: #100db1;
  opacity: 1;
  font-size: 14px;
}

.active-approval {
  float: right;
}

.type-patient {
  letter-spacing: 0px;
  color: #96969c;
  opacity: 1;
  font-size: 14px;
}

/* ----------------------------New Layout---------------------------- */

.appointmentPage-container {
  margin-top: 100px;
}

.consultation-number {
  box-shadow: 0px 10px 20px #00000029;
  border-radius: 5px;
  border-left: 5px solid #8107d1;
  /* background: purple; */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
.consultation-number.purple {
  border-left: 5px solid #8107d1;
}
.consultation-number.red {
  border-left: 5px solid #97012f;
}
.consultation-number.black {
  border-left: 5px solid #414146;
}

p.count {
  letter-spacing: 0px;
  font-weight: 600;
  font-size: 58px;
  display: flex;
  align-items: center;
}

.count.purple {
  color: #8107d1;
}
.count.red {
  color: #97012f;
}
.count.black {
  color: #414146;
}
span.purple-text {
  font-size: 21px;
}
p.purple-heading {
  letter-spacing: 0px;
  color: #8107d1;
  font-size: 16px;
  font-weight: 600;
}
.todayApp-height {
  max-height: 200px;
}

.todayApp-scroll {
  max-height: 200px;
}

/* .todayApp-scroll::-webkit-scrollbar {
  display: none;
} */

.allApp-scroll {
  max-height: 800px;
  overflow-y: auto;
}
/* .allApp-scroll::-webkit-scrollbar {
  display: none;
} */

.statusFilter {
  /* box-shadow: 0px 3px 6px #00000029; */
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 37px;
}
.statusFilter:focus {
  border: 1px solid #e3e3e3;
  box-shadow: 0px 3px 6px #00000029;
}
.selectDate {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 37px;
}
.selectDate :focus {
  border: 0;
  border-radius: 0;
  box-shadow: none;
}
.cross-container {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.cross-symbol {
  /* background: #8107d1; */
  border-radius: 50%;
  color: #8107d1;
  padding: 3px;
}

.searchbar {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 38px;
}
.searchbar:focus {
  border: 0;
  border-radius: 0;
  box-shadow: none;
}

input#app-search {
  border: 0;
  border-radius: 0;
  box-shadow: none;
}
.search-span {
  height: 44px;
  border-left: none;
  background-color: transparent;
}

.custom-table-head {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}

.appointements-section {
  background: #f8f5f9 0% 0% no-repeat padding-box;
  border-radius: 5px;
  padding: 10px;
}
p.app-text-size {
  font-size: 12px;
  color: #414146;
}
img.no-data-found-img {
  object-fit: contain;
}
span.purple-border-text {
  border: 1px solid #e1e1e1;
  border-radius: 3px;
  background: transparent linear-gradient(180deg, #7b009c 0%, #a200ce 100%) 0%
    0% no-repeat padding-box;
  color: white;
  padding: 6px;
  font-size: 10px;
}
.viewMedical-record,
.viewMedical-record:hover,
.viewMedical-record:focus,
.viewMedical-record:active {
  border: 1px solid #e1e1e1;
  border-radius: 6px;
  background-color: #b50000 !important;
  color: white;
  padding: 6px !important;
  font-size: 10px;
}
.black-reschedule-btn,
.black-reschedule-btn:hover {
  background: #414146 0% 0% no-repeat padding-box !important;
  border-radius: 7px;
  width: 100%;
  border: none;
  font-size: 14px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.orange-join-btn,
.orange-join-btn:hover {
  background: #f37721 0% 0% no-repeat padding-box !important;
  border-radius: 7px;
  width: 100%;
  border: none;
  font-size: 14px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.app-table-pink {
  border-radius: 10px;
  background-color: #f6f6f6;
  padding: 10px;
}
/* table.table {
  --bs-table-bg: #f6f6f6;
} */
thead.custom-table-head {
  border-style: hidden;
  font-size: 16px;
}
.single-line-appointment {
  white-space: nowrap; 
  overflow: hidden;
  text-overflow: ellipsis; 
  font-size: 13px;
}
.table.custom-bg-table{
  background-color: white;
}
.custom-bg-table td, 
.custom-bg-table th {
    border: 1px solid white;
    padding: .6rem .5rem;
}

th.appHead {
  color: #707070;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap; /* Prevents text wrapping */
  overflow: hidden; /* Ensures no overflow issues */
  text-overflow: ellipsis;
}
th.appHead.app-head-white{
  background-color: white;
}
tr.custom-table-body {
  border-style: hidden;
  --bs-table-bg: white;
}
td {
  font-size: 14px;
  color: #414146;
  font-weight: 500;
}

.viewPres-btn {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  background: #39c100 0% 0% no-repeat padding-box;
  border: none;
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
  height: 30px;
  width: 90%;
  padding: 0px;
}
.addPres-btn {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  background: #6c63ff 0% 0% no-repeat padding-box;
  border: none;
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
  height: 30px;
  width: 90%;
  padding: 0px;
}
.viewConsent-btn,
.viewConsent-btn:hover {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 600;
  border: 1px solid transparent;
  height: 30px;
  width: 100%;
  color: white;
  padding: 0px;
}

@media screen and (min-width: 576px) {
}
@media screen and (max-width: 768px) {
}
@media screen and (min-width: 768px) {
}
@media screen and (max-width: 992px) {
  .appointmentPage-container {
    margin-top: 0px;
  }
}
@media screen and (min-width: 768px) {
}
@media screen and (max-width: 991px) {
  .appointmentPage-container {
    margin-top: 100px;
  }
}

.placeHolder_loading {
  margin-bottom: 2%;
  border-radius: 8px;
}

.no_appoint_today {
  color: #5b5b5b;
  font-size: 20px;
}

::-webkit-scrollbar {
  display: none;
}

.custom-scroll::-webkit-scrollbar {
  display: none;
}

.calender-filter-container {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  width: 100%;
  position: relative;
}
span.date-filter-patient {
  background: #ffffff;
  border-radius: 3px;
  color: #5b5b5b;
  width: 100%;
  padding: 6px 6px 7px;
  display: inline-flex;
  height: 37px;
  /* box-shadow: 0px 3px 6px #00000029; */
  border: 1px solid #e3e3e3;
  align-items: center;
}

.calendar-icon-patient {
  /* margin-left: 189px; */
  margin-top: -3px;
  float: right;
  position: absolute;
  right: 5%;
  cursor: pointer;
  color: #8107d1;
}
.calender-icon-place {
  right: 24%;
}

.cross-icon-calendar {
  color: #ff7700;
}

.role-cross-container {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.role-cross-symbol {
  border-radius: 50%;
  color: #8107d1;
  padding: 5px 10px 5px 10px;
}
