import useAxiosAuth from "@/lib/hooks/useAxiosAuth";
// import {  } from "bootstrap";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { debounce } from "lodash";
import { useCallback } from "react";
import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";
import ReactPlayer from "react-player";
import { useParams } from "next/navigation";
import { toast } from "react-toastify";
import NoDataFound from "../noDataFound/NoDataFound";
import { FaVideoSlash } from "react-icons/fa6";
import HU_logo from "../../../public/images/HU_logo.png";
import { useSession } from "next-auth/react";

const renderLabel = (index = 1) => {
  if (index === 1) {
    return <span className="badge bg-warning">Pending</span>;
  } else if (index === 2) {
    return <span className="badge bg-success">Approved</span>;
  } else if (index === 3) {
    return <span className="badge bg-danger">Rejected</span>;
  }
};

const VideoApprovals = () => {
  const [doctorDetails, setDoctorDetails] = useState({});
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [introVideoRejAppLoading, setIntroVideoRejAppLoading] = useState(false);
  const [enableTextBox, setEnableTextBox] = useState(false);
  const [videoRejectReason, setVideoRejectReason] = useState("");
  const [noDtaError, setNoDtaError] = useState(false);

  const axiosAuth = useAxiosAuth();
  const params = useParams();
  const doctor_email1 =
    params && params?.user_id?.length > 0 && params?.user_id[1]
      ? params?.user_id[1]
      : "";
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;

  const doctor_email = decodeURIComponent(doctor_email1);

  const handleEnableTextbox = (doctorId) => {
    setEnableTextBox(true);
    setSelectedVideo(doctorId); // Ensure this properly tracks the selected video
    setVideoRejectReason("");
  };

  const fetchDoctorDetails = useCallback(
    async (doctor_email) => {
      try {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_DOCTOR_DATA}${doctor_email}/`
        );

        setDoctorDetails(response?.data?.user_data);
      } catch (error) {
        console.log(error);
        setNoDtaError(true);
      }
    },
    [axiosAuth]
  );
  const calculateTextAreaHeight = () => {
    return `${Math.max(50, videoRejectReason.length * 2)}px`;
  };

  const handleIntroVideoStatus = async (status) => {
    let intro_Status =
      status === "reject" ? 3 : status === "approve" ? 2 : null;

    if (!intro_Status) return;

    try {
      setIntroVideoRejAppLoading(true);

      let dataToSend = { IntroVideoStatus: intro_Status };

      // Include rejection reason only if rejecting
      if (status === "reject") {
        if (!videoRejectReason.trim()) {
          toast.error("Please enter a reason for rejection.");
          setIntroVideoRejAppLoading(false);
          return;
        }
        dataToSend.IntroVideo_Reason = videoRejectReason;
      }
      const doctor_id = doctorDetails && doctorDetails?.id;
      if (!doctor_id) {
        toast.error("Something went wrong. Please try again later...");
        return;
      }
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CU_DOCTOR_INTRO_VIDEO_STATUS_BY_ADMIN}${doctor_id}/?user_id=${admin_id}`,
        dataToSend
      );

      const { message } = response?.data;

      if (message?.IntroVideoStatus === 2) {
        toast.success("Expert Intro Video Approved Successfully");
      } else if (message?.IntroVideoStatus === 3) {
        toast.success("Expert Intro Video Rejected Successfully");
      } else if (
        message === "Only pending videos can be approved or rejected"
      ) {
        toast.info(message);
      } else {
        toast.info(response?.data);
      }
      fetchDoctorDetails();
    } catch (error) {
      console.error("Error updating video status:", error);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setIntroVideoRejAppLoading(false);
      fetchDoctorDetails(doctor_email);
      setEnableTextBox(false);
    }
  };

  const debouncedHandleIntroVideoStatus = debounce(
    handleIntroVideoStatus,
    1000
  );
  // const handleVideoRejection = () => {
  //   debouncedHandleIntroVideoStatus("reject");
  // };
  const handleVideoRejection = () => {
    if (!videoRejectReason.trim()) {
      alert("Please enter a reason for rejection.");
      return;
    }

    // Send rejection with reason
    debouncedHandleIntroVideoStatus(
      "reject",
      doctorDetails?.doctor_other_details?.DoctorId,
      videoRejectReason
    );

    // Close the modal after rejection
    setEnableTextBox(false);
  };

  useEffect(() => {
    if (doctor_email) {
      fetchDoctorDetails(doctor_email);
    }
  }, [doctor_email, fetchDoctorDetails]);

  const introVideos = doctorDetails?.doctor_other_details?.IntVideoUrl || [];
  const introVideoStatus =
    doctorDetails?.doctor_other_details?.IntroVideoStatus;

  const renderButtons = (index = 1) => {
    if (index === 1) {
      return true;
    } else if (index === 2) {
      return false;
    } else if (index === 3) {
      return true;
    }
  };
  

  return (
    <>
      <div className="">
        <form>
          <div className="intro-video pe-0">
            <div className="d-flex justify-content-around align-items-center mt-4">
              {noDtaError ? (
                <div className="custom-margin-nodatafoud">
                  <NoDataFound />
                </div>
              ) : introVideos && introVideos?.length > 0 ? (
                introVideoStatus >= 1 &&
                introVideoStatus <= 3 && (
                  <>
                    <div className="row">
                      <div className="col-sm-6">
                        <div className="">
                          <div className="">
                            {renderButtons(
                              introVideos?.length == 1 && introVideoStatus == 1
                                ? 1
                                : introVideos?.length == 1 &&
                                  introVideoStatus == 2
                                ? 2
                                : introVideos?.length == 1 &&
                                  introVideoStatus == 3
                                ? 3
                                : (introVideos?.length == 2 &&
                                    introVideoStatus == 1) ||
                                  introVideoStatus == 2 ||
                                  introVideoStatus == 3
                                ? 2
                                : 1
                            ) && (
                              <input
                                type="checkbox"
                                checked={selectedVideo === 0}
                                onChange={() => {
                                  if (selectedVideo === 0) {
                                    setSelectedVideo(-1); // Unselect if already selected
                                  } else {
                                    setSelectedVideo(0); // Select if not selected
                                  }
                                }}
                                disabled={
                                  introVideos?.length == 1
                                    ? false
                                    : true || introVideoStatus == 2
                                    ? false
                                    : true
                                }
                              />
                            )}
                            &nbsp;
                            <label className="mb-3">
                              Intro Video 1{" "}
                              {renderLabel(
                                introVideos?.length == 1 &&
                                  introVideoStatus == 1
                                  ? 1
                                  : introVideos?.length == 1 &&
                                    introVideoStatus == 2
                                  ? 2
                                  : introVideos?.length == 1 &&
                                    introVideoStatus == 3
                                  ? 3
                                  : (introVideos?.length == 2 &&
                                      introVideoStatus == 1) ||
                                    introVideoStatus == 2 ||
                                    introVideoStatus == 3
                                  ? 2
                                  : 1
                              )}
                            </label>
                            <div
                              style={{
                                position: "relative",
                                width: "100%",
                                height: "auto",
                              }}
                              className="rounded"
                            >
                              <ReactPlayer
                                url={introVideos[0]}
                                controls
                                width="400px"
                                height={"254px"}
                                className="bg-dark rounded"
                              />
                              <Image
                                src={HU_logo}
                                width={90}
                                height={50}
                                alt="cu logo"
                                style={{
                                  position: "absolute",
                                  top: "15px",
                                  left: "8px",
                                  zIndex: 1,
                                }}
                              />
                            </div>
                          </div>

                          <div className="">
                            {renderButtons(
                              introVideos?.length == 1 && introVideoStatus == 1
                                ? 1
                                : introVideos?.length == 1 &&
                                  introVideoStatus == 2
                                ? 2
                                : introVideos?.length == 1 &&
                                  introVideoStatus == 3
                                ? 3
                                : (introVideos?.length == 2 &&
                                    introVideoStatus == 1) ||
                                  introVideoStatus == 2 ||
                                  introVideoStatus == 3
                                ? 2
                                : 1
                            ) && (
                              <div className="modal-footer mt-5">
                                <button
                                  type="button"
                                  className="btn allApproval-reject-btn"
                                  onClick={() =>
                                    handleEnableTextbox(
                                      doctorDetails?.doctor_other_details
                                        ?.DoctorId
                                    )
                                  }
                                >
                                  Reject
                                </button>

                                <button
                                  type="button"
                                  className="btn allApproval-approve-btn ms-2"
                                  onClick={() =>
                                    handleIntroVideoStatus(
                                      "approve",
                                      doctorDetails?.doctor_other_details
                                        ?.DoctorId
                                    )
                                  }
                                >
                                  Approve
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="col-sm-6">
                        <div className="">
                          <div className="">
                            {introVideos && introVideos?.length > 1 && (
                              <>
                                {renderButtons(introVideoStatus) && (
                                  <input
                                    type="checkbox"
                                    checked={selectedVideo === 1}
                                    onChange={() => {
                                      if (selectedVideo === 1) {
                                        setSelectedVideo(-1); // Unselect if already selected
                                      } else {
                                        setSelectedVideo(1); // Select if not selected
                                      }
                                    }}
                                    disabled={
                                      selectedVideo === null &&
                                      selectedVideo === -1 &&
                                      introVideoStatus === 2
                                    }
                                  />
                                )}
                                &nbsp;
                                <label className="mb-3">
                                  Intro Video 2 {renderLabel(introVideoStatus)}
                                </label>
                                <div
                                  style={{
                                    position: "relative",
                                    width: "100%",
                                    height: "auto",
                                  }}
                                  className="rounded"
                                >
                                  <ReactPlayer
                                    url={introVideos[1]}
                                    controls
                                    width="400px"
                                    className="bg-dark rounded"
                                    height={"254px"}
                                  />
                                  <Image
                                    src={HU_logo}
                                    width={90}
                                    height={50}
                                    alt="cu logo"
                                    style={{
                                      position: "absolute",
                                      top: "15px",
                                      left: "8px",
                                      zIndex: 1,
                                    }}
                                  />
                                </div>
                              </>
                            )}
                          </div>

                          <div className="">
                            {introVideos &&
                              introVideos?.length > 1 &&
                              renderButtons(introVideoStatus) && (
                                <div className="modal-footer mt-5">
                                  <button
                                    type="button"
                                    className="btn allApproval-reject-btn"
                                    onClick={() =>
                                      handleEnableTextbox(
                                        doctorDetails?.doctor_other_details
                                          ?.DoctorId
                                      )
                                    }
                                  >
                                    Reject
                                  </button>
                                  <button
                                    type="button"
                                    className="btn allApproval-approve-btn ms-2"
                                    onClick={() =>
                                      handleIntroVideoStatus(
                                        "approve",
                                        doctorDetails?.doctor_other_details
                                          ?.DoctorId
                                      )
                                    }
                                  >
                                    Approve
                                  </button>
                                </div>
                              )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )
              ) : (
                <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
                  <p className="mt-2 ms-2 text-secondary testimonial-note">
                    <span style={{ color: "#8107d1" }}>
                      <FaVideoSlash style={{ fontSize: "20px" }} />
                    </span>{" "}
                    No other videos available for approval.
                  </p>
                </div>
              )}
            </div>
          </div>
        </form>
      </div>
      {enableTextBox && (
        <Modal
          show={enableTextBox}
          onHide={() => {
            setEnableTextBox(false);
          }}
          size="lg"
          centered
        >
          <Modal.Header closeButton>
            <Modal.Title style={{ color: "#8107d1" }}>
              Reject Intro Video {selectedVideo + 1}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="row p-3">
              <label className="light-grey-text">Reason</label>
              <textarea
                className="text-area"
                value={videoRejectReason}
                disabled={!enableTextBox}
                onChange={(event) => setVideoRejectReason(event.target.value)}
                style={{ height: calculateTextAreaHeight() }}
              />
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              variant="secondary"
              onClick={() => {
                setEnableTextBox(false);
              }}
            >
              Close
            </Button>
            <button
              type="submit"
              className="btn btn-purple"
              disabled={!enableTextBox}
              onClick={(e) => {
                e.preventDefault();
                handleVideoRejection();
              }}
            >
              {introVideoRejAppLoading ? "Submitting.." : "Submit"}
            </button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

export default VideoApprovals;
