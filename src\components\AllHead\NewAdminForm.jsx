import Cookies from "js-cookie";
import React, { useCallback, useEffect, useState } from "react";
import { Form, Row, Col, Button } from "react-bootstrap";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import makeAnimated from "react-select/animated";
import { toast } from "react-toastify";
import moment from "moment-timezone";
import Select from "react-select";
import { debounce } from "lodash";
import { capitalizeFullName } from "../../utils/helperfunction";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import parsePhoneNumberFromString from "libphonenumber-js";
import ChildAdminToggle from "./ChildAdminToggle";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
const validatePhoneNumber = (phoneNumber) => {
  const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
  return parsedPhoneNumber && parsedPhoneNumber.isValid();
};

/* Custom styles for react-select */
const customStyles = {
  control: (provided, state) => ({
    ...provided,
    border: state.isFocused ? "2px solid white" : "1px solid white",
    boxShadow: state.isFocused
      ? "00px 3px 6px #00000029"
      : "0px 3px 6px #00000029",
    borderColor: state.isFocused ? "1px solid #e3e3e3" : "1px solid #e3e3e3",
    width: "100%",
    height: "48px",
    ":hover": {
      borderColor: "white",
    },
  }),
  menu: (provided) => ({
    ...provided,
    backgroundColor: "white",
    width: "100%",
    borderRadius: "15px",
    overflow: "hidden !important" /* Hide the scrollbars */,
  }),

  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused ? "#8107d1" : "white",
    color: state.isFocused ? "white" : "#333",
    ":hover": {
      backgroundColor: "#8107d1",
      color: "white",
    },
    borderRadius: "5px",
  }),
};

const initialState = {
  perms: [],
  name: "",
  phone: "",
  email: "",
  user_role: "child_admin",
  // password: generateRandomPassword(),
  // gender: "",
  // timezone:"",
  // memberCode: "CU_CA_",
  Designation: "",
};
const NewAdminForm = ({ handleClose }) => {
  const [formData, setFormData] = useState(initialState);
  const [packagetimezones, setPackagetimezones] = useState([]);
  const [permissionsList, setPermissionsList] = useState([]);
  const { session } = useAdminContext();
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [alphaChecked, setAlphaChecked] = useState(false);
  const [childAdminDesignationList, setChildAdminDesignationList] = useState(
    []
  );
  const [loading, setLoading] = useState(false);
  const [iti, setIti] = useState(null);
  const animatedComponents = makeAnimated();

  const admin_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getListOfChildAdminDesignation = useCallback(async () => {
    try {
      if (admin_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}all/?Category=Designation&user_id=${admin_id}`
        );
        setChildAdminDesignationList(response?.data);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, admin_id]);

  const fetchListOfPermission = useCallback(async () => {
    try {
      const permissionResponse = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_FETCH_PERMISSIONS}?user_id=${admin_id}`
      );
      setPermissionsList(permissionResponse?.data);
    } catch (e) {
      console.log(e, "permission error");
    }
  }, [axiosAuth, admin_id]);

  const togglePermission = (value, code) => {
    setFormData((prevFormData) => {
      let perms = [...prevFormData.perms];
      const index = perms.findIndex((perm) => perm[0] === value);

      if (index !== -1) {
        perms = perms.filter((perm) => perm[0] !== value);
      } else {
        perms.push([code, value, 1]);
      }

      return { ...prevFormData, perms };
    });
  };
  const handlePhoneChange = (value, country, e, formattedValue) => {
    setFormData((prevData) => ({ ...prevData, phone: formattedValue }));
  };

  useEffect(() => {
    const timezoneList = moment.tz.names();
    setPackagetimezones(timezoneList);

    if (admin_id) {
      fetchListOfPermission();
      getListOfChildAdminDesignation();
    }
  }, [fetchListOfPermission, admin_id, getListOfChildAdminDesignation]);

  const handleChange = (id, value) => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      [id]: value,
    }));
  };

  const handleSubmit = debounce(async () => {
    try {
      setLoading(true);
      if (
        !formData?.name ||
        !formData?.email ||
        // !formData?.password ||
        !formData?.phone ||
        !formData?.Designation
      ) {
        toast.error("Please fill all the required fields.");
        return;
      }

      if (
        !formData?.name ||
        !formData?.email ||
        // !formData?.password ||
        !formData?.Designation ||
        (!formData?.phone && formData?.perms.length > 0)
      ) {
        toast.error("Please fill all data.");
        return;
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData?.email)) {
        toast.error("Please enter a valid email address.");
        return;
      }
      // if (formData?.password.length < 6) {
      //   toast.error("Password must be at least 6 characters long.", {
      //     autoClose: 3000,
      //     position: "top-center",
      //   });
      //   return;
      // }

      const isValidPhoneNumber = validatePhoneNumber(formData?.phone);
      if (!isValidPhoneNumber) {
        toast.error("Invalid phone number");
        return;
      }

      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_CHILD_ADMIN_API}?user_id=${admin_id}`,
        formData
      );

      if (
        response?.data ===
        "{'email': [ErrorDetail(string='cu user with this email address already exists.', code='unique')], 'phone': [ErrorDetail(string='cu user with this phone already exists.', code='unique')]}"
      ) {
        toast.error("User with this email address and phone already exists.");
      } else if (
        response?.data ===
        "{'email': [ErrorDetail(string='cu user with this email address already exists.', code='unique')]}"
      ) {
        toast.error("User with this email address already exists.");
      } else if (
        response?.data ===
        "{'phone': [ErrorDetail(string='cu user with this phone already exists.', code='unique')]}"
      ) {
        toast.error("User with this phone number already exists.");
      } else {
        toast.success("Child Admin Added successfully.");
      }

      setFormData(initialState);
      setSelectedOptions([]);
      handleClose();
      setLoading(false);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Error submitting form", {
        autoClose: 3000,
        position: "top-center",
      });
    } finally {
      setFormData(initialState);
      setSelectedOptions([]);
      setLoading(false);
    }
  }, 1000);

  const handleCheckboxChange = (value, code, checked) => {
    if (checked) {
      setSelectedOptions([...selectedOptions, value]);
      togglePermission(value, code); // Add permission if checkbox is checked
    } else {
      setSelectedOptions(selectedOptions.filter((option) => option !== value));
      togglePermission(value, code); // Remove permission if checkbox is unchecked
    }
  };

  return (
    <>
      <Row>
        <Col sm={7}>
          <h6 className="edit-plans-pricing mt-2">Select Permissions for this user</h6>
        </Col>
      </Row>
      <Row className="mt-2">
        <Col sm={8}>
          <Row>
            {permissionsList &&
              permissionsList?.map((option, index) => (
                <Col sm={8} key={index}>
                  <div className="form-check form-switch d-flex">
                    <ChildAdminToggle
                      value={option.Name}
                      code={option.Codename}
                      handleCheckboxChange={handleCheckboxChange}
                      // notification={notification}
                      // onToggle={toggleNotification}
                    />

                    <label
                      className="form-check-label"
                      htmlFor={`switch-${option.Name}`}
                    >
                      {option.Name}
                    </label>
                  </div>
                </Col>
              ))}
          </Row>
          <style jsx>{`
            /* Custom styles for the checkbox input */
            .custom-checkbox {
              appearance: none;
              -webkit-appearance: none;
              -moz-appearance: none;
              outline: none;
              width: 44px;
              height: 25px;
              background-color: #8107d1; /* Default background color */
              border-radius: 15px;
              position: relative;
              cursor: pointer;
              box-shadow: 0px 3px 6px #00000029;
            }

            /* Style the inner circle of the checkbox when checked */
            .custom-checkbox:checked::before {
              content: "";
              display: block;
              width: 18px;
              height: 18px;
              background-color: white; /* Color when checked */
              border-radius: 50%;
              position: absolute;
              top: 3px;
              left: 19px;
              transform: translateX(15%);
            }

            /* Style the toggle button before it is checked */
            .custom-checkbox:not(:checked)::before {
              content: "";
              display: block;
              width: 18px;
              height: 18px;
              background-color: #fff; /* Color before checked */
              border-radius: 50%;
              position: absolute;
              top: 3px;
              left: 1px;
              transform: translateX(0);
              transition: transform 0.3s ease; /* Smooth transition */
            }
            .form-check-input.custom-checkbox:focus {
              border-color: transparent;
              outline: 0;
              box-shadow: none;
            }
            .form-check-input.custom-checkbox:checked {
              border-color: transparent !important;
            }

            /* Hide the default checkbox */
            .custom-checkbox input[type="checkbox"] {
              display: none;
            }
          `}</style>
        </Col>
        {/* <Col sm={4}>
          <Row>
            <Col sm={5} className="offset-1">
              <Form.Check
                type="checkbox"
                className="newadminlabel-1"
                label={<>Alpha</>}
                value="option9"
                checked={selectedOptions.includes("option9")}
                onChange={() => handleCheckboxChange("option9")}
              />
            </Col>
            <Col sm={6}>
              <Form.Check
                type="checkbox"
                className="newadminlabel-1"
                label={<>Beta</>}
                value="option10"
                checked={selectedOptions.includes("option10")}
                onChange={() => handleCheckboxChange("option10")}
              />
            </Col>
          </Row>
        </Col> */}
      </Row>
      <Row>
        <Col md={4}>
          <Form.Group controlId="name">
            <Form.Label className="label-edit mt-5">Name</Form.Label>
            <Form.Control
              type="text"
              className="input-form-modal"
              value={formData?.name}
              onChange={(e) =>
                handleChange("name", capitalizeFullName(e.target.value))
              }
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group controlId="user_role">
            <Form.Label className="label-edit mt-5">Designation</Form.Label>
            <Form.Select
              className="input-form-modal"
              value={formData?.Designation}
              onChange={(e) => handleChange("Designation", e.target.value)}
            >
              <option>Select Designation</option>
              {childAdminDesignationList &&
                Array.isArray(childAdminDesignationList) &&
                childAdminDesignationList.map((designation) => (
                  <option value={designation?.Content} key={designation?.id}>
                    {designation?.Content}
                  </option>
                ))}
            </Form.Select>
          </Form.Group>
        </Col>
      </Row>
      <Row>
        <Col md={4}>
          <Form.Group controlId="phone">
            <Form.Label className="label-edit mt-3">Phone Number</Form.Label>
            <PhoneInput
              ref={(itiRef) => setIti(itiRef)}
              id="floatingInputPhone"
              className="input-form-modal-phone"
              country={"in"}
              required="required"
              name="phoneNumber"
              value={formData?.phone}
              onChange={handlePhoneChange}
              inputStyle={{
                width: "260px",
                height: "48px",
                border: "1px solid #e3e3e3",
                boxShadow: "0px 3px 6px #00000029",
              }}
              buttonStyle={{
                border: "1px solid #e3e3e3",
                borderRight: 0,
                background: "white",
              }}
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group controlId="email">
            <Form.Label className="label-edit mt-3">Email</Form.Label>
            <Form.Control
              type="email"
              className="input-form-modal "
              value={formData?.email}
              onChange={(e) => handleChange("email", e.target.value)}
            />
          </Form.Group>
        </Col>
      </Row>
      <Row>
        <Col md={2} className="float-right">
          <Button onClick={handleSubmit} className="btn purple-modal-btn">
            {loading ? "Submitting.." : "Submit"}
          </Button>
        </Col>
      </Row>
    </>
  );
};

export default NewAdminForm;
