"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import Loader from "../loader/Loader";
import axios from "axios";
import Image from "next/image";
import "./verifymobile.css";
import RightLogin from "../Login/rightLogin";
import adminLogo from "../../../public/assets/adminloginlogo.png";
import ellipseadmin from "../../../public/assets/ellipseadmin.png";
import ellipsetop from "../../../public/assets/ellipse-top.png";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import {
  decryptBase,
  getCountryCodeFromPhoneNumber,
  getPhoneNumberWithoutCountryCode,
  safeDecode,
} from "../../utils/helperfunction";

const VerifyMobile = () => {
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [resendEnabled, setResendEnabled] = useState(true);
  const [registerButtonDisabled, setRegisterButtonDisabled] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const { getPatientData, isPhoneVerified } = useAdminContext();
  const { data: session } = useSession();
  const router = useRouter();
  const user_id = session?.user?.id;
  const user_email = session?.user?.email;
  const params = useSearchParams();
  const fullPhone = params.get("xts");

  const decryptedPhone = fullPhone ? decryptBase(safeDecode(fullPhone)) : null;

  const phoneNumberWithoutCountryCode =
    getPhoneNumberWithoutCountryCode(decryptedPhone);
  const countryCode = getCountryCodeFromPhoneNumber(decryptedPhone);

  const [countdown, setCountdown] = useState(() => {
    const endTime = localStorage.getItem("endTime");
    if (!endTime) return 120;
    const remainingTime = Math.round(
      (new Date(endTime).getTime() - Date.now()) / 1000
    );
    if (remainingTime <= 0) return 120;
    return remainingTime;
  });

  useEffect(() => {
    if (isPhoneVerified) {
      router.push("/");
    }
  }, [isPhoneVerified, router]);

  useEffect(() => {
    const storedEndTime = localStorage.getItem("endTime");

    if (storedEndTime) {
      const remainingTime = Math.round(
        (new Date(storedEndTime).getTime() - Date.now()) / 1000
      );

      if (remainingTime > 0) {
        setCountdown(remainingTime); // Resume timer from remaining time
      } else {
        setCountdown(0); // Timer expired
        setResendEnabled(true); // Enable resend button
        localStorage.removeItem("endTime"); // Clear expired timer
      }
    } else {
      setCountdown(0); // No timer found, assume expired
      setResendEnabled(true); // Enable resend button
    }
  }, []);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            clearInterval(timer);
            setResendEnabled(true);
            localStorage.removeItem("endTime");
            return 0;
          }
          return prevCountdown - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [countdown]);

  const formatCountdown = (countdown) => {
    const minutes = Math.floor(countdown / 60)
      .toString()
      .padStart(2, "0");
    const seconds = (countdown % 60).toString().padStart(2, "0");
    return `${minutes}:${seconds}`;
  };

  const axiosAuth = useAxiosAuth();

  const handleResendOtp = async (event) => {
    event.preventDefault();

    if (countdown > 0) {
      return;
    }
    setResendEnabled(false);
    setResendSuccess(false);

    try {
      setLoading(true);

      const formData = {
        email: user_email,
        phone: phoneNumberWithoutCountryCode,
        country_code: countryCode,
      };
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_USER_RESEND_OTP}`,
        formData
      );
      const message = response?.data?.message;

      if (message === "OTP Re-sent successfully.") {
        toast.success(message, {
          autoClose: 1500,
          theme: "colored",
          position: "top-center",
        });
        setCountdown(120);
        localStorage.setItem(
          "endTime",
          new Date(Date.now() + 120 * 1000).toISOString()
        );
        setResendSuccess(true);
      } else {
        toast.error(message || "Something went wrong. Please try again.", {
          autoClose: 2000,
          theme: "colored",
          position: "top-center",
        });
      }

      //   console.log("response", response);

      //   if (response?.data?.message === "something went wrong") {
      //     toast.error(response?.data?.message, {
      //       autoClose: 1500,
      //       theme: "colored",
      //       position: "top-center",
      //     });
      //   } else if (response?.data?.message === "otp sent") {
      //     toast.success("Otp Re-sent Successfully", {
      //       autoClose: 1500,
      //       theme: "colored",
      //       position: "top-center",
      //     });
      //     setCountdown(120);
      //     localStorage.setItem(
      //       "endTime",
      //       new Date(Date.now() + 120 * 1000).toISOString()
      //     );
      //     setResendSuccess(true);
      //   } else {
      //     toast.error("OTP verification failed", {
      //       autoClose: 1500,
      //       theme: "colored",
      //       position: "top-center",
      //     });
      //   }
    } catch (error) {
      console.error("Error resending OTP:", error);

      const status = error.response?.status;
      const errMsg =
        error.response?.data?.message ||
        (status === 404 && "OTP not found. Please register again.") ||
        (status === 429 &&
          "Please wait at least 1 minute before requesting another OTP.") ||
        (status === 500 && "OTP couldn't be sent. Please try again later.") ||
        "Something went wrong. Please try again.";

      toast.error(errMsg, {
        autoClose: 2000,
        theme: "colored",
        position: "top-center",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (event) => {
    event.preventDefault();

    try {
      setLoading(true);

      const data = {
        email: user_email,
        otp: otp,
        user_role: "child_admin",
        user_id: user_id,
      };

      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_VERIFY_OTP}`,
        data
      );
      const message = response?.data?.message;

      if (message === "Mobile verification successful") {
        toast.success("Mobile verified successfully!", {
          autoClose: 1500,
          theme: "colored",
          position: "top-center",
        });
        setRegisterButtonDisabled(true);
        getPatientData();
        setTimeout(() => {
          router.replace("/");
        }, 1000);
        //   else if (response?.data?.message === "Otp expired") {
        //     toast.error("OTP has Expired !", {
        //       autoClose: 1500,
        //       theme: "colored",
        //       position: "top-center",
        //     });
        //     setOtp("");
        //     setRegisterButtonDisabled(true);
      } else {
        toast.error(message || "OTP verification failed.", {
          autoClose: 1500,
          theme: "colored",
          position: "top-center",
        });
        setOtp("");
        setRegisterButtonDisabled(false);
      }
    } catch (error) {
      const backendMessage =
        error?.response?.data?.message ||
        error?.response?.data ||
        "Something went wrong during OTP verification.";

      toast.error(backendMessage, {
        autoClose: 2000,
        theme: "colored",
        position: "top-center",
      });
      console.error("error in registering:", backendMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {loading == true ? (
        <>
          <Loader />
        </>
      ) : (
        <div className="row">
          <div className="col-sm-6 gx-0">
            <Image src={adminLogo} className="admin-logo ms-4" alt="" />
            <Image src={ellipsetop} className="ellipse-top" alt="" />
            <div className="login-box">
              <div className="otp-box">
                <div className="verify-container">
                  <p className="verify-mobile fw-semibold">Verify Mobile</p>
                </div>
                <div className="otp-group-box">
                  <form onSubmit={handleVerifyOtp}>
                    <div className="text-left">
                      <p className="para">
                        {resendSuccess
                          ? "OTP has been resent to your phone."
                          : "We have sent you an OTP on"}
                        <br />
                      </p>
                      <p className="number">
                        {"+" +
                          countryCode +
                          " " +
                          phoneNumberWithoutCountryCode}
                      </p>
                      <p />
                    </div>
                    <div className="form-group">
                      <label htmlFor="text" className="OTP">
                        OTP
                      </label>
                      <input
                        type="text"
                        className="form-control OTP"
                        placeholder="Please enter the 6 digit OTP here to verify"
                        required
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                      />
                    </div>
                    <div className="resndOTP d-flex">
                      <div className=" me-auto">
                        <Link
                          href=""
                          onClick={handleResendOtp}
                          className={`resend-otp ${
                            countdown > 0 ? "disabled" : ""
                          }`}
                          style={{ opacity: countdown > 0 ? 0.5 : 1 }}
                        >
                          Resend OTP
                        </Link>
                      </div>
                      <div
                        style={{ fontSize: "13px", color: "#EF5F12" }}
                        className="valid-timer"
                      >
                        Valid for ({`${formatCountdown(countdown)}`})
                      </div>
                    </div>
                    <button
                      disabled={loading || registerButtonDisabled}
                      type="submit"
                      className="btn orange-btn verify-mobile-submit"
                    >
                      {loading ? "Submitting..." : "Submit"}
                    </button>
                  </form>
                </div>
              </div>
            </div>
            <Image src={ellipseadmin} className="ellipse-down" alt="" />
          </div>
          <div className="col-sm-6 gx-0">
            <RightLogin />
          </div>
        </div>
      )}
    </>
  );
};

export default VerifyMobile;
