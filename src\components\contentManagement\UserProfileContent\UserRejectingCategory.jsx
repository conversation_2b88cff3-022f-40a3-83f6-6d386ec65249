import React, { useState, useEffect, useCallback } from "react";
import { MdEdit, MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "react-bootstrap";
import Loader from "../../loader/Loader";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Swal from "sweetalert2";
import ContentManPlaceholder from "./ContentManPlaceholder";

const UserRejectingCategory = () => {
  const [showRejectedEditModal, setShowRejectedEditModal] = useState(false);
  const [listOfRejectionCategory, setListOfRejectionCategory] = useState(null);
  const [addRejectionCategory, setAddRejectionCategory] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Expert Rejection"); // Default to Expert Rejection
  const [formData, setFormData] = useState({ id: "", type: "" });
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getListOfRejectionCategory = useCallback(async () => {
    try {
      if (user_id) {
        const rejectCategoryResponse = await axiosAuth.get(
          `${
            process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE
          }all/?Category=${encodeURIComponent(
            selectedCategory
          )}&user_id=${user_id}`
        );
        setListOfRejectionCategory(rejectCategoryResponse?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, selectedCategory, user_id]);

  useEffect(() => {
    getListOfRejectionCategory();
  }, [user_id, getListOfRejectionCategory, axiosAuth]);

  const handleAddRejectionCategory = async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_CONTENT_TYPE}?user_id=${user_id}`,
        {
          Content: addRejectionCategory,
          Category: selectedCategory,
        }
      );
      setLoading(false);
      if (response?.data) {
        toast.success(`Rejection Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfRejectionCategory();
      }
      setAddRejectionCategory("");
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  const handleEditClick = (rejection) => {
    setFormData({ id: rejection.id, type: rejection.Content });

    setShowRejectedEditModal(true);
  };

  const handleClose = () => {
    setShowRejectedEditModal(false);
  };

  const handleDeleteClick = async (designationId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${designationId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Rejection Deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getListOfRejectionCategory();
          }
        } catch (error) {
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${formData.id}/?user_id=${user_id}`,
        {
          Content: formData.type,
          Category: selectedCategory,
        }
      );
      if (response?.data) {
        toast.success(`Rejection Edited Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfRejectionCategory();
      }
      setFormData({ id: "", type: "" });
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    handleClose();
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <>
      <div className="row">
        <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
          Add Rejection Category for users
        </h5>
        <div className="col-sm-4">
          <div className="mb-3">
            <div className="form-label custom-label">Rejection Category</div>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="radio"
                name="category"
                id="expertCategory"
                value="Expert Rejection"
                checked={selectedCategory === "Expert Rejection"}
                onChange={() => setSelectedCategory("Expert Rejection")}
              />
              <label className="form-check-label" htmlFor="expertCategory">
                Expert Rejection
              </label>
            </div>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="radio"
                name="category"
                id="patientCategory"
                value="Patient Rejection"
                checked={selectedCategory === "Patient Rejection"}
                onChange={() => setSelectedCategory("Patient Rejection")}
              />
              <label className="form-check-label" htmlFor="patientCategory">
                Patient Rejection
              </label>
            </div>
          </div>
          <div className="mb-3">
            <input
              type="text"
              className="form-control custom-form-control"
              id="designation"
              value={addRejectionCategory}
              onChange={(e) => setAddRejectionCategory(e.target.value)}
              placeholder="Enter Rejection Category"
            />
          </div>
          <button
            disabled={loading}
            type="submit"
            onClick={handleAddRejectionCategory}
            className="btn purple-button"
          >
            {loading ? "Adding Rejection Category" : "Add Rejection Category"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px", fontSize: "14px" }} className="fw-semibold">
                    Slno
                  </th>
                  <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">{selectedCategory} </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {listOfRejectionCategory &&
                  listOfRejectionCategory.map((item, index) => (
                    <tr key={index}>
                      <td scope="row" className="col custom-font ">{index + 1}</td>
                      <td className="custom-font">{item.Content}</td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item.id)}
                        >
                          <MdDelete />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      <Modal show={showRejectedEditModal} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Rejection</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="formDesignationType">
            <Form.Label>{selectedCategory} Type</Form.Label>
            <Form.Control
              type="text"
              name="type"
              value={formData.type}
              onChange={handleChange}
            />
          </Form.Group>
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            onClick={handleSubmit}
          >
            Update
          </Button>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default UserRejectingCategory;
