"use client";

import React, { Suspense, useEffect, useState } from "react";
import ChildMainProfile from "../../../../components/childAdminIndividual/ChildMainProfile";
import Navbar from "../../../../components/navbar/Navbar";
import "../../../../components/childAdminIndividual/childadminindividual.css";
import { Placeholder, Badge } from "react-bootstrap";
import Link from "next/link";
import ChildAdminProfileApproval from "../../../../components/childAdminIndividual/ChildAdminProfileApproval";
import { FiSettings } from "react-icons/fi";
import { LuBell } from "react-icons/lu";
import useAxiosAuth from "../../../../lib/hooks/useAxiosAuth";
import { capitalizeFullName } from "../../../../utils/helperfunction";
import Loading from "../../../../components/Loading/PageLoading/Loading";
import { useRouter } from "next/navigation";
import { useAdminContext } from "../../../../Context/AdminContext/AdminContext";
import withAuth from "../../../../withAuth/withAuth";
import BellIconNotification from "../../../../components/BellIconNotification/BellIconNotification.jsx";
import { useSession } from "next-auth/react";
import { useNotification } from "../../../../Context/NotificationContext/NotificationContext";

const ChildAdminSinglePage = ({ params = { user_id: [null, null] } }) => {
  const [childAdmin, setChildAdmin] = useState({});
  const [loading, setLoading] = useState(true);
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const { totalNotifications, notificationLoading } = useNotification();
  const router = useRouter();
  const { isAdminChildAdmin } = useAdminContext();
  const [redirecting, setRedirecting] = useState(isAdminChildAdmin);
  const authenticated = session?.status === "authenticated";

  const toggleNotificationPopup = () => {
    setIsNotificationOpen((prevState) => !prevState);
  };

  useEffect(() => {
    if (isAdminChildAdmin) {
      setRedirecting(true);
      router.push("/");
    }
  }, [isAdminChildAdmin, router]);

  let email =
    params && params.user_id.length > 0 && params.user_id[1]
      ? params.user_id[1]
      : "";

  useEffect(() => {
    if (email) {
      const fetchUserData = async () => {
        try {
          const response = await axiosAuth.get(
            `${process.env.NEXT_PUBLIC_GET_PATIENT_DATA}${email}/`
          );
          setLoading(false);
          setChildAdmin(response?.data?.user_data);
        } catch (error) {
          console.error(error);
        }
      };
      fetchUserData();
    } else {
      return;
    }
  }, [email, axiosAuth]);

  if (loading || redirecting) {
    return <Loading />;
  }
  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-9">
                    <p className="main-purple-text">
                      User Management {">"}
                      <span className=" ms-1">
                        {loading === false ? (
                          <Link
                            className="href_link"
                            href="/usermanagement/childAdmin"
                          >
                            {capitalizeFullName(
                              childAdmin ? childAdmin?.role : ""
                            )}
                          </Link>
                        ) : (
                          <>
                            <Placeholder as="span" animation="glow">
                              <Placeholder xs={1} />
                            </Placeholder>
                          </>
                        )}
                        {">"}
                      </span>
                      <span className=" ms-1">
                        {loading === true ? (
                          <>
                            <Placeholder as="span" animation="glow">
                              <Placeholder xs={1} />
                            </Placeholder>
                          </>
                        ) : (
                          <>
                            {capitalizeFullName(
                              childAdmin ? childAdmin?.name : ""
                            )}
                          </>
                        )}
                      </span>
                    </p>
                  </div>
                  <div className="col-sm-2">
                    <Suspense>
                      <ChildAdminProfileApproval />
                    </Suspense>
                  </div>
                  <div className="col-sm-1 d-flex justify-content-around align-items-center">
                    <Link
                      className=" text-decoration-none"
                      href={"/profilesetting"}
                    >
                      <FiSettings className="icon-setting" />
                    </Link>

                    <span className="nav-item d-none d-xl-block">
                      <span
                        onClick={(e) => {
                          e.stopPropagation(); // Stop the propagation of the click event
                          toggleNotificationPopup(); // Toggle the notification popup
                        }}
                        id="notification-link"
                        style={{
                          position: "relative",
                          display: "inline-block",
                        }}
                      >
                        <span className="btn btn-bell">
                          <LuBell className="icon-bell" cursor={"pointer"} />
                          <span className="start-100 translate-middle badge rounded-pill text-center">
                            {totalNotifications ? totalNotifications : 0}

                            <span className="visually-hidden">
                              unread messages
                            </span>
                          </span>
                        </span>

                        {authenticated && (
                          <>
                            {notificationLoading ? (
                              <Placeholder
                                as="div"
                                animation="glow"
                                className="notification-badge-placeholder"
                                style={{ width: "20px" }}
                              >
                                <Placeholder
                                  xs={12}
                                  size={"sm"}
                                  style={{
                                    height: "20px",
                                    borderRadius: "5px",
                                  }}
                                />
                              </Placeholder>
                            ) : (
                              <Badge bg="danger" className="notification-badge">
                                {/* {totalNotifications > 100 ? "100+" : totalNotifications} */}
                                {totalNotifications ? totalNotifications : 0}
                              </Badge>
                            )}
                          </>
                        )}
                      </span>
                    </span>
                    {isNotificationOpen && (
                      <BellIconNotification
                        isNotificationOpen={isNotificationOpen}
                        onClose={toggleNotificationPopup}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
            <Suspense>
              <ChildMainProfile params={params} />
            </Suspense>
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(ChildAdminSinglePage);
