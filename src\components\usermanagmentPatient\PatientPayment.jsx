"use client";

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { useSession } from "next-auth/react";
import { formatDateToYMD, highlightText } from "../../utils/helperfunction";
import { Placeholder } from "react-bootstrap";
import _ from "lodash";
import InvoiceModal from "./PatientPaymentDetails/InvoiceModal";
import PaymentFilter from "./PatientPaymentDetails/PaymentFilter";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import CustomPagination from "../CustomPagination/CustomPagination";
import NoDataFound from "../noDataFound/NoDataFound";
import { toast } from "react-toastify";

const PatientPayment = ({ id }) => {
  const axiosAuth = useAxiosAuth();
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [payments, setPayments] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [statusFilter, setStatusFilter] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [pageLimit, setPageLimit] = useState();

  const [error, setError] = useState(false);

  const handleClose = () => setShowModal(false);

  const handleShow = (payment) => {
    setSelectedPayment(payment);
    setShowModal(true);
  };
  const renderPlaceholders = (value) => {
    const placeholders = Array.from(
      { length: value === "initialLoad" ? 9 : 1 },
      (_, index) => (
        <div key={index} className="placeHolder_loading">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              size={"lg"}
              style={{ height: "43px", borderRadius: "4px" }}
            />
          </Placeholder>
        </div>
      )
    );

    return placeholders;
  };

  const getPayments = useCallback(
    async (pageNumber, perPage, query) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_PAYMENTS}${id}/?`;

        if (startDate && endDate && id) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
        }
        if (pageNumber && id) {
          url += `&page=${pageNumber}`;
        }
        if (query && id) {
          url += `&name=${query}`;
        }
        if (statusFilter && id) {
          url += `&status=${statusFilter}`;
        }
        if (perPage && id) {
          url += `&per_page=${perPage}`;
        }
        const response = await axiosAuth.get(url);

        setPayments(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (err) {
        console.log("Error in getting the payments:", err);
        setError(true);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [id, axiosAuth, endDate, statusFilter, startDate]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((page, perPage, query) => {
      getPayments(page, perPage, query);
    }, 400);
  }, [getPayments]);

  useEffect(() => {
    debouncedFetchData(currentPage, pageLimit, searchQuery);
  }, [searchQuery, debouncedFetchData, currentPage, pageLimit]);

  const handleClearQueryFilter = () => {
    setSearchQuery("");
    setCurrentPage(1);
  };


  return (
    <>
      <InvoiceModal
        showModal={showModal}
        handleClose={handleClose}
        selectedPayment={selectedPayment}
      />

      <div className="custom-width mb-3">
        <div className="row">
          <div className="col-md-4 col-xl-2">
            <PaymentFilter
              loading={loading}
              setStatusFilter={setStatusFilter}
              searchQuery={searchQuery}
              startDate={startDate}
              endDate={endDate}
              setEndDate={setEndDate}
              setStartDate={setStartDate}
              setSearchQuery={setSearchQuery}
              statusFilter={statusFilter}
              handleClearQueryFilter={handleClearQueryFilter}
              pageLimit={pageLimit}
              setPageLimit={setPageLimit}
              setCurrentPage={setCurrentPage}
              payments={payments}
            />
          </div>

          <div className="col-md-8 col-xl-10 mt-5">
            <div className="row hide-on-mobile ">
              <div className="col-sm-12">
                <div className="row ">
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Appointment Id</p>
                  </div>
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Payment Date</p>
                  </div>
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Payment Status</p>
                  </div>
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Doctor</p>
                  </div>
                  <div className="col-sm-1 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Amount</p>
                  </div>
                  <div className="col-sm-3 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">View/Download Invoice</p>
                  </div>
                </div>
              </div>
            </div>
            <hr />
            <div
              style={{ border: "none" }}
              className="custom-overflow-2 overflow-auto px-lg-3"
            >
              {initialLoading ? (
                renderPlaceholders("initialLoad")
              ) : payments?.items?.length > 0 ? (
                <>
                  {payments?.items &&
                    payments?.items?.map((payment, index) => (
                      <div key={payment?.AppointmentId} className="row mb-3">
                        <div className="col-sm-12">
                          <div className="content-holder-2 p-2">
                            <div className="row hide-on-mobile ">
                              <div className="col-sm-2 d-flex justify-content-center align-items-center">
                                <p className="mb-0">{payment?.AppointmentId}</p>
                              </div>
                              <div className="col-sm-2 d-flex justify-content-center align-items-center">
                                <p className="mb-0">
                                  {payment?.date?.split("T")[0]}
                                </p>
                              </div>
                              <div className="col-sm-2 d-flex justify-content-center align-items-center ">
                                <p
                                  className={`mb-0 rounded-4 payments-label text-white  ${
                                    payment.payment_status === "succeeded"
                                      ? " payment-success-bg"
                                      : payment.payment_status === "pending"
                                      ? "bg-warning"
                                      : "bg-danger"
                                  }`}
                                >
                                  {payment.payment_status === "succeeded"
                                    ? "success"
                                    : payment.payment_status}
                                </p>
                              </div>
                              <div className="col-sm-2 d-flex justify-content-center align-items-center">
                                <p className="mb-0 text-capitalize">
                                  {highlightText(payment.doctor, searchQuery)}
                                </p>
                              </div>
                              <div className="col-sm-1 d-flex justify-content-center align-items-center">
                                <p className="mb-0"> $ {payment.amount} </p>
                              </div>
                              <div className="col-sm-3 d-flex justify-content-center">
                                <div className="row">
                                  <div className="col-sm-7">
                                    <button
                                      type="button"
                                      className="btn btn-primary purple-button"
                                      data-bs-toggle="modal"
                                      data-bs-target="#exampleModal"
                                      onClick={() => handleShow(payment)}
                                    >
                                      View invoice
                                    </button>
                                  </div>
                                  <div className="col-sm-5">
                                    <button
                                      type="button"
                                      onClick={() => {
                                        const invoiceLink =
                                          payment?.invoice?.invoice_pdf;
                                        if (invoiceLink) {
                                          window.location.href = invoiceLink;
                                        } else {
                                          toast.error(
                                            "Invoice link not available"
                                          );
                                        }
                                      }}
                                      className="btn btn-primary purple-button"
                                    >
                                      Download
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                            {/* *******************************************************mobile design**************************************************** */}
                            <div className="d-xl-none">
                              <div className="d-flex justify-content-between ">
                                <div>
                                  <p className="payment-info mb-0 fw-light">
                                    Appoinment Id
                                  </p>
                                  <p className="payment-mobile-info">
                                    {payment?.AppointmentId}
                                  </p>
                                </div>
                                <div>
                                  <p className="payment-info mb-0 fw-light">
                                    Payment Date
                                  </p>
                                  <p className="payment-mobile-info">
                                    {payment?.date?.split("T")[0]}
                                  </p>
                                </div>
                                <div className="col-sm-2 d-flex justify-content-center align-items-start">
                                  <p
                                    className={`mb-0 rounded-4 payments-label text-white  ${
                                      payment.payment_status === "succeeded"
                                        ? " payment-success-bg"
                                        : payment.payment_status === "pending"
                                        ? "bg-warning"
                                        : "bg-danger"
                                    }`}
                                  >
                                    {payment.payment_status === "succeeded"
                                      ? "success"
                                      : payment.payment_status}
                                  </p>
                                </div>
                              </div>
                              <div className="d-flex justify-content-between align-items-center">
                                <div className="col-sm-auto">
                                  <p className="payment-info mb-0 fw-light">
                                    Expert Name
                                  </p>
                                  <p className="payment-mobile-info text-capitalize mb-1">
                                    {highlightText(payment.doctor, searchQuery)}
                                  </p>
                                </div>
                                <div className="col-sm-auto">
                                  <p className="payment-info mb-0 fw-light">
                                    Amount Paid
                                  </p>
                                  <p className="payment-mobile-info mb-1">
                                    {payment.amount} $
                                  </p>
                                </div>
                              </div>

                              <div className="d-flex justify-content-between">
                                <div className="col-6">
                                  <button
                                    type="button"
                                    className="black-button w-100"
                                    data-bs-toggle="modal"
                                    data-bs-target="#exampleModal"
                                    onClick={() => handleShow(payment)}
                                  >
                                    View invoice
                                  </button>
                                </div>
                                <div className="col-6">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      const invoiceLink =
                                        payment?.invoice?.invoice_pdf;
                                      if (invoiceLink) {
                                        window.location.href = invoiceLink;
                                      } else {
                                        toast.error(
                                          "Invoice link not available"
                                        );
                                      }
                                    }}
                                    className=" purple-button w-100"
                                  >
                                    Download
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  <div className="">
                    {loading && (
                      <div>
                        <Placeholder as="p" animation="glow">
                          <Placeholder
                            xs={12}
                            size={"lg"}
                            style={{ height: "43px", borderRadius: "4px" }}
                          />
                        </Placeholder>
                      </div>
                    )}
                  </div>
                </>
              ) : error ? (
                <NoDataFound />
              ) : (
                <>
                  <h3 className="text-secondary text-center my-5">
                    No payment has been made yet.
                  </h3>
                </>
              )}
            </div>
            {payments?.total_pages !== 1 && (
              <CustomPagination
                total_pages={payments?.total_pages}
                current_page={currentPage}
                setCurrent_Page={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default PatientPayment;
