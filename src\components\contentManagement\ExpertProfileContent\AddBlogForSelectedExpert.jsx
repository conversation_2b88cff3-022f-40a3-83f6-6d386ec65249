import React, { useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";
import { toast } from "react-toastify";
import ModalImage from "react-modal-image";
import dynamic from "next/dynamic";
import "react-quill/dist/quill.snow.css";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { capitalizeFullName } from "../../../utils/helperfunction";
import { useSession } from "next-auth/react";

// Load React Quill dynamically
const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
const initialFormData = {
  title: "",
  articleBody: "",
  category: "",
  description: "",
  blogBannerImage: null,
  blogFeatureImage: null,
  blogImages: [],
};
const AddBlogForSelectedExpert = ({
  setAddBlogModal,
  addBlogModal,
  selectedDoctor,
  setSelectedDoctor,
}) => {
  const [formData, setFormData] = useState();
  const [articleLoading, setArticleLoading] = useState(false);
  const [bannerPreview, setBannerPreview] = useState(null);
  const [featurePreview, setFeaturePreview] = useState(null);
  const [multiplePreviews, setMultiplePreviews] = useState([]);
  const [categories, setCategories] = useState([]);
  const axiosAuth = useAxiosAuth();
  const expert_id = selectedDoctor && selectedDoctor?.id;
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  // Handle Quill editor changes
  const handleQuillChange = (value) => {
    setFormData((prevData) => ({ ...prevData, articleBody: value }));
  };

  // Handle file input changes
  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files[0];
    setFormData((prevData) => ({ ...prevData, [name]: file }));

    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (name === "blogBannerImage") {
          setBannerPreview(reader.result);
        } else if (name === "blogFeatureImage") {
          setFeaturePreview(reader.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle multiple file inputs
  const handleMultipleFileChange = (e) => {
    const { files } = e.target;
    setFormData((prevData) => ({ ...prevData, blogImages: files }));

    const previews = [];
    Array.from(files).forEach((file) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        previews.push(reader.result);
        setMultiplePreviews([...previews]);
      };
      reader.readAsDataURL(file);
    });
  };
  const handleCancelBlog = () => {
    setSelectedDoctor(null);
    setFormData(initialFormData);
    setAddBlogModal(false);
  };
  // Form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setArticleLoading(true);

    if (!formData?.blogImages || formData?.blogImages.length === 0) {
      toast.error("Images are required");
      setArticleLoading(false);
      return;
    }
    if (!formData?.title || !formData?.articleBody) {
      toast.error("All fields are required");
      setArticleLoading(false);
      return;
    }
    if (!formData?.category || formData?.category === "") {
      toast.error("All fields are required");
      setArticleLoading(false);
      return;
    }

    if (
      !formData?.blogBannerImage ||
      formData?.blogBannerImage === "" ||
      !formData?.blogImages ||
      formData?.blogImages === "" ||
      !formData?.blogFeatureImage ||
      formData?.blogFeatureImage === ""
    ) {
      toast.error("All fields are required");
      setArticleLoading(false);
      return;
    }

    const wordCount = formData?.description?.trim()?.split(/\s+/)?.length;

    if (wordCount < 50 || wordCount > 100) {
      toast.error("Description should be between 50 and 100 words", {
        position: "top-center",
        autoClose: 5000,
      });
      setArticleLoading(false);
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append("BlogTitle", formData?.title);
    formDataToSend.append("BlogBody", formData?.articleBody);
    formDataToSend.append("BlogCategoryVal", formData?.category);
    formDataToSend.append("BlogSummary", formData?.description);
    formDataToSend.append("BlogBannerImage", formData?.blogBannerImage);
    formDataToSend.append("BlogFeatureImage", formData?.blogFeatureImage);
    if (formData?.blogImages.length > 0) {
      formDataToSend.append(`BlogImages[0]`, formData?.blogImages[0]);
    }

    const apiEndpoint = `${process.env.NEXT_PUBLIC_GET_POST_BLOG_FOR_SELECTED_EXPERT}${expert_id}/add/?user_id=${admin_id}`;

    try {
      const response = await axiosAuth.post(apiEndpoint, formDataToSend);

      if (response.statusText === "Created" || response.status == 201) {
        toast.success("Blog added successfully!");
        setArticleLoading(false);
        setAddBlogModal(false);
        handleCancelBlog();
      } else {
        console.error("Failed to upload blog");
        toast.error("Failed to upload blog!");
        setAddBlogModal(false);
      }
    } catch (error) {
      console.error("An error occurred:", error);
      setArticleLoading(false);
      setAddBlogModal(false);
    }
  };

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_POST_BLOG_CATEGORY}`
      );
      if (response.status === 200) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error("Error fetching categories from the API: ", error);
    }
  }, [axiosAuth]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return (
    <>
      <Modal
        show={addBlogModal}
        onHide={() => setAddBlogModal(false)}
        backdrop="static"
        keyboard={false}
        centered
        scrollable
        size="xl"
      >
        <Modal.Header>
          <Modal.Title>Add Blog</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-3">
            <div className="row form-label custom-label">
              <div className="col-sm-auto">
                <span>Upload Podcast for Selected Expert</span>
              </div>
            </div>
            <div className="cancellation-desclaimer-section">
              <strong>Disclaimer:</strong> Podcasts can be uploaded for
              individual experts.
            </div>
          </div>
          {selectedDoctor && (
            <div>
              <p>
                You have selected{" "}
                <strong>{capitalizeFullName(selectedDoctor?.role)}</strong>
                -&nbsp;
                <strong>{capitalizeFullName(selectedDoctor?.name)}</strong> with
                an ID {selectedDoctor?.id}
              </p>
            </div>
          )}
          <Form onSubmit={handleSubmit}>
            <Form.Group controlId="formBlogTitle" className="mb-3">
              <Form.Label className="fw-bold">Blog Title</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={formData?.title}
                onChange={handleInputChange}
                required
              />
            </Form.Group>
            <Form.Group controlId="formBlogDescription" className="mb-3">
              <Form.Label className="fw-bold">Blog Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={formData?.description}
                onChange={handleInputChange}
              />
              <p className="fs-6 text-danger text-start">
                *Add a description with a minimum of 50 words and a maximum of
                100 words.
              </p>
            </Form.Group>
            <Form.Group controlId="formBlogCategory" className="mb-3 ">
              <Form.Label className="fw-bold">Blog Category</Form.Label>
              <Form.Select
                name="category"
                value={formData?.category}
                onChange={handleInputChange}
              >
                <option value="">Select Category</option>
                {categories &&
                  Array.isArray(categories) &&
                  categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.Category}
                    </option>
                  ))}
              </Form.Select>
            </Form.Group>

            <Form.Group controlId="formBlogBody" className="mb-3">
              <Form.Label className="fw-bold">Blog Summary</Form.Label>
              {typeof window !== "undefined" && (
                <ReactQuill
                  modules={{
                    toolbar: {
                      container: [
                        [{ header: [1, 2, 3, 4, 5, 6, false] }],
                        [{ size: [] }],
                        ["bold", "italic", "underline", "strike", "blockquote"],
                        [
                          { list: "ordered" },
                          { list: "bullet" },
                          { indent: "-1" },
                          { indent: "+1" },
                        ],
                        ["link"],
                        ["clean"],
                      ],
                    },
                  }}
                  formats={[
                    "header",
                    "font",
                    "size",
                    "bold",
                    "italic",
                    "underline",
                    "strike",
                    "blockquote",
                    "list",
                    "bullet",
                    "indent",
                    "link",
                  ]}
                  theme="snow"
                  onChange={handleQuillChange}
                  value={formData?.articleBody}
                  style={{ height: "400px" }}
                  placeholder="Enter your Blog Summary"
                />
              )}
            </Form.Group>

            <Form.Group controlId="formBlogBannerImage" className="mb-3 mt-5">
              <Form.Label className="fw-bold">Blog Banner Image</Form.Label>
              <Form.Control
                type="file"
                name="blogBannerImage"
                onChange={handleFileChange}
                required
              />
              {bannerPreview && (
                <ModalImage
                  small={bannerPreview}
                  large={bannerPreview}
                  showRotate={true}
                  alt={"banner image"}
                  className={"custom-small-image"}
                />
              )}
            </Form.Group>

            {/* <Form.Group controlId="formBlogFeatureImage" className="mb-3 ">
              <Form.Label className="fw-bold">Blog Feature Image</Form.Label>
              <Form.Control
                type="file"
                name="blogFeatureImage"
                onChange={handleFileChange}
                required
              />
              {featurePreview && (
                <ModalImage
                  small={featurePreview}
                  large={featurePreview}
                  showRotate={true}
                  alt={"featurePreview image"}
                  className={"custom-small-image"}
                />
              )}
            </Form.Group> */}

            {/* <Form.Group controlId="formBlogImages" className="mb-3">
              <Form.Label className="fw-bold">Blog Main Images</Form.Label>
              <Form.Control
                type="file"
                multiple
                name="blogImages"
                onChange={handleMultipleFileChange}
                required
              />
              {multiplePreviews.length > 0 && (
                <div style={{ marginTop: "10px" }}>
                  {multiplePreviews.map((preview, index) => (
                    <ModalImage
                      key={index}
                      small={preview}
                      large={preview}
                      showRotate={true}
                      alt={"preview image"}
                      className={"custom-small-image"}
                    />
                  ))}
                </div>
              )}
            </Form.Group> */}

            <Modal.Footer>
              <Button variant="secondary" onClick={handleCancelBlog}>
                Cancel
              </Button>
              <Button variant="primary" type="submit" disabled={articleLoading}>
                {articleLoading ? "Saving..." : "Save"}
              </Button>
            </Modal.Footer>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default AddBlogForSelectedExpert;
