"use client";

import React, { useState } from "react";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Cookies from "js-cookie";

const CancellationReason = ({ appointment, fetchAllAppointments }) => {
  const [approveLoading, setApproveLoading] = useState(false); // Loading state for approve button
  const [rejectLoading, setRejectLoading] = useState(false);
  const { data: session } = useSession();
  const userId = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const handleCancelAppointment = async (isApproved) => {
    const shouldEdit = await Swal.fire({
      title:
        isApproved === "C"
          ? "Do You Want to Approve the Cancellation ?"
          : "Do You Want to Reject the Cancellation ?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancel",
      confirmButtonColor: "#9426B2",
      cancelButtonColor: "#B50000",
      backdrop: "rgba(0,0,0,0.4)",
    });
    if (!shouldEdit.isConfirmed) {
      return;
    }

    const bodyData = { status: isApproved };
    try {
      if (isApproved) {
        setApproveLoading(true); // Set loading state for approve button
      } else {
        setRejectLoading(true); // Set loading state for reject button
      }
      if (isApproved) {
        const response = await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_CANCEL_APPOINTMENT}${appointment?.id}/?user_id=${userId}`,
          bodyData
        );

        if (response?.data?.message?.status === "C") {
          toast.success("Cancellation approved Successfully!");
        } else if (response?.data?.message?.status === "C_R") {
          toast.success("Cancellation rejected Successfully!");
        }
      }
      await fetchAllAppointments(1);
    } catch (error) {
      toast.error("Error in Approving/Rejecting the Cancellation");
      // Handle the error (e.g., show an error message to the user)
    } finally {
      setApproveLoading(false); // Reset loading state for approve button
      setRejectLoading(false);
    }
  };
  return (
    <>
      {appointment?.status === "C_P" || appointment?.status === "C" ? (
        <div className="col-sm-5">
          <div className="review-appointment">
            <div className="row">
              <div className="">
                <span className="timeline-1">Cancellation Reason</span>
              </div>
              <div className="col-sm-12">
                <div className="review-lines">
                  <p className="article-lines mx-2 mb-0 mt-2">
                    {appointment?.appointment_cancel_reason ||
                      "No reason available."}
                  </p>
                </div>
              </div>
              <div className="col-sm-12 pt-3">
                {appointment?.status === "C_P" ? (
                  <div className=" d-flex">
                    <button
                      onClick={() => handleCancelAppointment("C")}
                      type="button"
                      className="btn approve-cancel me-4"
                    >
                      {approveLoading ? "loading..." : "Approve"}
                    </button>
                    <button
                      onClick={() => handleCancelAppointment("C_R")}
                      type="button"
                      className="btn hide-Review"
                    >
                      {rejectLoading ? "loading..." : "Reject"}
                    </button>
                  </div>
                ) : appointment?.status === "C" &&
                  appointment?.appointment_cancel_reason ? (
                  <>
                    <button
                      type="button"
                      disabled
                      className="btn approve-cancel w-100 me-4"
                    >
                      Approved
                    </button>
                  </>
                ) : null}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="col-sm-5">
          <div className="review-appointment-shadow">
            <div className="row">
              <div className=""></div>
              <div className="col-sm-12">
                <div className="h-100">
                  <p className="text-center text-secondary">
                    No appointment cancellations have been made.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CancellationReason;
