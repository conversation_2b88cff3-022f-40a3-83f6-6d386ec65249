.search-loader {
  height: 35px;
  width: 35px;
  background: transparent;
  border: #8107d1 3px solid;
  border-top: transparent;
  border-radius: 50%;
  animation: animate81323 0.8s linear infinite;
}

@keyframes animate81323 {
  0% {
    border: 3px solid #8107d1;
    border-top: transparent;
    border-left: transparent;
    transform: rotate(0deg);
  }

  50% {
    border: 3px dashed rgb(240, 41, 240);
    border-top: transparent;
    border-left: transparent;
    transform: rotate(270deg);
  }

  100% {
    border: 3px dotted #ff2e2e;
    border-top: transparent;
    border-left: transparent;
    transform: rotate(360deg);
  }
}
