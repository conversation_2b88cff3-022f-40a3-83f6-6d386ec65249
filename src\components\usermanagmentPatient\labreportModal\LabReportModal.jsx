"use client";

import React, { useEffect, useRef, useState } from "react";
import { Modal } from "react-bootstrap";
import "./labreport.css";
import logo from "../../../../public/images/footer_image.png";
import Image from "next/image";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { calculateAge } from "../../../utils/helperfunction";
import "../../expert-doc/pdfViewer/PdfViewer.css";
import dynamic from "next/dynamic";

const LabPdfViewer = dynamic(() => import("./LabPdfViewer"), {
  ssr: false, // This ensures it's only loaded on the client-side
});

const LabReportModal = ({
  id,
  selectedRecord,
  userEmail,
  show,
  onHide,
  handleDownloadPres,
}) => {
  const [user, setUser] = useState();
  const contentRef = useRef(null);
  const axiosAuth = useAxiosAuth();

  useEffect(() => {
    const getPatientDetails = async () => {
      try {
        const resp = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_PATIENT_DATA}${userEmail}/`
        );
        const doctorData = resp?.data?.user_data;

        setUser(doctorData);
      } catch (err) {
        console.error("unable to fetch user", err);
      }
    };
    getPatientDetails();
  }, [userEmail, selectedRecord, id, axiosAuth]);

  return (
    <Modal show={show} onHide={onHide} centered size="lg">
      <Modal.Header closeButton></Modal.Header>
      <div
        className="modal-dialog mt-0 mb-0"
        id={id}
        tabIndex="-1"
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
        ref={contentRef}
      >
        {/* <div className="modal-dialog modal-dialog-centered custom-modal" dialogClassName="custom-modal"> */}
        <div className="modal-content">
          <div className="modal-header d-block">
            <h1 className="modal-title fs-5" id="exampleModalLabel">
              <div className=" d-flex align-items-center">
                <div className=" me-auto">
                  <Image className="headerImage1" src={logo} alt="header" />
                </div>
                <div className="">
                  <p className="date lab-date mb-1">
                    Date{" "}
                    <span>
                      {selectedRecord?.generation_date?.split("T")[0]}
                    </span>
                  </p>

                  <p className="date black-content-2 lab-date mb-0">
                    Report Id :
                    <span className="me-2">{selectedRecord?.id}</span>
                  </p>
                </div>
              </div>

              <div className="">
                <div className="">
                  <button
                    type="button"
                    className=" float-end me-xl-3 download-lab-btn"
                    onClick={() => handleDownloadPres(selectedRecord?.id)}
                  >
                    Download Report
                  </button>
                </div>
              </div>
            </h1>
          </div>

          <div className="modal-body">
            <form className=" ">
              <div className="row g-3">
                <div className="col-3 col-md-2 mt-1">
                  <label
                    htmlFor="inputId"
                    className="form-label purple-content"
                  >
                    Id
                  </label>

                  <input
                    type="text"
                    className="form-control custom-form-control"
                    id="inputId"
                    defaultValue={selectedRecord?.id}
                    readOnly
                  />
                </div>

                <div className="col-9 col-md-3 mt-1">
                  <label
                    htmlFor="inputName"
                    className="form-label purple-content"
                  >
                    Name
                  </label>

                  <input
                    type="name"
                    className="form-control custom-form-control"
                    id="inputName"
                    defaultValue={user?.name}
                    readOnly
                  />
                </div>

                <div className="col-md-3 mt-1">
                  <label
                    htmlFor="inputEmail"
                    className="form-label purple-content"
                  >
                    Email
                  </label>

                  <input
                    type="mail"
                    className="form-control custom-form-control"
                    id="inputEmail"
                    defaultValue={user?.email}
                  />
                </div>

                <div className="col-6 col-md-2 mt-1">
                  <label
                    htmlFor="inputEmail"
                    className="form-label purple-content"
                  >
                    Sex
                  </label>

                  <input
                    type="mail"
                    className="form-control custom-form-control"
                    id="inputEmail"
                    defaultValue={user?.sex}
                  />
                </div>

                <div className="col-6 col-md-2 mt-1">
                  <label
                    htmlFor="inputEmail"
                    className="form-label purple-content"
                  >
                    Age
                  </label>

                  <input
                    type="mail"
                    className="form-control custom-form-control"
                    id="inputEmail"
                    readOnly
                    value={calculateAge(user?.date_of_birth)}
                  />
                </div>
              </div>
              <div className="row mt-1 g-3">
                <div className="col-md-4 mt-1">
                  <label
                    htmlFor="inputId"
                    className="form-label purple-content"
                  >
                    Report Name
                  </label>

                  <input
                    type="text"
                    className="form-control custom-form-control"
                    id="inputId"
                    defaultValue={selectedRecord?.reportname}
                    readOnly
                  />
                </div>
                <div className="col-md-4 mt-1">
                  <label
                    htmlFor="inputId"
                    className="form-label purple-content"
                  >
                    Report Type
                  </label>

                  <input
                    type="text"
                    className="form-control custom-form-control"
                    id="inputId"
                    defaultValue={selectedRecord?.reporttype}
                    readOnly
                  />
                </div>
                <div className="col-md-4 mt-1">
                  <label
                    htmlFor="inputId"
                    className="form-label purple-content"
                  >
                    Generation Date
                  </label>

                  <input
                    type="text"
                    className="form-control custom-form-control"
                    id="inputId"
                    defaultValue={
                      selectedRecord?.generation_date?.split("T")[0]
                    }
                    readOnly
                  />
                </div>
              </div>
              <div className="row mt-1">
                <div className="col-md-12">
                  <label
                    htmlFor="inputId"
                    className="form-label purple-content"
                  >
                    Report Summary
                  </label>

                  <input
                    type="text"
                    className="form-control custom-form-control"
                    id="inputId"
                    defaultValue={selectedRecord?.reportsummary}
                    readOnly
                  />
                </div>
              </div>

              <LabPdfViewer selectedRecord={selectedRecord} />
            </form>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default LabReportModal;
