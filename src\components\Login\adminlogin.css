/* -----------fileds CU logo in left---------------- */
body {
  overflow: hidden;
  overflow-x: hidden;
  overflow-y: hidden;
}
.admin-logo {
  width: 131px;
  height: 94px;
  object-fit: contain;
  position: absolute;
}

/* --------------------------Right template style ----------------------------------------- */
.admin-bg {
  height: 100vh;
  width: 100%;
  object-fit: fill;
}
.adminbglogo {
  object-fit: contain;
  height: 90px;
}

.superadmin-text {
  font-size: 50px;
  font-weight: 600;
  color: #ffff;
}

.admin-logo-text {
  position: absolute;
  top: 22%;
  left: 68%;
  transform: translate(-21%, -74%);
}

img.super-bg {
  width: 499px;
  height: 490px;
  top: 30%;
  left: 60%;
  object-fit: contain;
  position: absolute;
}

/* --------------------------Form fileds style----------------------------------------- */
.login-form {
  padding: 25px;
  width: 487px;
  height: 358px;
  top: 35%;
  left: 12%;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 7px 3px 22px #00000038;
  border-radius: 5px;
  position: absolute;
}

.login-heading {
  color: #8107d1;
  font-size: 30px;
  font-weight: 500;
  position: relative;
  top: 30%;
  left: 25%;
}

.admin-login-label {
  color: #707070;
  font-size: 16px;
  font-weight: normal;
}

button.btn.btn-admin-login {
  font-size: 15px;
  border: none;
  font-weight: 500;
  color: #fff;
  height: 56px;
  width: 100%;
  border-radius: 0;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
}

button.btn.btn-admin-login:focus {
  font-size: 15px;
  border: none;
  font-weight: 500;
  color: #fff;
  height: 56px;
  width: 100%;
  border-radius: 0;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
}

.admin-continue {
  text-decoration: none;
  font-size: 15px;
  padding-top: 15px;
  text-align: center;
  border: none;
  font-weight: 500;
  color: #fff;
  height: 56px;
  width: 100%;
  border-radius: 0;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
}

.admin-submit {
  text-decoration: none;
  font-size: 15px;
  text-align: center;
  border: none;
  font-weight: 500;
  color: #fff;
  height: 56px;
  width: 100%;
  border-radius: 50px;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  display: flex;
  justify-content: center;
  align-items: center;
}
input.form-control.form-login-fields {
  height: 42px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: no;
  border-radius: 0;
  position: relative;
}

.btn-forgot {
  text-decoration: none;
  color: #8107d1;
  border: none;
  background-color: #fff;
  float: right;
}

.btn-forgot:focus {
  color: #8107d1;
  border: none;
  text-decoration: none;
  background-color: #fff;
  float: right;
}

.btn-forgot:hover {
  color: #8107d1;
  border: none;
  text-decoration: none;
  background-color: #fff;
  float: right;
}

.register-email {
  color: #707070;
  font-size: 22px;
  font-weight: normal;
}

button.btn.btn-admin-submit {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  color: #fff;
  border-radius: 0;
  height: 43px;
  width: 100%;
  border: none;
  margin-top: 35px;
}

button.btn.btn-admin-submit:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  color: #fff;
  border-radius: 0;
  height: 43px;
  width: 100%;
  border: none;
}

/* Select the input and style its placeholder text */
input::placeholder {
  color: #b2b0b0;
  font-size: 14px;
  font-weight: normal;
}

/*------------------------------------ Login form ellipse top and down------------------------------------------------------------- */
.ellipse-down {
  position: absolute;
  object-fit: contain;
  margin-top: 580px;
  margin-left: -60px;
  width: 340px;
  height: 341px;
}

.ellipse-top {
  position: absolute;
  object-fit: contain;
  width: 400px;
  height: 174px;
  margin-left: 270px;
}

.form-group {
  position: relative;
}

.visible-icon {
  position: absolute;
  top: 50%;
  right: 0%;
}

.show-hide-password-section {
  position: absolute;
  right: 10px; /* Adjust as needed */
  top: 50%;
  transform: translateY(-50%);
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.show-hide-password {
  border: none;
  background-color: transparent !important;
}
