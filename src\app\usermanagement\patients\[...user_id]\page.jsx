'use client'
import React, { Suspense } from "react";
import "../../../../components/usermanagmentPatient/usermanagementpatient.css";
import "../../../../components/AllHead/allhead.css";
import Navbar from "../../../../components/navbar/Navbar";
import ToggleUserPatient from "../../../../components/usermanagmentPatient/ToggleUserPatient";
import withAuth from "../../../../withAuth/withAuth";

const PatientDetailsPage = ({ params = { user_id: [null, null] } }) => {
  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <Suspense>
              <ToggleUserPatient params={params} />
            </Suspense>
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(PatientDetailsPage);
