import React from "react";
import { BsFillInfoCircleFill } from "react-icons/bs";
import "react-tooltip/dist/react-tooltip.css";

const PatientTableHead = () => {
  return (
    <thead className="custom-border">
      <tr className="custom-name">
        <th
          scope="col"
          className="fw-semibold text-center"
          style={{ fontSize: "14px" }}
        >
          Patient Id
        </th>
        <th scope="col" className="fw-semibold " style={{ fontSize: "14px" }}>
          Patient Name
        </th>
        {/* <th scope="col" className="fw-semibold text-center" style={{fontSize: "14px"}}>
          Role
        </th> */}
        <th
          scope="col"
          className="fw-semibold text-center"
          style={{ fontSize: "14px" }}
        >
          Date of Joining
        </th>
        {/* <th
          scope="col"
          className="fw-semibold text-center tooltip"
          style={{ opacity: 1, fontSize: "14px" }}
        >
          <span style={{ fontSize: "14px", zIndex: -1 }}>
            Expert Name <BsFillInfoCircleFill color="black" />{" "}
          </span>
          <span className="tooltiptext">
            Name of the expert from the patients last appointment
          </span>
        </th> */}
        <th
          scope="col"
          className="fw-semibold text-center"
          style={{ fontSize: "14px" }}
        >
          Status
        </th>
      </tr>
    </thead>
  );
};

export default PatientTableHead;
