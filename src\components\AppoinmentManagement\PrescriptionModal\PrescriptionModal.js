import "./prescriptionModal.css";
import Image from "next/image";
import img1 from "../../../../public/assets/cancer_unwired_logo.png";

import Modal from "react-bootstrap/Modal";
import { But<PERSON> } from "react-bootstrap";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { useState } from "react";
import Cookies from "js-cookie";
import defaultImageIcon from "../../../../public/assets/default-image-icon.jpeg";
import { convertDateFormat } from "../../../utils/helperfunction";

// import html2pdf from "html2pdf.js";

const PrescriptionModal = ({ show, onClose, data }) => {
  const [showInnerModal, setShowInnerModal] = useState(false);
  const handleImageError = (event) => {
    event.target.src = defaultImageIcon;
  };
  return (
    <Modal show={show} onHide={onClose} dialogClassName="custom-modal-width">
      <Modal.Header className="custom-modal-header">
        {/* <Button variant="primary" onClick={handleDownload}>
          Download
        </Button> */}
        <div className="row">
          <div className="col-sm-9">
            <p className="purple-content mb-1 fw-semibold">
              {data?.doctor_details?.name &&
                data.doctor_details.name.charAt(0).toUpperCase() +
                  data.doctor_details.name.slice(1)}
            </p>

            <p className="purple-content fs-6 mb-0">
              Dept. of {data?.doctor_details?.doctor_other_details?.Dept}
            </p>
          </div>

          <div className="col-sm-3">
            <Image className="headerImage1" src={img1} alt="header" />
          </div>
        </div>

        <div className="row">
          <div className="col-sm-9">
            <p className="black-content mb-1">MD license: 12-136547</p>

            <p className="black-content mb-1">
              Doctor ID: {data?.doctor_details?.id}
            </p>

            <p className="black-content fw-medium mb-0">
              {convertDateFormat(data?.prescription_data?.DateOfCreatingPresc)}
            </p>
          </div>

          <div className="col-sm-3">
            <p className="purple-content mb-1 fw-semibold">Cancer Hospital</p>

            <p className="black-content mb-0">
              <a className="link-opacity-100" href="#">
                www.cancerhospital.com
              </a>
            </p>
          </div>
        </div>
      </Modal.Header>
      <Modal.Body>
        <form className="row g-3">
          <div className="col-md-2 mt-0">
            <label
              htmlFor="inputId"
              className="form-label purple-content-2 fw-medium mb-1"
            >
              Id
            </label>
            <input
              type="text"
              className="form-control custom-form-control"
              id="inputId"
              value={data?.patient_details?.id}
              readOnly
            />
          </div>
          <div className="col-md-3 mt-0">
            <label
              htmlFor="inputName"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Name
            </label>
            <input
              type="name"
              className="form-control custom-form-control"
              id="inputName"
              value={data?.patient_details?.name}
              readOnly
            />
          </div>
          <div className="col-md-3 mt-0">
            <label
              htmlFor="inputEmail"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Consultation Id
            </label>
            <input
              type="mail"
              className="form-control custom-form-control"
              id="inputEmail"
              value={data?.prescription_data?.AppointmentId}
              readOnly
            />
          </div>
          <div className="col-md-2 mt-0">
            <label
              htmlFor="inputEmail"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Sex
            </label>
            <input
              type="mail"
              className="form-control custom-form-control"
              id="inputEmail"
              value={data?.patient_details?.sex}
              readOnly
            />
          </div>
          <div className="col-md-2 mt-0">
            <label
              htmlFor="inputEmail"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Age
            </label>
            <input
              type="mail"
              className="form-control custom-form-control"
              id="inputEmail"
              value={data?.patient_details?.age}
              readOnly
            />
          </div>

          <div className="input-group mb-0">
            <span className="input-group-text purple-content" id="basic-addon1">
              Current Diagnosis
            </span>
            <input
              type="text"
              className="form-control custom-form-control"
              aria-label="Username"
              aria-describedby="basic-addon1"
              value={data?.prescription_data?.CurrentDiagnosis}
              readOnly
            />
          </div>

          <div className="border p-2">
            <div className="border p-3">
              {/* TABLE 1 */}
              <h2 className="medication-heading">Oral Medication</h2>
              <table className="table">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      className="purple-content mb-0 table-header-cell"
                    >
                      Sno.
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Oral Medication
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Dose Strength
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Frequency
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Duration (Days)
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Remarks
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {/* <tr>
                    <th scope="row" className="fw-normal text-center">
                      1
                    </th>
                    <td>
                      {data?.prescription_data?.oral_medication?.MedicineName}
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.oral_medication?.DoseStrength}
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.oral_medication?.Frequency}
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.oral_medication?.Duration}
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.oral_medication?.Remarks}
                    </td>
                  </tr> */}
                  {data?.prescription_data?.oral_medication.map(
                    (medication, index) => (
                      <tr key={index}>
                        <th scope="row" className="fw-normal text-center">
                          {index + 1}
                        </th>
                        <td>{medication.MedicineName}</td>
                        <td className="text-center">
                          {medication.DoseStrength}
                        </td>
                        <td className="text-center">{medication.Frequency}</td>
                        <td className="text-center">{medication.Duration}</td>
                        <td className="text-center">{medication.Remarks}</td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>

            <div className="border p-3 mt-3">
              {/* TABLE 2*/}
              <h2 className="medication-heading">IV/IM Medication</h2>
              <table className="table">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      className="purple-content mb-0 table-header-cell"
                    >
                      Sno.
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      IV Medication
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Dose Strength
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Mode of Administration
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Frequency
                    </th>
                    <th scope="col" className="purple-content mb-0 text-center">
                      Remarks
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {/* <tr>
                    <th scope="row" className="fw-normal text-center">
                      1
                    </th>
                    <td>
                      {data?.prescription_data?.iv_medication?.MedicineName}
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.iv_medication?.DoseStrength}
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.iv_medication?.Frequency}
                    </td>
                    <td className="text-center">
                      {
                        data?.prescription_data?.iv_medication
                          ?.ModeOfAdministration
                      }
                    </td>
                    <td className="text-center">
                      {data?.prescription_data?.iv_medication?.Remarks}
                    </td>
                  </tr> */}
                  {data?.prescription_data?.iv_medication.map(
                    (medication, index) => (
                      <tr key={index}>
                        <th scope="row" className="fw-normal text-center">
                          {index + 1}
                        </th>
                        <td>{medication.MedicineName}</td>
                        <td className="text-center">
                          {medication.DoseStrength}
                        </td>
                        <td className="text-center">{medication.Frequency}</td>
                        <td className="text-center">
                          {medication.ModeOfAdministration}
                        </td>
                        <td className="text-center">{medication.Remarks}</td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>

            <div className="row exist-treatment-container mt-3">
              <div className="col-sm-auto">
                <p className="purple-content">Existing Treatment</p>
              </div>
              <div className="col-sm d-flex justify-content-end">
                <button
                  type="button"
                  className="btn prescription-btn"
                  onClick={() => setShowInnerModal(true)}
                >
                  Detailed View
                </button>
              </div>
            </div>

            <div className="form-floating mt-3">
              <textarea
                className="form-control"
                placeholder="Leave a comment here"
                id="floatingTextarea"
                style={{ height: "100px" }}
                value={data?.prescription_data?.Recommendation}
              />
              <label htmlFor="floatingTextarea" className="purple-content">
                Recommendation
              </label>
            </div>
          </div>
          <div className="form-floating">
            <textarea
              className="form-control"
              placeholder="Leave a comment here"
              id="floatingTextarea"
              style={{ height: "100px" }}
              value={data?.prescription_data?.SpecialInstructions}
            />
            <label htmlFor="floatingTextarea" className="purple-content">
              Special Instructions
            </label>
          </div>

          <div className="input-group mb-0 mt-2">
            <span className="input-group-text purple-content" id="basic-addon1">
              Follow-up Appointment
            </span>
            <input
              type="text"
              className="form-control custom-form-control"
              aria-label="Username"
              aria-describedby="basic-addon1"
              value={data?.prescription_data?.FollowUp}
              readOnly
            />
          </div>

          <div className="col-sm-3 offset-sm-9 border">
            <p className="purple-signature">Digital Signature</p>
            {data?.prescription_data?.DoctorSignature !== "undefined" &&
            data?.prescription_data?.DoctorSignature !== undefined &&
            data?.prescription_data?.DoctorSignature !== "null" &&
            data?.prescription_data?.DoctorSignature !== null ? (
              <Image
                src={data?.prescription_data?.DoctorSignature}
                alt=""
                className="ms-5 mb-2"
                width={100}
                height={70}
                onError={handleImageError}
              />
            ) : (
              <Image
                src={defaultImageIcon}
                alt=""
                className="ms-5 mb-2"
                width={100}
                height={70}
                onError={handleImageError}
              />
            )}
          </div>
          {/* <div className="col-sm-1 mx-auto">
            <Button className="prescription-btn" onClick={handleDownload}>
              Download
            </Button>
          </div> */}
        </form>
      </Modal.Body>

      {/* Inner Modal */}
      <Modal
        show={showInnerModal}
        onHide={() => setShowInnerModal(false)}
        dialogClassName="modal-dialog modal-lg modal-dialog-scrollable "
      >
        <Modal.Header>
          <h1 className="modal-title fs-5 purple-content">
            Existing Treatment
          </h1>
          <button
            type="button"
            className="btn-close"
            onClick={() => setShowInnerModal(false)}
          />
        </Modal.Header>
        <Modal.Body>
          <textarea
            id="editable-paragraph"
            className="form-control"
            rows="100"
            placeholder="Existing Treatment"
            value={data?.prescription_data?.ExistingTreatment}
          ></textarea>
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => setShowInnerModal(false)}
          >
            close
          </button>
        </Modal.Footer>
      </Modal>
    </Modal>
  );
};

export default PrescriptionModal;
