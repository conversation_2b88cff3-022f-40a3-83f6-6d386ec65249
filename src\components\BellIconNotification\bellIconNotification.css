.notification-popup-cu {
  position: absolute;
  top: 07%; /* Adjust as needed */
  right: 1%; /* Adjust as needed */
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1000; /* Ensure it appears above other elements */
  width: 400px; /* Adjust as needed */
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px 0px 5px 5px;
  opacity: 0; /* Initially hidden */
  animation: slideIn 0.3s forwards ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px); /* Adjust as needed */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.notification-popup-cu.closing {
  animation: slideOut 0.3s forwards ease-in-out;
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px); /* Adjust as needed */
  }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ccc;
}

.notification-list {
  padding: 10px;
  overflow-y: auto;
  max-height: 500px;
}

.notification-item {
  padding: 5px 6px;
  /* background-color: #f3fff5; */
  background-color: #faf3fd;
  margin-bottom: 6px;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  cursor: pointer;
}
.notification-item:hover {
  background-color: #ecdcf3;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

.notification-text {
  color: #8107d1;
  font-size: 12px;
  font: 500;
}

.buttons-in-notifications {
  border: 0px;
  font-size: 12px;
  background-color: transparent;
}
.dismiss-btn {
  color: #ff2e2e;
}
.view-btn {
  color: #8107d1;
  font: 700;
}
.notification-desc {
  margin-bottom: 2px !important;
  font-size: 12px;
  color: #414146;
  font-weight: 400;
}
.notification-time {
  font-size: 10px;
  font-weight: 500px;
  margin-top: 3px;
}

.dismiss-text {
  font-weight: 700;
  font-size: 12px;
  color: #ff2e2e;
}

/* Scroll bar styles */
/* Define scrollbar styles */
.notification-list::-webkit-scrollbar {
  width: 5px; /* Width of the scrollbar */
}

/* Track styles */
.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1; /* Color of the track */
}

/* Handle styles */
.notification-list::-webkit-scrollbar-thumb {
  background: #8107d1; /* Color of the handle */
  border-radius: 4px; /* Rounded corners */
}

/* Handle on hover */
.notification-list::-webkit-scrollbar-thumb:hover {
  background: #a948e9; /* Darker color on hover */
}

.notification-item.exit {
  animation: exitAnimation 2s ease-out forwards;
}

@keyframes exitAnimation {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.notification-item {
  transition: transform 0.9s ease-out, opacity 0.9s ease-out;
}

.notification-item.exit {
  opacity: 0;
  transform: translateY(-20px);
}

.hover-close-btn {
  visibility: hidden;
}

.notification-item:hover .hover-close-btn {
  visibility: visible;
}
