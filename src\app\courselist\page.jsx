"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { Table, Container } from "react-bootstrap";
import Image from "next/image";

const CourseList = () => {
  const [courses, setCourses] = useState([]);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const response = await axios.get(
          "https://hopeful-borg.62-138-26-233.plesk.page/api/course_list/"
        );
        setCourses(response.data);
      } catch (error) {
        console.error("Error fetching the courses", error);
      }
    };

    fetchCourses();
  }, []);

  return (
    <Container>
      <h2 className="my-4">Course List</h2>
      <Table striped bordered hover>
        <thead>
          <tr>
            <th>#</th>
            <th>Course Name</th>
            <th>Description</th>
            <th>Image</th>
            <th>Rating</th>
            <th>Actual Price</th>
            <th>Offer Price</th>
          </tr>
        </thead>
        <tbody>
          {courses.map((course, index) => (
            <tr key={course.course_id}>
              <td>{index + 1}</td>
              <td>{course.course_name}</td>
              <td>{course.course_description}</td>
              <td>
                <Image
                  src={`https://${course.course_image}`}
                  alt={course.course_name}
                  // style={{ width: '100px', height: 'auto' }}
                  width={"100px"}
                  height={"100px"}
                />
              </td>
              <td>{course.course_rating}</td>
              <td>{course.actual_price}</td>
              <td>{course.offer_price}</td>
            </tr>
          ))}
        </tbody>
      </Table>
    </Container>
  );
};

export default CourseList;
