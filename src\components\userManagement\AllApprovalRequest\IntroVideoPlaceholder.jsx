import React from "react";
import { Placeholder } from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";

const IntroVideoPlaceholder = () => {
  const placeholders = Array.from({ length: 6 }).map((_, index) => (
    <div key={index} className="col-sm-6 px-4 py-2">
      <div className="row introvideo-bg p-3">
        <div className="col-sm-6">
          <div
            style={{
              position: "relative",
              width: "100%",
              height: "100%",
              borderRadius: "15px",
              overflow: "hidden",
              borderBottomLeftRadius: "15px",
              borderBottomRightRadius: "15px",
              borderTopLeftRadius: "15px",
              borderTopRightRadius: "15px",
              backgroundColor: "#e0e0e0", // Grey background for placeholder
            }}
          ></div>
        </div>
        <div className="col-sm-6">
          <Placeholder as="p" animation="glow" className="allApproval-heading">
            <Placeholder xs={8} />
          </Placeholder>
          <div className="row">
            <div className="col-sm-auto">
              <Placeholder animation="glow" className="allApproval-image">
                <Placeholder
                  style={{ width: 50, height: 50, borderRadius: "50%" }}
                />
              </Placeholder>
            </div>
            <div className="col-sm-auto p-0">
              <Placeholder
                as="p"
                animation="glow"
                className="allApproval-expert-name mb-0"
              >
                <Placeholder xs={6} />
              </Placeholder>
              <Placeholder
                as="p"
                animation="glow"
                className="allApproval-expert-role"
              >
                <Placeholder xs={4} />
              </Placeholder>
            </div>
          </div>
          <div className="modal-footer mt-5">
            <Placeholder.Button variant="secondary" xs={2} />
            <Placeholder.Button variant="secondary" xs={2} className="ms-2" />
          </div>
        </div>
      </div>
    </div>
  ));

  return (
    <div className="row overflow-auto allApproval-tab-scroll">
      {placeholders}
    </div>
  );
};

export default IntroVideoPlaceholder;
