const capitalizeFullName = (name) => {
  if (typeof name === "undefined" || name === null) {
    return "";
  }
  if (name.includes(" ")) {
    let names = name.split(" ");
    for (let i = 0; i < names.length; i++) {
      names[i] = names[i].charAt(0).toUpperCase() + names[i].slice(1);
    }
    return names.join(" ");
  } else {
    return name.charAt(0).toUpperCase() + name.slice(1);
  }
};

const formatPhoneNumber = (number) => {
  let phoneNumber = String(number);
  if (phoneNumber.startsWith("+")) {
    phoneNumber = phoneNumber.slice(1);
  }
  let countryCode = phoneNumber.slice(0, 2);
  let remainingNumber = phoneNumber.slice(2);
  return "+" + countryCode + " " + remainingNumber;
};
const formatDate = (timestamp) => {
  let date = new Date(timestamp);
  let day = String(date.getDate()).padStart(2, "0");
  let month = String(date.getMonth() + 1).padStart(2, "0"); 
  let year = date.getFullYear();
  return day + "-" + month + "-" + year;
};
function timeDifference(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  const minute = 60;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30;
  const year = day * 365;
  const decade = year * 10;

  if (diffInSeconds < minute) {
    return `${diffInSeconds} seconds ago`;
  } else if (diffInSeconds < hour) {
    return `${Math.floor(diffInSeconds / minute)} minutes ago`;
  } else if (diffInSeconds < day) {
    return `${Math.floor(diffInSeconds / hour)} hours ago`;
  } else if (diffInSeconds < month) {
    return `${Math.floor(diffInSeconds / day)} days ago`;
  } else if (diffInSeconds < year) {
    return `${Math.floor(diffInSeconds / month)} months ago`;
  } else if (diffInSeconds < decade) {
    return `${Math.floor(diffInSeconds / year)} years ago`;
  } else {
    return `${Math.floor(diffInSeconds / decade)} decades ago`;
  }
}
export { capitalizeFullName, formatPhoneNumber, formatDate, timeDifference };
