import React, { useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import useAxiosAuth from "@/lib/hooks/useAxiosAuth";
import { Table, Form, Button, Pagination, Spinner } from "react-bootstrap";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import {
  capitalizeFullName,
  formatCustomDateAndTime,
} from "@/utils/helperfunction";

const UserSubscriptions = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [status, setStatus] = useState("");
  const [loading, setLoading] = useState(false);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [error, setError] = useState(false);
  const { data: session } = useSession();
  const userId = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  // Function to fetch subscriptions
  const fetchSubscriptions = useCallback(async () => {
    try {
      setLoading(true);
      let url = `${process.env.NEXT_PUBLIC_USER_SUBSCRIPTIONS}?user_id=${userId}&page=${current_page}`;

      // Append status only if it's not empty
      if (status) {
        url += `&status=${status}`;
      }

      const response = await axiosAuth.get(url);

      if (response.data.isSuccess) {
        setSubscriptions(response.data.items || []);
        setTotalPages(response?.data?.total_pages || 1);
      } else {
        setSubscriptions([]);
      }
    } catch (err) {
      setError(true);
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [userId, status, current_page, axiosAuth]);

  // Fetch subscriptions whenever status or page changes
  useEffect(() => {
    if (userId) {
      fetchSubscriptions();
    }
  }, [fetchSubscriptions, userId]);

  return (
    <>
      <div className="mb-4 d-flex justify-content-between align-items-center ">
        <div>
          {" "}
          <h5 className="mb-3 admin-add-blog-list-header fw-semibold">
            Users Subscription List
          </h5>
        </div>
        <Form.Group controlId="statusFilter" className="fw-bold cursor-pointer">
          <Form.Control
            as="select"
            onChange={(e) => setStatus(e.target.value)}
            value={status}
          >
            <option className="cursor-pointer" value="">
              Select Subscription Status
            </option>
            <option className="cursor-pointer" value="subscribed">
              Subscribed
            </option>
            <option className="cursor-pointer" value="unsubscribed">
              Unsubscribed
            </option>
          </Form.Control>
        </Form.Group>
      </div>

      <table className="table">
        <thead className="sticky-table-head">
          <tr className="text-center">
            <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Sl No</th>
            <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Name</th>
            <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Email ID</th>
            <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Status</th>
            <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Subscribed At</th>
          </tr>
        </thead>
        <tbody>
          {subscriptions.map((subscription, index) => (
            <tr key={index} className="">
              <td scope="row" className=" custom-font-size custom-font">
                {(current_page - 1) * 10 + index + 1}
              </td>
              <td  className=" custom-font-size custom-font">{capitalizeFullName(subscription.name)}</td>
              <td  className=" custom-font-size custom-font">{subscription.email}</td>
              <td  className=" custom-font-size custom-font">
                {subscription.is_subscribed ? "Subscribed" : "Unsubscribed"}
              </td>
              {/* <td>{new Date(subscription.subscribed_at).toLocaleString()}</td> */}
              <td  className="custom-font-size custom-font">{formatCustomDateAndTime(subscription.subscribed_at)}</td>
            </tr>
          ))}

          {subscriptions?.length == 0 && !error && (
            <tr>
              <td colSpan="6" className="text-center">
                No user have subscribed yet.
              </td>
            </tr>
          )}
          {error && subscriptions.length == 0 && (
            <tr>
              <td colSpan="6" className="text-center">
                No Data Found
              </td>
            </tr>
          )}
        </tbody>
      </table>
      {totalPages > 1 && (
        <CustomPagination
          total_pages={totalPages}
          current_page={current_page}
          setCurrent_Page={setCurrent_Page}
        />
      )}
    </>
  );
};

export default UserSubscriptions;
