import React from "react";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { FaFile } from "react-icons/fa";
import { formatDate, timeDifference } from "./db.js";
import NoDataFound from "../noDataFound/NoDataFound.jsx";

const BlogsApproval = ({
  articleLoading,
  noDtaError,
  articles,
  handleArticleModal,
  renderPlaceholders,
}) => {
  return (
    <>
      {articleLoading === false ? (
        <>
          {noDtaError ? (
            <div className="custom-margin-nodatafoud">
              <NoDataFound />
            </div>
          ) : articles && articles?.length > 0 ? (
            <div className="overflow-hidden">
              <div className="overflow-auto allApproval-tab-scroll">
                <div className="row">
                  {articles?.map((item, index) => (
                    <div key={index} className="col-6 mb-2">
                      <div className="d-flex bg-grey upload-reviews">
                        <div className="col-sm-1">
                          <FaFile
                            style={{
                              fontSize: "30px",
                              color: "#8107D1",
                            }}
                          />
                        </div>
                        <div className="col-sm-3">
                          <p className="fw-semibold mb-0">
                            {formatDate(item?.blog_details?.BlogDateTime)}
                          </p>
                          <div className="">
                            <p className="custom-transperent-btn">
                              {timeDifference(item.blog_details.BlogDateTime)}
                            </p>
                          </div>
                        </div>
                        <div className="col-sm-7 mb-0">
                          <p> {item.blog_details?.BlogTitle}</p>
                        </div>
                        <div className="col-sm-1">
                          <button
                            type="button"
                            className="btn btn-yellow"
                            onClick={() => handleArticleModal(item)}
                          >
                            Review
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No other Blogs available for approval. 
              </h3>
            </div>
          )}
        </>
      ) : (
        renderPlaceholders()
      )}
    </>
  );
};

export default BlogsApproval;
