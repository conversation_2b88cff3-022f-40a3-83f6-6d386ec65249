import React from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import ModalImage from "react-modal-image";

import Image from "next/image";
import {
  capitalizeFullName,
  formatDate,
} from "../../../../utils/helperfunction";

const ViewBlogModal = ({ showBlogModal, setShowBlogModal, singleBlog }) => {
  return (
    <Modal
      show={showBlogModal}
      onHide={() => {
        setShowBlogModal(false);
      }}
      size="xl"
      centered
    >
      <Modal.Header>
        <Modal.Title>
          <span className="allApproval-blog-modal-heading mb-3">
            Blog Title :
          </span>
          &nbsp; &nbsp;
          <span className="blog-title">{singleBlog?.BlogTitle}</span>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="row">
          <div className="col-sm-1">
            <Image
              src={
                singleBlog?.expert_details?.doctor_other_details?.ProfilePhoto
              }
              width={50}
              height={50}
              alt="cu logo"
              className="allApproval-image"
            />
          </div>
          <div className="col-sm p-0">
            <p className="allApproval-expert-name mb-0">
              {singleBlog?.expert_details?.name}
            </p>
            <p className="allApproval-expert-role">
              {capitalizeFullName(
                singleBlog?.expert_details?.role
                  ? singleBlog?.expert_details?.role
                  : "expert role"
              )}
            </p>
          </div>
          <div className="col-sm-2 d-flex justify-content-end ">
            <p className="allApproval-expert-para mt-0">
              {formatDate(singleBlog?.BlogDateTime)}
            </p>
          </div>
        </div>
        <div className="overflow-hidden">
          <div className="overflow-auto" style={{ maxHeight: "450px" }}>
            <p
              dangerouslySetInnerHTML={{
                __html: singleBlog?.BlogBody,
              }}
              className="allApproval-expert-para"
            ></p>
          </div>
        </div>

        <div className="blog-images-view-container d-flex justify-content-center align-items-center my-5">
          {singleBlog?.BlogImages?.map((blogImage, index) => (
            <div key={index} className="viewblog-image-wrapper">
              <ModalImage
                small={blogImage}
                large={blogImage}
                // alt={`blog-image-${index}`}
                alt="blog"
                hideDownload={true}
                hideZoom={true}
              />
            </div>
          ))}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={() => {
            setShowBlogModal(false);
          }}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ViewBlogModal;
