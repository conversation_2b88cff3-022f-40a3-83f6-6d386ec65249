"use client";

import LoginForm from "../../../components/Login/LoginForm";
// import Loading from "../../../components/Loading/PageLoading";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import Loading from "../../../components/Loading/PageLoading/Loading";

const Login = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "authenticated") {
      router.replace("/");
    }
  }, [status, router]);

  if (status === "loading") {
    return (
      <div>
        <Loading />
      </div>
    );
  }

  if (status !== "authenticated") {
    return (
      <div className="">
        <LoginForm />
      </div>
    );
  }

  return null;
};

export default Login;
