import { toast } from "react-toastify";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import React, { useEffect, useState } from "react";

const PlatformFee = () => {
  const [platformfee, setPlatformfee] = useState();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    PlatformCharges: 0,
    TransactionCharges: 0,
  });
  const axiosAuth = useAxiosAuth();

  const fetchPricing = async () => {
    try {
      const postPricing = await axiosAuth.get(
        process.env.NEXT_PUBLIC_PLATFORM_PRICING
      );
      setPlatformfee(postPricing?.data?.[0]);
      // Handle success or additional logic if needed
    } catch (err) {
      console.log("Error in posting the pricing", err);
      // Handle error if needed
    }
  };
  useEffect(() => {
    fetchPricing();
  }, [axiosAuth]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const postPricing = await axiosAuth.post(
        process.env.NEXT_PUBLIC_PLATFORM_PRICING,
        formData
      );
      toast.success("Pricing Updated Successfully", {
        autoClose: 1500,
        theme: "colored",
        position: "top-center",
      });
      setFormData({
        PlatformCharges: 0,
        TransactionCharges: 0,
      });
      fetchPricing();
      setLoading(false);
    } catch (err) {
      toast.error("Error in Pricing Updated", {
        autoClose: 1500,
        theme: "colored",
        position: "top-center",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  return (
    <div className="row">
      <div className="col-sm-4">
        <div className="row">
          <div className="col-sm-8">
            <p>Platform Charges</p>
            <p>Transaction charges</p>
          </div>

          <div className="col-sm-4">
            <p>: ${platformfee?.PlatformCharges}</p>
            <p>: {platformfee?.TransactionCharges}%</p>
          </div>
        </div>
        <p className="" style={{ color: "#8107d1", fontSize: "11px" }}>
          <span style={{ color: "#8107d1", fontWeight: "600" }}>Note:</span>{" "}
          Transaction Charges = {platformfee?.TransactionCharges}% ×
          (Doctor&apos;s Charges + Platform Charges). Transaction Charges will
          be reflected on the patient&apos;s checkout page.
        </p>
      </div>
      <div className="col-sm-4 mx-auto">
        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label
              htmlFor="platformCharges"
              className="form-label custom-form-label"
            >
              Platform Charges
            </label>
            <div className="input-group">
              <span className="input-group-text">$</span>
              <input
                type="text"
                className="form-control"
                id="platformCharges"
                name="PlatformCharges"
                value={formData.PlatformCharges}
                onChange={handleInputChange}
                aria-describedby="platformChargesHelp"
              />
            </div>
          </div>
          <div className="mb-3">
            <label
              htmlFor="transactionCharges"
              className="form-label custom-form-label"
            >
              {/* Transaction charges = 7% of (Doctors charges + Platform Charges) */}
              Transaction charges
            </label>
            <div className="input-group">
              <span className="input-group-text">%</span>
              <input
                type="text"
                className="form-control"
                id="transactionCharges"
                name="TransactionCharges"
                value={formData.TransactionCharges}
                onChange={handleInputChange}
                aria-describedby="transactionChargesHelp"
              />
            </div>
          </div>

          <button disabled={loading} type="submit" className="btn purple-btn">
            {loading ? "Submitting..." : "Submit"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default PlatformFee;
