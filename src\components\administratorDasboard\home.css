.main-purple-text {
  color: #8107d1;
  font-size: 21px;
  font-weight: 600;
}
.content-scroll {
  max-height: 815px;
  /* overflow-y: scroll; */
}

.content-scroll::-webkit-scrollbar {
  display: none;
}

button.btn.btn-newuser {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffffff;
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
button.btn.btn-newuser:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffffff;
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

button.btn.btn-newadmin {
  background: #ff7700 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  width: 100%;
  color: #ffff;
}
button.btn.btn-newadmin:focus {
  background: #ff7700 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  width: 100%;
  color: #ffff;
}
.icon-setting {
  font-size: 35px;
  color: #8107d1;
}

.icon-bell {
  stroke-width: 2px;
  font-size: 37px;
  color: #8107d1;
}

button.btn.btn-bell {
  position: relative;
}

span.start-100.translate-middle.badge.rounded-pill {
  border: 2px solid #6d00b5;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 2px 10px #ababab4a;
  position: absolute;
  margin-top: 6px;
  margin-left: -21px;
  top: 0;
  padding-left: 3px;
  height: 61%;
  color: #7100bb;
  width: 50%;
  padding-top: 8px;
  padding-right: -38px;
}
button.btn.btn-bell {
  position: relative;
}

.href_link {
  text-decoration: none;
  color: #8107d1;
}
/* ********************Admin Dasboard****************** */
.dashboard-bg {
  background: #fbfbfb;
  border-radius: 3px;
  /* border: 1px solid  black; */
}
.home-heading {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-size: 16px;
  font-weight: 600;
}
.custom-border {
  border-radius: 5px;
  opacity: 1;
  background-color: white;
  box-shadow: 0px 3px 6px #00000012;
  border: 1px solid var(--unnamed-color-ffffff);
}

.sub-heading {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-weight: 600;
  font-size: 12px !important;
  margin-bottom: 0px;
  padding-top: 5px;
}
.purple-num {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  font-size: 40px;
}
.dashboard-content-border {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  padding: 10px;
}
.dashboard-content {
  font-size: 14px;
}
.green-count {
  letter-spacing: 0px;
  color: #04ab20;
  opacity: 1;
  font-size: 20px;
}
.black-count {
  letter-spacing: 0px;
  color: #414146;
  opacity: 1;
  font-size: 18px;
}
.red-count {
  letter-spacing: 0px;
  color: #ff2e2e;
  opacity: 1;
  font-size: 18px;
}
th.th-custom {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-size: 12px;
  font-weight: 500;
}
tbody,
td,
tfoot,
th,
thead,
tr {
  border-style: none;
}
td.custom-td {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-size: 14px;
  font-weight: 200;
}
td.custom-td-purple {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  font-size: 14px;
  font-weight: 500;
}
.purple-patient-name {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  font-size: 14px;
  font-weight: 500;
}
.view-btn {
  background: #8107d1;
  box-shadow: 0px 3px 6px #00000014;
  border-radius: 3px;
  opacity: 1;
  color: white !important;
  border: none;
  font-size: 10px;
  font-weight: 500;
  padding: 5px 30px 5px 30px;
}
.view-btn:hover {
  background: #a745e9;
}
.view-prescription {
  color: #8107d1;
  font-weight: bold;
  font-size: 14px;
}
.app-scroll {
  overflow-y: auto;
}
.app-head {
  position: sticky;
  top: 0;
  background-color: white;
}
.table-wrapper {
  max-height: 350px;
  overflow-y: auto;
}
.table-wrapper::-webkit-scrollbar {
  display: none;
}

.expert-searchbar {
  border: 1px solid var(--unnamed-color-e3e3e3);
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
}
.form-control.allsearchinput {
  height: 44px;
  border: 0px;
  box-shadow: none;
}
.form-control.allsearchinput:focus {
  border: 0px;
  box-shadow: none;
}

/* //dropdown */
.dropdown-search-home {
  position: absolute;
  top: 100%; /* Position below the search bar */
  left: 12px;
  z-index: 9999; /* Ensure dropdown appears above other elements */
  width: 97%;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  padding: 0.5rem 0;
}
.users-search-list {
  padding-left: 20px;
  margin-bottom: 0px;
}
.searched-user {
  margin-right: 10px;
  padding: 5px 15px 5px 5px;
  border-radius: 5px;
  background-color: #f1f1f1;
  margin-bottom: 3px;
}
.searched-user:hover {
  padding: 5px 15px 5px 5px;
  background-color: #8107d1;
  color: white;
}

/* range filter */
.calender-filter-container {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #dee2e6;
  border-radius: 3px;
}
.heading,
.expert-heading,
.expert-total {
  letter-spacing: 0px;
  color: #5b5b5b !important;
  opacity: 1;
  /* font-size: 20px; */
  font-weight: 600;
}

span.date-filter-patient {
  background: #fff;
  border-radius: 3px;
  padding: 5px;
  display: inline-flex;
  justify-content: space-between;
  width: 100%;
  font-size: 13px;
}

.calendar-icon-patient {
  position: relative;
  z-index: 99;
}
.customDatePicker {
  left: -2px;
  position: relative;
  top: 141px;
}

.cross-icon-calendar {
  color: #ff7700;
}
.calender-icon-calendar {
  color: #8107d1;
}

/* bell icon notification css */
.notification-badge {
  font-size: 10px;
  position: absolute;
  right: -13px;
  border-radius: 3px;
}

.notification-badge-placeholder {
  position: absolute;
  right: 0;
  bottom: 59%;
}

.expert-total {
  color: #8107d1 !important;
}
.year-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.select-chart-year {
  width: 10em;
  height: 2em;
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000014;
  border: 1px solid #fdf5ff;
  border-radius: 3px;
  opacity: 1;
  color: #5b5b5b;
  /* float: right; */
}

.chart-section-background {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  border: 1px solid var(--unnamed-color-e3e3e3);
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  padding: 0px 10px;
}

.select-chart-year:focus,
.select-chart-year:active {
  border: none;
  outline: none;
}

.chart-title {
  text-align: center;
  font: 17px;
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-weight: 600;
}
