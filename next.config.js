/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
    ],
    // domains: ["cu-files.s3.amazonaws.com"],
  },
  webpack: (config) => {
    config.resolve.alias.canvas = false;

    return config;
  },
  eslint: {
    ignoreDuringBuilds: true, // Disable ESLint during build
  },
};

module.exports = nextConfig;
