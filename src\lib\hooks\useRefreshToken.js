"use strict";

import Cookies from "js-cookie";
import { jwtDecode } from "jwt-decode";
import axios from "../axios";
import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";

export const useRefreshToken = () => {
  const [url, setUrl] = useState("");
  const { data: session, update } = useSession();

  const router = useRouter();
  const newRefreshToken = useMemo(() => {
    if (session?.user?.refresh_token) {
      return session.user?.refresh_token;
    }
    return null;
  }, [session]);

  const newAccessToken = useMemo(() => {
    if (session?.user?.access_token) {
      return session.user?.access_token;
    }
    return null;
  }, [session]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const newUrl = new URL(window.location.href);
      const pathWithParams = `${newUrl.pathname}${newUrl.search}`;
      setUrl(pathWithParams);
    }
  }, [router]);

  const handleSignOut = async () => {
    try {
      await signOut({ redirect: false, callbackUrl: "/" });
      // toast.success("Logged Out Successfully", {
      //   autoClose: 3000,
      //   theme: "colored",
      //   position: "top-center",
      // });

      Cookies.remove("user_id");
      setTimeout(() => {
        router.push(`/auth/login?returnUrl=${url}`);
      }, 2000);
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const newUrl = new URL(window.location.href);
      const pathWithParams = `${newUrl.pathname}${newUrl.search}`;
      setUrl(pathWithParams);
    }
  }, [router]);

  const refreshToken = async () => {
    try {
      if (!newRefreshToken) {
        // Handle scenario when refresh token is not available
        // console.error("Refresh token is not available.");
        return;
      }

      // Decode the refresh token
      const decodedRefreshToken = jwtDecode(newRefreshToken);
      const decodedAccessToken = jwtDecode(newAccessToken);
      const currentTime = Math.floor(Date.now() / 1000);
      // Check if the refresh token is expired
      if (decodedRefreshToken.exp && decodedRefreshToken.exp < currentTime) {
        handleSignOut();
        return "Refesh token expired";
      }
      // Check if the access token is expired
      else if (decodedAccessToken.exp && decodedAccessToken.exp < currentTime) {
        const res = await axios.post(process.env.NEXT_PUBLIC_CU_TOKEN_REFRESH, {
          refresh: newRefreshToken,
        });

        if (res.status === 200) {
          if (session?.user) {
            let updatedUser = {
              ...session.user,
              access_token: res.data?.access,
            };

            await update({
              ...session,
              user: updatedUser,
            });

            return res.data?.access;
          }
        }
      }
    } catch (error) {
      if (
        (error.response.data.detail ===
          "Given token not valid for any token type" ||
          error.response.data.detail === "Token is invalid or expired") &&
        error.response.data.code === "token_not_valid"
      ) {
        handleSignOut();
      }
    }
  };

  return refreshToken;
};
