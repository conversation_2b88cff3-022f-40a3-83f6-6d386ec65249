import React from "react";

const DashboardStatistics = ({ expertsPendingCount, statsData }) => {
  return (
    <div className="custom-shadow">
      <div className="row">
        <div className="col-sm-2 side-border">
          <p className="text-center  single-line sub-heading mt-1 mb-1">
            {statsData[1]?.heading}
          </p>
          <p className="text-center purple-num mb-2">{statsData[1]?.value}</p>
        </div>
        <div className="col-sm-2 side-border">
          <p className="text-center single-line sub-heading mt-1 mb-1">
            {statsData[2]?.heading}
          </p>
          <p className="text-center purple-num mb-2">{statsData[2]?.value}</p>
        </div>
        <div className="col-sm-2 side-border">
          <p className="text-center sub-heading mt-1 mb-1">
            {statsData[3]?.heading}
          </p>
          <p className="text-center purple-num mb-2">{statsData[3]?.value}</p>
        </div>
        <div className="col-sm-2 side-border">
          <p className="text-center sub-heading mt-1 mb-1">
            {statsData[4]?.heading}
          </p>
          <p className="text-center purple-num mb-2">{statsData[4]?.value}</p>
        </div>
        <div className="col-sm-2 side-border">
          <p className="text-center sub-heading mt-1 mb-1">
            {statsData[6]?.heading}
          </p>
          <p className="text-center purple-num mb-2">{statsData[6]?.value}</p>
        </div>
        <div className="col-sm-2">
          <p className="text-center single-line sub-heading mt-1 mb-1">
            {statsData[5]?.heading}
          </p>
          <p className="text-center purple-num mb-2">{statsData[5]?.value}</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardStatistics;
