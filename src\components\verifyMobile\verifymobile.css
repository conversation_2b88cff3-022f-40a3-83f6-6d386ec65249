* {
  font-family: "Poppins", sans-serif, 20px;
}

.custom-col-right {
  height: 100vh;
}

.login-box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.otp-box {
  width: 450px;
  height: 370px;
  margin: auto;
  box-shadow: 0px 3px 6px #00000038;
  opacity: 1;
  overflow: hidden;
  padding-top: 2px;
}

.verify-container {
  border-bottom: 1px solid rgba(112, 112, 112, 0.23);
}
.otp-group-box {
  padding: 20px;
  padding-top: 4px;
}
label.OTP {
  color: #ef5f12;
  padding-left: 1px;
  padding-bottom: 10px;
  opacity: 1;
  font-size: 15px;
}
input.OTP {
  border: 1px solid #a14b9e;
  border-radius: 0%;
}
input.OTP:focus {
  border-color: #a14b9e;
  outline: 0;
  box-shadow: 0px 3px 6px #00000029;
}
.verify-mobile {
  font-size: 20px;
  color: #8107d1;
  opacity: 1;
  padding-top: 15px;
  padding-left: 18px;
}
.resend-otp {
  color: #ef5f12;
  text-decoration: none;
  font-size: 13px;
}
.number {
  font-size: 22px;
  color: #707070;
}
.resndOTP {
  padding-top: 5px;
}
.orange-btn {
  background-color: #8107d1;
  border: 1px solid #8d4f9f;
  border-radius: 0;
  width: 100%;
  min-height: 50px;
  margin-top: 30px;
  box-shadow: 0px 3px 6px #00000029;
  color: white;
}
.orange-btn:hover {
  background-color: #8b26ce;
  border: 1px solid #8d4f9f;
  border-radius: 0;
  width: 100%;
  min-height: 50px;
  margin-top: 30px;
  box-shadow: 0px 3px 6px #00000029;
  color: white;
}
.verify-mobile-submit:disabled{
  background-color: #a67ec0;
}
