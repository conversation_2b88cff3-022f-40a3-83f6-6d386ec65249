import React from 'react';
import { Placeholder, Button } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';

const NotificationPlaceholder = () => {
  const placeholders = Array.from({ length: 5 }).map((_, index) => (
    <div
      key={index}
      className="notification-item mb-2">
      <div className="pt-1 d-flex justify-content-between align-items-center">
        <Placeholder
          as="p"
          animation="glow"
          className="notification-text mb-0"
          style={{ width: '70%' }}>
          <Placeholder xs={7} />
        </Placeholder>
      </div>
      <div
        style={{
          textDecoration: 'none',
          color: 'inherit',
          display: 'block',
          width: '100%',
        }}>
        <Placeholder
          as="p"
          animation="glow"
          className="notification-desc">
          <Placeholder xs={10} />
        </Placeholder>
        <Placeholder
          as="p"
          animation="glow"
          className="notification-time">
          <Placeholder xs={5} />
        </Placeholder>
      </div>
    </div>
  ));

  return <>{placeholders}</>;
};

export default NotificationPlaceholder;
