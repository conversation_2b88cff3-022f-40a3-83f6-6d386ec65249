import React, { useState, useCallback } from "react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import { formatCustomDateAndTime } from "../../utils/helperfunction";
import { But<PERSON>, Modal } from "react-bootstrap";
import { toast } from "react-toastify";
import Image from "next/image";
import dummyProfile from "../../../public/images/dummy-avatar.jpg";
import { IoMdCloudUpload } from "react-icons/io";
import { FaFilePdf } from "react-icons/fa";
import { FileUploader } from "react-drag-drop-files";
import { useRouter } from "next/navigation";
import { IoReceipt } from "react-icons/io5";
import CustomPagination from "../CustomPagination/CustomPagination";

const PendingPayment = ({
  expertsWalletAmountDetails = [],
  fetchExpertAmountRequest,
  totalPages,
  loading,
  renderPlaceholders,
  current_page,
  setCurrent_Page,
  per_page,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [paymentInvoice, setPaymentInvoice] = useState(null);
  const [expertBalanceAmount, setExpertBalanceAmount] = useState(0);

  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const router = useRouter();

  const handleFileChange = (file) => setPaymentInvoice(file);

  const clearPayment = async (expertId, transactionId) => {
    if (!paymentInvoice) {
      toast.error("Please add invoice.");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("invoice", paymentInvoice);
      formData.append("PaymentStatus", 1);

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CLEAR_EXPERT_PAYMENT}${expertId}/${transactionId}/?user_id=${session?.user?.id}`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );

      if (response?.data === "successfully approved") {
        toast.success("Payment cleared successfully.");
        setShowModal(false);
        fetchExpertAmountRequest();
        clearFormDetails();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const fetchExpertTotalAmount = useCallback(
    async (expert_Id) => {
      try {
        const totalAmount = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_EXPERT_WALLET_API}${expert_Id}/`
        );
        if (totalAmount?.data == "No transactions has been done yet.") {
          setExpertBalanceAmount(0);
        } else {
          setExpertBalanceAmount(totalAmount?.data?.BalanceAmount);
        }
      } catch (e) {
        console.error(e);
      }
    },
    [axiosAuth]
  );
  const renderTableBody = () => {
    return (
      <>
        {expertsWalletAmountDetails?.map((walletDetails, index) => (
          <tr key={index}>
            <td>{(current_page - 1) * per_page + index + 1}</td>
            <td>
              <Image
                src={
                  walletDetails?.expert_details?.expert_profile_photo ||
                  dummyProfile
                }
                alt={`Dr. ${
                  walletDetails?.expert_details?.expert_name ||
                  "Name not available"
                }`}
                width={35}
                height={35}
                className="wallet-details-expert-image"
              />
              <span className="expert-name">
                {walletDetails?.expert_details?.expert_name || "Expert Name"}
              </span>
            </td>
            <td>
              {walletDetails?.expert_details?.expert_member_code ||
                "No MemberCode"}
            </td>
            <td>
              {walletDetails?.TransactionDate
                ? formatCustomDateAndTime(walletDetails.TransactionDate)
                : "Date not available"}
            </td>
            <td>${walletDetails?.TransactionAmount?.toFixed(2) || "0.00"}</td>
            <td>
              <Button
                className="purple-btn"
                onClick={() => {
                  setSelectedPayment({
                    expertId: walletDetails?.expert_details?.expert_id,
                    walletDetails,
                  });
                  fetchExpertTotalAmount(
                    walletDetails?.expert_details?.expert_id
                  );
                  setShowModal(true);
                }}
              >
                Clear payment
              </Button>
            </td>
          </tr>
        ))}
      </>
    );
  };

  const clearFormDetails = () => {
    setPaymentInvoice(null);
    setSelectedPayment(null);
  };

  return (
    <>
      <div>
        <table className="table custom-table mt-1">
          <thead>
            <tr className="text-center">
              <th className="pending-row-custom">Sl No</th>
              <th className="pending-row-custom">Expert Name</th>
              <th className="pending-row-custom">Expert MemberCode</th>
              <th className="pending-row-custom">Requested Date</th>
              <th className="pending-row-custom">Requested Amount</th>
              <th className="pending-row-custom">Clear Amount</th>
            </tr>
          </thead>
          <tbody>{loading ? renderPlaceholders(4) : renderTableBody()}</tbody>
        </table>
        {!loading && totalPages > 1 && (
          <div className="pagination-container">
            <CustomPagination
              total_pages={totalPages}
              current_page={current_page}
              setCurrent_Page={setCurrent_Page}
            />
          </div>
        )}
      </div>
      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        backdrop="static"
        size="lg"
        centered
        keyboard={false}
        scrollable
      >
        <Modal.Header className="d-flex justify-content-between align-items-center fw-bold custom-modal-header">
          <Modal.Title
            style={{ fontSize: "18px", color: "#5B5B5B", fontWeight: "bold" }}
          >
            Clear Payment
          </Modal.Title>
          <div
            style={{ cursor: "pointer", color: "#FF2E2E" }}
            onClick={() => {
              setShowModal(false);
              clearFormDetails();
            }}
          >
            Cancel
          </div>
        </Modal.Header>
        <Modal.Body>
          <div className="d-flex align-items-center mb-4 gap-2">
            <div
              className="p-2 px-4"
              style={{ background: "#F6F6F6", width: "50%" }}
            >
              <p className="label-text">Total Wallet Amount</p>
              <p className="value-text-grey">
                {expertBalanceAmount
                  ? `$ ${expertBalanceAmount.toFixed(2)}`
                  : "$ 00.00"}
              </p>
            </div>
            <div
              className="p-2 px-4"
              style={{ background: "#F6F6F6", width: "70%" }}
            >
              <p className="label-text">Request Amount</p>
              <p className="value-text">
                {selectedPayment?.walletDetails?.TransactionAmount
                  ? `$ ${selectedPayment.walletDetails.TransactionAmount?.toFixed(
                      2
                    )}`
                  : "$ 00.00"}
              </p>
            </div>
          </div>
          <div className="d-flex align-items-center gap-2 mb-4 custom-modal-content-bg">
            <div className="d-flex align-items-center" style={{ width: "41%" }}>
              <div className="p-2 px-4">
                <p className="label-text">Expert Name</p>
                <p className="value-text-grey mb-0">
                  <Image
                    src={
                      selectedPayment?.walletDetails?.expert_details
                        ?.expert_profile_photo || dummyProfile
                    }
                    alt={`Dr ${selectedPayment?.walletDetails?.expert_details?.expert_name}`}
                    width={35}
                    height={35}
                    className="wallet-details-expert-image"
                  />
                  <span
                    onClick={() =>
                      router.push(
                        `usermanagement/experts/experts-doctor/${selectedPayment?.walletDetails?.expert_details?.expert_id}/${selectedPayment?.walletDetails?.expert_details?.expert_email}/${selectedPayment?.walletDetails?.expert_details?.expert_approval}`
                      )
                    }
                    style={{ cursor: "pointer" }}
                  >
                    {
                      selectedPayment?.walletDetails?.expert_details
                        ?.expert_name
                    }
                  </span>
                </p>
              </div>
            </div>
            <div className="px-4" style={{ width: "40%" }}>
              <p className="label-text">Date of Request</p>
              <p className="value-text-grey mb-0">
                {formatCustomDateAndTime(
                  selectedPayment?.walletDetails?.TransactionDate
                )}
              </p>
            </div>
          </div>
          <div className="border border-secondary-subtle rounded p-2">
            <div className="mb-4">
              <p className="label-text">Uploaded Documents</p>
              <div className="uploaded-documents">
                {paymentInvoice ? (
                  <div className="transction-receipt px-5 py-3 rounded">
                    <FaFilePdf className="custom-FaFilePdf-icon" />
                    <br className="document-break-line" />
                    Receipt.pdf
                  </div>
                ) : (
                  <span className="text-center no-transaction-receipt fw-bold my-3">
                    <IoReceipt size={25} /> &nbsp; No Transaction Receipt
                  </span>
                )}
              </div>
            </div>
            <div>
              <p className="label-text">Upload Transaction Proof</p>
              <FileUploader
                handleChange={handleFileChange}
                name="file"
                types={["PDF"]}
                style={{ width: "100%", border: "1px solid red" }}
                multiple={false}
                required
              >
                <div className="upload-box">
                  <div className="my-3">
                    <IoMdCloudUpload className="clear-payment-custom-IoMdCloudUpload-icon" />
                    <p className="">Click here to select items</p>
                    or
                    <p className="mt-2">Drag Files Here</p>
                  </div>
                </div>
              </FileUploader>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="border-0">
          <Button
            className="submit-button border-0"
            onClick={() => {
              clearPayment(
                selectedPayment?.walletDetails?.expert_details?.expert_id,
                selectedPayment?.walletDetails?.id
              );
            }}
          >
            Submit
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default PendingPayment;
