import React, { useCallback, useEffect, useState, useRef } from "react";
import Image from "next/image";
import ReactPlayer from "react-player";
import { SiApplepodcasts, SiPodcastaddict } from "react-icons/si";
import noData from "../../../../public/images/nodata.png";
import { Button, Form, InputGroup, Modal } from "react-bootstrap";
import { FaEdit, FaSpotify } from "react-icons/fa";
import { MdDelete } from "react-icons/md";
import { AiFillEye, AiFillYoutube } from "react-icons/ai";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import EditSinglePodcast from "./EditSinglePodcast";
import {
  capitalizeFullName,
  formatCustomDateAndTime,
  skeletonLoader,
} from "../../../utils/helperfunction.js";
import Swal from "sweetalert2";
import { useSession } from "next-auth/react";
import { RiHourglass2Fill } from "react-icons/ri";
import NoDataFound from "../../noDataFound/NoDataFound.jsx";

const ViewPodcastList = ({
  showPodcastModal,
  setShowPodcastModal,
  viewExpertPodcast,
}) => {

  const doctorId = viewExpertPodcast?.expert_details?.id;
  const doctorName = viewExpertPodcast?.expert_details?.name;

  const [podcasts, setPodcasts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editPodcast, setEditPodcast] = useState(null);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;

  // Reference to the ReactPlayer instance
  const playerRef = useRef(null);

  const fetchPodcasts = useCallback(async () => {
    setLoading(true);
    try {
      if (doctorId) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_EXPERTS_PODCAST_REQUEST}${doctorId}`
        );

        if (response.status === 200) {
          setPodcasts(response.data);
        } else {
          console.error("Error fetching podcasts:", response.statusText);
        }
      }
    } catch (error) {
      console.error("Error fetching podcasts:", error);
    } finally {
      setLoading(false);
    }
  }, [doctorId, axiosAuth]);

  useEffect(() => {
    if (showPodcastModal) {
      fetchPodcasts();
    }
  }, [fetchPodcasts, showPodcastModal]);

  const handleDelete = async (podcastId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_PUBLISH_EXPERTS_PODCAST_REQUEST}${podcastId}/?user_id=${admin_id}`
          );
          if (response.statusText === "OK" || response.status === 200) {
            fetchPodcasts();
            Swal.fire("Deleted!", "Your podcast has been deleted.", "success");
          } else {
            console.error("Error deleting podcast: ", response.statusText);
            Swal.fire("Failed!", "Failed to delete your podcast.", "error");
          }
        } catch (error) {
          console.error("Error deleting podcast: ", error);
          Swal.fire(
            "Failed!",
            "An error occurred while deleting the podcast.",
            "error"
          );
        }
      }
    });
  };

  // Function to handle clicking on timestamps
  const handleTimestampClick = (time) => {
    if (playerRef.current) {
      // Convert timestamp (e.g., "00:30") to seconds
      const [minutes, seconds] = time.split(":").map(Number);
      const timeInSeconds = minutes * 60 + seconds;

      // Seek to the corresponding time in the video
      playerRef.current.seekTo(timeInSeconds);
    }
  };
  const confirmEdit = (podcast) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you want to edit this podcast?",
      icon: "info",
      showCancelButton: true,
      confirmButtonText: "Yes, edit it!",
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        setEditPodcast(podcast);
      }
    });
  };
  const confirmDelete = (podcastId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        handleDelete(podcastId); // Call delete logic
      }
    });
  };

  return (
    <>
      <Modal
        show={showPodcastModal}
        onHide={() => setShowPodcastModal(false)}
        size="xl"
        centered
        scrollable
      >
        <Modal.Header className="d-flex justify-content-between align-items-center">
          <Modal.Title style={{ color: "#8107d1" }}>
            Podcasts List for{" "}
            <span className="fw-semibold">
              {doctorName && capitalizeFullName(doctorName)}
            </span>
          </Modal.Title>
          <Button
            onClick={() => setShowPodcastModal(false)}
            className="bg-transparent border-0 text-danger fw-bold"
          >
            Close
          </Button>
        </Modal.Header>
        <Modal.Body>
          <div>
            {loading ? (
              <>{skeletonLoader("200px", "15px", 4)}</>
            ) : (
              <>
                {podcasts?.items?.length > 0 ? (
                  podcasts.items?.map((podcast) => {
                    return (
                      <div key={podcast?.id} id={podcast?.id} className="mb-2">
                        <div className="row">
                          <div className="d-flex justify-content-between align-items-center mb-3">
                            <div className="d-flex mb-2">
                              <SiPodcastaddict color="#8107D1" size={30} />
                              &nbsp;
                              <p className="ps-2 mb-0">
                                <strong className="custom-purple-text-color">
                                  Title :
                                </strong>{" "}
                                {podcast?.PodcastTopic}
                              </p>
                            </div>
                            <div className="d-flex justify-content-end">
                              <Button
                                className="me-2 podcast-edit-button"
                                onClick={() => confirmEdit(podcast)}
                                style={{
                                  backgroundColor: "#8107d1",
                                  border: "none",
                                }}
                              >
                                <FaEdit size={20} />
                              </Button>
                              <Button
                                variant="danger"
                                onClick={() => confirmDelete(podcast.id)}
                              >
                                <MdDelete size={20} />
                              </Button>
                            </div>
                          </div>
                          <div className="row">
                            <p className="mb-2">
                              <span className="fw-bold custom-purple-text-color">
                                Category:{" "}
                              </span>
                              {podcast?.PodcastCategoryVal ||
                                "No category available"}
                            </p>
                            <p className="mb-2 mt-2 text-justify">
                              <span className="fw-bold custom-purple-text-color">
                                Description:{" "}
                              </span>
                              {podcast?.PodcastDescription ||
                                "No description available"}
                            </p>
                          </div>
                          <div className="d-flex justify-content-between align-items-center mt-2 ">
                            <p>
                              <strong className="custom-purple-text-color">
                                Posted On:{" "}
                              </strong>
                              {podcast.PodcastDate
                                ? formatCustomDateAndTime(podcast.PodcastDate)
                                : "No Date Available"}
                            </p>
                            <p className="float-end  d-flex justify-content-center align-items-center fw-bold">
                              <AiFillEye color="#8107d1" className="fs-5" />{" "}
                              &nbsp;
                              {podcast.PodcastViews} &nbsp; Views
                            </p>
                          </div>

                          <div className="row">
                            <InputGroup className="mb-3 ">
                              <InputGroup.Text id="basic-addon1">
                                <a
                                  href={podcast?.Platforms?.youtube?.url || "#"}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-decoration-none text-black"
                                >
                                  <AiFillYoutube
                                    color="#FF0000"
                                    className="fs-3"
                                  />
                                  &nbsp; Youtube Podcast URL
                                </a>
                              </InputGroup.Text>
                              <Form.Control
                                placeholder="No URL found"
                                type="url"
                                value={podcast?.Platforms?.youtube?.url || ""}
                                readOnly
                              />
                            </InputGroup>
                            <div className="col-sm-12 mb-4 d-flex justify-content-center rounded g-0 overflow-hidden align-items-center">
                              <ReactPlayer
                                ref={playerRef} // Set the ref to ReactPlayer
                                url={podcast?.Platforms?.youtube?.url || "none"}
                                light={
                                  podcast?.ThumbnailImage ||
                                  podcast?.Platforms?.youtube?.thumbnail ||
                                  noData
                                }
                                width={"100%"}
                                height="38em"
                                controls
                                className="border rounded ms-4"
                              />
                            </div>
                          </div>

                          <div className="row">
                            <div className="mb-4">
                              <div className="mt-4">
                                <label
                                  htmlFor="YoutubePodcastTranscription"
                                  className="form-label custom-label "
                                >
                                  Podcast Transcription
                                </label>
                                <textarea
                                  className="form-control custom-input"
                                  id="YoutubePodcastTranscription"
                                  rows="10"
                                  value={
                                    podcast?.Platforms?.youtube?.transcript
                                  }
                                  placeholder="No Youtube Transcript Found"
                                  readOnly
                                />
                              </div>
                              <div className="row">
                                <InputGroup className="mb-3 mt-4">
                                  <InputGroup.Text id="basic-addon1">
                                    <RiHourglass2Fill
                                      color="#FF0000"
                                      className="fs-3"
                                    />
                                    &nbsp; Youtube Podcast Duration
                                  </InputGroup.Text>
                                  <Form.Control
                                    placeholder="No Youtube Podcast Duration Found."
                                    value={
                                      podcast?.Platforms?.youtube?.duration ||
                                      ""
                                    }
                                    readOnly
                                  />
                                </InputGroup>
                              </div>
                              <div
                                style={{
                                  maxHeight:
                                    Array.isArray(
                                      podcast?.Platforms?.youtube?.timestamps
                                    ) &&
                                    podcast?.Platforms?.youtube?.timestamps
                                      .length > 5
                                      ? "200px"
                                      : "auto",
                                  overflowY:
                                    Array.isArray(
                                      podcast?.Platforms?.youtube?.timestamps
                                    ) &&
                                    podcast?.Platforms?.youtube?.timestamps
                                      .length > 5
                                      ? "scroll"
                                      : "visible",
                                }}
                              >
                                <strong className="custom-purple-text-color">
                                  Time Stamps :{" "}
                                </strong>
                                {Array.isArray(
                                  podcast?.Platforms?.youtube?.timestamps
                                ) &&
                                  podcast?.Platforms?.youtube?.timestamps?.map(
                                    (timestamp, index) => (
                                      <div key={index}>
                                        <strong>
                                          <button
                                            onClick={() =>
                                              handleTimestampClick(
                                                timestamp.time
                                              )
                                            }
                                            className="btn btn-link p-0"
                                          >
                                            {timestamp.time}
                                          </button>
                                        </strong>
                                        : {timestamp.title}
                                      </div>
                                    )
                                  )}
                              </div>

                              <div>
                                {podcast?.Platforms?.spotify?.url && (
                                  <InputGroup className="mt-4">
                                    <InputGroup.Text id="basic-addon1">
                                      <SiApplepodcasts
                                        color="#8800D5"
                                        className="fs-4"
                                      />
                                      &nbsp; Apple Podcast URL
                                    </InputGroup.Text>
                                    <Form.Control
                                      placeholder="Enter Spotify URL"
                                      aria-label="ApplePodcastURL"
                                      aria-describedby="basic-addon1"
                                      type="url"
                                      value={podcast?.Platforms?.spotify?.url}
                                      readOnly
                                    />
                                  </InputGroup>
                                )}

                                {podcast?.Platforms?.apple_podcast?.url && (
                                  <InputGroup className="mt-4">
                                    <InputGroup.Text id="basic-addon1">
                                      <FaSpotify
                                        color="#1DB954"
                                        className="fs-4"
                                      />
                                      &nbsp; Spotify Podcast URL
                                    </InputGroup.Text>
                                    <Form.Control
                                      placeholder="Enter Spotify URL"
                                      aria-label="SpotifyPodcastURL"
                                      aria-describedby="basic-addon1"
                                      type="url"
                                      value={
                                        podcast?.Platforms?.apple_podcast?.url
                                      }
                                      readOnly
                                    />
                                  </InputGroup>
                                )}
                              </div>

                              <div className=" mt-4 mb-4">
                                <label
                                  htmlFor="helpfulLinks"
                                  className="form-label custom-label"
                                >
                                  Helpful Links
                                </label>
                                {Array.isArray(podcast?.HelpfulLinks) &&
                                  podcast?.HelpfulLinks?.map((link, index) => (
                                    <InputGroup key={index} className="mb-2">
                                      <Form.Control
                                        placeholder="enter the title..."
                                        aria-label="helpful title"
                                        aria-describedby="basic-addon1"
                                        type="text"
                                        value={link.title}
                                        readOnly
                                      />
                                      <Button
                                        variant="outline-secondary"
                                        id="button-addon2"
                                        onClick={() =>
                                          window.open(link.url, "_blank")
                                        }
                                      >
                                        View
                                      </Button>
                                    </InputGroup>
                                  ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="d-flex justify-content-center align-items-center">
                    <NoDataFound />
                  </div>
                )}
              </>
            )}
          </div>
        </Modal.Body>
      </Modal>

      {editPodcast && (
        <EditSinglePodcast
          podcast={editPodcast}
          setEditPodcast={setEditPodcast}
          fetchPodcasts={fetchPodcasts}
        />
      )}
    </>
  );
};

export default ViewPodcastList;
