import React, { useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { highlightText } from "../../../utils/helperfunction";
import noDataFound from "../../../../public/assets/noDataFound.png";
import { Placeholder } from "react-bootstrap";
import ViewTestimonialModal from "./ViewTestimonialModal";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import NoDataFound from "@/components/noDataFound/NoDataFound";
import { toast } from "react-toastify";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 7 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "S No",
  "Select Testimonial",
  "Patient Name",
  "Date of Upload",
  "Cancer Treatment Type",
  "View Testimonial",
  "Delete Ranking",
];

const TestimonialsMainList = ({
  loading,
  initialLoading,
  current_pagePatient,
  setCurrent_PagePatient,
  testimonials,
  searchQuery,
  handleDeleteRanking,
  handleAddRanking,
}) => {
  const [showTestimonialModal, setShowTestimonialsModal] = useState(false);
  const [singleTestimonial, setSingleTestimonial] = useState({});
  const [deletingTestimonialId, setDeletingTestimonialId] = useState(null);

  const handleViewTestimonialModal = (item) => {
    setSingleTestimonial(item);
    setShowTestimonialsModal(true);
  };

  const handleDeletePodcastRanking = async (id) => {
    try {
      setDeletingTestimonialId(id);
      await handleDeleteRanking(id);
    } catch (err) {
      toast.error("Error in delete ranking");
    } finally {
      setDeletingTestimonialId(null);
    }
  };

  return (
    <>
      {showTestimonialModal && (
        <ViewTestimonialModal
          showTestimonialModal={showTestimonialModal}
          setShowTestimonialsModal={setShowTestimonialsModal}
          id={singleTestimonial.id}
          name={singleTestimonial.patient_name}
          feedback={singleTestimonial.ExperienceSummary}
          time={singleTestimonial.CurrentTime}
          role={singleTestimonial.CancerTreatmentType}
        />
      )}
     
        <div className="row">
          <div className="col-sm-12 fixTableHead">
            <table className="table mt-2 custon-table-featured">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-semibold text-center child-admin-list-heading ${
                        index === 4 ? "col-4" : "col"
                      }`}
                      style={{fontSize: "14px"}}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="custom-border">
                {initialLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {testimonials &&
                    Array.isArray(testimonials.items) &&
                    testimonials?.items?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                          <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      testimonials &&
                      Array.isArray(testimonials.items) &&
                      testimonials?.items?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              <div className="d-flex justify-content-center align-items-cneter">
                              {(current_pagePatient - 1) * 10 + index + 1}
                              </div>
                            </td>

                            <td className=" custom-font ">
                              <div className=" d-flex align-items-center">
                                <input
                                  className=" ms-5 form-check-input  blogs-content-input"
                                  type="checkbox"
                                  name="category"
                                  id="expertCategory"
                                  value="Expert Rejection"
                                  checked={blog?.selected_content_id !== null}
                                  onChange={() =>
                                    handleAddRanking(blog.id, "patient")
                                  }
                                />
                                <div className=" ms-2 ">
                                  {blog?.status !== null && (
                                    <span className="badge badge-success bg-success">
                                      Selected
                                    </span>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="col purple-content text-capitalize">
                            <div className=" d-flex align-items-center">
                              {blog?.patient_photo ? (
                                <Image
                                  src={`${blog?.patient_photo}`}
                                  alt={`Dr ${blog?.patient_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.patient_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(blog?.patient_name, searchQuery)}
                              </div>
                            </td>
                            <td className="col custom-font text-center">
                            <div className=" d-flex align-items-center">
                              {blog?.CurrentTime?.split("T")[0]}
                              </div>
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                            <div className=" d-flex align-items-center">
                              {blog?.CancerTreatmentType}
                              </div>
                            </td>
                            <td className="text-center fw-semibold col">
                              <button
                                style={{ color: "#8107D1" }}
                                className=" border-0 bg-white"
                                onClick={() => handleViewTestimonialModal(blog)}
                              >
                                view
                              </button>
                            </td>
                            <td className="text-center fw-semibold col">
                              <button
                                style={{ color: "red", fontSize: "15px" }}
                                className=" border-0 bg-white"
                                onClick={() =>
                                  handleDeletePodcastRanking(
                                    blog?.selected_content_id
                                  )
                                }
                                disabled={deletingTestimonialId === blog?.id}
                              >
                                {deletingTestimonialId === blog?.id
                                  ? "Deleting..."
                                  : "Delete"}
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
                {loading && renderPlaceholders("load")}
              </tbody>
            </table>
          </div>
        </div>
   
      <div className="d-flex justify-content-center align-items-center mt-3">
        <div className="d-none d-xl-block">
          <CustomPagination
            total_pages={testimonials?.total_pages}
            current_page={current_pagePatient}
            setCurrent_Page={setCurrent_PagePatient}
          />
        </div>
      </div>
    </>
  );
};

export default TestimonialsMainList;
