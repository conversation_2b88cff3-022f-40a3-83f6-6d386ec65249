import React, { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import noDataFound from "../../../../public/assets/noDataFound.png";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import { Placeholder } from "react-bootstrap";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import ViewBlogsContentModal from "../BlogsContent/ViewBlogsContentModal";
import { highlightText } from "../../../utils/helperfunction";
import NoDataFound from "../../noDataFound/NoDataFound";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 4 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "S No",
  "Select Blog",
  "Expert Name",
  "Date of Upload",
  "Blog Title",
  "Expert Role",
  "Blog Page",
];

const TrendingPodcasts = ({ fetchAllPodcasts }) => {
  const [showBlogsModal, setShowBlogsModal] = useState(false);
  const [singleBlog, setSingleBlog] = useState({});
  const [loading, setLoading] = useState(true);
  const [trendingBlogs, setTrendingBlogs] = useState([]);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const axiosAuth = useAxiosAuth();

  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const fetchTrendingBlogs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_FEATURED_bLOGS}${"1"}/Trending/`
      );
      setTrendingBlogs(response?.data);
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth]);
  useEffect(() => {
    fetchTrendingBlogs();
  }, [fetchTrendingBlogs]);

  const handleViewBlogsModal = (item) => {
    setSingleBlog(item);
    setShowBlogsModal(true);
  };
  const handleDeleteRanking = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete the ranking for this blog?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });
    if (result.isConfirmed) {
      const body = {
        blog_rank: 0,
      };

      try {
        setDeleteLoading(true);
        const response = await axiosAuth.delete(
          `${process.env.NEXT_PUBLIC_RANK_FEATURED_BLOGS}${id}/?user_id=${userId}`,
          { data: body }
        );
        if (response?.data === "successfully deleted") {
          toast.success(`The Rank has been ${response?.data}`, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        }
        setDeleteLoading(false);
        fetchTrendingBlogs();
        fetchAllPodcasts();
      } catch (err) {
        toast.error(`Error in deleting the rank`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
    }
  };
  return (
    <>
      {showBlogsModal && (
        <ViewBlogsContentModal
          showBlogsModal={showBlogsModal}
          setShowBlogsModal={setShowBlogsModal}
          singleBlog={singleBlog}
        />
      )}
      <div className="bg-color">
        <div className="row">
          <div className="col-sm-12">
            <table className="table mt-2">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-light text-center child-admin-list-heading ${
                        index === 4 ? "col-4" : "col"
                      }`}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>

              <tbody className="custom-border">
                {loading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {trendingBlogs &&
                    Array.isArray(trendingBlogs) &&
                    trendingBlogs?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                          <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      trendingBlogs &&
                      Array.isArray(trendingBlogs) &&
                      trendingBlogs?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row align-baseline table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {blog?.podcast_details?.PodcastRanking}
                            </td>
                            <td className="col purple-content text-capitalize">
                              {blog?.podcast_details?.expert_details
                                ?.expert_profile_photo ? (
                                <Image
                                  src={`${blog?.podcast_details?.expert_details?.expert_profile_photo}`}
                                  alt={`Dr ${blog?.podcast_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.podcast_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(
                                blog?.podcast_details?.expert_details
                                  ?.expert_name
                              )}
                            </td>
                            <td className="col custom-font text-center">
                              {
                                blog?.podcast_details?.PodcastDate?.split(
                                  "T"
                                )[0]
                              }
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {
                                blog?.podcast_details?.expert_details
                                  ?.expert_role
                              }
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                              {blog?.podcast_details?.PodcastTopic}
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "#8107D1" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleViewBlogsModal(blog?.podcast_details)
                                }
                              >
                                view
                              </button>
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "red", fontSize: "15px" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleDeleteRanking(blog?.podcast_details?.id)
                                }
                              >
                                Delete
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default TrendingPodcasts;
