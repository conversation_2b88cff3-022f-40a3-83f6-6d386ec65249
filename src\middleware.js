import { NextResponse } from "next/server";

export function middleware(request) {
  const authToken =
    request.cookies.get("next-auth.session-token")?.value ||
    request.cookies.get("__Secure-next-auth.session-token")?.value;

  const loggedInUserNotAccessPaths =
    request.nextUrl.pathname === "/auth/login" ||
    request.nextUrl.pathname == "/auth/register" ||
    request.nextUrl.pathname == "/verify";

  if (loggedInUserNotAccessPaths) {
    if (authToken) {
      //accessing not secure Route
      return NextResponse.redirect(
        new URL("/usermanagement/experts", request.url)
      );
    }
  } else {
    if (!authToken) {
      //accessing secure Route
      return NextResponse.redirect(new URL("/auth/login", request.url));
    }
  }
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    "/",
    "/auth/login",
    "/auth/register",
    // "/expert-doctor/:path*",
    "/subscription/:path*",
    "/payment-status/:path*",
    "/usermanagement/:path*",
    "/childadmin/:path*",
    "/profilesetting/:path*",
  ],
};
