import React from "react";

const Traffic = () => {
  const pageViewsPercentage = 70;
  const uniqueVisitorsPercentage = 30;
  return (
    <div className="custom-shadow mt-3 mb-3">
      <div className="row">
        <div className="col-sm-4">
          <p className="heading">Traffic Overview</p>
        </div>
      </div>
      <div className="row justify-content-center">
        <div className="col-sm-5">
          <div>
            813<span className="progress-label ps-3">Facebook</span>
          </div>
          <div className="progress mb-3">
            <div
              className="progress-bar bg-danger"
              role="progressbar"
              style={{ width: `${pageViewsPercentage}%` }}
              aria-valuenow={pageViewsPercentage}
              aria-valuemin="0"
              aria-valuemax="100"
            >
              {pageViewsPercentage}%
            </div>
          </div>
          <div>
            323<span className="progress-label ps-3 mt-2">LinkedIn</span>
          </div>
          <div className="progress mb-4">
            <div
              className="progress-bar"
              role="progressbar"
              style={{
                width: `${uniqueVisitorsPercentage}%`,
                backgroundColor: "#8107D1", // Add this line to set the background color
              }}
              aria-valuenow={uniqueVisitorsPercentage}
              aria-valuemin="0"
              aria-valuemax="100"
            >
              {uniqueVisitorsPercentage}%
            </div>
          </div>
        </div>
        <div className="col-sm-5">
          <div>
            717<span className="progress-label ps-3">Twitter</span>
          </div>
          <div className="progress mb-4">
            <div
              className="progress-bar bg-primary"
              role="progressbar"
              style={{ width: `${uniqueVisitorsPercentage}%` }}
              aria-valuenow={pageViewsPercentage}
              aria-valuemin="0"
              aria-valuemax="100"
            >
              {uniqueVisitorsPercentage}%
            </div>
          </div>
          <div>
            927<span className="progress-label ps-3 mt-4">Instagram</span>
          </div>
          <div className="progress mb-3">
            <div
              className="progress-bar bg-dark"
              role="progressbar"
              style={{ width: `${pageViewsPercentage}%` }}
              aria-valuenow={uniqueVisitorsPercentage}
              aria-valuemin="0"
              aria-valuemax="100"
            >
              {pageViewsPercentage}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Traffic;
