import React, { useEffect, useState } from "react";
import Image from "next/image";
import Modal from "react-bootstrap/Modal";
import "./prescription.css";
import img1 from "../../../public/assets/adminloginlogo.png";
import IllnessModal from "../../components/illnessModal/IllnessModal";
import { calculateAge } from "../../utils/helperfunction";
// import { calculateAge } from 'assets/helperFunctions';

const PrescriptionModal = ({
  selectedPresciption,
  prescriptionData,
  showPresModal,
  setShowPresModal,
}) => {
  const [loading, setLoading] = useState(true);
  const [showInnerModal, setShowInnerModal] = useState(false);
  const id = selectedPresciption?.prescriptions[0];


  if (!prescriptionData) {
    return null;
  }

  const {
    prescription_data,
    patient_details,
    doctor_details,
    appointment_details,
  } = prescriptionData;

  const iv_medication = prescription_data?.iv_medication;

  return (
    <>
      <IllnessModal
        prescription_data={prescription_data?.ExistingTreatment}
        showInnerModal={showInnerModal}
        setShowInnerModal={setShowInnerModal}
      />
      <Modal
        show={showPresModal}
        onHide={() => setShowPresModal(false)}
        size="xl"
        scrollable={true}
      >
        <Modal.Header>
        <div className="d-flex flex-column justify-content-between w-100">
          <div className="d-flex justify-content-between w-100">
            {/* Doctor Details Section */}
            <div className="col-9">
              <p className="purple-content mb-1 fw-semibold">
                {prescriptionData?.doctor_details?.name}
              </p>
              <p className="purple-content fs-6 mb-0">
                Dept. of{" "}
                {prescriptionData?.doctor_details?.doctor_other_details?.Dept}
              </p>
            </div>

            {/* Hospital Image */}
            <div className="col-3 text-end">
              <Image className="headerImage1" src={img1} alt="header" />
            </div>
          </div>

          <div className="d-flex justify-content-between w-100 mt-2">
            {/* License and Doctor ID */}
            <div className="col-9">
              <p className="black-content mb-1">MD license: 12-136547</p>
              <p className="black-content mb-1">
                Doctor ID: {prescriptionData?.doctor_details?.id}
              </p>
              <p className="black-content fw-medium mb-0">24th June, 2023</p>
            </div>

            {/* Hospital & Website */}
            <div className="col-3 text-end">
              <p className="purple-content mb-1 fw-semibold">
                {
                  prescriptionData?.doctor_details?.doctor_other_details
                    ?.PractisingHospital
                }
              </p>
              <p className="black-content mb-0">
                <a
                  className="link-opacity-100"
                  href="https://healthunwired.com/"
                >
                  Health Unwired
                </a>
              </p>
            </div>
          </div>
          </div>
        </Modal.Header>

        <Modal.Body className="modal-content-pres1">
          <form className="row g-3">
            <div className="col-md-2 mt-0">
              <label
                htmlFor="inputId"
                className="form-label purple-content-2 fw-medium mb-1"
              >
                Id
              </label>
              <input
                type="text"
                className="form-control custom-form-control"
                id="inputId"
                value={prescriptionData?.patient_details?.id}
                readOnly
              />
            </div>
            <div className="col-md-3 mt-0">
              <label
                htmlFor="inputName"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Name
              </label>
              <input
                type="name"
                className="form-control custom-form-control"
                id="inputName"
                value={prescriptionData?.patient_details?.name}
                readOnly
              />
            </div>
            <div className="col-md-3 mt-0">
              <label
                htmlFor="inputEmail"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Consultation Id
              </label>
              <input
                type="mail"
                className="form-control custom-form-control"
                id="inputEmail"
                value={prescriptionData?.appointment_details?.id}
                readOnly
              />
            </div>
            <div className="col-md-2 mt-0">
              <label
                htmlFor="inputEmail"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Sex
              </label>
              <input
                type="mail"
                className="form-control custom-form-control"
                id="inputEmail"
                value="M"
                readOnly
              />
            </div>
            <div className="col-md-2 mt-0">
              <label
                htmlFor="inputEmail"
                className="form-label  purple-content-2 fw-medium mb-1"
              >
                Age
              </label>
              <input
                type="mail"
                className="form-control custom-form-control"
                id="inputEmail"
                value={calculateAge(patient_details?.date_of_birth)}
                readOnly
              />
            </div>

            <div className="input-group mb-0">
              <span
                className="input-group-text purple-content"
                id="basic-addon1"
              >
                Current Diagnosis
              </span>
              <input
                type="text"
                className="form-control custom-form-control"
                aria-label="Username"
                aria-describedby="basic-addon1"
                value={prescription_data?.CurrentDiagnosis}
                readOnly
              />
            </div>

            <div className="border p-2">
              <div className="border p-3">
                <h2 className="medication-heading">Oral Medication</h2>
                <table className="table">
                  <thead>
                    <tr>
                      <th
                        scope="col"
                        className="purple-content mb-0 table-header-cell"
                      >
                        Sno.
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Oral Medication
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Dose Strength
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Frequency
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Duration (Days)
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Remarks
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {prescriptionData?.prescription_data?.oral_medication?.map(
                      (medication, index) => (
                        <tr key={index}>
                          <td>{index + 1}</td>
                          <td className="text-center">
                            {medication?.MedicineName}
                          </td>
                          <td className="text-center">
                            {medication?.DoseStrength}
                          </td>
                          <td className="text-center">
                            {medication?.Frequency}
                          </td>
                          <td className="text-center">
                            {medication?.Duration}
                          </td>
                          <td className="text-center">{medication?.Remarks}</td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              </div>

              <div className="border p-3 mt-3">
                <h2 className="medication-heading">IV/IM Medication</h2>
                <table className="table">
                  <thead>
                    <tr>
                      <th
                        scope="col"
                        className="purple-content mb-0 table-header-cell"
                      >
                        Sno.
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        IV Medication
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Dose Strength
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Mode of Administration
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Frequency
                      </th>
                      <th
                        scope="col"
                        className="purple-content mb-0 text-center"
                      >
                        Remarks
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {prescriptionData?.prescription_data?.iv_medication?.map(
                      (ivMedication, index) => (
                        <tr key={index}>
                          <td>{index + 1}</td>
                          <td>{ivMedication?.MedicineName}</td>
                          <td className="text-center">
                            {ivMedication?.DoseStrength}
                          </td>
                          <td className="text-center">
                            {ivMedication?.ModeOfAdministration}
                          </td>
                          <td className="text-center">
                            {ivMedication?.Frequency}
                          </td>
                          <td className="text-center">
                            {ivMedication?.Remarks}
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              </div>

              <div className="row align-items-center exist-treatment-container mt-3 ">
                <div className="col-sm-9 me-auto">
                  <p className="purple-content">Existing Treatment {}</p>
                </div>
                <div className="col-sm-3 ms-auto">
                  <button
                    type="button"
                    className="btn edit-button-pres"
                    onClick={() => setShowInnerModal(true)}
                  >
                    Detailed View
                  </button>
                </div>
              </div>

              <div className="form-floating mt-3">
                <textarea
                  className="form-control"
                  placeholder="Leave a comment here"
                  id="floatingTextarea"
                  style={{ height: "100px" }}
                  readOnly
                  value={prescription_data?.Recommendation}
                />
                <label htmlFor="floatingTextarea" className="purple-content">
                  Recommendation
                </label>
              </div>
            </div>
            <div className="form-floating">
              <textarea
                className="form-control"
                placeholder="Leave a comment here"
                id="floatingTextarea"
                style={{ height: "100px" }}
                readOnly
                value={prescription_data?.SpecialInstructions}
              />
              <label htmlFor="floatingTextarea" className="purple-content">
                Special Instructions
              </label>
            </div>

            <div className="col-sm-3 offset-sm-9 border">
              <p className="purple-signature">Digital Signature</p>
              {prescription_data?.DoctorSignature && (
                <Image
                  src={prescription_data.DoctorSignature}
                  alt="Dr Signature"
                  className="ms-5 mb-2"
                  width={100}
                  height={100}
                />
              )}
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default PrescriptionModal;
