import React, { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import noDataFound from "../../../../public/assets/noDataFound.png";
import { Placeholder } from "react-bootstrap";
import { highlightText } from "../../../utils/helperfunction";
import ViewPodcastRankingModal from "./ViewPodcastRankingModal";
import NoDataFound from "../../noDataFound/NoDataFound";
import { toast } from "react-toastify";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 4 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "Ranking",
  "Expert Name",
  "Date of Upload",
  "Expert Role",
  "Podcast Title",
  "Podcast Views",
  "Podcast Page",
  "Delete Ranking",
];

const TopPodcasts = ({
  fetchAllPodcasts,
  topLoading,
  topPodcasts,
  fetchTopPodcasts,
  handleDeleteRanking,
  deleteLoading,
}) => {
  const [showPodcastModal, setShowPodcastModal] = useState(false);
  const [singlePodcast, setSinglePodcast] = useState({});
  const [deletingPodcastId, setDeletingPodcastId] = useState(null);

  const handleViewBlogsModal = (item) => {
    setSinglePodcast(item);
    setShowPodcastModal(true);
  };
  const handleDeletePodcastRanking = async (id) => {
    try {
      setDeletingPodcastId(id);
      await handleDeleteRanking(id);
      await fetchTopPodcasts();
      await fetchAllPodcasts();
    } catch (err) {
      toast.error("Error in delete ranking");
    } finally {
      setDeletingPodcastId(null);
    }
  };
  return (
    <>
      {showPodcastModal && (
        <ViewPodcastRankingModal
          setShowPodcastModal={setShowPodcastModal}
          showPodcastModal={showPodcastModal}
          singlePodcast={singlePodcast}
        />
      )}
      <div className="bg-color">
        <div className="row">
          <div className="col-sm-12">
            <table className="table mt-2">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-light text-center child-admin-list-heading ${
                        index === 4 ? "col-4" : "col"
                      }`}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>

              <tbody className="custom-border">
                {topLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {topPodcasts &&
                    Array.isArray(topPodcasts) &&
                    topPodcasts?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                          <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      topPodcasts &&
                      Array.isArray(topPodcasts) &&
                      topPodcasts?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row align-baseline table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {blog?.podcast_details?.PodcastRanking}
                            </td>
                            <td className="col purple-content text-capitalize">
                              {blog?.podcast_details?.expert_details
                                ?.expert_profile_photo ? (
                                <Image
                                  src={`${blog?.podcast_details?.expert_details?.expert_profile_photo}`}
                                  alt={`Dr ${blog?.podcast_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.podcast_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(
                                blog?.podcast_details?.expert_details
                                  ?.expert_name
                              )}
                            </td>
                            <td className="col custom-font text-center">
                              {
                                blog?.podcast_details?.PodcastDate?.split(
                                  "T"
                                )[0]
                              }
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {
                                blog?.podcast_details?.expert_details
                                  ?.expert_role
                              }
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                              {blog?.podcast_details?.PodcastTopic}
                            </td>
                            <td className=" text-capitalize col custom-font">
                              {blog?.podcast_details?.PodcastViews}
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ fontSize: "15px", color: "#8107D1" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleViewBlogsModal(blog?.podcast_details)
                                }
                              >
                                view
                              </button>
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "red", fontSize: "15px" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleDeletePodcastRanking(
                                    blog?.podcast_details?.id
                                  )
                                }
                                disabled={
                                  deletingPodcastId ===
                                  blog?.podcast_details?.id
                                }
                              >
                                {deletingPodcastId === blog?.podcast_details?.id
                                  ? "Deleting..."
                                  : "Delete"}
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};
export default TopPodcasts;
