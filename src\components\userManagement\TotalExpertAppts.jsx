import React from "react";
import Image from "next/image";
import { calculateDuration } from "../../utils/helperfunction";
import profile from "../../../public/images/dummy-avatar.jpg";

const ExpertDataCard = ({ expertType, data }) => {
  if (!data) {
    return null; // or handle the missing data case in another way
  }
  const {
    cancelled_consultations,
    completed_consultations,
    latest_appointment_data,
    unattended_consultations,
    upcoming_consultations,
  } = data;
  const renderLatestAppointment = () => {
    if (latest_appointment_data === "no_data") {
      return (
        <p className="text-center no-latest-appointments mt-3 mb-3">
          No latest appointment available
        </p>
      );
    } else {
      return (
        <>
          <p className="fw-light mb-3 mt-2" style={{ fontSize: "12px" }}>
            Latest appointment{" "}
            <span className="purple-span">
              {latest_appointment_data?.appointment_date}
            </span>
          </p>
          <div className="d-flex">
            <div className="col-sm-1 gx-0">
              {latest_appointment_data?.expert_profile_photo ? (
                <>
                  <Image
                    src={latest_appointment_data?.expert_profile_photo}
                    alt={`Dr`}
                    width={40}
                    height={40}
                    className="expert_image"
                  />
                </>
              ) : (
                <>
                  <Image
                    src={profile}
                    alt={`Dr`}
                    width={40}
                    height={40}
                    className="expert_image"
                  />
                </>
              )}
            </div>
            <div className="col-sm-5 gx-0">
              <p className=" mb-1 fw-bold ms-1 doctor-patient-name">
                {latest_appointment_data?.expert_name}
              </p>
              <p className="fw-light mb-0 ms-1 custom-font-size">
                Duration for{" "}
                {calculateDuration(
                  latest_appointment_data?.appointment_session_start_time,
                  latest_appointment_data?.appointment_session_end_time
                )}
              </p>
            </div>
            <div className="col-sm-1 gx-0">
              {latest_appointment_data?.patient_profile_photo ? (
                <>
                  <Image
                    src={latest_appointment_data?.patient_profile_photo}
                    alt={`Dr`}
                    width={40}
                    height={40}
                    className="expert_image"
                  />
                </>
              ) : (
                <>
                  <Image
                    src={profile}
                    alt={`Dr`}
                    width={40}
                    height={40}
                    className="expert_image"
                  />
                </>
              )}
              {/* <Image
                src={latest_appointment_data?.patient_profile_photo}
                alt="patient image"
                width={45}
                height={45}
                style={{ borderRadius: "50%" }}
              /> */}
            </div>
            <div className="col-sm-5 gx-0">
              <p className=" mb-1 fw-bold ms-1 doctor-patient-name">
                {latest_appointment_data?.patient_name}
              </p>
              <p className="fw-light mb-0 ms-1" style={{ fontSize: "12px" }}>
                Patient
              </p>
            </div>
          </div>
          <div className="mt-2">
            <button
              type="button"
              className="btn btn-primary purple-btn"
              onClick={() => {
                if (typeof window !== "undefined") {
                  window.location.href = `/usermanagement/experts/experts-doctor/${latest_appointment_data?.expert_id}/${latest_appointment_data?.expert_email}/${latest_appointment_data?.approval}`;
                }
              }}
            >
              View
            </button>
          </div>
        </>
      );
    }
  };

  return (
    <div className="grey-container mb-2">
      <div className="row">
        <div className="col-sm-3 d-flex align-items-center">
          <p className="heading">{expertType}</p>
        </div>
        <div className="col-sm-9 d-flex justify-content-between align-items-center">
          <div className="text-center custom-border p-2">
            <p className="consultation-completed-count mb-1">
              {completed_consultations || 0}
            </p>
            <p className="mb-1" style={{ fontSize: "13px" }}>
              Completed
            </p>
          </div>
          <div className=" text-center custom-border p-2">
            <p className="orange-count mb-1">{unattended_consultations || 0}</p>
            <p className="mb-1" style={{ fontSize: "13px" }}>
              Unattended
            </p>
          </div>
          <div className=" text-center custom-border p-2">
            <p className="app-red-count mb-1">{cancelled_consultations || 0}</p>
            <p className="mb-1" style={{ fontSize: "13px" }}>
              Cancelled
            </p>
          </div>
        </div>
      </div>
      <div className="row mt-2">{renderLatestAppointment()}</div>
    </div>
  );
};

const TotalExpertAppts = ({ allExpertsAppointmentsDetails }) => {
  if (!allExpertsAppointmentsDetails) {
    return null; // or handle the missing data case in another way
  }
  const {
    doctor_appointments_data,
    influencer_appointments_data,
    researcher_appointments_data,
  } = allExpertsAppointmentsDetails;

  return (
    <div className="col-sm-4 custom-border">
      <p className="heading">Total Expert Appointments</p>
      <ExpertDataCard expertType="Doctor" data={doctor_appointments_data} />
      <ExpertDataCard
        expertType="Influencer"
        data={influencer_appointments_data}
      />
      <ExpertDataCard
        expertType="Researcher"
        data={researcher_appointments_data}
      />
    </div>
  );
};

export default TotalExpertAppts;
