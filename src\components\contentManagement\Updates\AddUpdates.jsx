import React, { useState, useEffect, useCallback } from "react";
import { MdEdit } from "react-icons/md";
import { MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import ContentManPlaceholder from "../UserProfileContent/ContentManPlaceholder";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { formatDateandTime } from "../../../utils/helperfunction";
import { Modal, Button, Form } from "react-bootstrap";

const AddUpdates = () => {
  const [showModal, setShowModal] = useState(false);
  const [currentUpdate, setCurrentUpdate] = useState({
    id: "",
    updates: "",
    updateTime: "",
  });
  const [update, setUpdate] = useState("");
  const [updateTime, setUpdateTime] = useState("");
  const [updates, setUpdates] = useState("");
  const [loading, setloading] = useState(true);
  const [error, setError] = useState("");
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const countWords = (text) => {
    return text.trim().split(/\s+/).length;
  };

  const getUpdates = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_UPDATE_BANNER}all/?user_id=${user_id}`
          // `${process.env.NEXT_PUBLIC_CREATE_UPDATES}?user_id=${user_id}`
        );
        setUpdates(response?.data);
        setloading(false);
      }
    } catch (err) {
      console.log("error in fetching ", err);
    }
    // } finally {
    //   setLoading(false);
    // }
  }, [axiosAuth, user_id]);

  const handlePostUpdates = async (updateId) => {
    if (!update || !updateTime) {
      return toast.error(`Please fill both the fields!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    if (countWords(update) < 20) {
      return toast.error("Update must be at least 20 words.", {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    try {
      setloading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_UPDATES}?user_id=${user_id}`,
        {
          updates: update,
          updateTime: updateTime,
        }
      );
      setloading(false);
      if (response?.data) {
        toast.success(`Update Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getUpdates();
      }

      setUpdate(" ");
      setUpdateTime(" ");
    } catch (error) {
      console.log("Error in adding the Update", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  useEffect(() => {
    getUpdates();
  }, [user_id, getUpdates, axiosAuth]);

  const handleEditClick = (updateitem) => {
    setCurrentUpdate(updateitem);
    setShowModal(true);
  };

  const handleSubmit = async () => {
    if (!currentUpdate?.updates || !currentUpdate?.updateTime) {
      return toast.error(`Please fill both the fields!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }

    if (countWords(currentUpdate?.updates) < 20) {
      return toast.error("Update must be at least 20 words.", {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    try {
      //   setloading(true);
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_GET_UPDATE_BANNER}${currentUpdate.id}/?user_id=${user_id}`,
        {
          updates: currentUpdate.updates,
          updateTime: currentUpdate.updateTime,
        }
      );
      //   setloading(false);
      if (response?.data) {
        toast.success(`Update edited Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getUpdates();
      }
      setCurrentUpdate(" ");
    } catch (error) {
      console.log("Error in adding the update", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    handleClose();
  };

  const handleClose = () => {
    setShowModal(false);
    setCurrentUpdate(null);
  };

  const handleDeleteClick = async (updateId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          // setloading(true);
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_GET_UPDATE_BANNER}${updateId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Update deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getUpdates();
          }
        } catch (error) {
          console.log("Error in deleting the Update", error);
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCurrentUpdate((prev) => ({ ...prev, [name]: value }));
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }
  return (
    <div className="">
      <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
        Add Latest Update
      </h5>
      <div className="row">
        <div className="col-sm-4">
          <div className="mb-3">
            <label htmlFor="cancerType" className="form-label custom-label">
              Add Latest Updates
            </label>
            <input
              type="text"
              className="form-control custom-form-control"
              id="cancerType"
              value={update}
              onChange={(e) => {
                setUpdate(e.target.value);
              }}
              placeholder="Enter the update / min-words-20"
            />
            {error && <small className="text-danger">{error}</small>}
          </div>
          <div className="mb-3">
            <label htmlFor="cancerType" className="form-label custom-label">
              Add Update Time
            </label>
            <input
              type="datetime-local"
              className="form-control custom-form-control"
              id="cancerType"
              value={updateTime}
              onChange={(e) => {
                setUpdateTime(e.target.value);
              }}
              placeholder="Enter the update datetime"
            />
          </div>

          <button
            disabled={loading}
            type="submit"
            onClick={handlePostUpdates}
            className="btn purple-button"
          >
            {loading ? "Submitting" : "Submit"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">Update Time</th>
                  <th scope="col">Update</th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {updates &&
                  updates?.map((item, index) => (
                    <tr key={index}>
                      <th scope="row" className="custom-font-size">
                        {index + 1}
                      </th>
                      <td className="custom-font-size">
                        {formatDateandTime(item.updateTime)}
                      </td>
                      <td className="custom-font-size">{item.updates}</td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm purple-button"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit className="custom-font-size" />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item?.id)}
                        >
                          <MdDelete className="custom-font-size" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      <Modal show={showModal} onHide={handleClose}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Update</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="formReportType">
            <Form.Label>Update</Form.Label>
            <Form.Control
              type="text"
              name="updates"
              value={currentUpdate?.updates}
              onChange={handleChange}
            />
          </Form.Group>
          <Form.Group controlId="formReportType">
            <Form.Label>Update Time</Form.Label>
            <Form.Control
              type="datetime-local"
              name="updateTime"
              value={currentUpdate?.updateTime}
              onChange={handleChange}
            />
          </Form.Group>
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            // onClick={() => handleEditSubmit()}
            onClick={() => handleSubmit()}
          >
            Save Changes
          </Button>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default AddUpdates;
