import React from "react";
import { Doughnut } from "react-chartjs-2";

const ChildAdminStatistics = ({ chartData, totalChildAdmin }) => {
  return (
    <div className="col-sm-5">
      <p className="heading">Child Admin Data</p>
      <hr></hr>
      <div className="row">
        <div className="col-sm-4">
          <p className="text-center fw-light mt-1 mb-2">Child Admins</p>
          <p className="text-center purple-num mb-2">{totalChildAdmin}</p>
        </div>
        <div className="col-sm-4">
          <p className="text-center fw-light mt-1 mb-2">New Requests Handled</p>
          <p className="text-center purple-num mb-2">{totalChildAdmin}</p>
        </div>
        <div className="col-sm-4">
          <p className="text-center fw-light mt-1 mb-2">All Requests</p>
          <p className="text-center purple-num mb-2">{totalChildAdmin}</p>
        </div>
      </div>

      <div className="light-grey-bg ">
        <div className="row mt-4">
          <div className="col-sm-5">
            <Doughnut data={chartData} />
          </div>
          <div className="col-sm-7">
            <div className=" p-2 mb-1">
              <div className="row">
                <p className=" mb-2 fw-light">Current Request Handling Rate</p>
                <p className=" purple-count">20</p>
              </div>
              <div className="row">
                <p className=" mb-2 fw-light">Current Request Delay Rate</p>
                <p className="orange-count">20</p>
              </div>
              <div className="row">
                <p className=" mb-2 fw-light">Current Request Denial Rate</p>
                <p className="red-count">20</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChildAdminStatistics;
