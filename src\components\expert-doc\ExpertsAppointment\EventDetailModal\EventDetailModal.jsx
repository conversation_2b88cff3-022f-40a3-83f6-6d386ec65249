import React, { useContext, useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import "./eventDetailModal.css";
import {
  changeDateFormat,
  convertIntoTime,
} from "../../../../utils/helperfunction";
// import { UserContext } from "@/Context/UserContext";

const EventDetailModal = ({ show, isOpen, event, onClose, expertTimeZone }) => {

  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title className="freeSlotModal-heading">
          {event?.status === "R" ? "Slot Not Available - " : "Slot Details - "}
        </Modal.Title>
        <p
          style={{
            margin: "5px 0px 0px 10px",
            color: "#763cef",
            fontWeight: "bold",
          }}
        >
          {/* {extractDate(event?.start)} */}
          {changeDateFormat(event?.schedule_start_time?.split("T")[0])}
        </p>
      </Modal.Header>
      <Modal.Body>
        <p className=" d-flex align-baseline">
          <h5 className="eventModal-label">Start Time</h5> :{"    "}
          {event?.schedule_start_time
            ? convertIntoTime(event?.schedule_start_time, expertTimeZone)
            : convertIntoTime(event?.slot_start_time, expertTimeZone)}
          {/* {separateDateTime(event?.schedule_start_time)} */}
        </p>
        <p className=" d-flex align-baseline">
          <h5 className="eventModal-label">End Time</h5> :{"    "}
          {event?.schedule_end_time
            ? convertIntoTime(event?.schedule_end_time, expertTimeZone)
            : convertIntoTime(event?.slot_end_time, expertTimeZone)}
          {/* {separateDateTime(event?.schedule_end_time)} */}
        </p>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose} className="closeButton">
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EventDetailModal;
