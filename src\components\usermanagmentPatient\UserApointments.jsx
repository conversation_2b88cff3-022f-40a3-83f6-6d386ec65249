import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import Select from "react-select";
import { FaCalendar, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import CountdownProgressBar from "../../components/countdownprogress/CountdownProgressBar";
import PrescriptionModal from "../../components/prescriptionModal/PrescriptionModal";
import PrescriptionModalResInf from "./PrescriptionModalResInf";
import CancellationReason from "../usermanagmentPatient/cancellationReason/CancellationReason";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import {
  calculateMeetingDuration,
  formatDateToYMD,
  highlightText,
  renderStarsApps,
} from "../../utils/helperfunction";
import CalenderFilter from "./CalenderFilter";
import { useSession } from "next-auth/react";
import _ from "lodash";
import { GrSearch } from "react-icons/gr";
import { Placeholder } from "react-bootstrap";
import CustomPagination from "../CustomPagination/CustomPagination";
import NoDataFound from "../noDataFound/NoDataFound";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 5 }, (_, index) => (
    <div className="placeholder-container row" key={index}>
      <div className="placeholder-left col-sm-8">
        <Placeholder as="div" animation="glow">
          <Placeholder
            className="rounded placeholder-item mb-4"
            style={{
              width: "100%",
              height: "150px",
            }}
          />
        </Placeholder>
      </div>
      <div className="placeholder-right col-sm-4">
        <Placeholder as="div" animation="glow">
          <Placeholder
            className="rounded placeholder-item mb-4"
            style={{
              width: "100%",
              height: "150px",
            }}
          />
        </Placeholder>
      </div>
    </div>
  ));

  return <div className="placeholders-wrapper">{placeholders}</div>;
};

const UserAppointments = ({ id }) => {
  const [statusFilter, setStatusFilter] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [showPicker, setShowPicker] = useState(false);
  const [prescriptionData, setPrescriptionData] = useState();
  const [selectedPresciption, setSelectedPresciption] = useState(null);
  const [showPresModal, setShowPresModal] = useState(false);
  const [appointments, setAppointments] = useState();
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [initialLoading, setInitialLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState();
  const [
    showResearcherInfluencerPrescriptionModal,
    setShowResearcherInfluencerPrescriptionModal,
  ] = useState(false);

  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const searchMenu = useRef(null);
  const userId = session?.user?.id;

  const fetchAllAppointments = useCallback(
    async (pageNumber, query) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_PATIENT_APPOINTMENTS}${id}/?`;

        if (startDate && endDate && id) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
        }
        if (query && id) {
          url += `&name=${query}`;
        }
        if (statusFilter && id) {
          url += `&status=${statusFilter}`;
        }
        if (pageNumber && id) {
          url += `&page=${pageNumber}`;
        }
        const response = await axiosAuth.get(url);
        console.log("Appointments API Response:", response);
        setAppointments(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    // [id, axiosAuth]
    [axiosAuth, endDate, id, statusFilter, startDate]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((page, query) => {
      fetchAllAppointments(page, query);
    }, 500);
  }, [fetchAllAppointments]);

  useEffect(() => {
    debouncedFetchData(currentPage, searchQuery);
  }, [searchQuery, debouncedFetchData, currentPage]);

  const handleViewPrescription = async (record) => {
    setSelectedPresciption(record);

    const role = record?.expert_role;
    if (role == "doctor") {
      setShowPresModal(true);
    } else if (role === "researcher" || role === "influencer") {
      setShowResearcherInfluencerPrescriptionModal(true);
    }
  };
  const Presid = selectedPresciption?.prescriptions[0];

  const fetchPrescription = useCallback(
    async (id) => {
      try {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_PRESCRIPTION}${id}`
        );
        setPrescriptionData(response?.data);
      } catch (error) {
        console.error("Error fetching prescription:", error);
        // Handle the error (e.g., show an error message to the user)
      } finally {
        // setLoading(false);
      }
    },
    [axiosAuth]
  );

  const fetchResearcherInfluencerPrescription = useCallback(
    async (id) => {
      try {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_PRESCRIPTION_INF_DOC}/${id}`
        );
        setPrescriptionData(response?.data);
      } catch (error) {
        console.error(
          "Error fetching researcher/influencer prescription:",
          error
        );
      }
    },
    [axiosAuth]
  );

  useEffect(() => {
    if (selectedPresciption) {
      const role = selectedPresciption?.expert_role;

      if (role === "doctor") {
        fetchPrescription(selectedPresciption?.prescriptions[0]);
      } else if (role === "researcher" || role === "influencer") {
        fetchResearcherInfluencerPrescription(
          selectedPresciption?.prescriptions[0]
        );
      }
    }
  }, [
    selectedPresciption,
    fetchPrescription,
    fetchResearcherInfluencerPrescription,
  ]);

  const statusOptions = [
    { value: "All", label: "All" },
    { value: "Upcoming", label: "Upcoming" },
    { value: "Cancelled", label: "Cancelled" },
    { value: "Expired", label: "Expired" },
    { value: "Completed", label: "Completed" },
    { value: "Cancellation Rejected", label: "Cancellation Rejected" },
    { value: "Cancellation Pending", label: "Cancellation Pending" },
  ];

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handleDateFilterChange = (start, end) => {
    setStartDate(start);
    setEndDate(end);
    if (start && end) {
      setShowPicker(false);
    }
  };

  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      border: "none", // Remove the border
      boxShadow: "none",
      fontSize: "14px",
      height: "44",
      "&:hover": {
        borderColor: "none",
        height: "44",
        // Remove border color on hover
      },
    }),
    indicatorSeparator: (provided, state) => ({
      ...provided,
      // display: "none", // Hide the indicator separator
    }),
    option: (provided, state) => ({
      ...provided,
      // backgroundColor: "#FDF5FF",
      backgroundColor: state.isSelected
        ? "#8107d1"
        : state.isFocused
        ? "#FDF5FF"
        : null,
      border: "none",
      borderStyle: "none", // Background color of the options
      "&:hover": {
        backgroundColor: "#8107d1", // Background color on hover
        color: "#FFF", // Text color on hover
      },
      "&:active": {
        backgroundColor: "#8107d1", // Background color when active
        color: "#FFF", // Text color when active
      },
    }),
    dropdownIndicator: (provided, state) => ({
      ...provided,
      color: "#8107d1", // Initial color
      "&:hover": {
        color: "#8107D1", // Color on hover
      }, // Change the color of the dropdown icon
    }),
    menu: (provided, state) => ({
      ...provided,
      marginTop: 0, // Remove the top margin
    }),
    menuList: (provided, state) => ({
      ...provided,
      paddingTop: 0, // Remove the top padding
      paddingBottom: 0, // Remove the bottom padding
    }),
    // Add more styles as needed
  };

  const handleStatusFilterChange = (selectedOption) => {
    setStatusFilter(selectedOption.value);
    setCurrentPage(1);
  };

  const handleClearFilter = () => {
    handleDateFilterChange(null, null);
    setShowPicker(false);
  };
  const handleClearQueryFilter = () => {
    setSearchQuery("");
  };
  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  return (
    <>
      <PrescriptionModalResInf
        selectedPresciption={selectedPresciption}
        showResearcherInfluencerPrescriptionModal={
          showResearcherInfluencerPrescriptionModal
        }
        setShowResearcherInfluencerPrescriptionModal={
          setShowResearcherInfluencerPrescriptionModal
        }
        prescriptionData={prescriptionData}
      />
      <div className="payment-back">
        <div className=" overflow-hidden">
          <div className="user-management-scroll overflow-auto">
            <div className="row">
              <div className="col-sm-9 col-size">
                <div className="filters-background">
                  <div className="row">
                    <div className="col-sm-2">
                      <span className="filter-all">Filters</span>
                    </div>
                    <div className="col-sm-3 d-flex justify-content-center align-items-center">
                      <div className="input-group search-input-patient">
                        <input
                          type="text"
                          style={{
                            height: "38px",
                            borderRadius: "3px",
                            border: "none",
                            fontSize: "14px",
                          }}
                          className="form-control search-input-focus"
                          placeholder="Search by Name or Id"
                          aria-label="Recipient's username"
                          aria-describedby="button-addon2"
                          // onChange={(e) => debouncedSearch(e.target.value)}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          value={searchQuery}
                        />
                        {!searchQuery && (
                          <span
                            className="input-group-text custom-search-icon border-0"
                            id="button-addon2"
                            style={{ borderRadius: "5px" }}
                          >
                            <GrSearch style={{ color: "#8107d1" }} />
                          </span>
                        )}
                        {searchQuery && (
                          <span
                            style={{ zIndex: 10 }}
                            className="cancel-app-search-btn"
                          >
                            <FaTimes
                              className="cross-icon-calendar "
                              onClick={handleClearQueryFilter}
                            />
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="col-sm-3 d-flex justify-content-center align-items-center">
                      <div
                        ref={searchMenu}
                        className=" calender-container-app w-100"
                      >
                        <span
                          style={{ gap: "2px" }}
                          className="date-filter d-flex "
                          onClick={handleCalendarClick}
                        >
                          {startDate
                            ? `${startDate.toLocaleDateString()} - ${
                                endDate ? endDate.toLocaleDateString() : ""
                              }`
                            : "Select Date"}
                          <span className="calendar-icon-patient ms-auto">
                            {startDate ? (
                              <FaTimes
                                className=" cross-icon-calendar"
                                onClick={handleClearFilter}
                              />
                            ) : (
                              <FaCalendar className=" calender-icon-calendar" />
                            )}
                          </span>
                        </span>

                        {showPicker && (
                          <div style={{ position: "relative", zIndex: 1 }}>
                            <DatePicker
                              selected={startDate}
                              startDate={startDate}
                              endDate={endDate}
                              selectsRange
                              inline
                              showTimeSelect={false} // Disable time selection
                              onChange={(dates) =>
                                handleDateFilterChange(dates[0], dates[1])
                              }
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="col-sm-4 d-flex justify-content-center align-items-center">
                      <Select
                        options={statusOptions}
                        placeholder="By Status"
                        menuPlacement="bottom"
                        isSearchable={false} // Disable search if not needed
                        className="select-dropdown"
                        styles={customStyles}
                        value={statusOptions.find(
                          (option) => option.value === statusFilter
                        )}
                        onChange={handleStatusFilterChange}
                      />
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden">
                  <div className="content-scroll overflow-auto p-2">
                    <div className="pb-5">
                      {initialLoading ? (
                        renderPlaceholders()
                      ) : appointments?.items?.length > 0 ? (
                        <>
                          {appointments?.items?.map((appointment) => {
                            const hasPatientQuery =
                              appointment?.p_queries?.[0] !== "0";
                            const hasDoctorReply =
                              appointment?.p_queries?.[1] !== "0";
                            const statusMessage = !hasPatientQuery
                              ? "Ask Query"
                              : hasDoctorReply
                              ? "Replied"
                              : "Query Asked";

                            return (
                              <div key={appointment?.id} className="row mt-3">
                                <div className="col-sm-7">
                                  <div className="appointment-bg">
                                    <div className="row">
                                      <div className="col-sm-12">
                                        <div className="d-flex justify-content-between align-content-center">
                                          <p className="fw-bold">
                                            Dr.
                                            {highlightText(
                                              appointment?.doctor_name,
                                              searchQuery
                                            )}
                                          </p>
                                          <span className="star-color ms-auto">
                                            {renderStarsApps(
                                              appointment?.doctor_rating
                                            )}
                                          </span>
                                        </div>
                                        <div className="row">
                                          <div className="col-6 d-flex">
                                            <p className="col-7 timeline">
                                              Appointment Date
                                            </p>
                                            <div className="col-5">
                                              <span className="appointment-text text-capitalize ms-auto text-center">
                                                {
                                                  appointment?.slot_start_time?.split(
                                                    "T"
                                                  )[0]
                                                }
                                              </span>
                                            </div>
                                          </div>
                                          <div className="col-6 d-flex">
                                            <p className="col-7 timeline">
                                              Appointment Time
                                            </p>
                                            <div className="col-5">
                                              <span className="appointment-text text-capitalize ms-auto text-center">
                                                {appointment?.slot_start_time
                                                  ?.split("T")[1]
                                                  .substring(0, 5)}
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                        {/* <div className="d-flex mb-1">
                                          <span className="timeline">
                                            Appointment Time
                                          </span>
                                          <span className="name-doctor text-capitalize ms-auto">
                                            {appointment?.slot_start_time
                                              ?.split("T")[1]
                                              .substring(0, 5)}
                                          </span>
                                        </div> */}
                                        {/* <div className="d-flex mb-1">
                                          <span className="timeline">
                                            {"Doctor's Name"}
                                          </span>
                                          <span className="name-doctor text-capitalize ms-auto">
                                            {highlightText(
                                              appointment?.doctor_name,
                                              searchQuery
                                            )}
                                          </span>
                                        </div> */}
                                        <div className="row">
                                          <div className="col-6 d-flex">
                                            <p className="col-7 timeline">
                                              Appointment Duration
                                            </p>

                                            <div className="col-5">
                                              <span className="appointment-text text-capitalize ms-auto text-center">
                                                {calculateMeetingDuration(
                                                  appointment
                                                    ?.meeting_session_details[0]
                                                    ?.SessionStartTime,
                                                  appointment
                                                    ?.meeting_session_details[0]
                                                    ?.SessionEndTime
                                                )}
                                              </span>
                                            </div>
                                          </div>

                                          <div className="col-6 d-flex">
                                            <p className="col-7 timeline">
                                              Query Status
                                            </p>
                                            <div className="col-5">
                                              <span className=" text-capitalize ms-auto text-center yellow-bg-status">
                                                {statusMessage}
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                        {/* <div className="d-flex mb-1">
                                          <span className="timeline">
                                            Query Status
                                          </span>
                                          <span className="name-doctor ms-auto">
                                            {statusMessage}
                                          </span>
                                        </div> */}
                                        {/* <div className="d-flex mb-1">
                                          <span className="name-doctor text-capitalize">
                                            Doctor Ratings
                                          </span>
                                          <span className="star-color ms-auto">
                                            {renderStarsApps(
                                              appointment?.doctor_rating
                                            )}
                                          </span>
                                        </div> */}
                                      </div>
                                      <div className="col-sm-12">
                                        <div className="row">
                                          <div className="col-sm-7">
                                            <div className="remaining-time">
                                              <CountdownProgressBar
                                                starttime={
                                                  appointment.slot_start_time
                                                }
                                                meetingStatus={
                                                  appointment
                                                    .meeting_session_details[0]
                                                    ?.MeetingStatus
                                                }
                                                current_status={
                                                  appointment?.current_status
                                                }
                                                status={appointment?.status}
                                                hasPatientQuery={
                                                  hasPatientQuery
                                                }
                                                hasDoctorReply={hasDoctorReply}
                                                appointmentCancelReason={
                                                  appointment?.appointment_cancel_reason
                                                }
                                              />
                                            </div>
                                          </div>
                                          <div className="col-sm-5">
                                            <button
                                              type="button"
                                              className="btn btn-prescription"
                                              data-bs-toggle="modal"
                                              onClick={
                                                appointment?.prescriptions
                                                  .length > 0
                                                  ? () =>
                                                      handleViewPrescription(
                                                        appointment
                                                      )
                                                  : null
                                              }
                                              disabled={
                                                appointment?.prescriptions
                                                  .length === 0
                                              }
                                            >
                                              <span className="appointment-prescription-status">
                                                {appointment?.prescriptions
                                                  .length > 0
                                                  ? "View Call Summary"
                                                  : "Pending Call Summary"}
                                              </span>
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <CancellationReason
                                  appointment={appointment}
                                  fetchAllAppointments={fetchAllAppointments}
                                />
                              </div>
                            );
                          })}
                          <div className="">
                            {loading && (
                              <div>
                                <Placeholder as="p" animation="glow">
                                  <Placeholder
                                    xs={12}
                                    size={"lg"}
                                    style={{
                                      height: "43px",
                                      borderRadius: "4px",
                                    }}
                                  />
                                </Placeholder>
                              </div>
                            )}
                          </div>
                          {appointments?.total_pages > 1 && (
                            <div className="mt-4">
                              <CustomPagination
                                total_pages={appointments?.total_pages}
                                current_page={currentPage}
                                setCurrent_Page={setCurrentPage}
                              />
                            </div>
                          )}
                        </>
                      ) : (
                        <div style={{ height: "680px" }} className="">
                          <NoDataFound />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-sm-3">
                <CalenderFilter id={id} />
              </div>
            </div>
          </div>
        </div>
      </div>
      {showPresModal && (
        <PrescriptionModal
          selectedPresciption={selectedPresciption}
          showPresModal={showPresModal}
          setShowPresModal={setShowPresModal}
          prescriptionData={prescriptionData}
        />
      )}
    </>
  );
};
export default UserAppointments;
