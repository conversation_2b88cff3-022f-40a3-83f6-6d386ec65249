import { useSession } from "next-auth/react";
import { useCallback, useEffect, useState } from "react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Loader from "../../loader/Loader";
import { toast } from "react-toastify";
import ContentManPlaceholder from "./ContentManPlaceholder";

const ReschedulingMeetingUpdate = () => {
  const [loading, setLoading] = useState(false);
  const [numberOfRescheduling, setNumberOfRescheduling] = useState([]);
  const [editedNumber, setEditedNumber] = useState("");
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const reschedulingTimes = [0, ...Array.from({ length: 5 }, (_, i) => i + 1)];
  const handleChange = (e, index) => {
    const value = Number(e.target.value);
    if (value < 0) {
      return;
    }
    setEditedNumber(value);
  };
  const handleUpdate = async () => {
    if (!user_id) {
      toast.error("User ID is missing.");
      return;
    }

    try {
      setLoading(true);
      if (numberOfRescheduling && numberOfRescheduling[0]?.id) {
        // Update existing reschedule
        const updatedRescheduleResponse = await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${numberOfRescheduling[0]?.id}/?user_id=${user_id}`,
          {
            Content: editedNumber,
            Category: "Reschedule",
          }
        );
        if (updatedRescheduleResponse.status === 200) {
          toast.success("Rescheduling updated successfully.");
          getNumberOfReschedule();
          setEditedNumber("");
        } else {
          toast.error("Failed to update rescheduling.");
        }
      } else {
        // Create new reschedule
        const postRescheduleResponse = await axiosAuth.post(
          `${process.env.NEXT_PUBLIC_CREATE_CONTENT_TYPE}?user_id=${user_id}`,
          {
            Content: editedNumber,
            Category: "Reschedule",
          }
        );
        if (postRescheduleResponse?.status === 200) {
          toast.success("Rescheduling added successfully.");
          getNumberOfReschedule();
          setEditedNumber("");
        } else {
          toast.error("Failed to add rescheduling.");
        }
      }
    } catch (error) {
      toast.error("Failed to update rescheduling.");
      console.error("Error updating rescheduling data: ", error);
    } finally {
      setLoading(false);
    }
  };

  const getNumberOfReschedule = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}all/?Category=Reschedule&user_id=${user_id}`
        );
        setNumberOfRescheduling(response?.data);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  useEffect(() => {
    getNumberOfReschedule();
  }, [getNumberOfReschedule]);

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <div>
      <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
        Add Rescheduling Policy
      </h5>
      <label htmlFor="reschedulingUpdate" className="form-label custom-label">
        Meeting Rescheduling for Patient
      </label>

      <div className="row">
        <div className="col-sm-4">
          <div className="rescheduling-desclaimer-section">
            <strong>Disclaimer:</strong> The individual updating this section is
            setting the number of times a patient can reschedule their meeting.
          </div>
          <div className="row">
            <div className="col d-flex flex-column">
              <label
                htmlFor="rescheduleInput"
                className="form-label custom-label"
              >
                Reschedule Input
              </label>

              <div className="row mb-3">
                <div>
                  <input
                    type="number"
                    className="form-control"
                    placeholder="Enter No.of Times Rescheduling ..."
                    value={
                      editedNumber !== null && editedNumber !== undefined
                        ? editedNumber
                        : numberOfRescheduling[0]?.Content
                    }
                    onChange={(e) => handleChange(e)}
                  />
                </div>
              </div>
            </div>
            {/* <div className="col d-flex flex-column">
              <label
                htmlFor="rescheduleSelect"
                className="form-label custom-label"
              >
                Select Rescheduling Times
              </label>
              <select
                className="form-select"
                onChange={(e) => {
                  const value = Number(e.target.value);
                  setEditedNumber(value);
                }}
              >
                {reschedulingTimes.map((option, index) => (
                  <option key={option} value={index}>
                    {option === 1 || option === 0
                      ? `${option} time`
                      : `${option} times`}
                  </option>
                ))}
              </select>
            </div> */}
          </div>
          <div className="row">
            <div className="col mt-3">
              <button
                disabled={loading}
                type="submit"
                onClick={handleUpdate}
                className="btn purple-button"
                // style={{ width: "80%" }}
              >
                {loading ? "Updating..." : "Update"}
              </button>
            </div>
          </div>
        </div>

        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">Rescheduling Times</th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {numberOfRescheduling &&
                  Array.isArray(numberOfRescheduling) &&
                  numberOfRescheduling.map((item, index) => (
                    <tr key={index}>
                      <th scope="row" className="custom-font-size">{index + 1}</th>
                      <td className="custom-font-size">{item?.Content}</td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReschedulingMeetingUpdate;
