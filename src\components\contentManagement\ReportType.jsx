import React, { useState, useEffect, useCallback } from "react";
import { MdEdit } from "react-icons/md";
import { MdDelete } from "react-icons/md";
import EditModal from "./EditModal";
import Loader from "../loader/Loader";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import ContentManPlaceholder from "./UserProfileContent/ContentManPlaceholder";

const ReportType = () => {
  const [showModal, setShowModal] = useState(false);
  const [currentReport, setCurrentReport] = useState(null);
  const [reportType, setReportType] = useState("");
  const [report, setReport] = useState("");
  const [loading, setloading] = useState(true);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getReportType = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_CREATE_REPORT_TYPE}all/?user_id=${user_id}`
        );
        setReport(response?.data);
        setloading(false);
      }
    } catch (err) {
      console.log("error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  const handlePostReportType = async () => {
    try {
      setloading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CRUD_CREATE_REPORT_TYPE}?user_id=${user_id}`,
        {
          type: reportType,
        }
      );
      setloading(false);
      if (response?.data) {
        toast.success(`Report Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getReportType();
      }

      setReportType(" ");
    } catch (error) {
      console.log("Error in adding the report", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  useEffect(() => {
    getReportType();
  }, [user_id, getReportType, axiosAuth]);

  const handleEditClick = (reportItem) => {
    setCurrentReport(reportItem);
    setShowModal(true);
  };

  const handleClose = () => {
    setShowModal(false);
    setCurrentReport(null);
  };

  const handleDeleteClick = async (reportId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          // setloading(true);
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_GET_CREATE_REPORT_TYPE}${reportId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Report deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getReportType();
          }
        } catch (error) {
          console.log("Error in adding the report", error);
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }
  return (
    <>
      <div className="row">
        <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
          Add Report type for Medical Records
        </h5>
        <div className="col-sm-4">
          <div className="mb-3">
            <label htmlFor="cancerType" className="form-label custom-label">
              Report type
            </label>
            <input
              type="text"
              className="form-control custom-form-control"
              id="cancerType"
              value={reportType}
              onChange={(e) => {
                setReportType(e.target.value);
              }}
              placeholder="Enter type of report"
            />
          </div>

          <button
            disabled={loading}
            type="submit"
            onClick={handlePostReportType}
            className="btn purple-button"
          >
            {loading ? "Submitting" : "Submit"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">Report Type</th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {report &&
                  report?.map((item, index) => (
                    <tr key={index}>
                      <th scope="row" className="custom-font-size">
                        {index + 1}
                      </th>
                      <td className="custom-font-size">{item.type}</td>
                      <td className="custom-font-size">
                        <button
                          className="btn btn-primary btn-sm purple-button"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit className="custom-font-size" />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item?.id)}
                        >
                          <MdDelete className="custom-font-size" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      {currentReport && (
        <EditModal
          show={showModal}
          handleClose={handleClose}
          initialData={currentReport}
          axiosAuth={axiosAuth}
          user_id={user_id}
          dataFetch={getReportType}
          commonTopic={false}
        />
      )}
    </>
  );
};

export default ReportType;
