import React, { useEffect } from "react";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from "chart.js";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import { BsFillInfoCircleFill } from "react-icons/bs";

const PatientsListStats = ({ patientsStatistics }) => {
  // const {
  //   total_patients,
  //   recent_consultations,
  //   total_consultations,
  //   total_cancelled_consultations,
  //   total_completed_consultations,
  //   total_rescheduled_consultations,
  //   total_upcoming_consultations,
  //   total_unattended_consultations,
  // } = patientsStatistics;
  const {
    total_patients = 0,
    recent_consultations = 0,
    total_consultations = 0,
    total_cancelled_consultations = 0,
    total_completed_consultations = 0,
    total_rescheduled_consultations = 0,
    total_upcoming_consultations = 0,
    total_unattended_consultations = 0,
  } = patientsStatistics || {};
  const chartData = {
    datasets: [
      {
        label: "Consultations",
        data: [
          total_completed_consultations,
          total_upcoming_consultations,
          total_unattended_consultations,
          total_rescheduled_consultations,
          total_cancelled_consultations,
        ],
        backgroundColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
          "rgba(181, 0, 0, 1)",
        ],
        borderColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
          "rgba(181, 0, 0, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };
  ChartJS.register(ArcElement, Tooltip, Legend);
  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
  );

  // const chartData = {
  //   datasets: [
  //     {
  //       label: "# of Votes",
  //       data: [60, 20, 20, 10],
  //       backgroundColor: [
  //         "rgba(4, 171, 32, 1)",
  //         "rgba(129, 7, 209, 1)",
  //         "rgba(255, 151, 26, 1)",
  //         "rgba(255, 46, 46, 1)",
  //       ],
  //       borderColor: [
  //         "rgba(4, 171, 32, 1)",
  //         "rgba(129, 7, 209, 1)",
  //         "rgba(255, 151, 26, 1)",
  //         "rgba(255, 46, 46, 1)",
  //       ],
  //       borderWidth: 1,
  //     },
  //   ],
  // };
  
  useEffect(() => {
    const bootstrap = require("bootstrap");
    new bootstrap.Tooltip(document.body, {
      selector: '[data-toggle="tooltip"]',
    });
  }, []);

  return (
    <div className="col-sm-5">
      <p className="heading">Patient Data by Expert</p>
      <div className="custom-shadow">
        <div className="row">
          <div className="col-sm-4 side-border">
            <p className="text-center sub-heading mt-1 mb-1"> Total Patients</p>
            <p className="text-center purple-num mb-2">{total_patients}</p>
          </div>
          <div className="col-sm-4 side-border">
            <p
              className="text-center sub-heading single-line mt-1 "
              data-toggle="tooltip"
              title="Last 2 Weeks Consultations"
            >
              Recent Consultations <BsFillInfoCircleFill color="#8107d1" />
            </p>
            <p className="text-center purple-num mb-2">
              {recent_consultations}
            </p>
          </div>
          <div className="col-sm-4">
            <p className="text-center sub-heading mt-1 mb-1">
              Total Consultations
            </p>
            <p className="text-center purple-num mb-2">{total_consultations}</p>
          </div>
        </div>
      </div>
      <div className="row mt-4">
        <p className="heading mb-1">Total Appointment Request Rate</p>
        <div className="col-sm-4 pink-bg">
          <div className="pink-bg chartpadding">
            <Doughnut data={chartData} />
          </div>
        </div>
        <div className="col-sm-8">
          <div className="custom-border pink-bg">
            <div className="row">
              <p className="text-center sub-heading mb-1 mt-1">
                Appointment Consultations
              </p>
            </div>
            <div className="bg-white p-2 mb-1">
              <div className="row">
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Completed
                  </p>
                  <p className="text-center consultation-completed-count mb-0">
                    {total_completed_consultations}
                  </p>
                </div>
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Upcoming
                  </p>
                  <p className="text-center purple-count mb-0">
                    {total_upcoming_consultations}
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Unattended
                  </p>
                  <p className="text-center consultation-unattended-count mb-0">
                    {total_unattended_consultations}
                  </p>
                </div>
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Rescheduled
                  </p>
                  <p className="text-center consultation-recheduled-count mb-0">
                    {total_rescheduled_consultations}
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col-sm-12">
                  <p className="text-center mb-0 fw-light">Cancelled</p>
                  <p className="text-center consultation-cancelled-count mb-0">
                    {total_cancelled_consultations}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientsListStats;
