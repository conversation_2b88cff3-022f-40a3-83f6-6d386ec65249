"use client";
import React, { useEffect, useState } from "react";
import { FiSettings } from "react-icons/fi";
import { LuBell } from "react-icons/lu";
import MyProfile from "../AllHead/MyProfile";
import Link from "next/link";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useParams, usePathname, useSearchParams } from "next/navigation";
import { capitalizeFullName } from "../../utils/helperfunction";
import { useNotification } from "../../Context/NotificationContext/NotificationContext";
import { useSession } from "next-auth/react";
import { Badge, Placeholder } from "react-bootstrap";
import BellIconNotification from "../BellIconNotification/BellIconNotification";
const PatientHead = () => {
  const [patientsData, setPatientsData] = useState({});
  const [loading, setLoading] = useState(true);
  const axiosAuth = useAxiosAuth();
  let params = useParams();

  const email = params.user_id[1];
  const patientName = decodeURIComponent(params.user_id[3]);
  const patientStatus = decodeURIComponent(params.user_id[2]);
  const patientId = decodeURIComponent(params.user_id[0]);
  const { totalNotifications, notificationLoading } = useNotification();
  const { data: session } = useSession();
  const authenticated = session?.status === "authenticated";
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const toggleNotificationPopup = () => {
    setIsNotificationOpen((prevState) => !prevState); // Toggle the visibility of the notification popup
  };

  useEffect(() => {
    if (email) {
      const fetchUserData = async () => {
        try {
          const response = await axiosAuth.get(
            `${process.env.NEXT_PUBLIC_GET_PATIENT_DATA}${email}/`
          );
          setLoading(false);
          setPatientsData(response?.data?.user_data);
        } catch (error) {
          console.error(error);
        }
      };
      fetchUserData();
    } else {
      return;
    }
  }, [email, axiosAuth]);

  return (
    <>
      <div className="row mt-3">
        <div className="col-sm-9">
          {/* <p className="main-purple-text">User Management {">"} Patient</p> */}
          <p className="main-purple-text">
            <Link className="href_link" href="/usermanagement/experts">
              User Management {">"}{" "}
            </Link>
            <Link className="href_link" href="/usermanagement/patients">
              Patients {">"}{" "}
            </Link>
            <span className=" ms-1">
              {loading === false ? (
                <Link
                  className="href_link"
                  href={`/usermanagement/patients/${patientId}/${email}/${patientStatus}/${patientName}`}
                >
                  {capitalizeFullName(patientName)}
                </Link>
              ) : (
                <>
                  <Placeholder as="span" animation="glow">
                    <Placeholder xs={1} />
                  </Placeholder>
                </>
              )}
            </span>
          </p>
        </div>
        <div className="col-sm-2">
          <MyProfile />
        </div>
        <div className="col-sm-1 d-flex justify-content-around align-items-center">
          <Link className=" text-decoration-none" href={"/profilesetting"}>
            <FiSettings className="icon-setting" />
          </Link>

          <span className="nav-item d-none d-xl-block">
            <span
              onClick={(e) => {
                e.stopPropagation(); // Stop the propagation of the click event
                toggleNotificationPopup(); // Toggle the notification popup
              }}
              id="notification-link"
              style={{
                position: "relative",
                display: "inline-block",
              }}
            >
              <span className="btn btn-bell">
                <LuBell className="icon-bell" cursor={"pointer"} />
                <span className="start-100 translate-middle badge rounded-pill text-center">
                  {totalNotifications ? totalNotifications : 0}
                  <span className="visually-hidden">unread messages</span>
                </span>
              </span>

              {authenticated && (
                <>
                  {notificationLoading ? (
                    <Placeholder
                      as="div"
                      animation="glow"
                      className="notification-badge-placeholder"
                      style={{ width: "20px" }}
                    >
                      <Placeholder
                        xs={12}
                        size={"sm"}
                        style={{
                          height: "20px",
                          borderRadius: "5px",
                        }}
                      />
                    </Placeholder>
                  ) : (
                    <Badge bg="danger" className="notification-badge">
                      {/* {totalNotifications > 100 ? "100+" : totalNotifications} */}
                      {totalNotifications ? totalNotifications : 0}
                    </Badge>
                  )}
                </>
              )}
            </span>
          </span>
          {isNotificationOpen && (
            <BellIconNotification
              isNotificationOpen={isNotificationOpen}
              onClose={toggleNotificationPopup}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default PatientHead;
