import Image from "next/image";
import React, { useCallback, useEffect, useState, useContext } from "react";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import {
  capitalizeFullName,
  formatDate,
  timeDifference,
} from "../../../utils/helperfunction";
import PodcastPlaceholder from "./PodcastPlaceholder";
import { AdminDetailsContext } from "../../../Context/AdminContext/AdminContext";
import AddPodcastForSelectedExpert from "../../contentManagement/ExpertProfileContent/AddPodcastForSelectedExpert";
import NoDataFound from "@/components/noDataFound/NoDataFound";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { FaVideoSlash } from "react-icons/fa6";

const AllPodcasts = () => {
  const [loading, setLoading] = useState(true);
  const [podcasts, setPodcasts] = useState([]);
  const [noDataError, setNoDataError] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [addPodcastModal, setAddPodcastModal] = useState(false);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const axiosAuth = useAxiosAuth();
  const { session } = useContext(AdminDetailsContext);
  const admin_id = session?.user.id;

  const fetchAllPodcasts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERT_PODCASTS}all/?page=${current_page}`
      );
      const items = response?.data?.items;
      const total_Pages = response?.data?.total_pages;
      setPodcasts(items);
      setTotalPages(total_Pages);
      setNoDataError(false);
    } catch (error) {
      console.log(error, "podcast error");
      setNoDataError(true);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, current_page]);

  useEffect(() => {
    fetchAllPodcasts();
  }, [fetchAllPodcasts, current_page]);

  if (loading) {
    return <PodcastPlaceholder />;
  }


  const handleSelectExpert = (expert) => {
    setSelectedDoctor(expert);
    setAddPodcastModal(true);
  };

  return (
    <>
      <div className="overflow-hidden">
        <div
          className={`${
            Array.isArray(podcasts) && podcasts > 0
              ? "overflow-auto allApproval-tab-scroll"
              : ""
          }`}
        >
          <div className="row ">
            {noDataError ? (
              <div className=" mt-5 custom-margin-nodatafoud">
                <NoDataFound />
              </div>
            ) : podcasts && Array.isArray(podcasts) && podcasts.length > 0 ? (
              podcasts?.map((item) => (
                <div key={item?.id} className="col-sm-6 p-2">
                  <div className="d-flex justify-content-evenly align-items-center upload-reviews">
                    <div className="">
                      {item?.expert_profile_photo ? (
                        <Image
                          src={`${item?.expert_profile_photo}`}
                          width={40}
                          height={40}
                          alt="profile-image"
                          className="allApproval-image"
                        />
                      ) : (
                        <Image
                          src={dummyProfile}
                          width={40}
                          height={40}
                          alt="profile-image"
                          className="allApproval-image"
                        />
                      )}
                    </div>
                    <div className="p-0">
                      <p
                        className="allApproval-expert-name mb-0"
                        style={{ fontSize: "12px" }}
                      >
                        {item?.expert_name}
                      </p>
                      <p className="allApproval-expert-role mb-0">
                        {capitalizeFullName(
                          item?.expert_role ? item?.expert_role : "Expert Role"
                        )}
                      </p>
                    </div>

                    <div className="fw-semibold col-sm-4 mb-0 ">
                      <p className="mb-0" style={{ fontSize: "12px" }}>
                        {" "}
                        {item?.PodcastTopic}
                      </p>
                    </div>
                    <div className="">
                      <p
                        style={{ fontSize: "13px" }}
                        className="mb-0 allapproval-podcast-formatedate"
                      >
                        {formatDate(item?.PodcastDate)}
                      </p>
                      <div
                        style={{ fontSize: "12px" }}
                        className="allapproval-podcast-time-difference"
                      >
                        <p className="custom-transperent-btn mb-0">
                          {timeDifference(item.PodcastDate)}
                        </p>
                      </div>
                    </div>
                    <div className="">
                      <button
                        className="border-0 all-podcast-pushlish-button px-4 rounded py-2"
                        onClick={() => handleSelectExpert(item)}
                      >
                        Publish
                      </button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
                <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                  <span style={{ color: "#8107d1" }}>
                    <FaVideoSlash style={{ fontSize: "20px" }} />
                  </span>{" "}
                  No other podcasts available for approval.
                </h3>
              </div>
            )}
          </div>
        </div>
      </div>
      <div>
        {totalPages > 1 && (
          <CustomPagination
            total_pages={totalPages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        )}
      </div>
      {addPodcastModal && (
        <AddPodcastForSelectedExpert
          addPodcastModal={addPodcastModal}
          selectedDoctor={selectedDoctor}
          setAddPodcastModal={setAddPodcastModal}
          setSelectedDoctor={setSelectedDoctor}
          fetchAllPodcasts={fetchAllPodcasts}
          from="allApprovalsPodcast"
        />
      )}
    </>
  );
};

export default AllPodcasts;
