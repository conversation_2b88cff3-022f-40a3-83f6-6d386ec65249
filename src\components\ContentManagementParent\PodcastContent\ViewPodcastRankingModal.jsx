import React from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import { SiPodcastaddict } from "react-icons/si";
import { AiFillEye } from "react-icons/ai";
import ReactPlayer from "react-player";
import { capitalizeFullName } from "../../../utils/helperfunction";
import dummyVideoThumbnail from "../../../../public/images/blackscreen.jpg";

const ViewPodcastRankingModal = ({
  showPodcastModal,
  setShowPodcastModal,
  singlePodcast,
}) => {
  return (
    <div>
      <Modal
        show={showPodcastModal}
        onHide={() => {
          setShowPodcastModal(false);
        }}
        size="lg"
        centered
        backdrop="static"
      >
        <Modal.Header>
          <Modal.Title style={{ color: "#8107d1" }}>
            Podcasts for{" "}
            <span className="fw-semibold">
              {singlePodcast?.expert_details?.expert_name &&
                capitalizeFullName(singlePodcast?.expert_details?.expert_name)}
            </span>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="overflow-hidden">
            <div
              className="overflow-auto"
              style={{ maxHeight: "650px", padding: "30px" }}
            >
              {/* {loading ? (
                <>{skeletonLoader("200px", "15px", 1)}</>
              ) : ( */}
              <>
                <div className="mb-2">
                  <div className="row">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <div className="d-flex mb-2">
                        <SiPodcastaddict color="#8107D1" size={30} />
                        &nbsp;
                        <p className="ps-2 mb-0">
                          {singlePodcast?.PodcastTopic}
                        </p>
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-sm-12 mb-4">
                        <ReactPlayer
                          url={
                            singlePodcast?.PodcastURL
                              ? singlePodcast?.PodcastURL
                              : "none"
                          }
                          light={
                            singlePodcast.ThumbnailImage !== null ||
                            singlePodcast.ThumbnailImage === ""
                              ? singlePodcast.ThumbnailImage
                              : dummyVideoThumbnail
                          }
                          controls
                          onError={(e) => console.error("onError", e)}
                        />
                      </div>
                    </div>
                    <div className="row">
                      <div className=" mb-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <p className="mb-0">
                            <span className="fw-bold"> Category:</span>{" "}
                            {singlePodcast?.PodcastCategoryVal
                              ? singlePodcast?.PodcastCategoryVal
                              : "No category available"}
                          </p>
                          <p className="float-end mt-3 d-flex justify-content-center align-items-center fw-bold">
                            <AiFillEye color="#8107d1" className="fs-5" />{" "}
                            &nbsp;
                            {singlePodcast.PodcastViews}
                            &nbsp; Views
                          </p>
                        </div>
                        <p className="mb-0 text-justify">
                          <span className="fw-bold"> Description: </span>
                          {singlePodcast?.PodcastDescription
                            ? singlePodcast?.PodcastDescription
                            : "No description available"}
                        </p>
                      </div>
                    </div>
                    <span className="fw-bold"> Timestamps: </span>
                    <div
                      style={{ maxHeight: "190px" }}
                      className="single-podcast-timestamp overflow-y-auto overflow-x-hidden"
                    >
                      {singlePodcast?.Platforms?.youtube?.timestamps.length >
                      0 ? (
                        <div className="py-4">
                          {singlePodcast?.Platforms?.youtube?.timestamps
                            ?.filter(
                              (timestamp) => timestamp?.time && timestamp?.title
                            ) // Filter out timestamps without time or title
                            ?.map((timestamp, index) => (
                              <div
                                key={index}
                                className="single-podcast-timestamps row mb-2"
                              >
                                <span className="col-2 time-stamp">
                                  {timestamp?.time}
                                </span>
                                <span className="col-10 timestamp-title">
                                  {timestamp?.title}
                                </span>
                              </div>
                            ))}
                        </div>
                      ) : (
                        <h1>No timestamps available</h1>
                      )}
                    </div>
                  </div>
                </div>
              </>
              {/* )} */}
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            onClick={() => {
              setShowPodcastModal(false);
            }}
          >
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ViewPodcastRankingModal;
