"use client";
import React, { useState, useEffect, useContext } from "react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { AdminDetailsContext } from "../../../Context/AdminContext/AdminContext";
import dummyVideoThumbnail from "../../../../public/images/blackscreen.jpg";
import "./podcastpreview.css";
import ReactPlayer from "react-player";
import { toast } from "react-toastify";
import Image from "next/image";
import { formatDate } from "../../../utils/helperfunction";
import { AiFillEye } from "react-icons/ai";
import { FaPodcast, FaSpotify, FaYoutube } from "react-icons/fa6";
const getCategoryIdByName = (categories, categoryName) => {
  if (!categoryName) {
    return null;
  }

  const filteredCategories = categories?.filter(
    (cat) => cat.Category === categoryName
  );

  return filteredCategories.length > 0 ? filteredCategories[0].id : null;
};
const PodcastPreview = ({
  selectedPodcastDetails = {},
  fetchPodcastList,
  setShowPodcastPreview,
}) => {
  const {
    PodcastURL = "",
    PodcastTopic = "",
    PodcastStatus = "",
    ThumbnailImage = "",
    PodcastDate = "",
    PodcastCategory = "",
    PodcastDescription = "",
    PodcastViews = "",
    id = "",
    Platforms = {},
    PodcastSectionName = "",
  } = selectedPodcastDetails;
  const { session } = useContext(AdminDetailsContext);
  const [url, setUrl] = useState(PodcastURL || "");
  const [inputUrl, setInputUrl] = useState(PodcastURL || "");
  const [thumbnail, setThumbnail] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState(
    ThumbnailImage || ""
  );
  const [editable, setEditable] = useState(false);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(
    PodcastCategory || ""
  );
  const [title, setTitle] = useState(PodcastTopic || "");
  const [description, setDescription] = useState(PodcastDescription || "");
  const axiosAuth = useAxiosAuth();
  const admin_id = session?.user?.id;

  // Fetch categories when the component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_POST_PODCAST_CATEGORY}`
        );
        if (response.status === 200) {
          setCategories(response.data);
        }
      } catch (error) {
        console.error("Error fetching categories: ", error);
      }
    };

    fetchCategories();
  }, [axiosAuth]);

  // Update local state when selectedPodcastDetails changes
  useEffect(() => {
    setUrl(PodcastURL || "");
    setInputUrl(PodcastURL || "");
    setThumbnail(null);
    setThumbnailPreview(ThumbnailImage || "");
    setTitle(PodcastTopic || "");
    setDescription(PodcastDescription || "");
    setSelectedCategory(PodcastCategory || "");
  }, [
    selectedPodcastDetails,
    PodcastTopic,
    PodcastURL,
    PodcastDescription,
    ThumbnailImage,
    PodcastCategory,
    Platforms,
    PodcastSectionName,
  ]);

  const handleInputUrlChange = (event) => {
    setInputUrl(event.target.value);
  };

  const handleThumbnailChange = (event) => {
    const file = event.target.files[0];
    setThumbnail(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setThumbnailPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
  };

  const handleTitleChange = (event) => {
    setTitle(event.target.value);
  };

  const handleDescriptionChange = (event) => {
    setDescription(event.target.value);
  };

  const handleSubmitPodcast = async () => {
    try {
      const formData = new FormData();
      formData.append("PodcastURL", inputUrl);
      formData.append("PodcastTopic", title);
      formData.append("PodcastDescription", description);
      // formData.append("PodcastCategory", selectedCategory);
      let categoryID = getCategoryIdByName(categories, selectedCategory);
      if (categoryID) {
        formData.append("PodcastCategoryVal", categoryID);
      } else {
        toast.error("Invalid category selected");
        return;
      }
      if (thumbnail) {
        formData.append("ThumbnailImage", thumbnail);
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_PUBLISH_EXPERTS_PODCAST_REQUEST}${id}/?user_id=${admin_id}`,
        formData
      );
      toast.success(`Podcast Updated Successfully!`);
      setUrl("");
      setInputUrl("");
      setThumbnail(null);
      setThumbnailPreview("");
      fetchPodcastList();
      setShowPodcastPreview(false);
    } catch (e) {
      console.log(e, "error in updating the podcast");
    }
  };

  const handleUploadClick = () => {
    setUrl(inputUrl);
  };

  return (
    <div className="mb-5">
      <div className="title-view-count-section d-flex justify-content-between align-items-center px-3">
        <h4 className="text-center podcast-preview-section mt-3 float-start mb-0">
          Podcast Preview 
        </h4>
        <p className="float-end  mt-3 d-flex justify-content-center align-items-center fw-bold mb-0">
          <AiFillEye color="#8107d1" className="fs-5 " /> &nbsp;
          {PodcastViews}
          &nbsp; Views
        </p>
        <span className="featured-category mt-3">🌟 {PodcastSectionName}</span>
      </div>
      <div className="p-3 fw-medium fs-small">
        <div className="preview-podcast-thumbnail gap-2 mb-3">
          {(ThumbnailImage || thumbnailPreview) && (
            <div className="thumbnail-preview">
              <Image
                src={thumbnailPreview || ThumbnailImage}
                alt="Thumbnail Preview"
                width={200}
                className=" thumbnail-preview "
                height={200}
              />
            </div>
          )}
          <div className="">
            <p className="text-left custom-font-size">
              <span className="podcast-preview-title">Title - </span>
              {title || "No title available"}
            </p>

            <p className="col-auto custom-font-size">
              <span className="podcast-preview-title">Posted on - </span>
              {PodcastDate ? formatDate(PodcastDate) : "No date available"}
            </p>

            <div className="d-flex justify-content-between align-items-center mt-3">
              <div className="">
                {Platforms?.apple_podcast?.url && (
                  <a
                    className="text-decoration-none me-3"
                    href={Platforms.apple_podcast.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <button className="podcast-click-icons border-0 bg-light">
                      <FaPodcast
                        style={{
                          color: "#B150E2",
                          fontSize: "20px",
                        }}
                      />
                    </button>
                  </a>
                )}

                {Platforms?.youtube?.url && (
                  <a
                    className="text-decoration-none me-3"
                    href={Platforms.youtube.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <button className="podcast-click-icons border-0 bg-light">
                      <FaYoutube
                        style={{
                          color: "#ff0000",
                          fontSize: "20px",
                        }}
                      />
                    </button>
                  </a>
                )}
                {Platforms?.spotify?.url && (
                  <a
                    className="text-decoration-none"
                    href={Platforms.spotify.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <button className="podcast-click-icons border-0 bg-light">
                      <FaSpotify
                        style={{
                          color: "#1DB954",
                          fontSize: "20px",
                        }}
                      />
                    </button>
                  </a>
                )}
              </div>
              
            </div>
          </div>
        </div>
        <div className="">
          <div className="col-auto custom-font-size">
            <span className="podcast-preview-title me-2">Category - </span>
            {selectedCategory || "No category available"}
          </div>
          <p className="text-left custom-font-size">
            <span className="podcast-preview-title">Description - </span>
            {description || "No description available"}
          </p>

          {Platforms?.youtube?.transcript ? (
            <>
              <span className="podcast-preview-title custom-font-size">Transcrption - </span>
              <div className="transcription-container">
                <p className="transcription-text">
                  {Platforms.youtube.transcript}
                </p>
              </div>
            </>
          ) : (
            <p className="text-muted">No transcription available</p>
          )}

          {/* Mapping timestamps below the transcription */}
          {Platforms?.youtube?.timestamps?.length > 0 && (
            <div className="timestamps-container ">
              <p className="podcast-preview-title custom-font-size mb-1">⏳ Timestamps :</p>
              <ul className="timestamps-list">
                {Platforms.youtube.timestamps.map((item, index) => (
                  <li key={index} className="timestamp-item">
                    <strong>{item.time}</strong> - {item.title}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* <div className="d-flex justify-content-center  g-0 rounded overflow-hidden mb-3">
        <ReactPlayer
          light={ThumbnailImage ? ThumbnailImage : dummyVideoThumbnail}
          url={inputUrl ? inputUrl : url}
          controls
          onError={(e) => console.error("onError", e)}
          className="rounded"
        />
      </div> */}

        {/* <div className="update-podcast-thumbnail mb-3">
          <input
            type="file"
            className="form-control custom-podcast-input-control"
            onChange={handleThumbnailChange}
            placeholder="Update thumbnail"
            style={{ height: "100%", borderRadius: "10px" }}
          />
        </div> */}
      </div>
      {/* <div className="podcast-submit-section">
        <input
          type="url"
          placeholder="Enter video URL"
          value={inputUrl}
          className="form-control custom-podcast-input-control"
          onChange={handleInputUrlChange}
        />

        <button
          className="podcast-upload-btn"
          onClick={!url ? handleUploadClick : handleSubmitPodcast}
        >
          {inputUrl ? "Submit" : PodcastStatus === 1 ? "Upload" : "Update"}
        </button>
        {PodcastStatus === 2 && (
          <button
            className="btn btn-secondary ms-2"
            onClick={() => setEditable(!editable)}
          >
            {editable ? "Cancel" : "Edit"}
          </button>
        )}
      </div> */}
    </div>
  );
};

export default PodcastPreview;
