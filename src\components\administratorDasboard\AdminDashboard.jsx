import React, { useCallback, useEffect, useState } from "react";
import "../../components/administratorDasboard/home.css";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import { Doughn<PERSON>, Line } from "react-chartjs-2";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import Loading from "../Loading/PageLoading/Loading"; // Assume this is your loading component
import HomeStatistics from "./HomeStatistics";
import AppointmentsInformation from "./AppointmenstInformation";
import HomeRegistration from "./HomeRegistrations";
import SearchAll from "./SearchAll";
import Home from "../../components/administratorDasboard/Home";
import { Placeholder } from "react-bootstrap";
import { toast } from "react-toastify";

// Register chart.js components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title
);

const monthLabels = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const AdminDashboard = () => {
  const [selectedExpertise, setSelectedExpertise] = useState("");
  const [homeData, setHomeData] = useState({
    user_information: {},
    appointment_information: {},
    recent_appointments: { recent_appointments: [] },
    registration_info: {},
    search_user_data: [],
  });
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState([]);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [chartLoading, setChartLoading] = useState(true);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  const fetchChartsData = useCallback(async () => {
    try {
      setChartLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_CHART_DATA}${currentYear}/all/?user_id=${admin_id}`
      );
      const fetchedData = response?.data;

      const mappedData = Array(12).fill(null);

      fetchedData.forEach((item) => {
        const month = parseInt(Object.keys(item)[0], 10) - 1; // Month as index (0-based)
        const value = parseInt(Object.values(item)[0], 10);
        mappedData[month] = value !== 0 ? value : null; // Plot non-zero values only
      });

      setChartData(mappedData);
    } catch (err) {
      console.log("Error in getting the chart data", err);
      toast.error("Somthing went wrong. Please try again...");
    } finally {
      setChartLoading(false);
    }
  }, [admin_id, axiosAuth, currentYear]);

  const fetchHomeData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_HOMEPAGE_DATA}name=${selectedExpertise}&user_id=${admin_id}`
      );
      setHomeData(response?.data);
    } catch (err) {
      console.log("Error in getting the home data", err);
      toast.error("Somthing went wrong. Please try again...");
    } finally {
      setLoading(false);
    }
  }, [admin_id, axiosAuth, selectedExpertise]);

  useEffect(() => {
    if (admin_id && currentYear) {
      fetchChartsData();
      fetchHomeData();
    }
  }, [fetchHomeData, fetchChartsData, admin_id, currentYear]);

  const handleYearChange = (e) => {
    setLoading(true); // Start loading when year changes
    setCurrentYear(e.target.value);
    setChartData([]); // Clear the previous chart data while loading new data
  };

  const lineChartData = {
    labels: monthLabels,
    datasets: [
      {
        label: `Revenue`,
        data: chartData,
        borderColor: "#8107D1",
        tension: 0.5,
        pointRadius: 6,
        borderWidth: 3,
        pointHoverRadius: 8,
        pointBorderWidth: 2,
        pointHoverBorderWidth: 2,
        borderDashOffset: 0,
        pointBackgroundColor: "white",
        spanGaps: true,
      },
    ],
  };

  const chartOptions = {
    scales: {
      x: {
        grid: {
          display: false,
        },
        title: {
          display: true,
          text: "Monthly",
          color: "#5B5B5B",
          font: {
            size: 16,
            weight: "600",
          },
        },
        ticks: {
          font: {
            size: 14,
          },
        },
      },
      y: {
        grid: {
          display: true,
          color: "#E3E3E3",
          lineWidth: 1,
          borderDash: [2, 2],
        },
        title: {
          display: true,
          text: "Revenue ($)",
          color: "#5B5B5B",
          font: {
            size: 16,
            weight: "600",
          },
        },
        ticks: {
          callback: function (value) {
            return `$ ${value}`;
          },
          font: {
            size: 14,
          },
        },
      },
    },
    plugins: {
      legend: {
        display: false,
        position: "top",
        labels: {
          color: "#8107d1",
          font: {
            size: 24,
            weight: "bold",
          },
        },
        title: {
          color: "black",
          fontWeight: "bold",
          fontSize: 20,
        },
        backgroundColor: "#8107d1",
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem) {
            let label = tooltipItem.formattedValue;
            return `Revenue : $ ${label}`;
          },
        },
      },
    },
  };

  const {
    user_information = {},
    appointment_information = {},
    recent_appointments = {},
    registration_info = {},
    search_user_data = [],
  } = homeData || {};

  const handleSearchChange = (e) => {
    setSelectedExpertise(e.target.value);
  };

  return (
    <div className="dashboard-bg">
      <div className="row">
        <div className="col-sm-12">
          <div className="row mt-3">
            <div className="col-sm-3">
              <p className="main-purple-text">Administrator Dashboard</p>
            </div>
            <SearchAll
              setSelectedExpertise={setSelectedExpertise}
              loading={loading}
              selectedExpertise={selectedExpertise}
            />
            <Home />
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-sm-12">
          <p className="heading">User Data</p>
        </div>
      </div>

      <div className="row">
        <div className="col-sm-6">
          <HomeStatistics
            search_user_data={search_user_data}
            user_information={user_information}
            loading={loading}
          />
          <AppointmentsInformation
            appointment_information={appointment_information}
            recent_appointments={recent_appointments}
            loading={loading}
          />
        </div>
        <div className="col-sm-6">
          <div className="chart-section-background">
            <div className="year-navigation mt-2">
              <div className="chart-title fs-5">Total Revenue</div>
              <div className="year-select-chart">
                <select
                  id="year-select"
                  value={currentYear}
                  onChange={handleYearChange}
                  className="select-chart-year"
                >
                  <option>Select Year</option>
                  {Array.from({ length: 28 }, (_, i) => 2023 + i).map(
                    (year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    )
                  )}
                </select>
              </div>
            </div>

            {chartLoading  ? (
              <div className="loading-chart">
                <Placeholder as="p" animation="glow">
                  <Placeholder
                    xs={12}
                    size={"lg"}
                    style={{ height: "300px", borderRadius: "5px" }}
                  />
                </Placeholder>
              </div>
            ) : (
              <div className="p-5">
                <Line data={lineChartData} options={chartOptions} />
              </div>
            )}
          </div>

          <HomeRegistration
            loading={loading}
            registration_info={registration_info}
          />
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
