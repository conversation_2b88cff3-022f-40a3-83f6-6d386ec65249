"use client";
import React from "react";
import "../../components/userManagement/usermanagement.css";
import Navbar from "../../components/navbar/Navbar.jsx";
import UserManagementComp from "../../components/userManagement/UserManagement";
import Home from "../../components/administratorDasboard/Home";
import "../../components/administratorDasboard/home.css";
import withAuth from "../../withAuth/withAuth.js";

const Usermanagement = () => {
  return (
    <div className="row">
      <div className="col-sm-1 navbar-parent">
        <Navbar />
      </div>
      <div className="col-sm-11">
        <div className="row">
          <div className="col-sm-12">
            <div className="row mt-3">
              <div className="col-sm-8">
                <p className="main-purple-text">User Management</p>
              </div>
              <Home />
            </div>
          </div>
        </div>
        <UserManagementComp />
      </div>
    </div>
  );
};

export default withAuth(Usermanagement);
