import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { IoClose } from "react-icons/io5";
import { convertDateFormat } from "../../../utils/helperfunction";
import { toast } from "react-toastify";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";

const FeedbackModal = ({
  showFeedbackModal,
  setShowFeedbackModal,
  fetchFeedback,
  singleFeedback,
  showButtons,
  admin_id,
}) => {
  const axiosAuth = useAxiosAuth();

  const handleFeedbackApprovals = async (status, blog_id) => {
    let blog_status = status === "approve" ? 1 : 0;

    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_APPROVE_REJECT_FEEDBACK}${blog_id}/?user_id=${admin_id}`,
        { status: blog_status }
      );

      if (response?.data?.status === 1) {
        toast.success(`Feedback Approved.`);
      } else if (response?.data?.status === 0) {
        toast.error(`Feedback Rejected.`);
      }
      setShowFeedbackModal(false);
      fetchFeedback();
    } catch (error) {
      console.error(error);
      toast.error(`An error occurred. Please try again.`);
    }
  };

  const handleClose = () => setShowFeedbackModal(false);

  return (
    <Modal
      show={showFeedbackModal}
      onHide={handleClose}
      centered
      size="lg"
      dialogClassName="custom-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title style={{ color: "#8107D1" }}>
          Feedback - {singleFeedback?.id}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <h5>{singleFeedback?.FeedbackCategory}</h5>
        <div
          className="d-flex px-2"
          style={{ color: "#8107D1", fontWeight: "bold" }}
        >
          <p className="text-capitalize">{singleFeedback?.patient_name}</p>
          <p className="ms-auto">
            {convertDateFormat(singleFeedback?.CurrentTime?.split("T")[0])}
          </p>
        </div>
        <div>
          <p>{singleFeedback?.Feedback}</p>
        </div>
      </Modal.Body>
      {showButtons && (
        <Modal.Footer>
          <Button
            variant="danger"
            onClick={() =>
              handleFeedbackApprovals("reject", singleFeedback?.id)
            }
            className="rounded-1"
          >
            Reject
          </Button>
          <Button
            variant="success"
            onClick={() =>
              handleFeedbackApprovals("approve", singleFeedback?.id)
            }
            className="ms-2"
          >
            Approve
          </Button>
        </Modal.Footer>
      )}
    </Modal>
  );
};

export default FeedbackModal;
