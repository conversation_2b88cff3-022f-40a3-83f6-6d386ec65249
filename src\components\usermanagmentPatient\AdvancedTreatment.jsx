import React, { useRef, useState } from "react";
import { Form, InputGroup, Button } from "react-bootstrap";
import { ImAttachment } from "react-icons/im";
import { HiFlag, HiOutlineFlag } from "react-icons/hi2";
import { IoChevronBack } from "react-icons/io5";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { convert_time, formatNameInitials } from "../../utils/helperfunction";
import { FaFileUpload } from "react-icons/fa";

const AdvancedTreatment = ({
  title,
  summary,
  time,
  ticketId,
  handleShowAllTickets,
  threadData,
  ticketStatus,
}) => {
  const fileInputRef = useRef(null);
  const [selectedFileName, setSelectedFileName] = useState(null);

  const handleAttachmentClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];

    setSelectedFileName(selectedFile ? selectedFile.name : null);
  };
  const [flagFilled, setFlagFilled] = useState(false);

  const handleFlagClick = ({ title, summary }) => {
    setFlagFilled((prevFlagFilled) => !prevFlagFilled);
  };
  return (
    <>
      <div className="col-sm">
        <div className="queiries-lines">
          <div className="ticket_heading">
            {/* <div>
              <button
                onClick={handleShowAllTickets}
                className="ticket_back_button"
              >
                <IoChevronBack color={"#8107d1"} size={20} /> Back
              </button>
            </div> */}
          </div>
          <div className="ticket_id">
            <span className="span_ticket_id">Ticket ID</span> - #{ticketId}
          </div>
          <span className="article-query">
            <span className="span_ticket_title">Patient Query </span>-{" "}
            {title || "Article on Advanced Treatment of cancer cells"}
          </span>
          {/* <span className="flag-advance"> */}
          <span className="query-status px-auto flag-advance">
            <span
              className={`query-status-text ${
                ticketStatus === "Open"
                  ? "bg-success"
                  : ticketStatus === "Closed"
                  ? "bg-danger"
                  : ticketStatus === "escalated"
                  ? "bg-info"
                  : ticketStatus === "onhold"
                  ? "bg-warning"
                  : ""
              }`}
            >
              {ticketStatus}
            </span>
            {/* </span> */}
            {/* {" "}
            {flagFilled ? (
              <HiFlag color="red" onClick={handleFlagClick} />
            ) : (
              <HiOutlineFlag color="gray" onClick={handleFlagClick} />
            )} */}
          </span>
          {/* <p className="article-lines">
            {" "}
            <span className="span_ticket_summary">Patient Query </span>
            {summary || "No summary"}
          </p> */}
          <div className="ticket_gen_date">{time}</div>
          {/* this is reply to the threads */}
          {/* <Form>
            <Form.Group controlId="exampleForm.ControlTextarea1">
              <Form.Control as="textarea" className="text-advance" rows={5} />
            </Form.Group>
            <div className="attach-button">
              <label htmlFor="fileInput" className="label-attach">
                <ImAttachment
                  className="icon-attach"
                  onClick={handleAttachmentClick}
                  style={{ color: "#808080" }}
                />
         
                <input
                  id="fileInput"
                  ref={fileInputRef}
                  type="file"
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                />
                Attach
              </label>

              
              {selectedFileName && <p>Selected File: {selectedFileName}</p>}
              <Button className="btn btn-reply-msg" type="button">
                Replay
              </Button>
            </div>
          </Form> */}

          {threadData && threadData.length > 0 ? (
            <div style={{ overflowY: "auto", maxHeight: "550px" }}>
              {threadData
                ?.slice()
                ?.reverse()
                ?.map((threadItem, index) => (
                  <div
                    key={index}
                    className="thread-item"
                    // style={{ marginLeft: index !== 0 ? `${2}%` : "0" }}
                  >
                    {/* #F37721 */}
                    <div className=" row">
                      <div className="thread_name_time_avatar_section">
                        <div
                          className={`thread_avatar`}
                          style={{
                            background:
                              threadItem.author_name === "Oncofit Solutions"
                                ? `#F37721`
                                : "#8107d1",
                          }}
                        >
                          <p>{formatNameInitials(threadItem.author_name)}</p>
                        </div>
                        <div className="thread_name_time">
                          <p className="mb-0">{threadItem.author_name}</p>
                          <p className="created-date-ticket">
                            {convert_time(threadItem.CreatedTime)}
                          </p>
                        </div>
                      </div>

                      <div className="thread_summary">
                        <p>{threadItem.summary}</p>
                      </div>
                      {}
                      <div className="preview-doc">
                        <FaFileUpload className="" />
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="fs-4 d-flex align-items-center justify-content-center no-Patient-tickets-found">
              <PiFolderNotchOpenFill className="PiFolderNotchOpenFill_icon" />
              &nbsp; No Threads Found
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AdvancedTreatment;
