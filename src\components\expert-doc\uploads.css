.uploads-custom-scroll {
  max-height: 750px;
}
.allApproval-tab-scroll {
  max-height: 520px;
  padding: 12px;
}
.custom-margin-nodatafoud {
  margin-top: 10%;
}
.payment-back {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  background-color: white;
}
.btn.btn-view-text,
.btn.btn-view-text:hover,
.btn.btn-view-text:active {
  color: #8107d1;
  border: none;
  font-size: 14px;
}
.btn.btn-purple-text,
.btn.btn-purple-text:hover,
.btn.btn-purple-text:active {
  color: #8107d1;
 
}
.btn.btn-delete-text,
.btn.btn-delete-text:hover,
.btn.btn-delete-text:active {
  color: red;
  border: none;
  font-size: 14px;
}
.btn.btn-red-text,
.btn.btn-red-text:hover,
.btn.btn-red-text:active {
  color: red;
 
}
.grey-text {
  color: var(--bs-secondary);
  font-size: 14px;
  font-weight: 500;
}
.custom-table {
  border-collapse: collapse; /* Ensures borders are not doubled */
}

.custom-table th,
.custom-table td {
  border: none; /* Removes borders from cells */
}

.form-control.custom-placeholder::placeholder{
  font-size: 12px;
}
.btn.allApproval-reject-btn,
.btn.allApproval-reject-btn:hover,
.btn.allApproval-reject-btn:active {
  background: #ff2e2e 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 400;
  color: #fbfbfb;
}
.btn.allApproval-approve-btn,
.btn.allApproval-approve-btn:hover,
.btn.allApproval-approve-btn:active {
  background: #04ab20 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
/* .user-management-scroll {
  max-height: 520px;
  padding: 12px;
} */
.custom-form-select {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
}
.form-select.custom-form-select:focus,
.form-control:focus {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  border-color: transparent;
}
.custom-form-select option:hover {
  background-color: red;
}
.list-of-articles,
.list-of-podcast {
  max-height: 500px;
  overflow-y: scroll;
  padding: 7px;
}

/* For Firefox */
.list-of-articles,
.list-of-podcast {
  scrollbar-color: #8107d1 "white"; /* scrollbar-track-color scrollbar-thumb-color */
  scrollbar-width: thin;
}

/* For WebKit browsers (e.g., Chrome, Safari) */
.list-of-articles::-webkit-scrollbar,
.list-of-podcast::-webkit-scrollbar {
  width: 10px; /* width of the scrollbar */
  border-radius: 5px;
}

.list-of-articles::-webkit-scrollbar-track,
.list-of-podcast::-webkit-scrollbar-track {
  background-color: white; /* color of the scrollbar track */
}

.list-of-articles::-webkit-scrollbar-thumb,
.list-of-podcast::-webkit-scrollbar-thumb {
  background-color: #8107d1; /* color of the scrollbar thumb */
}

.placeHolder_loading {
  margin-bottom: 4%;
  border-radius: 8px;
}
.content-scroll-uploads {
  max-height: 250px;
  /* overflow-y: scroll; */
}

.content-scroll-uploads::-webkit-scrollbar {
  display: none;
}
.content-scroll-articles {
  max-height: 500px;
  /* overflow-y: scroll; */
}
.content-scroll-articles::-webkit-scrollbar {
  display: none;
}

.next_image {
  border-radius: 50%;
}

.podcast-icon {
  color: #8107d1;
  size: 25;
}

.podcast-approved-requested {
  color: red;
  font-weight: 600;
}

.podcast-approved-published {
  color: #39c100;
  font-weight: 600;
  font-size: 12px;
}
.blog-approval-status-under-review {
  color: #ff7700;
  font-weight: 600;
}

.blog-approval-status-rejected {
  color: red;
  font-weight: 600;
}
.blog-approval-status-approved {
  color: #39c100;
  font-weight: 500;
  font-size: 14px;
}
.bg-color-border {
  min-height: 3%;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  opacity: 1;
  padding: 15px;
  background-color: white;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  opacity: 1;
}
.fixed-icon-size {
  font-size: 30px;
  color: #8107d1;
  flex-shrink: 0;
}

.article-image-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10;
}

.article-image-wrapper {
  margin: 0 10px;
}

.custom-small-image {
  max-width: 100px;
  max-height: 100px;
}
.search-input-focus:focus {
  border-color: transparent;
  box-shadow: none;
}

.text-scrollable {
  overflow-x: auto; /* Enable horizontal scrolling */
  white-space: nowrap; /* Prevent line breaks */
}

.text-scrollable::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Webkit browsers */
}


.light-grey-text {
  color: #6c757d; /* Adjust as needed */
}

.fixed-icon-size {
  font-size: 1.5rem; /* Adjust as needed */
}

.podcast-approved-requested {
  color: #ff971a; /* Adjust color for requested */
  font-size: 12px;
}



.uploads-blog-time-difference {
  color: #ff971a;
  font-size: small;
  font-weight: bold;
  text-align: left;
}
.uploads-blog-formatedate {
  color: #4d4d4d;
  font-size: small;
  font-weight: bold;
  text-align: right;
}

.blog-date {
  font-size: 18px;
  font-weight: 600;
  color: #8107d1;
}
