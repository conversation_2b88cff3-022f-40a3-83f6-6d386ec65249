"use client";

import React, { useEffect, useState } from "react";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FaEyeSlash, FaEye } from "react-icons/fa";
import { toast } from "react-toastify";
import Image from "next/image";
import adminLogo from "../../../public/assets/adminloginlogo.png";
import ellipseadmin from "../../../public/assets/ellipseadmin.png";
import ellipsetop from "../../../public/assets/ellipse-top.png";
import RightLogin from "./rightLogin";
import "./adminlogin.css";

const LoginForm = () => {
  const [url, setUrl] = useState("/");
  const [loading, setLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();
  const handleToggleVisibility = () => {
    setIsVisible((prev) => !prev);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const newUrl = window.location.href;
      const splitUrl = newUrl.split("returnUrl=");
      if (splitUrl.length > 1) {
        const orgUrl = splitUrl[1];
        setUrl(orgUrl);
      }
    }
  }, [router]);

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  const handleLogin = async (e) => {
    e.preventDefault();
    const { email, password } = formData;
    if (!email || !password) {
      toast.error("Email and password are required.");
      return; // Prevent further execution if fields are empty
    }
    try {
      setLoading(true);
      const response = await signIn("credentials", {
        email,
        password,
        redirect: false,
        callbackUrl: "/",
      });

      if (response?.error) {
        console.error("An error occurred during login:", response.error);
        toast.error("Login failed. Please check your credentials.", {
          autoClose: 1500,
          theme: "colored",
        });
      } else {
        toast.success("Login successful!", {
          autoClose: 1500,
          theme: "colored",
        });
        setTimeout(() => {
          url ? router.push(`${url}`) : "/";
        }, 2000);
      }
    } catch (error) {
      console.error("An error occurred during login:", error);
      toast.error("Login failed. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/*------------------------------- Login Page input fields---------------------- */}
      <div className="row">
        <div className="col-sm-6">
          <Image src={adminLogo} className="admin-logo ms-2" alt="" priority />
          <Image src={ellipsetop} className="ellipse-top" alt="" priority />
          <h5 className="login-heading">Login</h5>
          <div className="login-form">
            <form onSubmit={handleLogin}>
              <div className="form-group mt-2">
                <label htmlFor="userName" className="admin-login-label mb-1">
                  Email{" "}
                </label>
                {/* <span><button type="button" className="btn btn-primary">New User</button></span> */}
                <input
                  type="email"
                  className="form-control form-login-fields"
                  placeholder="Enter Email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  name="email"
                />
              </div>
              <div className="form-group mt-3">
                <label htmlFor="password" className="admin-login-label mb-1">
                  Password
                </label>
                <input
                  type={isVisible ? "text" : "password"}
                  className="form-control form-login-fields"
                  placeholder="Password"
                  value={formData.password}
                  onChange={handleInputChange}
                  name="password"
                  onPaste={(e) => e.preventDefault()}
                  autoComplete="off"
                />
                <div
                  className="password-toggle me-2 visible-icon"
                  onClick={handleToggleVisibility}
                >
                  {isVisible ? (
                    <FaEyeSlash size={25} color="#8107d1" cursor="pointer" /> // React Icons eye-slash
                  ) : (
                    <FaEye size={25} color="#8107d1" cursor="pointer" /> // React Icons eye
                  )}
                </div>
              </div>
              <span>
                {/* Use Link to create a navigation link styled like a button */}
                <Link href="/forgotpassword" className="btn-forgot mt-4">
                  Forgot Password ?
                </Link>
              </span>
              <button
                type="submit"
                className="btn btn-admin-login mt-4"
                disabled={loading}
              >
                {loading ? "Logging in ..." : "Login to Continue"}
              </button>
            </form>
          </div>
          <Image src={ellipseadmin} className="ellipse-down" alt="" />
        </div>
        {/* ------------------------------Login Right Template---------------------------------- */}
        <div className="col-sm-6">
          <RightLogin />
        </div>
      </div>
    </>
  );
};

export default LoginForm;
