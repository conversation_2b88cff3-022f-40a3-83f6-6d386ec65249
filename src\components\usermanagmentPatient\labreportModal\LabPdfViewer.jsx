import { getFileExtension } from '@/utils/helperfunction';
import React, { useState } from 'react';
import ModalImage from 'react-modal-image';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs`;

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

const Loader = () => {
  return (
    <div
      style={{ color: '#8107d1' }}
      className="spinner-border"
      role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  );
};

const LabPdfViewer = ({ selectedRecord }) => {
  const [numPages, setNumPages] = useState();
  const [containerWidth, setContainerWidth] = useState();
  const [error, setError] = useState();

  function onDocumentLoadSuccess({ numPages: nextNumPages }) {
    setNumPages(nextNumPages);
  }

  const onLoadError = error => {
    setError(`Error loading PDF: ${error.message}`);
  };

  return (
    <div className="row mt-3">
      {selectedRecord?.report_file?.map((file, index) => {
        const fileExtension = getFileExtension(file);

        return fileExtension === 'pdf' ? (
          <div
            className="Example border mb-2 border-bottom-4"
            key={index}>
            <div className="Example__container">
              <div className="Example__container__document"></div>
              <Document
                file={file?.split('?')[0]} // Use the file directly
                onLoadSuccess={onDocumentLoadSuccess}
                // options={options}
                onLoadError={onLoadError}
                loading={<Loader />}>
                {Array.from(new Array(numPages), (_el, index) => (
                  <Page
                    key={`page_${index + 1}`}
                    pageNumber={index + 1}
                    width={containerWidth ? Math.min(containerWidth, 800) : 800}
                  />
                ))}
              </Document>
            </div>
          </div>
        ) : ['png', 'jpg', 'jpeg', 'jfif'].includes(fileExtension) ? (
          <div className=" col-12 col-sm-3 col-md-4 gap-1">
            <ModalImage
              small={file?.split('?')[0]}
              large={file?.split('?')[0]}
              alt={`Report-${index}`}
              hideDownload={true}
              hideZoom={true}
            />
          </div>
        ) : (
          <p key={index}>Unsupported file format</p>
        );
      })}
    </div>
  );
};

export default LabPdfViewer;
