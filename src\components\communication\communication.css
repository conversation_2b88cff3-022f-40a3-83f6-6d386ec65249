span.line-of-query-4 {
  width: 36%;
  display: flex;
  align-items: center;
  margin-left: 15px;
}
.user-management-scroll {
  max-height: 618px;
  padding: 20px;
}
span.line-of-query-3 {
  width: 40%;
  display: flex;
  align-items: center;
}

.query-border-0 {
  border-left: 3px solid #5b5b5b;
  /* Adjust the color and width as needed */
}

.custom-checkbox input[type="checkbox"] {
  border: 1px solid #8107d1;
}

.custom-checkbox input[type="checkbox"]:focus {
  outline: none;
  box-shadow: none;
}

.form-check-input:checked {
  background-color: #8107d1;
  border-color: #8107d1;
}

button.btn.btn-primary.grey-btn {
  background: #dfdfdf 0% 0% no-repeat padding-box;

  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #414146;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.doctor-type {
  font-size: 14px;
  font-weight: normal;
  color: #5b5b5b;
}

.category-select {
  background: #ffffff 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
}

.appoin-with {
  font-size: 13px;
}

div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm {
  background-color: #8107d1 !important;
}

.cancer-type-input {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029 !important;
  border: 1px solid #e3e3e3 !important;
  border-radius: 3px;
  height: 48px;
}

.cancer-type-input:disabled {
  background: #ffffff 0% 0% no-repeat padding-box;
}

button.btn.btn-primary.grey-btn:active {
  background: #fae5ff 0% 0% no-repeat padding-box;

  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

button.btn.btn-primary.grey-btn:focus {
  background: #fae5ff 0% 0% no-repeat padding-box;
  /* box-shadow: inset 0px 3px 6px #00000029; */
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.activeExpertsTab {
  background: #fae5ff !important;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff !important;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1 !important;
  font-size: 14px;
  color: #5b5b5b !important;
  font-weight: 500;
}

.payment-back {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  padding: 28px;
  background-color: white;
}

.queries-background {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 615px;
}

.box-color {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 2px 3px #00000029;
  border: 1px solid #e3e3e3;
  height: 41px;
  width: 169px;
  display: flex;
  align-items: center;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
}

.queries-no-color-1 {
  color: #8107d1;
  display: flex;
  margin-left: 25%;
  /* float: right; */
  font-weight: bold;
}

.queries-no-color-5 {
  color: #b50000;
  display: flex;
  margin-left: 25%;
  /* float: right; */
  font-weight: bold;
}

.queries-no-color-2 {
  color: #5b5b5b;
  display: flex;
  margin-left: 40%;
  font-weight: bold;
}

.queries-no-color-3 {
  color: #04ab20;
  display: flex;
  margin-left: 50%;
  font-weight: bold;
}

.queries-no-color-4 {
  color: #ff971a;
  display: flex;
  margin-left: 28%;
  font-weight: bold;
}

.queries-no-color-5 {
  color: #b50000;
  display: flex;
  margin-left: 50%;
  font-weight: bold;
}

.queiries-lines {
  background: #ffffff 0% 0% no-repeat padding-box;
  padding: 10px;
}

.remove-btn-style {
  border: none;
  box-shadow: none;
  background-color: transparent;
  border-right: 1px solid #dfdfdf;
  width: 3%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
}

.line-color {
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  height: auto;
  display: flex;
  cursor: pointer;
  color: #5b5b5b;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 10px;
  padding: 5px;
}

span.urgent-query {
  width: 30%;
  border-right: 1px solid #dfdfdf;
  display: flex;
  justify-content: left;
  align-items: center;
  text-align: left;
  margin-left: 2%;
}

span.query-time {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align-last: right;
  width: 25%;
  /* border: 1px solid red; */
}

span.query-status {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align-last: right;
  font-weight: 700;
  width: 12%;
}

.query-status-text {
  border-radius: 20px;
  padding: 1px 4px;
  min-width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.query-status-text-badge {
  border-radius: 20px;
  padding: 2px 4px 2px 4px;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

span.line-of-query {
  width: 82%;
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.ticket-heading {
  font-weight: bold;
}

/* Your existing CSS styles  #b50000*/

.query-border-1 {
  border-left: 3px solid #8107d1;
  cursor: pointer;
}

.query-border-5 {
  border-left: 3px solid#b50000;
  cursor: pointer;
}

.query-border-2 {
  border-left: 3px solid #96969c;
  cursor: pointer;
}

.query-border-3 {
  border-left: 3px solid #04ab20;
  cursor: pointer;
}

.query-border-4 {
  border-left: 3px solid #ff971a;
  cursor: pointer;
}

/* Add any other styles you need */
.text-advance {
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 0%;
}

.text-advance:focus {
  box-shadow: none;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 0%;
}

button.btn.btn-reply-msg.btn.btn-primary {
  width: 159px;
  height: 48px;
  float: right;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  margin: 10px 10px 10px 10px;
}

.attach-button {
  height: 72px;
  font-size: 14px;
  font-weight: bold;
  background: #ffffff 0% 0% no-repeat padding-box;
  border-left: 1px solid #e3e3e3;
  border-right: 1px solid #e3e3e3;
  border-bottom: 1px solid #e3e3e3;
}

.icon-attach {
  font-size: large;
  margin: 26px 12px 26px 16px;
}

.article-query {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
}

.article-lines {
  color: #5b5b5b;
  /* line-height: 28px; */
  font-size: 14px;
  font-weight: normal;
  /* padding: 15px; */
}

label.label-attach {
  color: #b5b2b2;
}

.flag-advance {
  float: right;
  margin-right: 10px;
}

.filters-background {
  background: #f6f6f6;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 95px;
}

.calander-patient {
  height: 750px;
  background: #fef9ff 0% 0% no-repeat padding-box;
  border-radius: 3px;
}

span.date-filter {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  color: #5b5b5b;
  width: 255px;
  padding: 10px;
  display: inline-flex;

  margin-top: 22px;
}

.calendar-icon {
  color: #8107d1;
  margin-left: 215px;
  margin-top: -3px;
  position: absolute;
}

.select-dropdown.css-b62m3t-container {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  border: none;
  margin-top: 22px;
  margin-right: 0px !important;
}

.select-dropdown.css-b62m3t-container {
  width: 162px;
  height: 45px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  border: none;
  margin-top: 22px;
}

.col-sm-9.col-size {
  width: 80%;
}

.select-status {
  margin-top: 20px;
}

.records-files {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 126px;
  padding: 10px;
}

span.icon-file {
  font-size: 37px;
  color: #8107d1;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  /* width: 200px; */
}

.file-view-bg {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  padding: 5px;
}

button.btn.download-btn {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 5px;
}

button.btn.download-btn:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 5px;
}

button.btn.download-btn-1 {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 15px 5px 5px 5px;
}

button.btn.download-btn-1:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 15px 5px 5px 5px;
}

button.btn.view-btn-patient {
  border: none;
  color: #8107d1;
  font-weight: 600;
  font-size: 16px;
  float: right;
  padding: 16px;
}

button.btn.view-btn-patient:focus {
  border: none;
  color: #8107d1;
  font-weight: 600;
  font-size: 16px;
  float: right;
  padding: 16px;
}

.date-bold {
  color: #5b5b5b;
  font-weight: 600;
  font-size: 15px;
}

.lab-report {
  font-size: 16px;
  font-weight: normal;
  color: #5b5b5b;
}

.records-patient {
  color: #5b5b5b;
  font-size: 16px;
  font-weight: bold;
}

.filter-all {
  display: inline;
  display: flex;
  margin: 33px;
  color: #5b5b5b;
  font-weight: 600;
  font-size: 16px;
  /* justify-content: center; */
}

.icon-cal {
  color: #8107d1;
  font-weight: bold;
  font-size: 25px;
  margin-top: -3px;
}

.data-range {
  font-size: 14px;
  /* margin-left: 70px; */

  font-weight: 600;
  color: #8107d1;
  text-align: left;
}

.timeline {
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.react-calendar {
  width: 305px !important;
  font-size: 17px;
  color: #1c2126;
  font-weight: bolder;
  border: none !important;
  max-width: 100%;
  background: transparent !important;
  border: none;
  font-family: Arial, Helvetica, sans-serif !important;
}

button.btn.refresh-btn {
  text-decoration: none;
  border: none;
  color: #8107d1;
  float: right;
  font-size: 14px;
  padding: 0px;
}

button.btn.refresh-btn:focus {
  text-decoration: none;
  border: none;
  color: #8107d1;
  float: right;
}

.react-calendar__month-view__weekdays {
  letter-spacing: 0.2px;
  color: #101011;
  font-size: 11px;
  font-weight: bold;
}

.sunday-text {
  color: black;
  /* Set the default color for Sundays */
}

.sunday-text:hover {
  background-color: #000;
  /* Change this to the desired hover color for Sundays */
}

.react-calendar .react-calendar__tile--range:hover {
  background: #8107d1 0% 0% no-repeat padding-box !important;
  color: #ffffff;
  border-radius: 5px;
  /* Change this to the desired hover color for the selected range */
}

.start-date {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for start date */
}

.start-date:focus {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for start date */
}

.end-date {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for end date */
}

.end-date:focus {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for end date */
}

.selected-range {
  background-color: transparent;
  color: #8107d1;
  /* Purple for selected range */
}

.selected-range:focus {
  background-color: transparent;
  color: #8107d1;
  /* Purple for selected range */
}

.class-span {
  display: flex;
  margin-top: 10px;
}

.react-calendar__month-view__weekdays {
  color: #9fa1a3;
  font-weight: bold;
  font-size: 12px;
  border: none;
}

.react-calendar .react-calendar__tile--now {
  color: #1c2126;
  /* Change this to the desired color for the current date */
  background-color: transparent;
}

.react-calendar .react-calendar__tile--now:hover {
  color: #1c2126;
  /* Change this to the desired color for the current date */
  background-color: transparent;
}

.circle-time {
  width: 15px;
  height: 15px;
  background-color: #ff971a;
  border-radius: 50%;
  display: inline-block;
  /* margin-top: 48px; */
  margin-left: 23px;
}

.vertical-line {
  margin-top: -6px;
  position: relative;
  width: 2px;
  height: 100px;
  background-color: #dfdfdf;
  margin-left: 29px;
  /* top: 13px; */
}

.circle-end {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #ff971a;
  margin-left: 23px;
}

button.btn.btn-back {
  color: #8107d1;
  font-weight: 500;
  border: none;
  font-size: 16px;
}

button.btn.btn-back:focus {
  color: #8107d1;
  border: none;
  font-weight: 500;
  font-size: 16px;
}

.profile-picture-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.profile-picture {
  width: 150px;
  height: 150px;
  overflow: hidden;
  border-radius: 50%;
  margin-top: 20px;
}

.profile-picture img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

input.form-control.form-fildes-read {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
}

textarea.form-control.text-form-fileds {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  font-size: 14px;
}

span.medical-info {
  color: #5b5b5b;
  font-weight: bold;
  font-size: 15px;
}

button.btn.btn-view-all {
  float: right;
  text-decoration: none;
  border: none;
  color: #5b5b5b;
  font-weight: bold;
  font-size: 15px;
}

button.btn.btn-view-all:focus {
  float: right;
  text-decoration: none;
  border: none;
  color: #5b5b5b;
  font-weight: bold;
  font-size: 15px;
}

.info-bg {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 3px;
  padding: 20px;
  height: 770px;
}

.plans-bg {
  height: 103px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
}

button.btn.btn-save-changes {
  /* background: #96969c 0% 0% no-repeat padding-box; */
  background: #414146 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  height: 48px;
  /* display: flex; */
  float: right;
  color: white;
}

.profile-pic-wrapper {
  /* margin-top: 45px; */
  /* width: 100%; */
  /* position: relative;
  display: flex;
  align-items: baseline; */
}

.pic-holder {
  text-align: center;
  position: relative;
  /* border-radius: 50%; */
  /* width: 100px; */
  /* height: 100px; */
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  /* margin-bottom: 20px; */
  border: 1px solid #9889a3 !important;
  border-radius: 50%;
}

.pic-holder .pic {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
}

.pic-holder .upload-file-block,
.pic-holder .upload-loader {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(90, 92, 105, 0.7);
  color: #f8f9fc;
  font-size: 12px;
  font-weight: 600;
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.pic-holder .upload-file-block {
  cursor: pointer;
}

.pic-holder:hover .upload-file-block,
.uploadProfileInput:focus ~ .upload-file-block {
  opacity: 1;
}

.uploadProfileInput {
  padding-left: 5px !important;
}

.pic-holder.uploadInProgress .upload-file-block {
  display: none;
}

.pic-holder.uploadInProgress .upload-loader {
  opacity: 1;
}

button.btn.btn-save-changes:focus {
  background: #96969c 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  height: 48px;
  /* display: flex; */
  float: right;
}

button.btn.btn-save-changes-color-change {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  height: 48px;
  /* display: flex; */
  float: right;
  color: white;
}

.plans-taken {
  letter-spacing: 0.44px;
  color: #52575d;
  font-size: 14px;
}

.plan-taken-cost {
  letter-spacing: 0.66px;
  color: #52575d;
  font-weight: bold;
  font-size: 21px;
}

button.btn.control-profile {
  background: #414146 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #f6f6f6;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  float: right;
}

button.btn.control-profile:focus {
  background: #414146 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #f6f6f6;
  border-radius: 3px;
  border: none;
  /* height: 48px; */
  color: #ffffff;
  float: right;
}

.savebtnedit {
  box-shadow: 0px 3px 6px #00000029;
  background-color: #00000029;
}

.profile-bg-control {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* width: 90%;
    float: inline-end; */
}

span.profile-active {
  color: #8107d1;
  font-weight: bold;
  font-size: 14px;
}

span.profile-last-edited {
  color: #5b5b5b;
  font-size: 14px;
  font-weight: normal;
}

.rounded-circle {
  border-radius: 50% !important;
}

svg.editprofile {
  position: absolute;
  margin-top: 50px;
  height: 35px;
  width: 35px;
  padding: 6px;
  border-radius: 50%;
  border: 50%;
  color: #8107d1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 2px solid #dfdfdf;
  font-size: 25px;
  margin-left: -17px;
}

.form-label-style {
  font-weight: bold;
  font-size: 14px;
  color: #5b5b5b;
}

.delete-from-storage {
  float: right;
  color: #ff2e2e;
  font-weight: bold;
  font-size: 14px;
}

span.name-doctor {
  font-size: 15px;
  font-weight: normal;
  color: #8107d1;
}

.appointment-bg {
  height: 200px;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  padding: 10px;
}

.video-app {
  height: 200px;
  background: #dfdfdf 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
}

.remaing-time {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;

  background: linear-gradient(to right, #e3e3e3 60%, #414146 50%);
  border-radius: 3px;
}

.star-color {
  font-size: 14px;
  color: #f37721;
  font-weight: 600;
}

button.btn.btn-prescription {
  width: 100%;
  height: 38px;
  font-size: 14px;
  background: #fae5ff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  color: #8107d1;
}

.review-appointment {
  background: #fbfbfb;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  /* height: 138px; */
  padding: 10px;
}

.review-lines {
  /* display: flex; */
  /* justify-content: center; */
  height: 101px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px;
}

.image-container {
  position: relative;
}

.content-scroll {
  max-height: 630px;
  /* overflow-y: scroll; */
}

.content-scroll-2 {
  max-height: 400px;
  /* overflow-y: scroll; */
}

.content-scroll-2::-webkit-scrollbar {
  display: none;
}

.content-scroll::-webkit-scrollbar {
  display: none;
}

button.btn.hide-Review {
  height: 38px;
  color: #ff2e2e;
  width: 100%;
  border: none;
  font: 14px;
  background: #fff3f3 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
}

button.btn.hide-Review:focus {
  height: 38px;
  color: #ff2e2e;
  font-size: 14px;
  border: none;
  width: 100%;
  background: #fff3f3 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
}

span.timeline-1 {
  display: flex;
  padding: 49px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
  justify-content: center;
}

.no-articles-found {
  min-height: 200px;
}

.rotate {
  animation: rotation 1s infinite linear;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.ticket_id {
  color: #5b5b5b;
  /* line-height: 28px; */
  font-weight: 35px;
  font-weight: bold;
  font-size: 2rem;
  margin-bottom: 2%;
}

.ticket_back_button {
  border: none;
  background-color: transparent;
  color: #8107d1;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ticket_heading {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1%;
}

.ticket_gen_date {
  color: #8107d1;
  font-weight: bold;
  font-size: 0.8rem;
  margin-bottom: 0.6%;
}

.span_ticket_title,
.span_ticket_summary {
  margin-bottom: 1%;
}

.span_ticket_summary {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
}

.span_ticket_title {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
  font-size: 1em;
}

input.form-control.custom-form-control {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
}
.form-control.custom-form-control::placeholder {
  font-size: 12px;
}
.btn.orange-btn-communication {
  background-color: #ff971a;
  color: white !important;
  padding: 10px;
  font-size: 12px;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #ff971a;
}
.btn.orange-btn-communication:hover {
  background-color: #ff971a;
}

.rotating-refresh-icon {
  transition: transform 0.5s;
  cursor: pointer;
  color: #8107d1;
}

.rotating-refresh-icon:hover {
  cursor: pointer;
  color: #8107d1;
}

.custom_refresh {
  cursor: pointer;
  color: #8107d1;
}

.custom-form-select {
  height: 45px;
}

select#ticket_select_box {
  border: 2px solid #eeeeee;
  border-radius: 5px;
  box-shadow: none;
}

.no-Patient-tickets-found {
  height: 100%;
}

.PiFolderNotchOpenFill_icon {
  color: #8107d1;
  size: 50;
}

.thread-item {
  display: flex;
  background-color: #e9e6e6;
  border-radius: 10px;
  align-items: center;
  height: auto;
  padding: 2%;
  margin-bottom: 2%;
}

.thread_name_time_avatar_section {
  display: flex;
  justify-content: space-around;
  align-items: start;
  align-content: center;
  width: 18%;
  height: auto;
}

.thread_avatar {
  width: 75px;
  height: 50px;
  border-radius: 50%;
  background-color: #8107d1;
  color: white;
  text-align: center;
  position: relative;
}

.thread_avatar > p {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.created-date-ticket {
  font-weight: 600;
}

.thread_name_time {
  font-size: 12px;
}

.thread_summary {
  width: 65%;
}

.preview-doc {
  /* width: 6%; */
  padding: 10px;
  font-size: 30px;
  /* margin-left: auto; */
}

/* In your CSS file */
.placeHolder_loading {
  margin-bottom: 2%;
  /* background-color: #f0f0f0; */
  border-radius: 8px;
  /* Add rounded corners */
  /* 
  padding: 15px; */
}

.clear {
  cursor: pointer;
  color: #8107d1;
  font-weight: bold;
}

/* .download_appointments_records {
   
} */
.download_appointments_records_icon {
  cursor: pointer;
  color: #8107d1;
  size: 30;
}

.calender_title_selection {
  padding-top: 5%;
  display: flex;
  justify-content: space-evenly;
}

.tickets_title {
  font-size: 1.7em;
  color: #8107d1;
  font-weight: bold;
}
