import React, {
  useState,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { FaCalendar, FaTimes } from "react-icons/fa";
import Select from "react-select";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FaFile } from "react-icons/fa";
import LabReportModal from "./labreportModal/LabReportModal";
import { IoIosArrowBack } from "react-icons/io";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import CalenderFilter from "./CalenderFilter";
import { formatDateToYMD } from "../../utils/helperfunction";
import _ from "lodash";
import { Placeholder } from "react-bootstrap";
import CustomPagination from "../CustomPagination/CustomPagination";
import NoDataFound from "../noDataFound/NoDataFound";

const UserUploads = ({ email, id }) => {
  const [records, setRecords] = useState();
  const [loading, setLoading] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [ViewLabModalShow, setViewLabModalShow] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [showPicker, setShowPicker] = useState(false);
  const [downloading, setDownloading] = useState({});
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [fileType, setFileType] = useState();
  const axiosAuth = useAxiosAuth();
  const searchMenu = useRef(null);

  const renderPlaceholders = () => {
    const placeholders = Array.from({ length: 8 }, (_, index) => (
      <div key={index} className="placeHolder_loading">
        <Placeholder as="p" animation="glow">
          <Placeholder
            xs={12}
            size={"lg"}
            style={{ height: "45px", borderRadius: "4px" }}
          />
        </Placeholder>
      </div>
    ));

    return placeholders;
  };

  const fetchAllMedicalRecords = useCallback(
    async (pageNumber) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_PATIENT_UPLOADS}${email}/?`;
        if (startDate && endDate && email) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
        }
        // if (query && email) {
        //   url += `&search=${query}`;
        // }
        if (pageNumber && email) {
          url += `&page=${pageNumber}`;
        }
        if (fileType && email) {
          url += `&type=${fileType}`;
        }
        const response = await axiosAuth.get(url);
        setRecords(response?.data);
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [axiosAuth, startDate, endDate, fileType, email]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((page) => {
      fetchAllMedicalRecords(page);
    }, 500);
  }, [fetchAllMedicalRecords]);

  useEffect(() => {
    debouncedFetchData(currentPage);
  }, [debouncedFetchData, fileType, currentPage]);

  const [selectedFileOption, setSelectedFileOption] = useState();

  const fileOptions = [
    { value: "", label: "All" },
    { value: "pdf", label: "PDF" },
    { value: "png", label: "PNG" },
    { value: "jpeg", label: "JPEG" },
  ];

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handleViewButtonClick = (record) => {
    setSelectedRecord(record);
    setViewLabModalShow(true);
  };

  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      border: "none", // Remove the border
      boxShadow: "none",
      height: "44",

      paddingTop: "5px", // Remove the box shadow
      "&:hover": {
        borderColor: "none",
        height: "44",
        // Remove border color on hover
      },
    }),
    indicatorSeparator: (provided, state) => ({
      ...provided,
      display: "none", // Hide the indicator separator
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#8107d1"
        : state.isFocused
        ? "#FDF5FF"
        : null,
      border: "none",
      borderStyle: "none", // Background color of the options
      "&:hover": {
        backgroundColor: "#8107d1", // Background color on hover
        color: "#FFF", // Text color on hover
      },
      "&:active": {
        backgroundColor: "#8107d1", // Background color when active
        color: "#FFF", // Text color when active
      },
    }),
    dropdownIndicator: (provided, state) => ({
      ...provided,
      color: "#8107D1", // Initial color
      "&:hover": {
        color: "#8107D1", // Color on hover
      }, // Change the color of the dropdown icon
    }),
    menu: (provided, state) => ({
      ...provided,
      marginTop: 0, // Remove the top margin
    }),
    menuList: (provided, state) => ({
      ...provided,
      paddingTop: 0, // Remove the top padding
      paddingBottom: 0, // Remove the bottom padding
    }),
    // Add more styles as needed
  };

  const handleDateChange = (rawStartDate, rawEndDate) => {
    setStartDate(rawStartDate);
    setEndDate(rawEndDate);
    if (rawStartDate && rawEndDate) {
      setShowPicker(false);
    }
  };

  const handleFileTypeChange = (selectedOption) => {
    const selectedFileType = selectedOption.value;
    setFileType(selectedFileType);
    setSelectedFileOption(selectedFileType);
  };

  const handleDownloadPres = async (id) => {
    try {
      // setDownloading(true);
      setDownloading((prev) => ({ ...prev, [id]: true }));
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_DOWNLOAD_LAB_REPORT}${id}/`
      );
      const url = window.URL.createObjectURL(new Blob([response?.data]));

      // Create a temporary <a> element to trigger the download
      const tempLink = document.createElement("a");
      tempLink.href = url;
      tempLink.setAttribute("download", `medical-record-${id}.pdf`);

      // Append the <a> element to the body and click it to trigger the download
      document.body.appendChild(tempLink);
      tempLink.click();

      // tempLink.remove();

      // Clean up the temporary elements and URL
      document.body.removeChild(tempLink);
      window.URL.revokeObjectURL(url);
      setDownloading((prev) => ({ ...prev, [id]: false }));
    } catch (error) {
      console.error("Error downloading PDF:", error);
    } finally {
      setDownloading((prev) => ({ ...prev, [id]: false }));
    }
  };

  const handleClearFilter = () => {
    setStartDate(null);
    setEndDate(null);
    setShowPicker(false);
  };

  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  return (
    <>
      <>
        <LabReportModal
          id="lab-report"
          selectedRecord={selectedRecord}
          userEmail={email}
          show={ViewLabModalShow}
          onHide={() => setViewLabModalShow(false)}
          handleDownloadPres={handleDownloadPres}
        />
        <div className="payment-back">
          {/* <div
            // href="/usermanagement/patients"\
            onClick={() => {
              if (typeof window !== "undefined") {
                window.history.back();
              }
            }}
            className="purple-content text-decoration-none d-flex align-items-center fw-bold"
          >
            <button
              type="button"
              className="btn btn-back mb-2 d-flex align-items-center fw-bold"
              style={{ color: "#8107d1" }}
            >
              <IoIosArrowBack color="#8107d1" /> Back
            </button>
          </div> */}
          <div className=" overflow-hidden">
            <div className="user-management-scroll overflow-auto">
              <div className="row">
                <div className="col-sm-9 col-size">
                  <div className="filters-background">
                    <div className="row">
                      <div className="col-sm-6">
                        <span className="filter-all">Filters</span>
                      </div>
                      <div ref={searchMenu} className="col-sm-3 mt-4 ">
                        <div className=" ">
                          <span
                            style={{ gap: "2px" }}
                            className="date-filter d-flex w-100"
                            onClick={handleCalendarClick}
                          >
                            {startDate
                              ? `${startDate.toLocaleDateString()} - ${
                                  endDate ? endDate.toLocaleDateString() : ""
                                }`
                              : "Select Date"}
                            <span className="calendar-icon-patient ms-auto">
                              {startDate ? (
                                <FaTimes
                                  className=" cross-icon-calendar"
                                  onClick={handleClearFilter}
                                />
                              ) : (
                                <FaCalendar className=" calender-icon-calendar" />
                              )}
                            </span>
                          </span>

                          {showPicker && (
                            <div style={{ position: "relative", zIndex: 1 }}>
                              <DatePicker
                                startDate={startDate}
                                endDate={endDate}
                                selectsRange
                                inline
                                showTimeSelect={false}
                                onChange={(dates) =>
                                  handleDateChange(dates[0], dates[1])
                                }
                              />
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="col-sm-3 mt-4">
                        <Select
                          options={fileOptions}
                          onChange={handleFileTypeChange}
                          placeholder="File Type"
                          menuPlacement="bottom"
                          className="select-dropdown"
                          styles={customStyles}
                        />
                      </div>
                    </div>
                  </div>
                  <h6 className="mt-4 records-patient">Records</h6>
                  {/* <div className="overflow-auto custom-overflow"> */}
                  {initialLoading ? (
                    renderPlaceholders()
                  ) : records?.items?.length > 0 ? (
                    <div className="image-container overflow-hidden">
                      <div className="content-scroll overflow-auto">
                        {records?.items?.map((file, index) => (
                          <div className="row mt-2" key={index}>
                            <div className="col-sm-12">
                              <div className="records-files">
                                <div className="file-view-bg">
                                  <span className="icon-file">
                                    <FaFile />
                                  </span>
                                  <span className="mx-3 date-bold">
                                    {file.generation_date?.split("T")[0]}
                                  </span>
                                  {/* <span className="mx-1">
                                {
                                  file.report_file[0]
                                    ?.split("_")[1]
                                    ?.split("?")[0]
                                }
                              </span> */}
                                  <span>
                                    <button
                                      type="button"
                                      className="btn view-btn-patient"
                                      onClick={() =>
                                        handleViewButtonClick(file)
                                      }
                                    >
                                      View
                                    </button>

                                    <button
                                      onClick={() =>
                                        handleDownloadPres(file?.id)
                                      }
                                      type="button"
                                      className="btn view-btn-patient"
                                      disabled={downloading[file.id]}
                                    >
                                      {downloading[file.id]
                                        ? "Downloading.."
                                        : "Download"}
                                    </button>
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        <div className="">
                          {loading && (
                            <div>
                              <Placeholder as="p" animation="glow">
                                <Placeholder
                                  xs={12}
                                  size={"lg"}
                                  style={{
                                    height: "43px",
                                    borderRadius: "4px",
                                  }}
                                />
                              </Placeholder>
                            </div>
                          )}
                        </div>
                        {records?.total_pages !== 1 && (
                          <div className=" mt-4">
                            <CustomPagination
                              total_pages={records?.total_pages}
                              current_page={currentPage}
                              setCurrent_Page={setCurrentPage}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <NoDataFound />
                  )}
                </div>
                <div className="col-sm-3">
                  <CalenderFilter id={id} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
      {/* )} */}
    </>
  );
};
export default UserUploads;
