import React from "react";

const TestimonialPreview = ({ selectedTestimonial }) => {
  return (
    <>
      <div className="row">
        <p className="m-3 introPreview-heading">
          {`Patient's`} Testimonial Preview
        </p>
        <div className="col-sm-10 mx-auto">
          <p className="fw-semibold mt-4">
            {" "}
            Patient Name : {selectedTestimonial?.patient_name}
          </p>
          <p className="fw-semibold mt-4">
            {" "}
            Improvement : {selectedTestimonial?.Improvement}
          </p>
          <p className="fw-semibold mt-4">
            {" "}
            CancerTreatmentType : {selectedTestimonial?.CancerTreatmentType}
          </p>
          <p className="fw-semibold mt-4">
            {" "}
            WaitingTime : {selectedTestimonial?.WaitingTime}
          </p>
          <p className="fw-semibold mt-4">
            {" "}
            Rating : {selectedTestimonial?.Rating}
          </p>
          <p className="">Description</p>
          <p
            className=""
            dangerouslySetInnerHTML={{
              __html: selectedTestimonial.ExperienceSummary,
            }}
          ></p>
        </div>
      </div>
    </>
  );
};

export default TestimonialPreview;
