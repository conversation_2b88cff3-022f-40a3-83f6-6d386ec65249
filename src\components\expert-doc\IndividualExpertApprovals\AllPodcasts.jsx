import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { FaFile } from "react-icons/fa";
import { Placeholder } from "react-bootstrap";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useParams } from "next/navigation";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};
const AllPodcasts = () => {
  const [loading, setLoading] = useState(true);
  const [podcasts, setPodcasts] = useState([]);

  const params = useParams();
  const doctor_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";

  const axiosAuth = useAxiosAuth();

  const fetchPodcasts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERT_PODCASTS}${doctor_id}`
      );
      setPodcasts(response?.data);
    } catch (error) {
      console.log("Error in getting the podcasts", error);
    } finally {
      setLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  useEffect(() => {
    if (doctor_id) {
      fetchPodcasts();
    }
  }, [doctor_id, fetchPodcasts]);
  return (
    <div className="col-sm-6">
      <div className="">
        <p className="fw-bold grey-text">Podcasts</p>
        <div className="">
          {loading === false ? (
            <>
              {podcasts && podcasts?.length === 0 ? (
                <div className="fs-4 d-flex align-items-center justify-content-center no-articles-found">
                  <PiFolderNotchOpenFill color={"purple"} size={30} />
                  &nbsp; No Records Found
                </div>
              ) : (
                <div
                  style={{ maxHeight: "130px", overflowY: "auto" }}
                  className="content-scroll-testimonials overflow-auto"
                >
                  {podcasts?.map(
                    (item, index) =>
                      item?.PodcastURL === null && (
                        <div key={index} className="bg-color mb-2">
                          <div className="row bg-grey upload-reviews">
                            <div className="col-sm-1">
                              <FaFile
                                style={{
                                  fontSize: "30px",
                                  color: "#8107D1",
                                }}
                              />
                            </div>
                            <div className="col-sm-3">
                              <p className="fw-semibold mb-0">
                                {/* {formatDate(item?.PodcastDate)} */}
                              </p>
                              <div className="">
                                <p className="custom-transperent-btn">
                                  {/* {timeDifference(item.PodcastDate)} */}
                                </p>
                              </div>
                            </div>
                            <div className="col-sm-8 mb-0">
                              <p> {item?.PodcastTopic}</p>
                            </div>
                            {/* <div className="col-sm-1">
                        <button
                          type="button"
                          className="btn btn-yellow"
                          onClick={() =>
                            handlePodcastApprovals(item)
                          }
                        >
                          Review
                        </button>
                      </div> */}
                          </div>
                        </div>
                      )
                  )}
                </div>
              )}
            </>
          ) : (
            renderPlaceholders()
          )}
        </div>
      </div>
    </div>
  );
};

export default AllPodcasts;
