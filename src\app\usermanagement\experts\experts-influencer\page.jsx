'use client'

import React from "react";
import "../../../../components/experts/experts.css";
import "../../../../components/userManagement/usermanagement.css";
import "../../../../components/administratorDasboard/home.css";
import Navbar from "../../../../components/navbar/Navbar";
import UserManagementComp from "../../../../components/userManagement/UserManagement";
import Home from "../../../../components/administratorDasboard/Home";
import AllInfluencersData from "../../../../components/influencers-data/AllInfluencersData";
import Link from "next/link";
import withAuth from "../../../../withAuth/withAuth";

const influencers = () => {
  return (
    <main>
      <div className="container-fluid"> 
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-8">
                  <p className="main-purple-text">
                        User Management {">"}
                        <Link
                          className="href_link"
                          href="/usermanagement/experts"
                        >
                          Expert {">"}
                        </Link>{" "}
                        <Link
                          className="href_link"
                          href="usermanagement/experts"
                        >
                          Health Guides {" "}
                        </Link>
                      </p>
                  </div>
                  <Home />
                </div>
              </div>
            </div>
            <UserManagementComp />
            <AllInfluencersData />
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(influencers);
