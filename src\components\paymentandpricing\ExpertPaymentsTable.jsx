import React, { useCallback, useEffect, useState } from "react";
import { Button, Placeholder } from "react-bootstrap";
import PendingPayment from "./PendingPayment";
import ClearedPayment from "./ClearedPayment";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import debounce from "lodash.debounce"; // lodash.debounce for debouncing
import { toast } from "react-toastify";
const renderPlaceholders = (value) => {
  const placeholders = Array.from({ length: value }, (_, index) => (
    <tr key={index}>
      <td colSpan="12">
        <div key={index} className="">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              size={"lg"}
              style={{ height: "50px", borderRadius: "4px" }}
            />
          </Placeholder>
        </div>
      </td>
    </tr>
  ));

  return placeholders;
};
const ExpertPaymentsTable = ({ expertRole = "doctor" }) => {
  const [activeView, setActiveView] = useState("pending"); // default view is pending
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [modalShow, setModalShow] = useState(false);
  const [selectedClearedAmount, setSelectedClearedAmount] = useState(null);
  const [expertsWalletAmountDetails, setExpertsWalletAmountDetails] = useState(
    []
  );
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [searchExpertName, setSearchExpertName] = useState("");
  const { data: session } = useSession();
  const admin_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const per_page = 6;

  const openModal = (rowData) => {
    setSelectedRowData(rowData);
    setIsModalOpen(true);
  };

  const handleViewChange = (view) => {
    // Clear date and search fields when view changes
    setStartDate("");
    setEndDate("");
    setSearchExpertName("");
    setActiveView(view);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedRowData(null);
  };

  const handleClearedAmountClick = (clearedAmount) => {
    setSelectedClearedAmount(clearedAmount);
    setModalShow(true);
  };

  const handleClose = () => {
    setModalShow(false);
    setSelectedClearedAmount(null);
  };

  // Fetch data with a stable function and URL
  const fetchExpertAmountRequest = useCallback(async () => {
    if (!admin_id) return;

    let url = `${process.env.NEXT_PUBLIC_GET_ALL_EXPERT_PAYMENT}${activeView}/all/?user_id=${admin_id}&role=${expertRole}&page=${current_page}&per_page=${per_page}`;

    if (startDate) url += `&start_date=${startDate}`;
    if (endDate) url += `&end_date=${endDate}`;
    if (searchExpertName) url += `&search=${searchExpertName}`;

    try {
      setLoading(true);
      const expertAmountResponse = await axiosAuth.get(url);
      setExpertsWalletAmountDetails(expertAmountResponse?.data?.items);
      setTotalPages(expertAmountResponse?.data?.total_pages);
    } catch (error) {
      console.error(error);
      toast.error("Something went wrong. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, [
    admin_id,
    activeView,
    current_page,
    expertRole,
    startDate,
    endDate,
    searchExpertName,
    axiosAuth,
  ]);

  // Debounce search input
  const debouncedSearchChange = debounce(setSearchExpertName, 500);

  useEffect(() => {
    if (admin_id) {
      fetchExpertAmountRequest();
    }
  }, [fetchExpertAmountRequest, admin_id]);

  return (
    <div className="mt-2 gx-5">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <div className="d-flex gap-2">
          <Button
            className={`choose-payment-btn rounded d-flex justify-content-center align-items-center ${
              activeView === "pending" ? "active" : ""
            }`}
            onClick={() => handleViewChange("pending")}
          >
            <input
              type="checkbox"
              checked={activeView === "pending"}
              readOnly
              className="me-2 border-0"
            />
            Pending Payments
          </Button>
          <Button
            className={`choose-payment-btn rounded  d-flex justify-content-center align-items-center clear-btn ${
              activeView === "cleared" ? "active" : ""
            }`}
            onClick={() => handleViewChange("cleared")}
          >
            <input
              type="checkbox"
              checked={activeView === "cleared"}
              readOnly
              className="me-2 border-0"
            />
            Cleared Payments
          </Button>
        </div>

        <div className="d-flex gap-2 align-items-center">
          <div className="custom-search-container">
            <label className="date-range-label">Select Date Range:</label>
            <div className="custom-input-group">
              <input
                type="date"
                className="form-control custom-date-input"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
              <input
                type="date"
                className="form-control custom-date-input"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
              <input
                type="text"
                placeholder="Search For Expert"
                className="form-control custom-search-input"
                onChange={(e) => debouncedSearchChange(e.target.value)}
              />
              <Button
                className="btn custom-button"
                onClick={fetchExpertAmountRequest}
              >
                Search
              </Button>
            </div>
          </div>
        </div>
      </div>

      {activeView === "pending" && (
        <PendingPayment
          expertsWalletAmountDetails={expertsWalletAmountDetails}
          fetchExpertAmountRequest={fetchExpertAmountRequest}
          totalPages={totalPages}
          loading={loading}
          renderPlaceholders={renderPlaceholders}
          current_page={current_page}
          setCurrent_Page={setCurrent_Page}
          per_page={per_page}
        />
      )}
      {activeView === "cleared" && (
        <ClearedPayment
          expertClearedAmountDetails={expertsWalletAmountDetails}
        />
      )}
    </div>
  );
};

export default ExpertPaymentsTable;
