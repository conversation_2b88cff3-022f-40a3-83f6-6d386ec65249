.podcast-title {
  /* text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: flex-start; */
  padding: 0% 5% 0% 5%;
  font-weight: 600;
}
.podcast-preview-section,
.podcast-preview-title {
  color: #8107d1;
  font-weight: 500;
  
}
.custom-font-size{
  font-size: 12px;
}

.video-player {
  width: 100%;
  height: 50%;
}

input.form-control.custom-podcast-input-control {
  /* box-shadow: 0px 3px 6px #00000029; */
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  height: 48px;
}
.podcast-submit-section,
.update-podcast-thumbnail,
.preview-podcast-thumbnail {
 
  margin: auto;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.transcription-container {
  background: #f9f9f9;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  margin-top: 5px;
}

.transcription-text {
  font-size: 12px;
  line-height: 1.6;
  color: #333;
  white-space: pre-line; /* Keeps line breaks */
}
.timestamps-container {
  margin-top: 15px;
}

.timestamps-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.timestamps-list {
  list-style-type: none;
  padding: 0;
  font-size: 12px;
}

.timestamp-item {
  font-size: 12px;
  padding: 5px 0;
  color: #555;
  display: flex;
  align-items: center;
}

.timestamp-item strong {
  color: #8107d1;
  margin-right: 8px;
}

.podcast-upload-btn {
  background-color: #ff971a;
  color: white;
  padding: 10px;
  padding-right: 40px;
  padding-left: 40px;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.podcast-upload-btn:hover {
  background-color: #ff971a;
  color: white;
  padding: 10px;
  padding-right: 40px;
  padding-left: 40px;
}
.custom-podcast-input-control:focus,
.custom-podcast-input-control:active {
  border: none;
}
.featured-category {
  background: linear-gradient(45deg, #ff8a00, #e52e71);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: bold;
  text-transform: uppercase;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: inline-block;
  font-size: 12px;
}


.thumbnail-preview {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}


