"use client";
import React, { Suspense, useState } from "react";
import UsersButtons from "../../components/communication/UsersButtons";
import Navbar from "../../components/navbar/Navbar";
import { FiSettings } from "react-icons/fi";
import { LuBell } from "react-icons/lu";
import "../../components/expert-doc/expert-doc.css";
import "../../components/userManagement/usermanagement.css";
import "../../components/communication/communication.css";
import "../../components/administratorDasboard/home.css";
import Link from "next/link";
import withAuth from "../../withAuth/withAuth";
import { useSession } from "next-auth/react";
import { Badge, Placeholder } from "react-bootstrap";
import BellIconNotification from "../../components/BellIconNotification/BellIconNotification";
import { useNotification } from "../../Context/NotificationContext/NotificationContext";

const TicketsPage = () => {
  const { totalNotifications, notificationLoading } = useNotification();
  const { data: session } = useSession();
  const authenticated = session?.status === "authenticated";
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const toggleNotificationPopup = () => {
    setIsNotificationOpen((prevState) => !prevState); // Toggle the visibility of the notification popup
  };
  return (
    <div className="row mx-0">
      <div className="col-sm-1 navbar-parent">
        <Navbar />
      </div>
      <div className="col-sm-11">
        <div className="row">
          <div className="col-sm-12">
            <div className="row mt-3">
              <div className="col-sm-11">
                <p className="main-purple-text">Communication</p>
              </div>
              <div className="col-sm-1 d-flex justify-content-around align-items-center">
                <Link
                  className=" text-decoration-none"
                  href={"/profilesetting"}
                >
                  <FiSettings className="icon-setting" />
                </Link>

                <span className="nav-item d-none d-xl-block">
                  <span
                    onClick={(e) => {
                      e.stopPropagation(); 
                      toggleNotificationPopup(); 
                    }}
                    id="notification-link"
                    style={{
                      position: "relative",
                      display: "inline-block",
                    }}
                  >
                    <span className="btn btn-bell">
                      <LuBell className="icon-bell" cursor={"pointer"} />
                      <span className="start-100 translate-middle badge rounded-pill text-center">
                        {totalNotifications ? totalNotifications : 0}
                        <span className="visually-hidden">unread messages</span>
                      </span>
                    </span>

                    {authenticated && (
                      <>
                        {notificationLoading ? (
                          <Placeholder
                            as="div"
                            animation="glow"
                            className="notification-badge-placeholder"
                            style={{ width: "20px" }}
                          >
                            <Placeholder
                              xs={12}
                              size={"sm"}
                              style={{
                                height: "20px",
                                borderRadius: "5px",
                              }}
                            />
                          </Placeholder>
                        ) : (
                          <Badge bg="danger" className="notification-badge">
                            {/* {totalNotifications > 100 ? "100+" : totalNotifications} */}
                            {totalNotifications ? totalNotifications : 0}
                          </Badge>
                        )}
                      </>
                    )}
                  </span>
                </span>
                {isNotificationOpen && (
                  <BellIconNotification
                    isNotificationOpen={isNotificationOpen}
                    onClose={toggleNotificationPopup}
                  />
                )}
              </div>
              <Suspense>
                <UsersButtons />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default withAuth(TicketsPage);
