import React from "react";
import { IoClose } from "react-icons/io5";
import Modal from "react-modal";
import {
  capitalizeFullName,
  convertDateFormat,
} from "../../../utils/helperfunction";

const ViewTestimonialModal = ({
  showTestimonialModal,
  setShowTestimonialsModal,
  id,
  name,
  feedback,
  time,
  role,
}) => {
  const handleClose = () => setShowTestimonialsModal(!showTestimonialModal);

  return (
    <div>
      <Modal
        isOpen={showTestimonialModal}
        onRequestClose={() => setShowTestimonialsModal(!showTestimonialModal)}
        className=" article-modal-cont"
        // contentLabel="Example Modal"
      >
        <div className="modal-content custom-modal-content-article">
          <div className="d-flex justify-content-between ">
            <div style={{ color: "#8107D1" }}>
              <h2>Testimonial - {id}</h2>
            </div>
            <div>
              <IoClose
                onClick={handleClose}
                color="#8107D1"
                size={25}
                cursor={"pointer"}
              />
            </div>
          </div>
          <div
            className="modal-body-custom"
            style={{ maxHeight: "400px", overflowY: "auto" }}
          >
            <h5>{role}</h5>
            <div
              className=" d-flex px-2"
              style={{ color: "#8107D1", fontWeight: "bold" }}
            >
              <p className=" text-capitalize">{capitalizeFullName(name)}</p>
              <p className=" ms-auto">{convertDateFormat(time)}</p>
            </div>
            <div className="c">
              <p> {feedback}</p>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ViewTestimonialModal;
