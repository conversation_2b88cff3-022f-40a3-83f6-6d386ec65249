"use client";
import React, {
  useEffect,
  useState,
  Suspense,
  useCallback,
  useMemo,
  useRef,
} from "react";
import DashboardStatistics from "./../experts/DashboardStatistics";
import ExpertsList from "../experts/ExpertsList";
import SearchDataBar from "../experts/SearchDataBar";
import ExpertsApproval from "../experts/ExpertsApproval";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import Loading from "../Loading/PageLoading/Loading";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { formatDateToYMD } from "../../utils/helperfunction";
import _ from "lodash";
import CustomPagination from "../../components/CustomPagination/CustomPagination.jsx";

const AllDoctorsData = () => {
  const [loading, setLoading] = useState(true);
  const [unApprovedDoctorsData, setunApprovedDoctorsData] = useState();
  const [approvedDoctorsData, setapprovedDoctorsData] = useState([]);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [doctorStatusCount, setDoctorStatusCount] = useState({
    approval_requested: 0,
    approved: 0,
    deactivated: 0,
    pending: 0,
    rejected: 0,
    deleted: 0,
    selfDeactivated: 0,
    totalDoctorNumber: 0,
  });
  const fetchedDataRef = useRef({});
  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const { data: session } = useSession();

  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const handleSearchChange = (query) => {
    setSearchQuery(query);
    setCurrent_Page(1);
  };

  const handleStatusChange = (status) => {
    setSelectedStatus(status);
    setCurrent_Page(1);
  };

  const DrHeaderTitles = [
    "Doctor Name",
    "Speciality",
    "Date of Application",
    "Date of Activation",
    "Status",
  ];

  const getAllApprovedUnapprovedExpertsList = useCallback(
    async (query) => {
      try {
        let url = `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}${selectedStatus}/doctor/?user_id=${user_id}`;

        if (startDate && endDate && user_id) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}&page=${current_page}&per_page=6`;
        } else if (query && user_id) {
          url += `&name=${query}&page=${current_page}&per_page=6`;
        } else if (!startDate && !endDate && !query) {
          url += `&page=${current_page}&per_page=6`;
        }

        // Check if data for this URL is already fetched
        if (fetchedDataRef.current[url]) {
          const cachedData = fetchedDataRef.current[url];
          setDoctorStatusCount(cachedData?.doctor_no);
          setapprovedDoctorsData(cachedData?.experts_data);
          setTotalPages(cachedData?.total_pages);

          // Filter out unapproved doctors
          // const unApprovedDoctorsData = cachedData?.experts_data?.filter(
          //   (user) => user.approval === "Approval_requested"
          // );
          // const unApprovedDoctorsData =
          //   cachedData?.approval_requested_data?.doctor_approval_requests;
          // setunApprovedDoctorsData(unApprovedDoctorsData);

          const expertiseData = await axiosAuth.get(
            process.env.NEXT_PUBLIC_FETCH_DOCTOR_EXPERTISE
          );

          // Map expertise IDs to names
          const expertiseMap = {};
          expertiseData?.data?.forEach((expertise) => {
            expertiseMap[expertise.id] = expertise.name;
          });

          // Update doctors' expertise to include names
          const doctorsWithExpertiseNames = cachedData?.experts_data.map(
            (doctor) => {
              const expertises = doctor.expertise.map((id) => expertiseMap[id]);
              return { ...doctor, expertiseNames: expertises };
            }
          );
          setapprovedDoctorsData(doctorsWithExpertiseNames);
        } else {
          const data = await axiosAuth.get(url);
          fetchedDataRef.current[url] = data?.data;
          setDoctorStatusCount(data?.data?.doctor_no);
          // const reversedDoctorsData = data?.data?.experts_data?.slice().reverse();
          // setapprovedDoctorsData(reversedDoctorsData);
          setapprovedDoctorsData(data?.data?.experts_data);
          setTotalPages(data?.data?.total_pages);

          // Filter out unapproved doctors
          const unApprovedDoctorsData =
            data?.data?.approval_requested_data?.doctor_approval_requests;

          setunApprovedDoctorsData(unApprovedDoctorsData);
          // const unApprovedDoctorsData = data?.data?.experts_data?.filter(
          //   (user) => user.approval === "Approval_requested"
          // );
          // setunApprovedDoctorsData(unApprovedDoctorsData);

          const expertiseData = await axiosAuth.get(
            process.env.NEXT_PUBLIC_FETCH_DOCTOR_EXPERTISE
          );

          // Map expertise IDs to names
          const expertiseMap = {};
          expertiseData?.data?.forEach((expertise) => {
            expertiseMap[expertise.id] = expertise.name;
          });

          // Update doctors' expertise to include names
          const doctorsWithExpertiseNames = data?.data?.experts_data.map(
            (doctor) => {
              const expertises = doctor.expertise.map((id) => expertiseMap[id]);
              return { ...doctor, expertiseNames: expertises };
            }
          );
          setapprovedDoctorsData(doctorsWithExpertiseNames);
        }
      } catch (err) {
        console.log("error in fetching ", err);
      } finally {
        setLoading(false);
      }
    },
    [
      user_id,
      setapprovedDoctorsData,
      setunApprovedDoctorsData,
      axiosAuth,
      selectedStatus,
      startDate,
      endDate,
      current_page,
    ]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      getAllApprovedUnapprovedExpertsList(query);
    }, 500);
  }, [getAllApprovedUnapprovedExpertsList]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  const DrstatsData = [
    {
      heading: "Doctors",
      value: doctorStatusCount?.["total doctor number"],
    },
    { heading: "Approved", value: doctorStatusCount?.approved },
    {
      heading: "Requested",
      value: doctorStatusCount?.approval_requested,
    },
    {
      heading: "Pending",
      value: doctorStatusCount?.pending,
    },
    {
      heading: "Rejected",
      value: doctorStatusCount?.rejected,
    },
    {
      heading: "Deactivated",
      value: doctorStatusCount?.deactivated,
    },
    {
      heading: "Deleted",
      value: doctorStatusCount?.deleted,
    },
  ];

  const approveSingleExpert = useCallback(
    async (expertId) => {
      const body = { approval: "Approved" };
      try {
        const response = await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${expertId}/?user_id=${user_id}`,
          body
        );
        if (response?.data?.message === "approved") {
          toast.success("Expert approved successfully!", {
            theme: "colored",
            autoClose: 1500,
          });
          // approveDoctors();
        } else {
          toast.error("Failed to approve expert. Please try again.", {
            theme: "colored",
            autoClose: 1500,
          });
          console.error("Failed to approve expert:", response);
        }
      } catch (err) {
        console.log("error in fetching ", err);
      }
    },
    [user_id, axiosAuth]
  );

  return (
    <>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="bg-color">
          <Suspense>
          <div className=" overflow-hidden">
          <div className="user-management-scroll overflow-auto">
            <SearchDataBar
              heading={`Doctor Data`}
              onSearchChange={handleSearchChange}
              onStatusChange={handleStatusChange}
              selectedStatus={selectedStatus}
              searchQuery={searchQuery}
              startDate={startDate}
              endDate={endDate}
              setEndDate={setEndDate}
              setStartDate={setStartDate}
              setSearchQuery={setSearchQuery}
              totalExperts={DrstatsData[0]?.value}
              expert={"Doctor"}
              setCurrent_Page={setCurrent_Page}
            />

            <div className="row">
              <div className="col-sm-7">
                <DashboardStatistics statsData={DrstatsData} />
                <ExpertsList
                  loading={loading}
                  expertiseListName="Doctors List"
                  expertsList={approvedDoctorsData}
                  headerTitles={DrHeaderTitles}
                  searchQuery={searchQuery}
                />

                <div className="d-flex justify-content-center align-items-center">
                  <div className="d-none d-xl-block">
                    <CustomPagination
                      total_pages={totalPages}
                      current_page={current_page}
                      setCurrent_Page={setCurrent_Page}
                    />
                  </div>
                </div>
              </div>
              <ExpertsApproval
                loading={loading}
                approveSingleExpert={approveSingleExpert}
                expertsApprovals={unApprovedDoctorsData}
              />
            </div>
            </div>
            </div>
          </Suspense>
        </div>
      )}
    </>
  );
};

export default AllDoctorsData;
