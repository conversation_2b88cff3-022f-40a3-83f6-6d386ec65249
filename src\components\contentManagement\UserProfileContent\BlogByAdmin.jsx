import React, { useState, useEffect, useCallback } from "react";

import { Table, Button, Image, Modal } from "react-bootstrap";
import { toast } from "react-toastify";
import { useSession } from "next-auth/react";
import AdminBlogEditModal from "./AdminBlogEditModal";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import dummyProfile from "../../../../public/images/headerImage.png";
import { FaBlogger } from "react-icons/fa6";
import {
  capitalizeFullName,
  formatCustomDateAndTime,
} from "../../../utils/helperfunction";
import AddBlogByAdmin from "./AddBlogByAdmin.jsx";
import Swal from "sweetalert2";
import ViewAdminAddedBlog from "./ViewAdminAddedBlog.jsx";

const BlogByAdmin = () => {
  const [adminBlogs, setAdminBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBlogForEdit, setSelectedBlogForEdit] = useState(null);
  const [openAddBlogByAdmin, setOpenAddBlogByAdmin] = useState(false);
  const [showAdminBlog, setShowAdminBlog] = useState(false);
  const [openBlogEditModal, setOpenBlogEditModal] = useState(false);
  const [selectedAdminBlog, setSelectedAdminBlog] = useState({});
  const { data: session } = useSession();
  const adminId = session && session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const fetchBlogs = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_POST_BLOG_AS_ADMIN}${adminId}/all/`
      );
      setAdminBlogs(response.data);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  }, [axiosAuth, adminId]);

  const handleView = (blog) => {
    setSelectedAdminBlog(blog);
    setShowAdminBlog(true);
  };

  const handleEdit = (blog) => {
    setSelectedBlogForEdit(blog);
    setOpenBlogEditModal(true);
  };

  const handleDelete = async (blogId) => {
    const confirmResult = await Swal.fire({
      title: "Are you sure want to delete this Blog?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#B50000",
      confirmButtonText: "Yes, delete it!",
    });
    if (confirmResult.isConfirmed) {
      try {
        const response = await axiosAuth.delete(
          `${process.env.NEXT_PUBLIC_PUT_DELETE_BLOG_AS_ADMIN}${blogId}/?user_id=${adminId}`
        );
        if (response.status === 200) {
          toast.success("Blog deleted successfully!");
          fetchBlogs();
        } else {
          toast.error("Failed to delete the blog");
        }
      } catch (error) {
        console.error(error);
        toast.error("An error occurred while deleting the blog");
      }
    }
  };

  const handleCloseEditModal = () => {
    setOpenBlogEditModal(false);
    setSelectedBlogForEdit(null);
    fetchBlogs();
  };

  useEffect(() => {
    if (adminId) {
      fetchBlogs();
    }
  }, [adminId, fetchBlogs]);

  if (loading) {
    return <div>Loading...</div>;
  }
  return (
    <>
      <div className="d-flex justify-content-between align-items-center">
        <h5 className=" admin-add-blog-list-header fw-semibold">
          Add Update Blogs
        </h5>
        <button
          className="purple-button"
          onClick={() => setOpenAddBlogByAdmin(true)}
        >
          + Upload Blog
        </button>
      </div>
      <div className="table-wrapper">
        <table className="table">
          <thead className="sticky-table-head">
            <tr>
              <th scope="col" className="text-center" style={{ width: "60px" }}>
                Sl No
              </th>
              <th scope="col">Author</th>
              <th scope="col">Title</th>

              <th scope="col">Role</th>
              <th scope="col" className="text-center">
                Uploaded Date
              </th>
              <th scope="col" className="text-center">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(adminBlogs) && adminBlogs.length > 0 ? (
              adminBlogs?.map((blog, index) => (
                <tr key={blog?.blog_details?.id} >
                  <td className="custom-font-size ">
                    {/* <div className="d-flex align-items-center "> */}
                      {index + 1}
                    {/* </div> */}
                  </td>
                  <td className="col purple-content custom-font-size ">
                    {/* <div className="d-flex align-items-cente "> */}
                    {blog?.blog_details?.expert_details?.expert_details
                      ?.doctor_other_details?.ProfilePhoto ? (
                      <Image
                        src={
                          blog?.blog_details?.expert_details?.expert_details
                            ?.doctor_other_details?.ProfilePhoto ||
                          "/headerImage.png"
                        }
                        className="profile-img-expert"
                        alt={
                          blog?.blog_details?.expert_details?.expert_details
                            ?.name || "Default Profile"
                        }
                        width={50}
                        height={50}
                      />
                    ) : (
                      <Image
                        src={dummyProfile}
                        className="profile-img-expert"
                        alt={"avatar"}
                        width={40}
                        height={40}
                      />
                    )}
                    {blog?.blog_details?.expert_details.expert_details?.name ||
                      "Author Name comes here"}
                    {/* </div> */}
                  </td>
                  <td className=" fw-medium custom-font-size ">
                    {/* <div className="d-flex align-items-center "> */}
                    {blog?.blog_details?.BlogTitle}
                    {/* </div> */}
                  </td>
                  <td className="custom-font-size">
                    {/* <div className="d-flex align-items-center "> */}
                      {capitalizeFullName(
                        blog?.blog_details?.expert_details.expert_details
                          ?.role || "Role comes here"
                      )}
                    {/* </div> */}
                  </td>

                  <td className="text-center custom-font-size">
                    {/* <div className="d-flex justify-content-between align-items-center "> */}
                      {formatCustomDateAndTime(
                        blog?.blog_details?.BlogDateTime
                      )}
                    {/* </div> */}
                  </td>
                  <td className="custom-font-size">
                    <span className="d-flex justify-content-between align-items-center ">
                      <Button
                        variant="info"
                        size="sm"
                        onClick={() => handleView(blog?.blog_details)}
                        className="me-2"
                      >
                        View
                      </Button>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handleEdit(blog?.blog_details)}
                        className="me-2"
                      >
                        Edit
                      </Button>
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={() => handleDelete(blog?.blog_details?.id)}
                      >
                        Delete
                      </Button>
                    </span>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center">
                  No adminBlogs found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {openBlogEditModal && (
        <AdminBlogEditModal
          openBlogEditModal={openBlogEditModal}
          handleClose={handleCloseEditModal}
          blogDetails={selectedBlogForEdit}
          fetchBlogs={fetchBlogs}
        />
      )}
      {openAddBlogByAdmin && (
        <AddBlogByAdmin
          fetchBlogs={fetchBlogs}
          openAddBlogByAdmin={openAddBlogByAdmin}
          handleClose={() => setOpenAddBlogByAdmin(false)}
        />
      )}

      {showAdminBlog && (
        <ViewAdminAddedBlog
          show={showAdminBlog}
          handleClose={setShowAdminBlog}
          blogDetails={selectedAdminBlog}
        />
      )}
    </>
  );
};

export default BlogByAdmin;
