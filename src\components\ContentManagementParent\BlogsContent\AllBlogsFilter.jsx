import React, { useEffect, useRef, useState } from "react";
import { IoCloseSharp } from "react-icons/io5";
import { GrSearch } from "react-icons/gr";
import { FaCalendar, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const AllBlogsFilter = ({
  searchQuery,
  setSearchQuery,
  startDate,
  endDate,
  loading,
  setStartDate,
  setEndDate,
  topAuthors,
  setExperience,
  experience,
  isTopExperts,
  isTestimonial,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const searchMenu = useRef(null);

  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handleClearSearch = () => {
    setSearchQuery("");
  };

  const handleDate = (start, end) => {
    setStartDate(start);
    setEndDate(end);
  };

  const handleClearDate = () => {
    setStartDate();
    setEndDate();
  };

  const maxExperience = 100; // You can change this value to set the maximum experience level
  const experienceValues = Array.from(
    { length: maxExperience },
    (_, index) => index + 1
  ); // Generate values from 1 to maxExperience

  return (
    <div className="col-sm-12">
      <div className="grey-bg">
        <div className="row ">
          <div
            className={`${
              isTestimonial ? "col-sm-8" : "col-sm-6"
            }  d-flex justify-content-start align-items-center`}
          >
            <p className="heading mb-0">Filters</p>
          </div>

          {!topAuthors && !isTestimonial && (
            <div className="col-sm-3 d-flex justify-content-center align-items-center">
              <div
                style={{ borderRadius: "3px", border: "none", width: "100%" }}
                ref={searchMenu}
                className=" calender-filter-container "
              >
                <span
                  className="date-filter-expert  "
                  onClick={handleCalendarClick}
                  style={{ width: "100%" }}
                >
                  {startDate
                    ? `${startDate.toLocaleDateString()} - ${
                        endDate ? endDate.toLocaleDateString() : ""
                      }`
                    : "Posted on - date range"}
                  <span
                    style={{ zIndex: 9999 }}
                    className="calendar-icon-expert"
                  >
                    {startDate ? (
                      <FaTimes
                        className=" cross-icon-calendar"
                        onClick={handleClearDate}
                      />
                    ) : (
                      <FaCalendar className=" calender-icon-calendar" />
                    )}
                  </span>
                </span>

                {showPicker && (
                  <div style={{ position: "absolute", zIndex: 1 }}>
                    <DatePicker
                      selected={startDate}
                      startDate={startDate}
                      endDate={endDate}
                      selectsRange
                      inline
                      showTimeSelect={false}
                      onChange={(dates) => handleDate(dates[0], dates[1])}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {isTopExperts && (
            <div className="col-sm-3 ">
              <select
                className="form-select filterwidth select-font"
                id="categorySelect"
                value={experience}
                onChange={(e) => setExperience(e.target.value)}
              >
                <option value="">Select an Experience</option>
                {experienceValues.map((exp) => (
                  <option key={exp} value={exp}>
                    {exp}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div
            className={`${
              isTestimonial ? "col-sm-4" : "col-sm-3"
            } d-flex justify-content-center align-items-center`}
          >
            <div className="input-group search-input-patient calender-filter-container">
              <input
                type="text"
                style={{
                  borderRadius: "3px",
                  border: "none",
                }}
                className="form-control search-input-focus"
                placeholder={
                  topAuthors
                    ? "Search Expert name"
                    : isTestimonial
                    ? "Search by Name"
                    : "Search by title/category name"
                }
                aria-label="Recipient's username"
                aria-describedby="button-addon2"
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
                value={searchQuery}
              />

              <span
                className="input-group-text custom-search-icon"
                id="button-addon2"
                style={{borderRadius: "5px"}}
              >
                {!searchQuery && (
                  <GrSearch
                    style={{
                      color: "#7B009C",
                    }}
                  />
                )}
              </span>

              <style jsx>{`
                ::placeholder {
                  color: #212529;
                }
              `}</style>
              {!loading && searchQuery && (
                <IoCloseSharp
                  className="clear-search-icon position-absolute"
                  style={{
                    fontSize: "20px",
                    cursor: "pointer",
                    right: "6px",
                    top: "10px",
                    color: "#7B009C",
                  }}
                  onClick={handleClearSearch}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllBlogsFilter;
