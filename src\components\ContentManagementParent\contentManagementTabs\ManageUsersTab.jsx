import React from "react";
import AskRuchikaExperts from "../contentManagementTabs/askRuchikaExperts/AskRuchikaExperts";
import AddPodcast from "../../contentManagement/ExpertProfileContent/AddPodcast";
import TopAuthors from "./topAuthors/TopAuthors";
import AddChildAdminDesignation from "../../contentManagement/ChildAdminContent/AddChildAdminDesignation";
import UserRejectingCategory from "../../contentManagement/UserProfileContent/UserRejectingCategory";
import UserDeactivatingCategory from "../../contentManagement/UserProfileContent/UserDeactivatingCategory";
import UserSubscriptions from "./UserSubscriptions/UserSubscriptions";
const ManageUsersTab = () => {
  return (
    <div
      style={{ maxHeight: "600px", padding: "12px" }}
      className=" overflow-y-auto"
    >
      <AskRuchikaExperts />
      <div className=" bg-color mb-4">
        <TopAuthors />
      </div>
      <div className="bg-color mb-4">
        <AddChildAdminDesignation />
      </div>
      <div className="bg-color mb-4">
        <UserRejectingCategory />
      </div>
      <div className="bg-color mb-4">
        <UserDeactivatingCategory />
      </div>

      <div className=" bg-color mb-4">
        <AddPodcast />
      </div>

      <div className=" bg-color mb-4">
        <UserSubscriptions />
      </div>
    </div>
  );
};

export default ManageUsersTab;
