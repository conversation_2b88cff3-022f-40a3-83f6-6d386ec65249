'use client'

import Home from "../../components/administratorDasboard/Home";
import ExpertsAppointmentsList from "../../components/AppoinmentManagement/ExpertsAppointmentsList";
import Navbar from "../../components/navbar/Navbar";
import "../../components/AppoinmentManagement/appointmentmanage.css";
import withAuth from "../../withAuth/withAuth";

const Homepage = () => {
  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-8">
                    <p className="main-purple-text">Appointment Management</p>
                  </div>
                  <Home />
                </div>
              </div>
            </div>
            <ExpertsAppointmentsList />
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(Homepage);
