import React, { useState } from "react";
import Image from "next/image";
import adminLogo from "../../../public/assets/adminloginlogo.png";
import ellipseadmin from "../../../public/assets/ellipseadmin.png";
import ellipsetop from "../../../public/assets/ellipse-top.png";
import RightLogin from "../Login/rightLogin";
import { validateEmail } from "../../utils/helperfunction";
import { debounce } from "lodash";
import axios from "axios";
import { toast } from "react-toastify";

const ForgotPassword = () => {
  const [emailId, setEmailId] = useState("");

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSubmit = debounce(async (e) => {
    e.preventDefault();
    if (emailId === "") {
      toast.error("Please enter your email.", {
        autoClose: 3000,
        position: "top-center",
      });
      setEmailId("");
      return;
    }

    try {
      let isValidEmail = validateEmail(emailId);
      if (!isValidEmail) {
        toast.error(
          "Invalid email address. Please use a valid email address.",
          {
            autoClose: 3000,
            position: "top-center",
          }
        );

        setEmailId(() => "");
        return;
      }
      let sendRestpassResponse = await axios.put(
        `${process.env.NEXT_PUBLIC_ADMIN_FORGOT_PASSWORD_RESET_LINK_API}${emailId}/`
      );
      if (sendRestpassResponse?.status === 200) {
        toast.success(`Reset password link is sent to ${emailId}`, {
          autoClose: 3000,
          position: "top-center",
        });
        if (typeof window !== "undefined") {
          setTimeout(() => {
            setEmailId(() => "");
            window.location.href = "/auth/login";
          }, 3000);
        }
      } else {
        toast.error(
          sendRestpassResponse?.data?.message ||
            "Failed to send the reset link",
          {
            autoClose: 3000,
            position: "top-center",
          }
        );
      }
    } catch (error) {
      console.error(error);
      if (error?.response?.data?.detail === "Not found.") {
        toast.error(`User not found with this email.`, {
          autoClose: 3500,
          position: "top-center",
        });
      } else {
        toast.error(`Failed to send the reset link.`, {
          autoClose: 3500,
          position: "top-center",
        });
      }
    }
  }, 1500);

  return (
    <>
      <div className="row">
        <div className="col-sm-6 gx-0">
          <Image src={adminLogo} className="admin-logo ms-2" alt="" />
          <Image src={ellipsetop} className="ellipse-top" alt="" />
          <h5 className="login-heading">Forgot Password?</h5>
          <div className="login-form">
            <form>
              <div className="row">
                <div className="col-sm-12">
                  <div className="form-group mt-2">
                    <label
                      htmlFor="userEmail"
                      className="admin-login-label mb-1"
                    >
                      Enter your email
                    </label>
                    <input
                      type="email"
                      className="form-control form-login-fields"
                      placeholder="<EMAIL>"
                      onChange={(e) => setEmailId(e.target.value)}
                      required
                      onKeyDown={handleKeyPress}
                    />
                  </div>
                </div>
                <span className="admin-login-label mt-4">
                  An activation link will be sent to your registered email.
                  Please check your email to access the link.
                </span>
                <button
                  type="button"
                  onClick={handleSubmit}
                  className="admin-submit mt-5"
                >
                  Submit
                </button>
              </div>
            </form>
          </div>
          <Image src={ellipseadmin} className="ellipse-down" alt="" />
        </div>

        <div className="col-sm-6 gx-0">
          <RightLogin />
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;
