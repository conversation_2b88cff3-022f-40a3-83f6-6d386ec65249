"use client";
import React from "react";
import ProfileSettingComp from "../../components/profileSettings/ProfileSettingComp";
import ChildAdminSettings from "../../components/profileSettings//childAdminSttings/ChildAdminSettings";
import Navbar from "../../components/navbar/Navbar";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import Home from "../../components/administratorDasboard/Home";
import { IoArrowBack } from "react-icons/io5";
import withAuth from "../../withAuth/withAuth";

const AdminProfileSetting = () => {
  const { isAdminChildAdmin } = useAdminContext();
  return (
    <div>
      <main>
        <div className="container-fluid">
          <div className="row">
            <div className="col-sm-1 navbar-parent">
              <Navbar />
            </div>
            <div className="col-sm-11">
              {isAdminChildAdmin ? (
                <>
                  {" "}
                  <div className="row">
                    <div className="col-sm-12">
                      <div className="row mt-3">
                        <div className="col-sm-8 d-flex align-items-center">
                          {/* <p className="main-purple-text">Profile Setting</p> */}
                          <div
                            onClick={() => {
                              if (typeof window !== "undefined") {
                                window.history.back();
                              }
                            }}
                            className="main-purple-text"
                          >
                            <IoArrowBack /> Profile Setting
                          </div>
                        </div>
                        <Home />
                      </div>
                    </div>
                  </div>
                  <ChildAdminSettings />
                </>
              ) : (
                <ProfileSettingComp />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default withAuth(AdminProfileSetting);
