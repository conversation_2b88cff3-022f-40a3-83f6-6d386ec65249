"use client";
import React, { useState, useEffect, useCallback, useContext } from "react";
import MainProfile from "./MainProfile";
import Approvals from "./Approvals";
import ListOfPatients from "./ListOfPatients";
import Uploads from "./Uploads";
import Communication from "./Communication";
import {
  useParams,
  useSearchParams,
  usePathname,
  useRouter,
} from "next/navigation";
import { toast } from "react-toastify";
import "../userManagement/usermanagement.css";
import "../experts/experts.css";
import "../expert-doc/expert-doc.css";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import AppointmentManage from "./ExpertsAppointment/AppointmentManage";
import { useSession } from "next-auth/react";
import { AdminDetailsContext } from "../../Context/AdminContext/AdminContext";
import ExpertDues from "./ExpertDues/ExpertDues";

const Menu = ({ doctorsData }) => {
  const searchParams = useSearchParams();
  const initialTab = searchParams.get("tab");
  const { session } = useContext(AdminDetailsContext);
  const [activeTab, setActiveTab] = useState(initialTab || "profile");
  const [testimonials, setTestimonials] = useState();
  const [doctorsConsent, setDoctorsConsent] = useState();
  const [approvedTestimonials, setApprovedTestimonials] = useState();
  const [approvedFeedback, setApprovedFeedback] = useState();
  const [noDtaError, setNoDtaError] = useState(false);
  const [UnApprovedFeedback, setUnApprovedFeedback] = useState();
  const [unApprovedFeedbackLoading, setUnApprovedFeedbackLoading] =
    useState(true);
  const [approvedFeedbackLoading, setApprovedFeedbackLoading] = useState(true);
  const [testimonialsLoading, setTestimonialsLoading] = useState(false);
  const [doctorConsentLoading, setDoctorConsentLoading] = useState(false);
  const [approvedTestimonialsLoading, setApprovedTestimonialsLoading] =
    useState(false);
  const [showTestimonialModal, setShowTestimonialModal] = useState(false);

  const pathname = usePathname();
  const params = useParams();
  const router = useRouter();

  const doctor_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params.user_id[0]
      : "";
  const axiosAuth = useAxiosAuth();

  const handleTabChange = (tab) => {
    const updatedPath = `${pathname}?tab=${tab}`;
    router.replace(updatedPath, { shallow: true });
    setActiveTab(tab);
  };

  const admin_id = session?.user.id;

  const fetchDoctorConsent = useCallback(async () => {
    try {
      setDoctorConsentLoading(true);
      if (admin_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_DOCTORS_CONSENT_FORM}${doctor_id}/?user_id=${admin_id}`
        );

        setDoctorsConsent(response?.data);
      }
    } catch (err) {
      toast.error("error in fetching consent form");
    } finally {
      setDoctorConsentLoading(false);
    }
  }, [doctor_id, axiosAuth, admin_id]);

  const fetchFeedback = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_FEEDBACK}${doctor_id}/all/`
      );

      const approvedTestimonials = response?.data?.filter(
        (test) => test?.status === 1
      );
      setApprovedFeedback(approvedTestimonials);

      const unApprovedTestimonials = response?.data?.filter(
        (test) => test?.status === 2
      );
      setUnApprovedFeedback(unApprovedTestimonials?.slice().reverse());
    } catch (err) {
      toast.error("error in fetching testimonial");
    } finally {
      setUnApprovedFeedbackLoading(false);
      setApprovedFeedbackLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  const fetchTestimonials = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_SHARE_UR_STORY}${doctor_id}/1/`
      );
      setApprovedTestimonials(response?.data?.slice().reverse());
    } catch (err) {
      toast.error("error in fetching testimonial");
      setNoDtaError(true);
    } finally {
      setApprovedTestimonialsLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  const fetchPendingTestimonials = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_SHARE_UR_STORY}${doctor_id}/2/`
      );
      setTestimonials(response?.data?.slice().reverse());
    } catch (err) {
      toast.error("error in fetching testimonial");
    } finally {
      setTestimonialsLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  useEffect(() => {
    if (doctor_id !== null || doctor_id !== undefined) {
      fetchTestimonials();
      fetchFeedback();
      fetchPendingTestimonials();
      fetchDoctorConsent();
    }
    const tab = searchParams.get("tab");
    setActiveTab(tab || "profile");
  }, [
    doctor_id,
    fetchTestimonials,
    fetchFeedback,
    fetchPendingTestimonials,
    searchParams,
    fetchDoctorConsent,
  ]);

  const handleTestmonialApprovals = async (status, testimonial_id) => {
    let testimonial_status;

    if (status === "reject") {
      testimonial_status = 0;
    } else if (status === "approve") {
      testimonial_status = 1;
    }
    try {
      setTestimonialsLoading(true);
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_APPROVE_REJECT_TESTIMONIAL}${testimonial_id}/?user_id=${admin_id}`,
        { status: testimonial_status }
      );
      if (response?.data?.status === 1) {
        toast.success(`Testimonial Approved.`);
        setShowTestimonialModal(false);
      } else if (response?.data?.status === 0) {
        toast.error(`Testimonial Rejected`);
        setShowTestimonialModal(false);
      }

      fetchPendingTestimonials();
      fetchTestimonials();
    } catch (error) {
      console.error(error);
      toast.error("Something went wrong. Please try again!");
    } finally {
      setTestimonialsLoading(false);
    }
  };

  return (
    <div>
      <>
        <p className="d-inline-flex gap-1 buttons-row mb-0 mb-0">
          <button
            className={`btn grey-btn ${
              activeTab === "profile" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("profile")}
          >
            Main Profile
          </button>
          <button
            className={`btn grey-btn ${
              activeTab === "approvals" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("approvals")}
          >
            Approvals
          </button>
          <button
            className={`btn grey-btn ${
              activeTab === "patients" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("patients")}
          >
            Consultations List
          </button>
          <button
            className={`btn grey-btn ${
              activeTab === "appointments" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("appointments")}
          >
            Appointments
          </button>
          <button
            className={`btn grey-btn ${
              activeTab === "uploads" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("uploads")}
          >
            Uploads
          </button>
          <button
            className={`btn grey-btn ${
              activeTab === "communications" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("communications")}
          >
            Communication
          </button>
          <button
            className={`btn grey-btn ${
              activeTab === "expertDues" ? "activeExpertsTab" : ""
            }`}
            onClick={() => handleTabChange("expertDues")}
          >
            Dues
          </button>
        </p>
        {activeTab === "profile" && (
          <MainProfile
            approvedTestimonials={approvedTestimonials}
            approvedFeedback={approvedFeedback}
            handleTestmonialApprovals={handleTestmonialApprovals}
            approvedFeedbackLoading={approvedFeedbackLoading}
            approvedTestimonialsLoading={approvedTestimonialsLoading}
          />
        )}
        {activeTab === "approvals" && (
          <>
            <Approvals
              doctorsData={doctorsData}
              doctorsConsent={doctorsConsent}
              noDtaError={noDtaError}
              testimonials={testimonials}
              handleTestmonialApprovals={handleTestmonialApprovals}
              showTestimonialModal={showTestimonialModal}
              testimonialsLoading={testimonialsLoading}
              setTestimonialsLoading={setTestimonialsLoading}
              setShowTestimonialModal={setShowTestimonialModal}
              unApprovedFeedbackLoading={unApprovedFeedbackLoading}
              UnApprovedFeedback={UnApprovedFeedback}
              fetchFeedback={fetchFeedback}
              doctorConsentLoading={doctorConsentLoading}
              setDoctorConsentLoading={setDoctorConsentLoading}
              fetchDoctorConsent={fetchDoctorConsent}
            />
          </>
        )}
        {activeTab === "patients" && <ListOfPatients />}
        {activeTab === "uploads" && (
          <Uploads doctorsConsent={doctorsConsent} doctorsData={doctorsData} />
        )}

        {activeTab === "communications" && <Communication />}
        {activeTab === "appointments" && <AppointmentManage />}
        {activeTab === "expertDues" && <ExpertDues />}
      </>
    </div>
  );
};

export default Menu;
