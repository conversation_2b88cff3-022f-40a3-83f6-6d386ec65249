import React, { useState } from "react";
import { formatDate, timeDifference } from "./db.js";
import NoDataFound from "../noDataFound/NoDataFound.jsx";
import AddPodcastForSelectedExpert from "../contentManagement/ExpertProfileContent/AddPodcastForSelectedExpert.jsx";
import { SiApplepodcasts } from "react-icons/si";
const PodcastApprovals = ({
  loading,
  noDtaError,
  podcasts,
  renderPlaceholders,
  fetchPodcasts,
}) => {
  const [addPodcastModal, setAddPodcastModal] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);

  const handleSelectExpert = (expert) => {
    setSelectedDoctor(expert);
    setAddPodcastModal(true);
  };
  return (
    <>
      {loading === false ? (
        <>
          {noDtaError ? (
            <div className="custom-margin-nodatafoud">
              <NoDataFound />
            </div>
          ) : podcasts && podcasts?.items?.length > 0 ? (
            <div className="overflow-hidden">
              <div className="overflow-auto allApproval-tab-scroll">
                <div className="row">
                  {podcasts?.items?.map((item, index) => {
                    if (item?.PodcastURL == null) {
                      return (
                        <div key={index} className="col-6 mb-2">
                          <div className="d-flex bg-grey upload-reviews">
                            <div className="col-sm-1">
                              <SiApplepodcasts
                                style={{
                                  fontSize: "30px",
                                  color: "#8107D1",
                                }}
                              />
                            </div>
                            <div className="col-sm-2">
                              <p className="fw-semibold mb-0">
                                {formatDate(item?.PodcastDate)}
                              </p>
                              <div className="">
                                <p className="custom-transperent-btn">
                                  {timeDifference(item.PodcastDate)}
                                </p>
                              </div>
                            </div>
                            <div className="col-sm-7 mb-0">
                              <p> {item?.PodcastTopic}</p>
                            </div>
                            <div className="col-sm-2">
                              <button
                                className="border-0 all-podcast-pushlish-button custom-purple-button px-4 rounded py-2"
                                onClick={() => handleSelectExpert(item)}
                              >
                                Publish
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    }
                  })}
                </div>
              </div>
            </div>
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No other Podcast available for approval.
              </h3>
            </div>
          )}
        </>
      ) : (
        renderPlaceholders()
      )}

      {addPodcastModal && (
        <AddPodcastForSelectedExpert
          addPodcastModal={addPodcastModal}
          selectedDoctor={selectedDoctor}
          setAddPodcastModal={setAddPodcastModal}
          setSelectedDoctor={setSelectedDoctor}
          fetchAllPodcasts={fetchPodcasts}
          from=""
        />
      )}
    </>
  );
};

export default PodcastApprovals;
