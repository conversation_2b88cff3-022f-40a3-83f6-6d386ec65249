import React, { useCallback, useEffect, useRef, useState } from "react";
import { IoChevronBack } from "react-icons/io5";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { FcDocument } from "react-icons/fc";
import {
  capitalizeFullName,
  convert_time,
  formatNameInitials,
  renderPlaceholders,
} from "../../utils/helperfunction";
import Image from "next/image";
import ModalImage from "react-modal-image";
import PdfViewerModal from "./pdfViewer/PdfViewerModal";
import { useSession } from "next-auth/react";
import useAxiosAuth from "@/lib/hooks/useAxiosAuth";

const SingleTicket = ({
  selectedTicketDetails,
  setSelectedTicketDetails,
  title,
  summary,
  time,
  ticketId,
  threadData,
  ticketStatus,
  profilephoto,
  author_photo,
  attachements_data,
}) => {
  const [showCertificate, setShowCertificate] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [singleTicketData, setSingleTicketData] = useState([]);
   const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);
   const { data: session } = useSession();
    const userId = session && session?.user?.id;
    const axiosAuth = useAxiosAuth();

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });

    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 767);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleClosePdfModal = () => {
    setShowCertificate(false);
  };

  const fetchSingleTicketData = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_SINGLE_TICKETS}${ticketId}/?user_id=${userId}`
      );
      setSingleTicketData(response?.data?.ticket_data);
    } catch (error) {
      console.error(error);
      setError(true);
    } finally {
      setLoading(false);
    }
  }, [ticketId, userId, axiosAuth]);

  useEffect(() => {
    if (ticketId) fetchSingleTicketData();
  }, [fetchSingleTicketData, ticketId]);

  const renderAttachment = (file, index) => {
    const fileUrl = file || file.url;
    const fileType = fileUrl?.split(".").pop()?.toLowerCase() || "";

    if (["jpg", "jpeg", "png"].includes(fileType)) {
      // Handle image files
      return (
        <div key={index} className="image-preview-doc  text-center ">
          <ModalImage
            small={fileUrl}
            medium={fileUrl}
            className="custom-small-image rounded"
            large={fileUrl}
            showRotate={true}
            alt={``}
            hideDownload={true}
          />{" "}
          <p className="View-image-doc text-small text-secondary  ">
            View Image
          </p>
        </div>
      );
    } else if (fileType === "pdf") {
      // Handle PDF files
      return (
        <div key={index} className="preview-doc  ">
          <button
            className="btn border-3 border rounded d-flex align-items-center gap-2"
            onClick={() => setShowCertificate(true)}
          >
            <FcDocument />
            View PDF
          </button>
          <PdfViewerModal
            show={showCertificate}
            handleClosePdfModal={handleClosePdfModal}
            document={fileUrl}
          />
        </div>
      );
    } else if (
      ["doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(fileType)
    ) {
      // Handle Microsoft Office files
      return (
        <div key={index} className="preview-doc  ">
          <a
            href={`https://docs.google.com/gview?url=${fileUrl}`}
            target="_blank"
            rel="noopener noreferrer"
            className="btn border-3 border rounded d-flex align-items-center gap-2"
          >
            <FcDocument />
            View Doc
          </a>
        </div>
      );
    } else if (["mp4", "webm", "ogg"].includes(fileType)) {
      // Handle video files
      return (
        <div key={index} className="preview-doc  ">
          <video controls>
            <source src={fileUrl} type={`video/${fileType}`} />
            Your browser does not support the video tag.
          </video>
        </div>
      );
    } else if (["mp3", "wav", "ogg"].includes(fileType)) {
      // Handle audio files
      return (
        <div key={index} className="preview-doc  ">
          <audio controls>
            <source src={fileUrl} type={`audio/${fileType}`} />
            Your browser does not support the audio tag.
          </audio>
        </div>
      );
    } else {
      // Handle unsupported or generic files
      return (
        <div key={index} className="preview-doc ">
          <a
            href={fileUrl}
            download
            className="btn btn-warning d-flex align-items-center gap-2"
          >
            <FcDocument />
            Download File
          </a>
        </div>
      );
    }
  };
  return (
    <>
      <div className="col-sm ticket_main_container">
        <div className="queiries-lines" style={{ height: "610px" }}>
          <div className="ticket_heading">
            <button
              onClick={() => setSelectedTicketDetails(null)}
              className="ticket_back_button"
            >
              <IoChevronBack color={"#8107d1"} size={20} /> Back
            </button>
          </div>
          <div className="ticket_id">
            <span className="span_ticket_id">Ticket ID</span> - #{ticketId}
          </div>
          <div className="row">
            <div className="col-sm-12 col-xl-10">
              <span className="article-query">
                <span className="span_ticket_title">Subject</span> - {title}
              </span>
            </div>
            <div className="col-sm-12 col-xl-2 d-flex justify-content-between align-items-center">
              <div className="ticket_gen_date d-xl-none">
                {convert_time(time)}
              </div>
              <span className="query-status">
                <span
                  className={`query-status-text ${
                    singleTicketData?.status === "Open"
                      ? "bg-success"
                      : singleTicketData?.status === "Closed"
                      ? "bg-danger"
                      : singleTicketData?.status === "escalated"
                      ? "bg-info"
                      : singleTicketData?.status === "onhold"
                      ? "bg-warning"
                      : ""
                  }`}
                >
                  {singleTicketData?.status}
                </span>
              </span>
            </div>
          </div>
          <div className="ticket_gen_date d-none d-xl-block">
            {convert_time(time)}
          </div>

          {loading ? (
            renderPlaceholders(8, "100px", "10px")
          ) : !error && singleTicketData && singleTicketData?.length > 0 ? (
            <div className="content-scroll-ticket overflow-auto">
              {singleTicketData
                ?.slice()
                ?.reverse()
                ?.map((threadItem, index) => (
                  <div
                    key={index}
                    className="thread-item"
                    style={{
                      marginLeft:
                        threadItem?.author_name === "Oncofit Solutions" &&
                        index !== 0 &&
                        !isSmallScreen
                          ? "0%"
                          : "0",
                      backgroundColor:
                        threadItem?.author_name === "Oncofit Solutions"
                          ? "#e5f1ff"
                          : "#f0f0f0",
                    }}
                  >
                    <div className="row flex-column flex-lg-row p-2 p-xl-0">
                      <div className="col-2 thread_name_time_avatar_section gap-2">
                        <div className="thread_avatar">
                          {threadItem?.author_name === "Oncofit Solutions" ? (
                            <>
                               {threadItem?.author_photo ? (
                                <>
                                  {" "}
                                  <Image
                                   src={threadItem?.author_photo}
                                   alt={threadItem?.author_name}
                                    width={50}
                                    height={50}
                                    style={{ borderRadius: "50%" }}
                                  />
                                </>
                              ) : (
                                <>
                                  <p className="text-center">
                                    {formatNameInitials(
                                      threadItem?.author_name
                                    )}
                                  </p>
                                </>
                              )}
                            </>
                          ) : (
                            <>
                              {threadItem?.profilephoto ? (
                                <>
                                  <Image
                                   src={threadItem?.profilephoto}
                                   alt={threadItem?.author_name}
                                    width={50}
                                    height={50}
                                    style={{ borderRadius: "50%" }}
                                  />
                                </>
                              ) : (
                                <>
                                  <p className="text-center">
                                    {formatNameInitials(
                                      threadItem?.author_name
                                    )}
                                  </p>
                                </>
                              )}
                            </>
                          )}
                        </div>
                        <div className="thread_name_time">
                          <p className="mb-0">{capitalizeFullName(threadItem?.author_name)}</p>
                          <p className="created-date-ticket">
                            {convert_time(threadItem?.CreatedTime)}
                          </p>
                        </div>
                      </div>
                      <div className="col-10 thread_summary">
                        <p>{threadItem?.summary || "No summary available"}</p>
                        <div>
                        {Array.isArray(attachements_data) &&
                            attachements_data.length > 0 && (
                              <div className="attachments-section mt-3">
                                <div className="row">
                                  {attachements_data?.map((file, idx) =>
                                    renderAttachment(file, idx)
                                  )}
                                </div>
                              </div>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="fs-4 text-center no-threads">
              <PiFolderNotchOpenFill />
              No Threads Found
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default SingleTicket;
