import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { FiClock } from "react-icons/fi";
import "./bookedAppointmentDetails.css";
import { convertIntoTime } from "../../../../utils/helperfunction";
const BookedAppointmentDetails = ({ event, eventDate, show, onClose }) => {
  return (
    <>
      <Modal show={show} onHide={onClose} centered size={"lg"}>
        <Modal.Header
          closeButton
          className=" d-flex justify-content-center align-items-center"
        >
          <Modal.Title
            className={`AppointmentModal-heading-${event?.status}  modal-title-section`}
          >
            {event?.status === "B" && "Booked Appointment Details :"}
            {event?.status === "C" && "Cancelled Appointment Details :"}
            {event?.status === "R" && "Rescheduled Appointment Details :"}
          </Modal.Title>
          <p
            style={{
              margin: "5px 0px 0px 10px",
              // color: "#8007d0",
              fontWeight: "bold",
            }}
            className={`event-status-${event?.status}`}
          >
            {eventDate}
          </p>
        </Modal.Header>
        <Modal.Body>
          <div className="d-flex">
            <div className="col-sm-auto me-2">
              <FiClock size={25} className={`event-status-${event?.status}`} />
            </div>
            <div
              className={`col-sm PatientDetail-heading event-status-${event?.status}`}
            >
              {convertIntoTime(event?.slot_start_time, event?.doctor?.TimeZone)}{" "}
              - {convertIntoTime(event?.slot_end_time, event?.doctor?.TimeZone)}
            </div>
          </div>

          <div className="row mt-2">
            <div className="form-group row">
              <label
                htmlFor="staticTitle"
                className="col-sm-3 col-form-label AppointmentModal-label"
              >
                Title
              </label>
              <div className="col-sm-9">
                <input
                  type="text"
                  readonly
                  className="form-control AppointmentModal-input"
                  id="staticTitle"
                  value={event?.summary}
                  readOnly
                />
              </div>
            </div>
          </div>

          <form>
            <h2 className="PatientDetail-heading mt-4">Patient Detail</h2>
            <div className="form-group row">
              <label
                htmlFor="staticAttendee"
                className="col-sm-3 col-form-label AppointmentModal-label"
              >
                Patient Name
              </label>
              <div className="col-sm-9 mb-1">
                <input
                  type="text"
                  readonly
                  className="form-control AppointmentModal-input"
                  id="patient-name"
                  value={event?.patient?.name}
                  readOnly
                />
              </div>
            </div>
            <div className="form-group row">
              <label
                htmlFor="inputEmail"
                className="col-sm-3 col-form-label AppointmentModal-label"
              >
                Email
              </label>
              <div className="col-sm-9 mb-1">
                <input
                  type="text"
                  className="form-control AppointmentModal-input"
                  id="inputEmail"
                  value={event?.patient?.email}
                  readOnly
                />
              </div>
            </div>
            <div className="form-group row">
              <label
                htmlFor="inputPhone"
                className="col-sm-3 col-form-label AppointmentModal-label"
              >
                Phone
              </label>
              <div className="col-sm-9 mb-1">
                <input
                  type="tel"
                  className="form-control AppointmentModal-input"
                  id="inputPhone"
                  value={event?.patient?.phone}
                  readOnly
                />
              </div>
            </div>
            <div className="form-group row">
              <label
                htmlFor="inputLocation"
                className="col-sm-3 col-form-label AppointmentModal-label"
              >
                Location
              </label>
              <div className="col-sm-9 mb-1">
                <input
                  type="tel"
                  className="form-control AppointmentModal-input"
                  id="inputLocation"
                  value={event?.location}
                  readOnly
                />
              </div>
            </div>
            <div className="form-group row">
              <label
                htmlFor="inputDescription"
                className="col-sm-3 col-form-label AppointmentModal-label"
              >
                Description
              </label>
              <div className="col-sm-9 mb-1">
                <textarea
                  // type="text"
                  className="form-control AppointmentModal-input"
                  id="inputDescription"
                  value={event?.description}
                  readOnly
                  rows={3}
                />
              </div>
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default BookedAppointmentDetails;
