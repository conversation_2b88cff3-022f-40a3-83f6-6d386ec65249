import React, { useState, useEffect } from "react";
import "./toggle.css";

const ToggleBtn = ({ isActive, onToggle, id, type }) => {
  const [active, setActive] = useState(isActive);

  useEffect(() => {
    setActive(isActive);
  }, [isActive]);

  const handleToggle = () => {
    const newStatus = !active ? 1 : 0;
    setActive(!active);
    onToggle(id, newStatus);
  };

  return (
    <div className={`toggle-container ${active ? "open" : "close"}`}>
      <input
        className="switch"
        type="checkbox"
        checked={active}
        onChange={handleToggle}
      />
    </div>
  );
};

export default ToggleBtn;
