import React from "react";
import Image from "next/image";
import profile from "../../../public/images/profile.png";
import ApprovalComponent from "@/components/experts/ApprovalComponent";

const items = [
  {
    name: "Georgia Grant",
    category: "Influencer",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Influencer",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Influencer",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Influencer",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Influencer",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Influencer",
    date: "24/06/2023",
  },
  // Add more items as needed
];

const InfluencerApprovals = () => {
  return (
    <div className="col-sm-5 custom-border">
      <div className="row">
        <div className="col-sm-9 ">
          <p>Approvals</p>
        </div>
        <div className="col-sm-3 text-center">
          <button type="button" className="btn btn-primary no-border-btn">
            Approve All
          </button>
        </div>
      </div>
      <div className=" overflow-hidden">
        <div className="content-scroll overflow-auto px-3">
          {items.map((item, index) => (
            <div key={index} className="custom-shadow2 mb-3">
              <div className="row p-0">
                <div className="col-sm-4 d-flex">
                  <Image src={profile} alt="" />
                  <p className="purple-content mt-2">{item.name}</p>
                </div>
                <div className="col-sm-2">
                  <p className="mt-2 fw-bold">{item.category}</p>
                </div>
                <div className="col-sm-2">
                  <p className="mt-2 custom-date">{item.date}</p>
                </div>
                <div className="col-sm-2 mt-1">
                  <button
                    type="button"
                    className="btn btn-primary black-btn p-1"
                  >
                    Review
                  </button>
                </div>
                <div className="col-sm-2 mt-1 ps-0">
                  <button
                    type="button"
                    className="btn btn-primary purple-btn custom-btn-width p-1 "
                  >
                    Approve
                  </button>
                </div>
              </div>
              {/* Render the ApprovalComponent here if needed */}
              <ApprovalComponent />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default InfluencerApprovals;
