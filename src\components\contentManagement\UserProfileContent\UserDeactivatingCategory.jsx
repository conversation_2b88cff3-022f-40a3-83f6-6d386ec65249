import React, { useState, useEffect, useCallback } from "react";
import { MdEdit, MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "react-bootstrap";
import Loader from "../../loader/Loader";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Swal from "sweetalert2";
import ContentManPlaceholder from "./ContentManPlaceholder";

const UserDeactivatingCategory = () => {
  const [showDeactivatedEditModal, setShowDeactivatedEditModal] =
    useState(false);
  const [listOfDeactivationCategory, setListOfDeactivationCategory] =
    useState(null);
  const [addDeactivationCategory, setAddDeactivationCategory] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(
    "Expert Deactivation"
  ); // Default to Expert Deactivation
  const [formData, setFormData] = useState({ id: "", type: "" });
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getListOfDeactivationCategory = useCallback(async () => {
    try {
      if (user_id) {
        const deactivateCategoryResponse = await axiosAuth.get(
          `${
            process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE
          }all/?Category=${encodeURIComponent(
            selectedCategory
          )}&user_id=${user_id}`
        );
        setListOfDeactivationCategory(deactivateCategoryResponse?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, selectedCategory, user_id]);

  useEffect(() => {
    getListOfDeactivationCategory();
  }, [user_id, getListOfDeactivationCategory, axiosAuth]);

  const handleAddDeactivationCategory = async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_CONTENT_TYPE}?user_id=${user_id}`,
        {
          Content: addDeactivationCategory,
          Category: selectedCategory,
        }
      );
      setLoading(false);
      if (response?.data) {
        toast.success(`Deactivation Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfDeactivationCategory();
      }
      setAddDeactivationCategory("");
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  const handleEditClick = (deactivation) => {
    setFormData({ id: deactivation.id, type: deactivation.Content });

    setShowDeactivatedEditModal(true);
  };

  const handleClose = () => {
    setShowDeactivatedEditModal(false);
  };

  const handleDeleteClick = async (designationId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${designationId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Deactivation Deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getListOfDeactivationCategory();
          }
        } catch (error) {
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${formData.id}/?user_id=${user_id}`,
        {
          Content: formData.type,
          Category: selectedCategory,
        }
      );
      if (response?.data) {
        toast.success(`Deactivation Edited Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfDeactivationCategory();
      }
      setFormData({ id: "", type: "" });
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    handleClose();
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <>
      <div className="row">
        <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
          Add Deactivation Category for users
        </h5>
        <div className="col-sm-4">
          <div className="mb-3">
            <div className="form-label custom-label">Deactivation Category</div>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="radio"
                name="category"
                id="expertCategory"
                value="Expert Deactivation"
                checked={selectedCategory === "Expert Deactivation"}
                onChange={() => setSelectedCategory("Expert Deactivation")}
              />
              <label className="form-check-label custom-font" htmlFor="expertCategory">
                Expert Deactivation
              </label>
            </div>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="radio"
                name="category"
                id="patientCategory"
                value="Patient Deactivation"
                checked={selectedCategory === "Patient Deactivation"}
                onChange={() => setSelectedCategory("Patient Deactivation")}
              />
              <label className="form-check-label custom-font" htmlFor="patientCategory">
                Patient Deactivation
              </label>
            </div>
            <div className="form-check form-check-inline mt-2">
              <input
                className="form-check-input"
                type="radio"
                name="category"
                id="adminCategory"
                value="Admin Deactivation"
                checked={selectedCategory === "Admin Deactivation"}
                onChange={() => setSelectedCategory("Admin Deactivation")}
              />
              <label className="form-check-label custom-font" htmlFor="patientCategory">
                Admin Deactivation
              </label>
            </div>
          </div>
          <div className="mb-3">
            <input
              type="text"
              className="form-control custom-form-control"
              id="designation"
              value={addDeactivationCategory}
              onChange={(e) => setAddDeactivationCategory(e.target.value)}
              placeholder="Enter Deactivation Category"
            />
          </div>
          <button
            disabled={loading}
            type="submit"
            onClick={handleAddDeactivationCategory}
            className="btn purple-button"
          >
            {loading
              ? "Adding Deactivation Category"
              : "Add Deactivation Category"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px", fontSize: "14px" }} className="fw-semibold">
                    Slno
                  </th>
                  <th scope="col" style={{  fontSize: "14px" }} className="fw-semibold">{selectedCategory} </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {listOfDeactivationCategory &&
                  listOfDeactivationCategory.map((item, index) => (
                    <tr key={index}>
                      <td scope="row" className="custom-font">{index + 1}</td>
                      <td className="custom-font">{item.Content}</td>
                      <td>
                        <button                                                         
                          className="btn btn-primary btn-sm"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item.id)}
                        >
                          <MdDelete />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      <Modal show={showDeactivatedEditModal} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Deactivation</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="formDesignationType">
            <Form.Label>{selectedCategory} Type</Form.Label>
            <Form.Control
              type="text"
              name="type"
              value={formData.type}
              onChange={handleChange}
            />
          </Form.Group>
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            onClick={handleSubmit}
          >
            Update
          </Button>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default UserDeactivatingCategory;
