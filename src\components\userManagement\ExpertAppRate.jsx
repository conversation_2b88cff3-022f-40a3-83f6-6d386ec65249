import React from "react";
import { Doughnut } from "react-chartjs-2";

const ExpertAppRate = ({ chartDatas }) => {
  const chartData = {
    datasets: [
      {
        label: "Profiles",
        data: [
          chartDatas.requests_approved,
          chartDatas.under_review,
          chartDatas.requests_approval,
          chartDatas.requests_rejected,
          chartDatas.requests_deactivated,
          chartDatas.requests_deleted,
        ],
        backgroundColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(255, 151, 26, 1)",
          "rgb(129,7,209,1)",
          "rgba(255, 46, 46, 1)",
          "#b50000",
          "#414146",
        ],
        borderColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(255, 151, 26, 1)",
          "rgb(129,7,209,1)",
          "rgba(255, 46, 46, 1)",
          "#b50000",
          "#414146",
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="row mt-2">
      <p className="heading mb-1">Expert Approval Rate</p>
      <div className="col-sm-4 pink-bg ">
        <div className="chartpadding">
          <Doughnut data={chartData} />
        </div>
      </div>
      <div className="col-sm-8">
        <div className="custom-border pink-bg">
          <div className="row">
            <p className="text-center sub-heading mb-1 mt-1">
              Expert User Approval Requests
            </p>
          </div>
          <div className="bg-white p-2 mb-1">
            <div className="row">
              <div className="col-sm-6">
                <p className="text-center  mb-2 text-muted" style={{fontSize: "13px"}}>
                  Profiles Approved
                </p>
                <p className="text-center mb-1 green-count">
                  {chartDatas.requests_approved}
                </p>
              </div>

              <div className="col-sm-6">
                <p className="text-center  mb-2 text-muted" style={{fontSize: "13px"}}>Profiles Pending</p>
                <p className="text-center orange-count mb-1">
                  {" "}
                  {chartDatas.under_review}
                </p>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                <p className="text-center  single-line  mb-2 text-muted" style={{fontSize: "13px"}}>
                  Profiles Requested
                </p>
                <p className="text-center purple-count mb-1">
                  {chartDatas.requests_approval}
                </p>
              </div>
              <div className="col-sm-6">
                <p className="text-center  mb-2 text-muted" style={{fontSize: "13px"}}>
                  Profiles Rejected
                </p>
                <p className="text-center mb-1 app-red-count">
                  {chartDatas.requests_rejected}
                </p>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                <p className="text-center single-line mb-2 text-muted" style={{fontSize: "13px"}}>
                  Profiles Deactivated
                </p>
                <p className="text-center mb-1 app-red-count">
                  {chartDatas.requests_deactivated}
                </p>
              </div>
              <div className="col-sm-6">
                <p className="text-center  mb-2 text-muted" style={{fontSize: "13px"}}>Profiles Deleted</p>
                <p className="text-center mb-1 app-black-count">
                  {chartDatas.requests_deleted}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpertAppRate;
