"use client";
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import "./profilereactivation.css";
import { useAdminContext } from "../../../Context/AdminContext/AdminContext";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { toast } from "react-toastify";
import { useParams, usePathname, useRouter } from "next/navigation";

const ProfileReactivationModal = ({
  showRequestedForApprovalModal,
  setShowRequestedForApprovalModal,
  expertProfileStatus,
  fetchDoctorDetails,
}) => {
  const [showRejectReason, setShowRejectReason] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [loading, setLoading] = useState(false);
  const { session } = useAdminContext();
  const axiosAuth = useAxiosAuth();
  const params = useParams();
  const { user_id } = params;
  const expert_id = user_id[0];
  const expert_email = decodeURIComponent(user_id[1]);
  const admin_id = session?.user?.id;
  const router = useRouter();
  const pathname = usePathname();

  const handleProfileApprovals = async (status) => {
    try {
      let body = { approval: status };
      if (status === "Rejected") {
        body.StatusChangeReason = rejectReason;
        body.ReasonType = "Rejection";
        body.ReasonCategory = "self-reactivation";
      }

      if (admin_id) {
        setLoading(true);
        const response = await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${expert_id}/?user_id=${admin_id}`,
          body
        );

        if (response?.data?.update_status === "success") {
          setLoading(false);
          toast.success(`The Profile has been ${status}`);
          setShowRequestedForApprovalModal(false);
          if (pathname.startsWith("/usermanagement/patients/")) {
            router.push(
              `/usermanagement/patients/${expert_id}/${expert_email}/${response?.data?.data?.approval}/${response?.data?.data?.name}`
            );
          } else if (pathname.startsWith("/usermanagement/experts/")) {
            router.push(
              `/usermanagement/experts/experts-doctor/${expert_id}/${expert_email}/${response?.data?.data?.approval}`
            );
          }
          if (typeof window !== "undefined") {
            // window.location.href = `/usermanagement/experts/experts-doctor/${expert_id}/${expert_email}/${response?.data?.data?.approval}`;
            // window.location.reload();
            // fetchDoctorDetails();
          }
        } else if (response?.data?.update_status == "failed") {
          toast.error(response?.data?.data);
          setShowRequestedForApprovalModal(false);
          if (typeof window !== "undefined") {
            // window.location.href = `/usermanagement/experts/experts-doctor/${expert_id}/${expert_email}/${response?.data?.data?.approval}`;
            window.location.reload();
            // fetchDoctorDetails();
          }
        }
      }
    } catch (error) {
      console.error(error);
      setLoading(false);
      toast.error(
        `Their is some error in updating ${status ? status : "status"}`,
        {
          position: "top-center",
          theme: "colored",
          autoClose: 3000,
        }
      );
      setShowRequestedForApprovalModal(false);
    }
  };

  return (
    <>
      <Modal
        show={showRequestedForApprovalModal}
        onHide={() => setShowRequestedForApprovalModal(false)}
        centered
        size="lg"
      >
        <Modal.Header closeButton>
          <Modal.Title className="activation-model-section">
            <div className="reactivation-title">
              Requested for Profile Activation.
            </div>
            <div className="activation-requested-date">
              Requested Date :{" "}
              <span className="activation-requested-date-value">
                {expertProfileStatus?.details?.self_reactivation_time
                  ?.split("T")[0]
                  ?.split("-")
                  ?.reverse()
                  ?.join("-") ||
                  expertProfileStatus?.details?.reactivation_time
                    ?.split("T")[0]
                    ?.split("-")
                    ?.reverse()
                    ?.join("-") ||
                  expertProfileStatus?.details?.resubmission_time
                    ?.split("T")[0]
                    ?.split("-")
                    ?.reverse()
                    ?.join("-")}
              </span>
            </div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-justify">
          {expertProfileStatus?.details?.self_reactivation_reason ||
            expertProfileStatus?.details?.resubmission_reason ||
            expertProfileStatus?.details?.reactivation_reason}
          {showRejectReason && (
            <div className="reject-reason-container">
              <div className="reason-for-rejecting-profile">
                {" "}
                Reason for Deactivating the request:
              </div>
              <textarea
                className="reject-reason-textarea"
                placeholder="Please provide a reason for rejection..."
                value={rejectReason}
                onChange={(e) => {
                  setRejectReason(e.target.value);
                }}
              ></textarea>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="reactivation-footer-section">
          <div className="col-4">
            {showRejectReason && (
              <Button
                onClick={() => setShowRejectReason(false)}
                className="reactivation-cancel-button"
              >
                Cancel
              </Button>
            )}
          </div>
          <div className="reactivation-approvals-section col-5">
            <Button
              onClick={() => {
                if (showRejectReason) {
                  handleProfileApprovals("Rejected");
                } else {
                  setShowRejectReason(true);
                }
              }}
              className="reactivation-reject-button"
            >
              {showRejectReason
                ? expertProfileStatus?.details?.reactivation_reason
                  ? "Reject Request"
                  : "Deny Self-Deactivation Request"
                : "Reject"}
            </Button>
            {!showRejectReason && (
              <Button
                onClick={() => handleProfileApprovals("Approved")}
                className="reactivation-approve-button"
                disabled={loading}
              >
                {loading ? "Please Wait..." : "Approve"}
              </Button>
            )}
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ProfileReactivationModal;
