.btn.grey-btn {
  background: #dfdfdf 0% 0% no-repeat padding-box;
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #414146;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.doctor-type {
  font-size: 14px;
  font-weight: normal;
  color: #5b5b5b;
}

.user-management-scroll {
  max-height: 606px;
  padding: 12px;
}

.category-select {
  background: #ffffff 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  font-size: 14px !important;
}

.cross-icon-calendar {
  color: #ff7700;
}

.calendar-icon-patient {
  color: #8107d1;
}

.appoin-with {
  font-size: 13px;
}

.cancel-app-search-btn {
  position: absolute;
  bottom: 10px;
  right: 17px;
  font-size: 16px;
}

div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm {
  background-color: #8107d1 !important;
}

.cancer-type-input {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029 !important;
  border: 1px solid #e3e3e3 !important;
  border-radius: 3px;
  height: 48px;
}

.cancer-type-input:disabled {
  background: #ffffff 0% 0% no-repeat padding-box;
}
.btn.grey-btn:hover,
.btn.grey-btn:active {
  background: #fae5ff 0% 0% no-repeat padding-box;

  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.btn.grey-btn:focus {
  background: #fae5ff 0% 0% no-repeat padding-box;
  /* box-shadow: inset 0px 3px 6px #00000029; */
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}
.custom-font-size {
  font-size: 12px;
}

.btn.grey-btn.activeExpertsTab {
  background: #fae5ff !important;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff !important;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1 !important;
  font-size: 14px;
  color: #5b5b5b !important;
  font-weight: 500;
}

.payment-back {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;

  background-color: white;
  padding: 10px;
}

.queries-background {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 610px;
}

.box-color {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 2px 3px #00000029;
  border: 1px solid #e3e3e3;
  height: 41px;
  width: 169px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
}

.box-color-2 {
  height: 41px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #5b5b5b;
  width: 450px;
  padding: 20px;
  background-color: #eeeeee;
  border-radius: 5px;
}

.queries-no-color-1 {
  color: #8107d1;
  display: flex;
  margin-left: 25%;
  /* float: right; */
  font-weight: bold;
}

.queries-no-color-2 {
  color: #5b5b5b;
  display: flex;
  margin-left: 40%;
  font-weight: bold;
}

.queries-no-color-3 {
  color: #04ab20;
  display: flex;
  margin-left: 50%;
  font-weight: bold;
}

.queries-no-color-4 {
  color: #ff971a;
  display: flex;
  margin-left: 28%;
  font-weight: bold;
}

.queiries-lines {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  padding: 22px;
  max-height: 610px;
  overflow: auto;
}

.remove-btn-style {
  border: none;
  box-shadow: none;
  background-color: transparent;
  border-right: 1px solid #dfdfdf;
  width: 3%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
}

.line-color {
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  height: auto;
  display: flex;
  cursor: pointer;
  color: #5b5b5b;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 10px;
  padding: 5px;
}

span.urgent-query {
  width: 30%;
  border-right: 1px solid #dfdfdf;
  display: flex;
  justify-content: left;
  align-items: center;
  text-align: left;
  margin-left: 2%;
}

span.query-time-patient {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align-last: right;
  /* padding-left: 3%;
  padding-right: 3%; */
  width: 20%;
}

span.query-status {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align-last: right;
  font-weight: 700;
  width: 12%;
}

.query-status-text {
  border-radius: 20px;
  padding: 1px 4px;
  min-width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

span.line-of-query-2 {
  width: 75%;
  display: flex;
  align-items: center;
  margin-left: 15px;
  /* border: 1px solid red; */
}

span.line-of-query {
  width: 75%;
  display: flex;
  align-items: center;
  margin-left: 15px;
  /* border: 1px solid red; */
}

span.query-time {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align-last: right;
  width: 25%;
  /* border: 1px solid red; */
}

.ticket-heading {
  font-weight: bold;
}

/* Your existing CSS styles */

.query-border-1 {
  border-left: 3px solid #8107d1;
  /* Adjust the color and width as needed */
}

.query-border-2 {
  border-left: 3px solid #96969c;
  /* Adjust the color and width as needed */
}

.query-border-3 {
  border-left: 3px solid #04ab20;
  /* Adjust the color and width as needed */
}

.query-border-4 {
  border-left: 3px solid #ff971a;
  /* Adjust the color and width as needed */
}

/* Add any other styles you need */
.text-advance {
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 0%;
}

.text-advance:focus {
  box-shadow: none;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 0%;
}

button.btn.btn-reply-msg.btn.btn-primary {
  width: 159px;
  height: 48px;
  float: right;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  margin: 10px 10px 10px 10px;
}

.attach-button {
  height: 72px;
  font-size: 14px;
  font-weight: bold;
  background: #ffffff 0% 0% no-repeat padding-box;
  border-left: 1px solid #e3e3e3;
  border-right: 1px solid #e3e3e3;
  border-bottom: 1px solid #e3e3e3;
}

.icon-attach {
  font-size: large;
  margin: 26px 12px 26px 16px;
}

.article-query {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
}

.article-lines {
  color: #5b5b5b;
  /* line-height: 28px; */
  font-size: 14px;
  font-weight: normal;
  /* padding: 15px; */
}

label.label-attach {
  color: #b5b2b2;
}

.flag-advance {
  float: right;
  margin-right: 10px;
}

.filters-background {
  background: #f6f6f6;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 95px;
}

.calander-patient {
  height: auto;
  background: #fef9ff 0% 0% no-repeat padding-box;
  border-radius: 3px;
}

span.date-filter {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  color: #5b5b5b;
  padding: 10px;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
}

.star-in-app {
  color: orange;
}

.calendar-icon {
  color: #8107d1;
  margin-left: 215px;
  margin-top: -3px;
  position: absolute;
}

.select-dropdown.css-b62m3t-container {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  border: none;
  margin-right: 0px !important;
}

.select-dropdown.css-b62m3t-container {
  width: 238px;
  height: auto;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  border: none;
  width: 90%;
}

/* .col-sm-9.col-size {
  width: 80%;
} */
.select-status {
  margin-top: 20px;
}

.records-files {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: auto;
  padding: 10px;
}

span.icon-file {
  font-size: 37px;
  color: #8107d1;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  /* width: 200px; */
}

.file-view-bg {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  padding: 5px;
}

button.btn.download-btn {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 5px;
}

button.btn.download-btn:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 5px;
}

button.btn.download-btn-1 {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 15px 5px 5px 5px;
}

button.btn.download-btn-1:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  color: #ffff;
  float: right;
  margin: 15px 5px 5px 5px;
}

button.btn.view-btn-patient {
  border: none;
  color: #8107d1;
  font-weight: 600;
  font-size: 16px;
  float: right;
  padding: 16px;
}

button.btn.view-btn-patient:focus {
  border: none;
  color: #8107d1;
  font-weight: 600;
  font-size: 16px;
  float: right;
  padding: 16px;
}

.date-bold {
  color: #5b5b5b;
  font-weight: 600;
  font-size: 14px;
}

.lab-report {
  font-size: 16px;
  font-weight: normal;
  color: #5b5b5b;
}

.records-patient {
  color: #5b5b5b;
  font-size: 16px;
  font-weight: bold;
}

.filter-all {
  display: inline;
  display: flex;
  margin: 33px;
  color: #5b5b5b;
  font-weight: 600;
  font-size: 16px;
  /* justify-content: center; */
}

.icon-cal {
  color: #8107d1;
  font-weight: bold;
  font-size: 25px;
  margin-top: -3px;
}

.data-range {
  font-size: 14px;
  /* margin-left: 70px; */

  font-weight: 600;
  color: #8107d1;
  text-align: left;
}

.timeline {
  font-size: 12px;
  color: #5b5b5b;
  font-weight: 500;
}

.react-calendar {
  width: 305px !important;
  font-size: 17px;
  color: #1c2126;
  font-weight: bolder;
  border: none !important;
  max-width: 100%;
  background: transparent !important;
  border: none;
  font-family: Arial, Helvetica, sans-serif !important;
}

button.btn.refresh-btn {
  text-decoration: none;
  border: none;
  color: #8107d1;
  float: right;
  font-size: 14px;
  padding: 0px;
}

button.btn.refresh-btn:focus {
  text-decoration: none;
  border: none;
  color: #8107d1;
  float: right;
}

.react-calendar__month-view__weekdays {
  letter-spacing: 0.2px;
  color: #101011;
  font-size: 11px;
  font-weight: bold;
}

.sunday-text {
  color: black;
  /* Set the default color for Sundays */
}

.sunday-text:hover {
  background-color: #000;
  /* Change this to the desired hover color for Sundays */
}

.react-calendar__tile--active {
  background-color: #8107d1 !important;
  color: #ffffff !important;
  border-radius: 10px !important;
}

.react-calendar .react-calendar__tile--range:hover {
  background: #8107d1 0% 0% no-repeat padding-box !important;
  color: #ffffff;
  border-radius: 5px;
  /* Change this to the desired hover color for the selected range */
}

.react-calendar .react-calendar__tile--range:active {
  background: #8107d1 0% 0% no-repeat padding-box !important;
  color: #ffffff;
  border-radius: 5px;
  /* Change this to the desired hover color for the selected range */
}

.start-date {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for start date */
}

.start-date:focus {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for start date */
}

.end-date {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for end date */
}

.end-date:focus {
  background-color: #8107d1;
  color: #ffff;
  /* Yellow for end date */
}

.selected-range {
  background-color: transparent;
  color: white !important;
  /* Purple for selected range */
}

.selected-range {
  background-color: #8107d1 !important;
  color: white;
  /* Purple for selected range */
  border-radius: 5px;
}

.selected-range:focus {
  background-color: #8107d1 !important;
  color: white;
  /* Purple for selected range */
}

.class-span {
  display: flex;
  margin-top: 10px;
}

.react-calendar__month-view__weekdays {
  color: #9fa1a3;
  font-weight: bold;
  font-size: 12px;
  border: none;
}

.react-calendar .react-calendar__tile--now {
  color: #1c2126;
  /* Change this to the desired color for the current date */
  background-color: transparent;
}

.react-calendar .react-calendar__tile--now:hover {
  color: #1c2126;
  /* Change this to the desired color for the current date */
  background-color: transparent;
}

.circle-time {
  width: 15px;
  height: 15px;
  background-color: #ff971a;
  border-radius: 50%;
  display: inline-block;
  /* margin-top: 48px; */
  margin-left: 23px;
}

.vertical-line {
  margin-top: -6px;
  position: relative;
  width: 2px;
  height: 70px;
  background-color: #dfdfdf;
  margin-left: 29px;
  /* top: 13px; */
}

.circle-end {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #ff971a;
  margin-left: 23px;
}

button.btn.btn-back {
  color: #8107d1;
  font-weight: 500;
  border: none;
  font-size: 16px;
}

button.btn.btn-back:focus {
  color: #8107d1;
  border: none;
  font-weight: 500;
  font-size: 16px;
}

.profile-picture-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.profile-picture {
  width: 150px;
  height: 150px;
  overflow: hidden;
  border-radius: 50%;
  margin-top: 20px;
}

.profile-picture img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

input.form-control.form-fildes-read {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: auto;
  font-size: 14px;
}

textarea.form-control.text-form-fileds {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  font-size: 12px;
}

span.medical-info {
  color: #5b5b5b;
  font-weight: bold;
  font-size: 15px;
}

button.btn.btn-view-all {
  float: right;
  text-decoration: none;
  border: none;
  color: #5b5b5b;
  font-weight: bold;
  font-size: 15px;
}

.custom-font-size {
  font-size: 12px;
}

button.btn.btn-view-all:focus {
  float: right;
  text-decoration: none;
  border: none;
  color: #5b5b5b;
  font-weight: bold;
  font-size: 15px;
}

.info-bg {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 3px;
  padding: 20px;
}

.plans-bg {
  height: 103px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
}

button.btn.btn-save-changes {
  /* background: #96969c 0% 0% no-repeat padding-box; */
  background: #414146 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  height: 48px;
  /* display: flex; */
  float: right;
  color: white;
}

.profile-pic-wrapper {
  /* margin-top: 45px; */
  /* width: 100%; */
  /* position: relative;
  display: flex;
  align-items: baseline; */
}

.pic-holder {
  text-align: center;
  position: relative;
  /* border-radius: 50%; */
  /* width: 100px; */
  /* height: 100px; */
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  /* margin-bottom: 20px; */
  border: 1px solid #9889a3 !important;
  border-radius: 50%;
}

.pic-holder .pic {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
}

.pic-holder .upload-file-block,
.pic-holder .upload-loader {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(90, 92, 105, 0.7);
  color: #f8f9fc;
  font-size: 12px;
  font-weight: 600;
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.pic-holder .upload-file-block {
  cursor: pointer;
}

.pic-holder:hover .upload-file-block,
.uploadProfileInput:focus ~ .upload-file-block {
  opacity: 1;
}

.uploadProfileInput {
  padding-left: 5px !important;
}

.pic-holder.uploadInProgress .upload-file-block {
  display: none;
}

.pic-holder.uploadInProgress .upload-loader {
  opacity: 1;
}

button.btn.btn-save-changes:focus {
  background: #96969c 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  height: 48px;
  /* display: flex; */
  float: right;
}

button.btn.btn-save-changes-color-change {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  height: 48px;
  /* display: flex; */
  float: right;
  color: white;
}

.plans-taken {
  letter-spacing: 0.44px;
  color: #52575d;
  font-size: 14px;
}

.plan-taken-cost {
  letter-spacing: 0.66px;
  color: #52575d;
  font-weight: bold;
  font-size: 21px;
}

button.btn.control-profile {
  background: #414146 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #f6f6f6;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  float: right;
  width: 100%;
}

button.btn.control-profile:focus {
  background: #414146 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #f6f6f6;
  border-radius: 3px;
  border: none;
  /* height: 48px; */
  color: #ffffff;
  float: right;
}

.savebtnedit {
  box-shadow: 0px 3px 6px #00000029;
  background-color: #00000029;
}

.profile-bg-control {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  /* width: 90%;
    float: inline-end; */
}

span.profile-active {
  color: #8107d1;
  font-weight: bold;
  font-size: 14px;
}

span.profile-last-edited {
  color: #5b5b5b;
  font-size: 14px;
  font-weight: normal;
}

svg.editprofile {
  position: absolute;
  margin-top: 50px;
  height: 35px;
  width: 35px;
  padding: 6px;
  border-radius: 50%;
  border: 50%;
  color: #8107d1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 2px solid #dfdfdf;
  font-size: 25px;
  margin-left: -17px;
}

.form-label-style {
  font-weight: bold;
  font-size: 14px;
  color: #5b5b5b;
}

.delete-from-storage {
  float: right;
  color: #ff2e2e;
  font-weight: bold;
  font-size: 14px;
}

span.name-doctor {
  font-size: 12px;
  font-weight: normal;
  color: #8107d1;
  cursor: pointer;
}
.appointment-text{
  font-size: 12px;
  font-weight: 600;
  color: #8107d1;
  cursor: pointer;
}

.appointment-bg {
  height: 170px;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  padding: 10px;
}
.yellow-bg-status {
  background-color: var(--toastify-color-progress-dark);
  color: white;
  padding: 2px 10px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
}
.video-app {
  height: 200px;
  background: #dfdfdf 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
}

.remaing-time {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;

  background: linear-gradient(to right, #e3e3e3 60%, #414146 50%);
  border-radius: 3px;
}

.star-color {
  font-size: 18px;
  color: #f37721;
  font-weight: 600;
}
.appointment-prescription-status {
  font-size: 12px;
}

button.btn.btn-prescription {
  width: 100%;
  height: 38px;
  font-size: 12px;
  background: #fae5ff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  color: #8107d1;
}
button.btn.btn-prescription:disabled {
  opacity: 0.5;
  border: 0px solid transparent;
  cursor: not-allowed;
}
button.btn.approve-cancel {
  width: 100%;
  height: 38px;
  font-size: 14px;
  background: rgb(180, 227, 180) 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  color: green;
}

.review-appointment {
  background: #fbfbfb;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  /* height: 138px; */
  padding: 10px;
}

.review-appointment-shadow {
  box-shadow: 0px 3px 6px #00000029;
  background: #fbfbfb;
  border-radius: 5px;
  height: 170px;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.review-lines {
  height: 100px;
  min-height: 100px;
  overflow: auto;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px;
}

.content-scroll {
  max-height: 560px;
  /* overflow-y: scroll; */
  padding: 5px;
}

.content-scroll-2 {
  max-height: 260px;
  /* overflow-y: scroll; */
  overflow-x: hidden;
}

.content-scroll-2::-webkit-scrollbar {
  display: none;
}

.content-scroll::-webkit-scrollbar {
  display: none;
}

button.btn.hide-Review {
  height: 38px;
  color: #ff2e2e;
  width: 100%;
  border: none;
  font: 14px;
  background: #fff3f3 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
}

button.btn.hide-Review:focus {
  height: 38px;
  color: #ff2e2e;
  font-size: 14px;
  border: none;
  width: 100%;
  background: #fff3f3 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
}

span.timeline-1 {
  font-size: 12px;
  font-weight: 500;
  color: #5b5b5b;
}

.no-articles-found {
  min-height: 200px;
}

.rotate {
  animation: rotation 1s infinite linear;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.ticket_id {
  color: #5b5b5b;
  /* line-height: 28px; */
  font-weight: 35px;
  font-weight: bold;
  font-size: 2rem;
  margin-bottom: 2%;
}

.ticket_back_button {
  border: none;
  background-color: transparent;
  color: #8107d1;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ticket_heading {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1%;
}

.ticket_gen_date {
  color: #8107d1;
  font-weight: bold;
  font-size: 0.8rem;
  margin-bottom: 0.6%;
}

.span_ticket_title,
.span_ticket_summary {
  margin-bottom: 1%;
}

.span_ticket_summary {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
}

.span_ticket_title {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
  font-size: 1em;
}

input.form-control.cummunication-search-bar {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  background-color: white !important;
}

.communication-search-icon {
  /* box-shadow: 0px 3px 6px #00000029; */
  border: 1px solid #e3e3e3;
  border-left: none;
  border-radius: 5px;
  opacity: 1;
  background-color: white !important;
}

.communication-search-icon:hover {
  border: 1px solid #e3e3e3;
  border-left: none;
  border-radius: 5px;
  opacity: 1;
  background-color: white !important;
}

.communication-search-icon:active {
  border-color: #e3e3e3 !important;
  border: 1px solid;
  border-left: none;
  border-radius: 5px;
  opacity: 1;
  background-color: white !important;
}

.orange-btn {
  background-color: #ff971a;
  color: white;
  padding: 10px;
  padding-right: 10%;
  padding-left: 10%;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
}

.orange-btn:hover {
  background-color: #ff971a;
  color: white;
  padding: 10px;
  padding-right: 40px;
  padding-left: 40px;
}

.rotating-refresh-icon {
  transition: transform 0.5s;
  cursor: pointer;
  color: #8107d1;
}

.rotating-refresh-icon:hover {
  cursor: pointer;
  color: #8107d1;
}

.custom_refresh {
  cursor: pointer;
  color: #8107d1;
}

/* .custom-form-select {
  height: 45px;
} */

select#ticket_select_box {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  box-shadow: none;
}

.no-Patient-tickets-found {
  height: 100%;
}

.PiFolderNotchOpenFill_icon {
  color: #8107d1;
  size: 50;
}

.thread-item {
  display: flex;
  background-color: #e9e6e6;
  border-radius: 10px;
  align-items: center;
  height: auto;
  padding: 2%;
  margin-bottom: 2%;
}

.thread_name_time_avatar_section {
  display: flex;
  justify-content: space-around;
  align-items: start;
  align-content: center;
  width: 18%;
  height: auto;
}

.thread_avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #8107d1;
  color: white;
  text-align: center;
  position: relative;
}

.thread_avatar > p {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.created-date-ticket {
  font-weight: 600;
}

.thread_name_time {
  font-size: 12px;
}

/* .thread_summary {
  width: 65%;
} */
.preview-doc {
  /* width: 6%; */
  padding: 10px;
  font-size: 30px;
  /* margin-left: auto; */
}

/* In your CSS file */
.placeHolder_loading {
  margin-bottom: 4%;
  /* background-color: #f0f0f0; */
  border-radius: 8px;
  /* Add rounded corners */
  /* 
  padding: 15px; */
}

.clear {
  cursor: pointer;
  color: #8107d1;
  font-weight: bold;
}

.download_appointments_records {
  color: #8107d1;
  font-size: 14px;
  cursor: pointer;
}

.download_appointments_records_icon {
  cursor: pointer;
  color: #8107d1;
  size: 30;
}

.calender_title_selection {
  padding-top: 3%;
  display: flex;
  justify-content: space-evenly;
}

.download_section {
  margin-left: 10px;
}

.swal-confirm-button-class {
  background-color: #4caf50 !important;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  margin-right: auto;
  /* This will push the button to the right */
  margin-left: 15px;
}

.swal-confirm-button-class:focus-visible {
  outline: none;
}

.swal-cancel-button-class {
  background-color: #ef5f12;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  margin-right: 15px;
}

.swal2-actions {
  width: 100%;
}

.custom-input-Country {
  box-shadow: none;
}

.search-input-patient {
  background: #ffffff !important;
  box-shadow: 0px 3px 6px #00000029;
  font-size: 14px;
  color: #5b5b5b;
  border-radius: 5px;
}

.input-group-text.custom-search-icon {
  background-color: white;
  border-left: none;
}

.calendar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  z-index: 1;
}

.calendar-container {
  position: relative;
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.download-icon-disable {
  color: gray;
  pointer-events: none;
  opacity: 0.5;
}

.form-select.select-font {
  font-size: 13px !important;
  box-shadow: 0px 3px 6px #00000029;
  height: 37px;
  border: none;
  border-radius: 0px;
}

.form-select.select-font:focus {
  border: none;
  box-shadow: 0px 3px 6px #00000029;
}

.search-input-focus:focus {
  border-color: transparent;
  box-shadow: none;
}

.query-status-text-badge {
  border-radius: 20px;
  padding: 2px 4px 2px 4px;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.ticket-status {
  cursor: pointer;
}

/* Hide the scrollbar but allow scrolling */
.queiries-lines {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
  overflow-y: scroll;
  /* Hide scrollbar but allow scrolling */
}

/* Hide scrollbar for Webkit browsers (Chrome, Safari) */
.queiries-lines::-webkit-scrollbar {
  display: none;
}

/* General scroll bar styling */
/* .queiries-lines {
  max-height: 85vh;
} */

.queries-no-color-5 {
  color: #ff2e2e;
  display: flex;
  margin-left: 50%;
  font-weight: bold;
}

/* patient payment section */
.custom-width {
  width: 100%;
}

.custom-overflow-2 {
  max-height: 560px;
  /* height: 680px; */
}

.custom-overflow-2::-webkit-scrollbar {
  display: none;
}

.content-holder-2 {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e1e1e1;
  border-radius: 5px;
  opacity: 1;
}

.payments-label {
  padding-left: 10px;
  padding-right: 10px;
}

.payment-success-bg {
  background-color: #4caf50;
}

.payment-info {
  letter-spacing: 0px;
  color: #c0c0c0;
  opacity: 1;
  font-size: 14px;
}

button.black-button {
  background: #3d3d3d 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  color: white;
  width: 100%;
  /* padding-right: 40px;
padding-left: 40px; */
  font-size: 14px;
  border: none;
  height: 40px;
}

.filters {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  opacity: 1;
  padding: 10px;
  /* height: 100%; */
  min-height: 620px;
}

.custom-heading {
  letter-spacing: 0px;
  color: #414146;
  opacity: 1;
  font-size: 16px;
  font-weight: 600;
}

.expert-search-bar {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  font-size: 13px;
  color: #5b5b5b;
  height: 40px;
  border: 1px solid #dee2e6;
}

.app-search-filter:focus {
  box-shadow: none;
}

.app-search-filter::placeholder {
  color: #212529;
  font-size: 14px;
}

.cancel-expert-search-btn {
  z-index: 99;
  position: absolute;
  top: 0px;
  right: -5px;
}

.custom-select {
  box-shadow: 0px 3px 6px #00000029;
  opacity: 1;
  padding: 10px;
}

.custom-select:focus {
  box-shadow: 0px 3px 6px #00000029;
  opacity: 1;
  padding: 10px;
  border: 1px solid transparent;
}

.calender-filter-container {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  width: 100%;
  position: relative;
}

span.date-filter-expert {
  background: #ffffff;
  border-radius: 3px;
  padding: 13px 6px 7px;
  display: inline-flex;
  font-size: 14px;
}

.calender-icon-calendar {
  color: #8107d1 !important;
}

/* .grey-bg {
  background-color: #eeeeee;
  padding: 20px;
} */

span.badge.rounded-pill.pill-bg {
  background-color: #5b0374;
  box-shadow: 0px 3px 6px #00000029;
  opacity: 1;
  padding: 2px 5px 2px 5px;
  font-size: 12px;
  font-weight: 500;
}

.purple-btn {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  background-color: #9426b2;
  font-size: 11px;
  border: 1px solid transparent;
  height: 38px;
  width: 100%;
}

.purple-btn.custom-btn {
  width: 100%;
  font-size: 14px;
}

.headerImage1 {
  width: 90px;
  height: 41px;
  object-fit: contain;
}

p.purple-content-2 {
  color: #713c8f;
  font-size: 14px;
  font-weight: 600;
}

p.black-content-2 {
  color: #5b5b5b;
  font-size: 12px;
  font-weight: 500;
}

button.purple-download-button {
  border: none;
  background-color: #713c8f;
  color: white;
  padding: 0px;
  /* font-weight: 600; */
  font-size: 11px;
  padding: 5px;
  border-radius: 20px;
  padding-left: 10px;
  padding-right: 10px;
}

button.purple-button {
  border: none;
  background-color: transparent;
  color: #9426b2;
  padding: 0px;
  font-weight: 600;
}

/* .ticket_main_container {
  margin-top: 7%;
  height: auto;
} */

.custom-small-image {
  width: 80px;
  height: 50px;
  /* border: 1px solid red; */
}
