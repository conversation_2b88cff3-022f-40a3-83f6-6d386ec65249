import React, { useCallback, useEffect, useState } from "react";
import { IoCloseOutline, IoCloseSharp } from "react-icons/io5";
import SearchLoader from "../../../administratorDasboard/searchLoader/SearchLoader";
import { GrSearch } from "react-icons/gr";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../../../lib/hooks/useAxiosAuth";

const ChildAdminFilters = ({
  startDate,
  endDate,
  searchQuery,
  handleDateChange,
  debouncedSearch,
  totalChildAdmin,
  handleSearchChange,
  loading,
  handleClearSearch,
  designation,
  handleDesignationChange,
  handleClearDesignationSearch,
  setDesignation,
  setCurrent_Page,
}) => {
  // const handleSearchChange = (event) => {
  //   const value = event.target.value;
  //   setSearchQuery(value);
  //   setDesignation(value);
  //   fetchChildAdminsList(value);
  // };
  const [childAdminDesignationList, setChildAdminDesignationList] = useState(
    []
  );
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const handleKeyDown = (event) => {
    if (event.key === "Backspace") {
      debouncedSearch(searchQuery.slice(0, -1));
    }
  };

  const getListOfChildAdminDesignationList = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}all/?Category=Designation&user_id=${user_id}`
        );
        setChildAdminDesignationList(response?.data);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  useEffect(() => {
    getListOfChildAdminDesignationList();
  }, [getListOfChildAdminDesignationList]);

  return (
    <div className="col-sm-12">
      <div className="grey-bg">
        <div className="row ">
          <div className="col-sm-4">
            <p className="heading mb-0">
              Total Child Admin - <span className="total-child-admin"></span>
              {totalChildAdmin}
            </p>
          </div>
          <div className="col-sm-4">
            <div className="d-flex expert-count-filter">
              <div className="input-group mb-0 custom-form-select-date">
                <input
                  style={{ height: "38px", borderRadius: "3px" }}
                  type="date"
                  className="form-control search-input-focus"
                  aria-label="Sizing example input"
                  aria-describedby="inputGroup-sizing-sm"
                  onChange={(event) => {
                    handleDateChange(event, "startDate");
                    setCurrent_Page(1);
                  }}
                  value={startDate}
                />
              </div>

              <div className="input-group mb-0 custom-form-select-date">
                <input
                  style={{ height: "38px", borderRadius: "3px" }}
                  type="date"
                  className="form-control search-input-focus"
                  aria-label="Sizing example input"
                  aria-describedby="inputGroup-sizing-sm"
                  onChange={(event) => {
                    handleDateChange(event, "endDate");
                    setCurrent_Page(1);
                  }}
                  value={endDate}
                />
              </div>
            </div>
          </div>
          <div className="col-sm-2 position-relative">
            <div className="input-group mb-3 search-input-patient">
              <input
                type="text"
                style={{ height: "38px", borderRadius: "3px", border: "none" }}
                className="form-control search-input-focus"
                placeholder="Search by Name"
                aria-label="Recipient's username"
                aria-describedby="button-addon2"
                value={searchQuery}
                // onChange={(e) => debouncedSearch(e.target.value)}
                onChange={handleSearchChange}
                // onKeyDown={handleKeyDown}
              />

              <span
                className="input-group-text custom-search-icon"
                id="button-addon2"
                style={{borderRadius: "5px"}}
              >
                {!searchQuery && <GrSearch />}
              </span>

              <style jsx>{`
                ::placeholder {
                  color: #212529;
                }
              `}</style>
              {!loading && searchQuery && (
                <IoCloseSharp
                  className="clear-search-icon position-absolute"
                  style={{
                    fontSize: "20px",
                    cursor: "pointer",
                    right: "6px",
                    top: "10px",
                    color: "#7B009C",
                  }}
                  onClick={handleClearSearch}
                />
              )}
              {loading && (
                <div className=" me-2">
                  <SearchLoader />
                </div>
              )}
            </div>
          </div>

          <div className="col-sm-2">
            <div className="input-group mb-3 search-input-patient">
              <select
                id="childAdminDesignationList"
                className="form-select custom-form-select-patient"
                value={designation}
                onChange={(e) => setDesignation(e.target.value)}
              >
                <option>Select Designation</option>
                {childAdminDesignationList &&
                  Array.isArray(childAdminDesignationList) &&
                  childAdminDesignationList.map((option,index) => (
                    <option key={index} value={option?.Content}>
                      {option?.Content}
                    </option>
                  ))}
              </select>
              {designation && (
                <span
                  className="input-group-text"
                  onClick={() => setDesignation("")}
                  style={{ cursor: "pointer" }}
                >
                  <IoCloseOutline />
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChildAdminFilters;
