import React from "react";
import { FaCirclePlay } from "react-icons/fa6";
import ReactPlayer from "react-player";

const Preview = () => {
  return (
    <>
      <div className="row">
        <div className="col-sm-10 mx-auto">
          <ReactPlayer
            url="https://www.youtube.com/watch?v=LXb3EKWsInQ"
            controls
            width="100%"
            height="300px"
          />

          <div className="row">
            <div className="col-sm-auto">
              <p>Podcast on Advanced Treatment of cancer cells</p>
            </div>
            <div className="col-sm d-flex justify-content-end">
              <p>56:26 mins</p>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-sm-10 mx-auto">
          <p className="">Description</p>
          <p className="">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua. Bibendum
            est ultricies integer quis. Iaculis urna id volutpat lacus laoreet.
            Mauris vitae ultricies leo integer malesuada. Ac odio tempor orci
            dapibus ultrices in. Egestas diam in arcu cursus euismod. Dictum
            fusce ut placerat orci nulla. Tincidunt ornare
          </p>
        </div>
      </div>

      <div className="row mb-2">
        <div className="col-sm-10 mx-auto">
          <div className="transcript-sec p-4">
            <div className="row">
              <p>Transcript</p>
            </div>
            <div className=" overflow-hidden">
              <div className="content-scroll-2 overflow-auto px-3">
                <div className="row">
                  <div className="col-sm-2">
                    <span className="fw-medium blue-text">00:00</span>
                  </div>
                  <div className="col-sm-10">
                    <p className="fw-light">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit,
                      sed do eiusmo
                    </p>
                  </div>
                </div>
                <div className="row">
                  <div className="col-sm-2">
                    <span className="fw-medium blue-text">00:00</span>
                  </div>
                  <div className="col-sm-10">
                    <p className="fw-light">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit,
                      sed do eiusmo
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Preview;
