.viewConsent-modal-width {
  max-width: 70%;
}
.model_title_doctor_consent > div {
  color: #8107d1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* .bg-color {
  background-color: #f0f0f5;
  padding: 20px;
} */
.consentform-custom-overflow {
  max-height: 900px;
  overflow-x: hidden;
}
.consentform-custom-overflow::-webkit-scrollbar {
  display: none;
}
.docPat-consent-form-box {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 12px;
  background-color: white;
  /* border: 1px solid red; */
  width: 98%;
}
.bg-heading {
  background: transparent linear-gradient(180deg, #8107d1 0%, #ca00d9 100%) 0%
    0% no-repeat padding-box;
  border-radius: 5px;
}

.consent-heading {
  color: white;
  font-size: 18px;
}

.consent-custom-overflow {
  max-height: 600px;
}

.content-scroll::-webkit-scrollbar {
  display: none;
}
.consent-addContent {
  background-color: #efeff4;
  padding: 30px;
}
.form-control.custom-form-placeholder::placeholder {
  font-weight: 500;
  font-size: 14px;
  color: black;
}
.custom-form-label {
  letter-spacing: -0.15px;
  color: #4a2f91;
  font-size: 17px;
  font-weight: 400;
}
input.form-control.custom-form-control {
  border-radius: 5px;
  background-color: white !important;
  border: none;
}
input.form-control.custom-form-control:focus {
  border-radius: 5px;
  background-color: white !important;
  border: none;
  box-shadow: none;
}
.custom-para {
  letter-spacing: -0.13px;
  font-size: 14px;
  border-radius: 10px;
  margin-bottom: 1%;
}
button.btn.orange-btn,
button.btn.orange-btn:hover,
button.btn.orange-btn:active,
button.btn.orange-btn:focus {
  background-color: #f37721 !important;
  border-radius: 16px;
  color: white;
  font-size: 12px;
  padding-right: 20px;
  padding-left: 20px;
  border: 1px solid #f37721 !important;
}
button.btn.orange-btn:disabled {
  opacity: 0.5;
}

.user_signature {
  pointer-events: none;
}
