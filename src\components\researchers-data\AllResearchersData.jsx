"use client";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import DashboardStatistics from "../experts/DashboardStatistics";
import Pagination from "../experts/Pagination";
import SearchDataBar from "../experts/SearchDataBar";
import ExpertsList from "../experts/ExpertsList";
import ExpertsApproval from "../experts/ExpertsApproval";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import Loading from "../Loading/PageLoading/Loading";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { countUserRoles, formatDateToYMD } from "../../utils/helperfunction";
import _ from "lodash";
import CustomPagination from "../CustomPagination/CustomPagination";

const AllResearchersData = () => {
  const [unApprovedExperts, setUnApprovedExperts] = useState();
  const [researcherData, setResearcherData] = useState();
  const [loading, setLoading] = useState(true);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [researcherCount, setResearcherCount] = useState({
    approval_requested: 0,
    approved: 0,
    deactivated: 0,
    pending: 0,
    rejected: 0,
    deleted: 0,
    selfDeactivated: 0,
    totalDoctorNumber: 0,
  });
  const fetchedDataRef = useRef({});

  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const user_id = session?.user?.id;

  const handleSearchChange = (query) => {
    setSearchQuery(query);
    setCurrent_Page(1);
  };


  const handleStatusChange = (status) => {
    setSelectedStatus(status);
    setCurrent_Page(1);
  };

  const ResesHeaderTitles = [
    "Researchers Name",
    "Speciality",
    "Date of Application",
    "Date of Activation",
    "Status",
  ];

  const ResearcstatsData = [
    {
      heading: "Researchers",
      value: researcherCount?.["total reseracher number"],
    },
    { heading: "Approved", value: researcherCount?.approved },
    {
      heading: "Requested",
      value: researcherCount?.approval_requested,
    },
    { heading: "Pending", value: researcherCount?.pending },
    { heading: "Rejected", value: researcherCount?.rejected },
    { heading: "Deactivated", value: researcherCount?.deactivated },
    { heading: "Deleted", value: researcherCount?.deleted },
  ];

  const approveSingleExpert = async (expertId) => {
    const body = { approval: "Approved" };
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${expertId}/?user_id=${user_id}`,
        body
      );
      if (response?.data?.message === "approved") {
        toast.success("Expert approved successfully!", {
          theme: "colored",
          autoClose: 1500,
        });
        approveDoctors();
      } else {
        toast.error("Failed to approve expert. Please try again.", {
          theme: "colored",
          autoClose: 1500,
        });
        console.error("Failed to approve expert:", response);
      }
    } catch (err) {
      console.log("error in fetching ", err);
    }
  };

  const countTotal = countUserRoles(unApprovedExperts);

  const getAllApprovedUnapprovedExpertsList = useCallback(
    async (query) => {
      try {
        let url = `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}${selectedStatus}/researcher/?user_id=${user_id}`;

        if (startDate && endDate && user_id) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}&page=${current_page}&per_page=6`;
        } else if (query && user_id) {
          url += `&name=${query}&page=${current_page}&per_page=6`;
        } else if (!startDate && !endDate && !query) {
          url += `&page=${current_page}&per_page=6`;
        }

        // Check if data for this URL is already fetched
        if (fetchedDataRef.current[url]) {
          const cachedData = fetchedDataRef.current[url];
          setResearcherCount(cachedData?.researcher_no);
          setTotalPages(cachedData?.total_pages);

          // const requestApprovalResearchersData =
          //   cachedData?.experts_data?.filter(
          //     (user) => user.approval === "Approval_requested"
          //   );

          // const requestApprovalResearchersData =
          //   cachedData?.approval_requested_data?.researcher_requests;
          // setUnApprovedExperts(requestApprovalResearchersData);

          const expertiseData = await axiosAuth.get(
            process.env.NEXT_PUBLIC_FETCH_DOCTOR_EXPERTISE
          );

          // Map expertise IDs to names
          const expertiseMap = {};
          expertiseData?.data?.forEach((expertise) => {
            expertiseMap[expertise.id] = expertise.name;
          });

          // Update doctors' expertise to include names
          const expertsWithExpertiseNames = cachedData?.experts_data?.map(
            (doctor) => {
              const expertises = doctor?.expertise.map(
                (id) => expertiseMap[id]
              );
              return { ...doctor, expertiseNames: expertises };
            }
          );
          setResearcherData(expertsWithExpertiseNames);
        } else {
          const data = await axiosAuth.get(url);

          fetchedDataRef.current[url] = data?.data; // Store fetched data in useRef
          setResearcherCount(data?.data?.researcher_no);
          // const reversedResearcherData = data?.data?.experts_data
          //   ?.slice()
          //   .reverse();
          // setResearcherData(reversedResearcherData);
          setTotalPages(data?.data?.total_pages);

          // const requestApprovalresearchersData =
          //   data?.data?.experts_data?.filter(
          //     (user) => user.approval === "Approval_requested"
          //   );
          const requestApprovalresearchersData =
            data?.data?.approval_requested_data?.researcher_requests;

          setUnApprovedExperts(requestApprovalresearchersData);

          const expertiseData = await axiosAuth.get(
            process.env.NEXT_PUBLIC_FETCH_DOCTOR_EXPERTISE
          );

          // Map expertise IDs to names
          const expertiseMap = {};
          expertiseData?.data?.forEach((expertise) => {
            expertiseMap[expertise.id] = expertise.name;
          });

          // Update doctors' expertise to include names
          const expertsWithExpertiseNames = data?.data?.experts_data?.map(
            (doctor) => {
              const expertises = doctor?.expertise.map(
                (id) => expertiseMap[id]
              );
              return { ...doctor, expertiseNames: expertises };
            }
          );
          setResearcherData(expertsWithExpertiseNames);
        }
      } catch (err) {
        console.log("error in fetching ", err);
      } finally {
        setLoading(false);
      }
    },
    [user_id, startDate, endDate, selectedStatus, axiosAuth, current_page]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      getAllApprovedUnapprovedExpertsList(query);
    }, 500);
  }, [getAllApprovedUnapprovedExpertsList]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  return (
    <>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="bg-color">
          <div className=" overflow-hidden">
            <div className="user-management-scroll overflow-auto">
              <SearchDataBar
                heading="Researchers Data"
                onSearchChange={handleSearchChange}
                // expertsList={currentItems}
                onStatusChange={handleStatusChange}
                selectedStatus={selectedStatus}
                searchQuery={searchQuery}
                startDate={startDate}
                endDate={endDate}
                setEndDate={setEndDate}
                setStartDate={setStartDate}
                setSearchQuery={setSearchQuery}
                totalExperts={ResearcstatsData[0]?.value}
                expert={"Researcher"}
              />

              <div className="row">
                <div className="col-sm-7">
                  <DashboardStatistics
                    expertsPendingCount={countTotal?.researcherCount}
                    statsData={ResearcstatsData}
                  />
                  <ExpertsList
                    expertiseListName="Researchers List"
                    expertsList={researcherData}
                    headerTitles={ResesHeaderTitles}
                    searchQuery={searchQuery}
                  />
                  <div className="d-flex justify-content-center align-items-center">
                    <div className="d-none d-xl-block">
                      <CustomPagination
                        total_pages={totalPages}
                        current_page={current_page}
                        setCurrent_Page={setCurrent_Page}
                      />
                    </div>
                  </div>
                </div>
                <ExpertsApproval
                  loading={loading}
                  expertsApprovals={unApprovedExperts}
                  approveSingleExpert={approveSingleExpert}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AllResearchersData;
