import React from "react";
import { BiSolidDownArrow } from "react-icons/bi";

const TableHead = ({ headerTitles }) => {

  return (
    <thead className="custom-border">
      <tr className="custom-name">
        {headerTitles.map((title, index) => (
          <th key={index} scope="col" className="text-center table-heading-custom">
            {title}
          </th>
        ))}
       
      </tr>
    </thead>
  );
};

export default TableHead;
