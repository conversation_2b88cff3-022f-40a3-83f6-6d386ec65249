ul.pagination.custom-pagination {
  --bs-pagination-border-color: white;
}

.custom-page-link:hover {
  background-color: transparent;
  border: none;
}
.custom-page-link:focus {
  background-color: transparent;
  border: none;
  box-shadow: none;
}
.page-link.active.page-link {
  background-color: transparent !important;
  border-bottom: 2px solid #8107d1 !important;
  border: none;
  color: #8107d1;
}

.active-index:hover {
  /* background-color: white !important; */
  color: #8107d1;
}
.active-index:focus {
  /* background-color: white !important; */
  color: #8107d1;
}

.purple-content {
  color: #8107d1;
}
.total-expert-due-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  border: 1px solid #ffffff;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000012;
  border-radius: 10px;
  opacity: 1;
}
.total-expert-due-download {
  cursor: pointer;
  font-weight: bold;
  padding-right: 4%;
}
.total-expert-dues {
  width: 40%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2% 2% 2% 0%;
  font-weight: bold;
}

.total-expert-due-download-btn {
  color: #8107d1;
  size: 25;
  cursor: pointer;
}

.total-due-amount {
  color: #8107d1;
  font-weight: bold;
}

.expert-dues-tooltip.tooltip-inner {
  background-color: transparent;
}

.no-Patient-tickets-found {
  min-height: 400px;
}

.custom-pdf-document {
  backdrop-filter: blur(0px) !important;
}
