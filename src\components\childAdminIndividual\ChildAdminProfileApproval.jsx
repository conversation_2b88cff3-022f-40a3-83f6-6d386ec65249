"use client";
import Cookies from "js-cookie";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import React, { useState } from "react";
import Dropdown from "react-bootstrap/Dropdown";
import { toast } from "react-toastify";
import "../AllHead/allhead.css";
import ExpertProfileStatus from "../Modals/ExpertProfileStatus";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import Swal from "sweetalert2";

const ChildAdminProfileApproval = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [isProfileRestricted, setIsProfileRestricted] = useState(false);
  const [deactivationReason, setDeactivationReason] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [reasonCategory, setReasonCategory] = useState("");

  const { isAdminChildAdmin, userPermissions, session } = useAdminContext();
  const isPermissible = userPermissions?.includes("cu_app.deactivate_user");

  const router = useRouter();
  const axiosAuth = useAxiosAuth();
  const admin_id = session?.user?.id;

  const params = useParams();
  const { user_id } = params;
  const childAdmin_id = user_id[0];
  // const childAdmin_id = user_id ? user_id[0] : null;
  const childAdmin_email = decodeURIComponent(user_id[1]);

  const childAdmin_status = user_id[2];

  const handleDropdownToggle = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const handleDeactivationConfirmation = async () => {
    if (!deactivationReason) {
      // You might want to handle this case, such as showing an error message to the user.
      console.error("Please provide a reason for deactivation.");
      return;
    }

    try {
      await handleProfileApprovals("Deactivated", deactivationReason);
      setDeactivationReason("");
      setShowModal(false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleRejectionConfirmation = async () => {
    if (!rejectionReason) {
      console.error("Please provide a reason for rejection.");
      return;
    }

    try {
      await handleProfileApprovals("Rejected", rejectionReason);
      setRejectionReason("");
      setShowModal(false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
  };
  const handleOpenModal = (action) => {
    if (isAdminChildAdmin && !isPermissible) {
      toast.info(`You do not have Permission to Deactivate the Profile`, {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
    } else {
      setSelectedAction(action);
      setShowModal(true);
    }
  };

  const handleDeleteConfirmation = () => {
    // Perform delete action or state update here

    // If you want to update the profile action, you can set it to null or another value
    setSelectedAction("Profile Deleted");

    setShowModal(false);
  };
  const handleProfileApprovals = async (status, reason = null) => {
    try {
      let body = { approval: status };
      if (status === "Deactivated") {
        body.StatusChangeReason = deactivationReason;
        body.ReasonType = "Deactivation";
        body.ReasonCategory = reasonCategory; // Hardcoded value for Deactivation
      } else if (status === "Rejected") {
        body.StatusChangeReason = rejectionReason;
        body.ReasonType = "Rejection";
        body.ReasonCategory = reasonCategory; // Hardcoded value for Rejection
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${childAdmin_id}/?user_id=${admin_id}`,
        body
      );
      if (response?.data?.data?.approval === status) {
        toast.success(`The Profile has been ${status}`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
        if (typeof window !== "undefined") {
          window.location.href = `/usermanagement/experts/experts-doctor/${childAdmin_id}/${childAdmin_email}/${status}`;
        }
        if (status == "Rejected") {
          toast.success(`The Profile has been ${status}`, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
          // router.push();
          if (typeof window !== "undefined") {
            window.location.href = `/usermanagement/experts/experts-doctor/${childAdmin_id}/${childAdmin_email}/${status}`;
          }
        }
      } else {
        toast.error(response?.data?.data, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteProfile = async (childAdmin_email) => {
    Swal.fire({
      title: "Are you sure you want to delete this profile?",
      text: "Deleting this profile will prevent the user from accessing the application, but the data will be stored in the database for 6 months.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete this profile.",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.put(
            `${process.env.NEXT_PUBLIC_DELETE_USER_PROFILE}${childAdmin_email}/?user_id=${admin_id}`,
            {
              approval: "Deleted",
            }
          );
          if (response?.status === 200) {
            toast.success("User profile deleted successfully");
            router.push(
              `/usermanagement/childAdmin/${childAdmin_id}/${childAdmin_email}/${response?.data?.approval}`
            );
          } else {
            toast.error("Failed to delete profile.");
          }
        } catch (error) {
          console.error("Error deleting profile: ", error);
          toast.error("Something went wrong!");
        }
      }
    });
  };

  const customModalStyles = {
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    content: {
      maxWidth: "30%",
      maxHeight: "29%",
      margin: "auto",
      overflow: "auto",
      background: "#FFFFFF", // Background color
      boxShadow: "0px 3px 10px #00000029", // Box shadow
      border: "1px solid #DFDFDF", // Border
      borderRadius: "5px", // Border radius
      padding: 0, // Remove padding
    },
  };

  return (
    <>
      <Dropdown
        show={dropdownOpen}
        onToggle={() => setDropdownOpen(!dropdownOpen)}
      >
        <Dropdown.Toggle variant="profile" id="dropdown-profile">
          {selectedAction ? `${selectedAction}` : "Profile Actions"}
        </Dropdown.Toggle>
        <Dropdown.Menu>
          {childAdmin_status !== "Approved" && (
            <Dropdown.Item className="actions text-justify">
              Approve Profile
              <hr />
            </Dropdown.Item>
          )}
          {childAdmin_status !== "Rejected" ||
            childAdmin_status !== "Approved" ||
            (childAdmin_status !== "Deactivated" && (
              <Dropdown.Item className="actions text-justify mb-2">
                Reject Profile
              </Dropdown.Item>
            ))}
          {childAdmin_status !== "Deactivated" ||
            (childAdmin_status !== "pending" && (
              <Dropdown.Item className="actions text-justify mb-2">
                Deactivate Profile
              </Dropdown.Item>
            ))}
          {childAdmin_status !== "Deleted" && (
            <Dropdown.Item
              className="actions text-justify mb-2"
              onClick={() => handleDeleteProfile(childAdmin_email)}
            >
              Delete Profile
            </Dropdown.Item>
          )}
        </Dropdown.Menu>
      </Dropdown>

      {showModal && (
        <ExpertProfileStatus
          selectedAction={selectedAction}
          showModal={showModal}
          setShowModal={setShowModal}
          setRejectionReason={setRejectionReason}
          handleProfileApprovals={handleProfileApprovals}
          rejectionReason={rejectionReason}
          reasonCategory={reasonCategory}
          setReasonCategory={setReasonCategory}
        />
      )}
    </>
  );
};
export default ChildAdminProfileApproval;
