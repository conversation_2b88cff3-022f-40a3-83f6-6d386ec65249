import React from "react";
import { BiSolidDownArrow } from "react-icons/bi";
import Image from "next/image";
import profile from "../../../public/images/profile.png";
import { MdModeEditOutline } from "react-icons/md";
import { MdDelete } from "react-icons/md";

const researchers = [
  {
    name: "<PERSON>",
    specialty: "Cancer Research",
    startDate: "24/05/2023",
    endDate: "26/05/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "Cancer Research",
    startDate: "18/06/2023",
    endDate: "20/06/2023",
    status: "Under Review",
  },
  {
    name: "<PERSON>",
    specialty: "Cancer Research",
    startDate: "10/07/2023",
    endDate: "15/07/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "Cancer Research",
    startDate: "05/08/2023",
    endDate: "10/08/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "Cancer Research",
    startDate: "02/09/2023",
    endDate: "07/09/2023",
    status: "Inactive",
  },
  {
    name: "<PERSON>",
    specialty: "Cancer Research",
    startDate: "29/09/2023",
    endDate: "03/10/2023",
    status: "Active",
  },
];

const getStatusColor = (status) => {
  switch (status.toLowerCase()) {
    case "active":
      return "green-text";
    case "inactive":
      return "red-text";
    case "under review":
      return "orange-text";
    default:
      return ""; // Default color if the status doesn't match any specific case
  }
};

const ResearchersList = () => {
  return (
    <div>
      <div className="row mt-3">
        <p className="heading">Researchers List</p>
      </div>

      <table className="table mt-2">
        <thead className="custom-border">
          <tr className="custom-name">
            <th scope="col" className="fw-light text-center">
              Researcher Name
            </th>
            <th scope="col" className="fw-light text-center">
              Department
              <BiSolidDownArrow
                style={{ fontSize: "11px", color: "#04AB20" }}
              />
            </th>
            <th scope="col" className="fw-light text-center">
              Date of Application
              <BiSolidDownArrow
                style={{ fontSize: "11px", color: "#04AB20" }}
              />
            </th>
            <th scope="col" className="fw-light text-center">
              Date of Onboarding
              <BiSolidDownArrow
                style={{ fontSize: "11px", color: "#04AB20" }}
              />
            </th>
            <th scope="col" className="fw-light text-center">
              Status
            </th>
            <th scope="col" className="fw-light text-center">
              Edit
            </th>
            <th scope="col" className="fw-light text-center">
              Delete
            </th>
          </tr>
        </thead>
        <tbody className="custom-border">
          {researchers?.map((researcher, index) => {
            return (
              <tr key={index} className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  {researcher.name}
                </td>
                <td className="custom-font text-center">
                  {researcher.specialty}
                </td>
                <td className="text-center">{researcher.startDate}</td>
                <td className="text-center">{researcher.endDate}</td>
                <td
                  className={`text-center ${getStatusColor(researcher.status)}`}
                >
                  {researcher.status}
                </td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default ResearchersList;
