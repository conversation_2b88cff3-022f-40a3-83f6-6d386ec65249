'use client'

import React, { Suspense } from "react";
import "../../../../components/experts/experts.css";
import "../../../../components/userManagement/usermanagement.css";
import "../../../../components/administratorDasboard/home.css";
import UserManagementComp from "../../../../components/userManagement/UserManagement";
import Navbar from "../../../../components/navbar/Navbar";
import Home from "../../../../components/administratorDasboard/Home";
import AllResearchersData from "../../../../components/researchers-data/AllResearchersData";
import Link from "next/link";
import withAuth from "../../../../withAuth/withAuth";

const researchers = () => {
  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-8">
                  <p className="main-purple-text">
                        User Management {">"}
                        <Link
                          className="href_link"
                          href="/usermanagement/experts"
                        >
                          Expert {">"}
                        </Link>{" "}
                        <Link
                          className="href_link"
                          href="usermanagement/experts"
                        >
                          Researchers {" "}
                        </Link>
                      </p>
                  </div>
                  <Home />
                </div>
              </div>
            </div>
            <UserManagementComp />
            <Suspense>
            <AllResearchersData />
            </Suspense>
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(researchers);
