// import React, { useEffect, useState } from "react";
// import { FiPlusCircle } from "react-icons/fi";
// import { MdDelete } from "react-icons/md";
// import { FaLinkedin, FaFacebook, FaInstagram, FaYoutube } from "react-icons/fa";
// import { FaSquareXTwitter } from "react-icons/fa6";

// const SocialMediaLinks = ({ userDetails, setUserDetails, editMode }) => {
//   const [socialMedia, setSocialMedia] = useState([]);
//   const label = [
//     { icon: <FaSquareXTwitter />, name: "Twitter" },
//     { icon: <FaLinkedin />, name: "LinkedIn" },
//     { icon: <FaFacebook />, name: "Facebook" },
//     { icon: <FaInstagram />, name: "Instagram" },
//     { icon: <FaYoutube />, name: "YouTube" },
//   ];

//   // Function to reset social media fields to the initial state
//   const resetSocialMedia = () => {
//     if (userDetails?.SocialLinks) {
//       const socialMediaTypes = userDetails.SocialLinks.map(
//         (link) => link.socialMediaType
//       );
//       setSocialMedia([...socialMediaTypes]);
//     } else {
//       setSocialMedia([]); // Empty if no social links exist
//     }
//   };

//   const addField = () => {
//     const availableLabel = label.find((lbl) => !socialMedia.includes(lbl.name));
//     if (availableLabel) {
//       setSocialMedia([...socialMedia, availableLabel.name]);
//     }
//   };

//   const deleteField = (index) => {
//     const newFields = [...socialMedia];
//     newFields.splice(index, 1); // Remove the field at the specified index
//     setSocialMedia(newFields);

//     // Update user details to remove the corresponding social link
//     const updatedSocialLinks = userDetails?.SocialLinks?.filter(
//       (_, i) => i !== index
//     );
//     setUserDetails((prevUserDetails) => ({
//       ...prevUserDetails,
//       SocialLinks: updatedSocialLinks,
//     }));
//   };

//   const handleChange = (index, event) => {
//     const newValue = event.target.value;

//     if (newValue.trim() !== "") {
//       setUserDetails((prevUserDetails) => {
//         const newUserDetails = { ...prevUserDetails };
//         const newSocialLinks = newUserDetails?.SocialLinks || [];

//         const newSocialLink = {
//           id: index + 1,
//           socialMediaType:
//             newUserDetails?.SocialLinks?.[index]?.socialMediaType ||
//             socialMedia[index],
//           socialMediaLink: newValue,
//         };

//         newSocialLinks[index] = newSocialLink;
//         newUserDetails.SocialLinks = newSocialLinks;
//         return newUserDetails;
//       });
//     }
//   };

//   // Effect to reset social media when userDetails changes
//   useEffect(() => {
//     resetSocialMedia();
//   }, [userDetails]);

//   return (
//     <div>
//       <div className="row">
//         <div className="col-sm-auto">
//           <p className="grey-text mt-2 mb-1">Social Links</p>
//         </div>
//         <div className="col-sm d-flex justify-content-end align-items-end">
//           {editMode && socialMedia.length < label.length && (
//             <FiPlusCircle
//               size={25}
//               color="#9426B2"
//               className="mb-1"
//               onClick={addField}
//               title="Add Social Media"
//             />
//           )}
//         </div>
//       </div>
//       <div className=" overflow-hidden">
//         <div className="content-scroll-3 overflow-auto">
//           <div className="research-custom-overflow">
//             {socialMedia.map((type, index) => {
//               const selectedLabel = label.find((lbl) => lbl.name === type);
//               return (
//                 <div
//                   key={index}
//                   className="input-group input-group-border my-2"
//                 >
//                   <span className="input-group-text label-col custom-font-mobile-socials w-25">
//                     <span className="icon-size">{selectedLabel?.icon}</span>
//                     <span style={{ fontSize: "14px" }}>
//                       &nbsp;{selectedLabel?.name}
//                     </span>
//                   </span>
//                   <input
//                     type="url"
//                     className="form-control custom-input-l"
//                     style={{ fontSize: "14px" }}
//                     id="socialLinks"
//                     placeholder={`Enter ${type} URL`}
//                     readOnly={!editMode}
//                     value={
//                       userDetails?.SocialLinks?.[index]?.socialMediaLink || ""
//                     }
//                     onChange={(event) => handleChange(index, event)}
//                   />
//                   {editMode && socialMedia.length > 1 && (
//                     <MdDelete
//                       size={20}
//                       color="#D80445"
//                       className="d-flex justify-content-center align-items-center mt-2"
//                       onClick={() => deleteField(index)}
//                     />
//                   )}
//                 </div>
//               );
//             })}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default SocialMediaLinks;

import React, { useEffect, useState } from "react";
import { FaLinkedin, FaFacebook, FaInstagram, FaYoutube } from "react-icons/fa";
import { FaSquareXTwitter } from "react-icons/fa6";

const SocialMediaLinks = ({ userDetails, setUserDetails, editMode }) => {
  const label = [
    { icon: <FaSquareXTwitter />, name: "Twitter" },
    { icon: <FaLinkedin />, name: "LinkedIn" },
    { icon: <FaFacebook />, name: "Facebook" },
    { icon: <FaInstagram />, name: "Instagram" },
    { icon: <FaYoutube />, name: "YouTube" },
  ];

  const handleChange = (index, event) => {
    const newValue = event.target.value;

    setUserDetails((prevUserDetails) => {
      const newUserDetails = { ...prevUserDetails };
      const newSocialLinks = newUserDetails?.SocialLinks || [];

      const newSocialLink = {
        id: index + 1,
        socialMediaType: label[index].name,
        socialMediaLink: newValue,
      };

      newSocialLinks[index] = newSocialLink;
      newUserDetails.SocialLinks = newSocialLinks;
      return newUserDetails;
    });
  };

  useEffect(() => {
    if (!userDetails?.SocialLinks) {
      setUserDetails((prevUserDetails) => ({
        ...prevUserDetails,
        SocialLinks: label.map((lbl) => ({
          id: lbl.name,
          socialMediaType: lbl.name,
          socialMediaLink: "",
        })),
      }));
    }
  }, [userDetails, setUserDetails, label]);

  return (
    <div>
      <div className="row">
        <div className="col-sm-auto">
          <p className="grey-text mt-2 mb-1">Social Links</p>
        </div>
      </div>
      <div className="overflow-hidden">
        <div className="content-scroll-3 overflow-auto">
          <div className="research-custom-overflow">
            {label.map((type, index) => (
              <div key={index} className="input-group input-group-border my-2">
                <span className="input-group-text label-col custom-font-mobile-socials w-25">
                  <span className="icon-size">{type.icon}</span>
                  <span style={{ fontSize: "14px" }}>&nbsp;{type.name}</span>
                </span>
                <input
                  type="url"
                  className="form-control custom-input-l"
                  style={{ fontSize: "14px" }}
                  id={`socialLinks-${type.name}`}
                  placeholder={`Enter ${type.name} URL`}
                  readOnly={!editMode}
                  value={
                    userDetails?.SocialLinks?.find(
                      (link) => link.socialMediaType === type.name
                    )?.socialMediaLink || ""
                  }
                  onChange={(event) => handleChange(index, event)}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialMediaLinks;
