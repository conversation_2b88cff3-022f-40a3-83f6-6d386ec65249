import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import Image from "next/image";
import profile from "../../../public/images/profile.png";
import { BiSolidDownArrow } from "react-icons/bi";
import { MdModeEditOutline } from "react-icons/md";
import { MdDelete } from "react-icons/md";
import { BsFastForwardFill } from "react-icons/bs";
import { TbPlayerTrackPrevFilled } from "react-icons/tb";
import { BsFillCircleFill } from "react-icons/bs";
// import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
// import {
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
// } from "chart.js";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { useParams } from "next/navigation";
import PatientsListStats from "./PatientsListStats";
import dummyProfile from "../../../public/images/dummy-avatar.jpg";
import Pagination from "../experts/Pagination";
import debounce from "lodash.debounce";
import { highlightText } from "../../utils/helperfunction";
import Link from "next/link";
import { Placeholder } from "react-bootstrap";
import { GrSearch } from "react-icons/gr";
import NoDataFound from "../noDataFound/NoDataFound";
import Loader from "../loader/Loader";
import { FaTimes } from "react-icons/fa";
import _ from "lodash";
import CustomPagination from "../CustomPagination/CustomPagination";
const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};
// ChartJS.register(ArcElement, Tooltip, Legend);
// ChartJS.register(
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend
// );

const ListOfPatients = () => {
  const [loading, setLoading] = useState(true);
  const [patientsList, setPatientsList] = useState();
  const [patientsStatistics, setPatientsStatistics] = useState({
    total_patients: 0,
    recent_consultations: 0,
    total_consultations: 0,
    total_cancelled_consultations: 0,
    total_completed_consultations: 0,
    total_rescheduled_consultations: 0,
    total_upcoming_consultations: 0,
    total_unattended_consultations: 0,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [searchQuery, setSearchQuery] = useState();
  const [current_page, setCurrent_Page] = useState(1);
  const [itemsPerPage] = useState(6);
  const params = useParams();
  const axiosAuth = useAxiosAuth();
  const expertId =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params.user_id[0]
      : "";
  const { data: session } = useSession();
  const userId = session?.user?.id;

  const fetchPendingTestimonials = useCallback(
    async (query) => {
      try {
        setLoading(true);
        let apiUrl = `${process.env.NEXT_PUBLIC_GET_EXPERTS_PATIENT_LIST}${expertId}/?user_id=${userId}&page=${current_page}`;
        if (startDate && endDate && userId) {
          apiUrl += `&start_date=${startDate}&end_date=${endDate}&page=${current_page}`;
        }
        if (selectedStatus && userId) {
          apiUrl += `&status=${selectedStatus}&page=${current_page}`;
        }

        if (query && userId) {
          apiUrl += `&name=${query}&page=${current_page}`;
        }

        const response = await axiosAuth.get(apiUrl);
        setPatientsList(response?.data?.items);
        // setPatientsList(response?.data?.patient_data?.slice().reverse());
        setTotalPages(response?.data?.total_pages);
        setPatientsStatistics({
          total_patients:
            response?.data?.patient_data_by_expert?.total_patients,
          recent_consultations:
            response?.data?.patient_data_by_expert?.recent_consultations,
          total_consultations:
            response?.data?.patient_data_by_expert?.total_consultations,
          total_cancelled_consultations:
            response?.data?.total_appointment_request_rate
              ?.total_cancelled_consultations,
          total_completed_consultations:
            response?.data?.total_appointment_request_rate
              ?.total_completed_consultations,
          total_rescheduled_consultations:
            response?.data?.total_appointment_request_rate
              ?.total_rescheduled_consultations,
          total_upcoming_consultations:
            response?.data?.total_appointment_request_rate
              ?.total_upcoming_consultations,
          total_unattended_consultations:
            response?.data?.total_appointment_request_rate
              ?.total_unattended_consultations,
        });
        setLoading(false);
      } catch (err) {
        console.log("error in fetching testimonial", err);
      } finally {
        setLoading(false);
      }
    },
    [expertId, axiosAuth, userId, endDate, startDate, selectedStatus]
  );

  const debouncedSearch = useMemo(() => {
    return _.debounce((query) => {
      fetchPendingTestimonials(query);
    }, 500);
  }, [fetchPendingTestimonials]);

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  // const debouncedSearch = useCallback(
  //   debounce((query) => {
  //     setSearchQuery(query);
  //     fetchPendingTestimonials(query);
  //   }, 300),
  //   []
  // );

  // useEffect(() => {
  //   if (searchQuery !== "") {
  //     debouncedSearch(searchQuery);
  //   }
  // }, [searchQuery, debouncedSearch]);

  // useEffect(() => {
  //   if (userId) {
  //     fetchPendingTestimonials();
  //   }
  // }, [fetchPendingTestimonials, userId]);

  // const totalPages = Math.ceil(patientsList?.length / itemsPerPage);

  const handleDateChange = (event, dateType) => {
    if (dateType === "startDate") {
      setStartDate(event.target.value);
    } else if (dateType === "endDate") {
      setEndDate(event.target.value);
    }
  };
  const handleClearQueryFilter = () => {
    setSearchQuery("");
  };

  return (
    <div className="payment-back">
      <div className=" overflow-hidden">
        <div className="user-management-scroll overflow-auto">
          <div className="row">
            <PatientsListStats patientsStatistics={patientsStatistics} />
            <div className="col-sm-7">
              <div className="row">
                <div className="col-sm-12 gx-0">
                  <div className="grey-bg">
                    <div className="row d-flex justify-content-center align-content-center text-center">
                      <div className="col-sm-1 d-flex align-items-center justify-content-center">
                        <p className="heading mb-0">Filters</p>
                      </div>
                      <div className="col-sm-6 d-flex">
                        <div className="input-group mb-0 custom-form-select">
                          <input
                            type="date"
                            className="form-control search-input-focus custom-placeholder d-flex justify-content-center align-items-center"
                            style={{ fontSize: "12px" }}
                            aria-label="Sizing example input"
                            aria-describedby="inputGroup-sizing-sm"
                            onChange={(event) =>
                              handleDateChange(event, "startDate")
                            }
                            value={startDate}
                          />
                        </div>

                        <div className="input-group mb-0 custom-form-select">
                          <input
                            type="date"
                            className="form-control search-input-focus  d-flex justify-content-center align-items-center"
                            style={{ fontSize: "12px" }}
                            aria-label="Sizing example input"
                            aria-describedby="inputGroup-sizing-sm"
                            onChange={(event) =>
                              handleDateChange(event, "endDate")
                            }
                            value={endDate}
                          />
                        </div>
                      </div>
                      <div className=" col-sm-4 gx-0">
                        <div
                          className="input-group search-input-patient"
                          style={{ height: "45px" }}
                        >
                          <input
                            type="text"
                            style={{
                              height: "45px",
                              borderRadius: "3px",
                              border: "none",
                              fontSize: "14px",
                            }}
                            className="form-control search-input-focus "
                            placeholder="Search by Name"
                            aria-label="Recipient's username"
                            aria-describedby="button-addon2"
                            // onChange={(e) => debouncedSearch(e.target.value)}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                          />
                          {searchQuery ? (
                            <span
                              // style={{ zIndex: 9999 }}
                              className="input-group-text"
                            >
                              {searchQuery && (
                                <FaTimes
                                  className=" cross-icon-calendar"
                                  onClick={handleClearQueryFilter}
                                />
                              )}
                            </span>
                          ) : (
                            <span
                              className="input-group-text custom-search-icon"
                              id="button-addon2"
                              style={{ borderRadius: "5px" }}
                            >
                              <GrSearch />
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="row mt-2">
                <table className="table custom-table mt-2">
                  <thead className="custom-border">
                    <tr className="custom-name" style={{ fontSize: "13px" }}>
                      <th scope="col" className="fw-light text-center">
                        Patient Id
                      </th>
                      <th scope="col" className="fw-light text-center">
                        Patient Name
                      </th>
                      <th scope="col" className="fw-light text-center">
                        Affliction
                      </th>
                      <th scope="col" className="fw-light text-center">
                        Date of Joining
                      </th>
                      <th scope="col" className="fw-light text-center">
                        Last Appointment
                      </th>
                      {/* <th scope="col" className="fw-light text-center">
                        Status
                      </th> */}
                    </tr>
                  </thead>
                  <tbody className="custom-border">
                    <>
                      {patientsList && patientsList.length > 0 ? (
                        patientsList?.map((patient) => (
                          <tr
                            key={patient?.details?.id}
                            className="custom-row "
                          >
                            <td className="custom-font text-center">
                              {patient?.details?.id}
                            </td>
                            <td className="purple-content">
                              {patient?.other_details?.ProfilePhoto ? (
                                <>
                                  <Image
                                    src={`${patient?.other_details?.ProfilePhoto}`}
                                    alt={`Dr`}
                                    width={35}
                                    height={35}
                                    className="expert_image"
                                  />
                                </>
                              ) : (
                                <>
                                  <Image
                                    src={dummyProfile}
                                    alt={`Dr`}
                                    width={35}
                                    height={35}
                                    className="expert_image"
                                  />
                                </>
                              )}
                              {highlightText(
                                patient?.details?.name,
                                searchQuery
                              )}
                            </td>
                            <td className="custom-font text-center ">
                              {patient?.details?.cancer_type[0]}
                            </td>
                            <td className="text-center custom-font">
                              {
                                patient?.details?.DateOfRegistration?.split(
                                  "T"
                                )[0]
                              }
                            </td>
                            <td className="text-center bg-custom custom-font">
                              {patient?.latest_appointment_date?.split(" ")[0]}
                              <span>
                                <Link
                                  href={`/usermanagement/patients/${patient?.details?.id}/${patient?.details?.email}/${patient?.details?.name}`}
                                  className=" text-decoration-none ms-2"
                                >
                                  view
                                </Link>
                              </span>
                            </td>
                            {/* <td className="text-center green-text custom-font">
                              {patient?.details?.approval}
                            </td> */}
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="10">
                            <NoDataFound />
                          </td>
                        </tr>
                      )}
                    </>
                  </tbody>
                </table>
                {totalPages > 1 && (
                  <CustomPagination
                    total_pages={totalPages}
                    current_page={current_page}
                    setCurrent_Page={setCurrent_Page}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListOfPatients;
