import Image from "next/image";
import React from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import TestimonialModal from "../testimonialModal/TestimonialModal";
import { formatDate, timeDifference } from "../db";
import { capitalizeFullName } from "../../../utils/helperfunction";

const AllTestimonials = ({
  testimonials,
  testimonialsLoading,
  handleTestmonialApprovals,
  showTestimonialModal,
  setShowTestimonialModal,
}) => {
  return (
    <div className="row">
      <p className="fw-bold grey-text">Testimonials</p>

      {testimonialsLoading === false ? (
        <>
          {testimonials && testimonials?.length === 0 ? (
            <div className="fs-4 d-flex align-items-center justify-content-center no-articles-found">
              <PiFolderNotchOpenFill color={"#8107d1"} size={30} />
              &nbsp; No Records Found
            </div>
          ) : (
            testimonials?.map((item, index) => (
              // <div key={index} className="bg-color mb-2">
              //   <div className="row bg-grey upload-reviews upload-reviews">
              //     <div className="col-sm-1">
              //       {item?.patient_photo ? (
              //         <Image
              //           src={item.patient_photo}
              //           alt="feedback"
              //           height={35}
              //           width={35}
              //           className="testimonial_image"
              //           onError={(e) => {
              //             e.target.src = "/images/profile.png"; // Set a fallback image when an error occurs
              //           }}
              //         />
              //       ) : (
              //         <Image
              //           src="/images/profile.png" // Fallback image
              //           alt="fallback"
              //           height={35}
              //           width={35}
              //           className="testimonial_image"
              //         />
              //       )}
              //     </div>
              //     <div className="col-sm-2">
              //       <p className="fw-semibold mb-0">
              //         {formatDate(item?.CurrentTime)}
              //       </p>
              //       <p className="custom-transperent-btn">
              //         {timeDifference(item?.CurrentTime)}
              //       </p>
              //     </div>
              //     <div className="col-sm-7">
              //       <p className="fw-semibold mb-0">
              //         {item?.CancerTreatmentType}
              //       </p>
              //     </div>
              //     <div className="col-sm-2">
              //       <button
              //         type="button"
              //         className="btn btn-yellow"
              //         onClick={() => handleTestimonialModal(item)}
              //       >
              //         Review
              //       </button>
              //     </div>
              //   </div>
              // </div>

              <div key={index} className="col-sm-6 p-2">
                <div className="introvideo-bg p-3">
                  <div className="row ">
                    <p className="allApproval-heading mb-3">
                      {" "}
                      Testimonial ID -{item?.id}
                    </p>
                    <p className="allApproval-heading mb-3">
                      {item?.CancerTreatmentType}
                    </p>
                  </div>
                  <div className="row">
                    <div className="col-sm-auto">
                      {item?.patient_photo ? (
                        <Image
                          src={item.patient_photo}
                          width={50}
                          height={50}
                          alt="testimonial_image"
                          className="allApproval-image"
                        />
                      ) : (
                        <Image
                          src="/images/profile.png" // Fallback image
                          alt="fallback"
                          height={35}
                          width={35}
                          className="testimonial_image"
                        />
                      )}
                    </div>
                    <div className="col-sm p-0">
                      <p className="allApproval-expert-name mb-0">
                        {capitalizeFullName(item?.patient_name)}
                      </p>
                      <p className="allApproval-expert-role">Patient</p>
                    </div>
                    <div className="col-sm-2">
                      <p className="fw-semibold mb-0">
                        {formatDate(item?.CurrentTime)}
                      </p>
                      <p className="custom-transperent-btn">
                        {timeDifference(item?.CurrentTime)}
                      </p>
                    </div>
                  </div>

                  <div className="row">
                    <p className="allApproval-expert-para">
                      {item?.ExperienceSummary}
                    </p>
                    <div className="modal-footer">
                      <button
                        type="button"
                        className="btn allApproval-reject-btn"
                        onClick={() =>
                          handleTestmonialApprovals("reject", item?.id)
                        }
                      >
                        Reject
                      </button>

                      <button
                        type="button"
                        className="btn allApproval-approve-btn ms-2"
                        onClick={() =>
                          handleTestmonialApprovals("approve", item?.id)
                        }
                      >
                        Approve
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </>
      ) : (
        renderPlaceholders()
      )}
    </div>
  );
};

export default AllTestimonials;
