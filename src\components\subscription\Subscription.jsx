'use client';
import React, { useState } from 'react';

const Subscription = () => {
    const [selectedPlan, setSelectedPlan] = useState('');

    const handleSubscribe = () => {
      // Handle subscription logic here
      // You can send the selected plan to your backend for further processing
    };
  return (
    <div className="container mt-5">
    <h2>Choose a Subscription Plan</h2>

    <div className="mb-3">
      <div className="form-check">
        <input
          className="form-check-input"
          type="radio"
          name="subscriptionPlan"
          id="basicPlan"
          value="Basic"
          checked={selectedPlan === 'Basic'}
          onChange={() => setSelectedPlan('Basic')}
        />
        <label className="form-check-label" htmlFor="basicPlan">
          Basic Plan
        </label>
      </div>
      <div className="form-check">
        <input
          className="form-check-input"
          type="radio"
          name="subscriptionPlan"
          id="premiumPlan"
          value="Premium"
          checked={selectedPlan === 'Premium'}
          onChange={() => setSelectedPlan('Premium')}
        />
        <label className="form-check-label" htmlFor="premiumPlan">
          Premium Plan
        </label>
      </div>
    </div>

    <button
      type="button"
      className="btn btn-primary"
      onClick={handleSubscribe}
    >
      Subscribe
    </button>
  </div>
  )
}

export default Subscription
