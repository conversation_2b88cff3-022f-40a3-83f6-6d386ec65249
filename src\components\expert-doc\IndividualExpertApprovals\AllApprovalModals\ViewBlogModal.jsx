import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import ModalImage from "react-modal-image";
import CU_logoImage from "../../../../../public/images/adminbglogo.png";
import Image from "next/image";

const ViewBlogModal = ({ showBlogModal, setShowBlogModal }) => {
  return (
    <Modal
      show={showBlogModal}
      onHide={() => {
        setShowBlogModal(false);
      }}
      size="xl"
      centered
    >
      <Modal.Header>
        <Modal.Title>
          <span className="allApproval-modal-heading">Blog Approval</span>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p className="allApproval-modal-heading mb-3">
          Blog Title1 {/* {blogDetails?.blog_details?.BlogTitle} */}
        </p>
        <div className="row">
          <div className="col-sm-1">
            <Image
              src={CU_logoImage}
              width={50}
              height={50}
              alt="cu logo"
              className="allApproval-image"
            />
          </div>
          <div className="col-sm p-0">
            <p className="allApproval-expert-name mb-0">Nandhakumar</p>
            <p className="allApproval-expert-role">Doctor</p>
          </div>
          <div className="col-sm-1 d-flex justify-content-end ">
            <p className="allApproval-expert-para mt-0">05/07/2024</p>
          </div>
        </div>
        <div className="overflow-hidden">
          <div className="overflow-auto" style={{ maxHeight: "450px" }}>
            <p
              //   dangerouslySetInnerHTML={{
              //     __html: blogDetails?.blog_details?.BlogBody,
              //   }}
              className="allApproval-expert-para"
            >
              Sample Blog Title1 Sample Blog Title1 Sample Blog Title1 Sample
              Blog Title1 Sample Blog Title1 Sample Blog Title1 Sample Blog
              Title1 Sample Blog Title1 Sample Blog Title1 Sample Blog Title1
              Sample Blog Title1 Sample Blog Title1
            </p>
          </div>
        </div>

        {/* <div className="blog-images-view-container d-flex justify-content-center align-items-center my-5">
          {blogDetails?.blog_details?.BlogImages?.map((blogImage, index) => (
            <div key={index} className="viewblog-image-wrapper">
              <ModalImage
                small={CU_logoImage}
                large={CU_logoImage}
                // alt={`blog-image-${index}`}
                alt="blog"
                hideDownload={true}
                hideZoom={true}
              />
            </div>
          ))}
        </div> */}
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={() => {
            setShowBlogModal(false);
          }}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ViewBlogModal;
