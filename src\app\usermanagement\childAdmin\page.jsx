'use client'

import React, { useEffect, useState } from "react";
import ChildAdminsComp from "../../../components/userManagement/ChildAdmins";
import "../../../components/userManagement/usermanagement.css";
import "../../../components/administratorDasboard/home.css";
import UserManagementComp from "../../../components/userManagement/UserManagement";
import Navbar from "../../../components/navbar/Navbar";
import Home from "../../../components/administratorDasboard/Home";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAdminContext } from "../../../Context/AdminContext/AdminContext";
import Loading from "../../../components/Loading/PageLoading/Loading";

const ChildAdmin = () => {
  const router = useRouter();
  const { isAdminChildAdmin, loading } = useAdminContext();
  const [redirecting, setRedirecting] = useState(isAdminChildAdmin);

  useEffect(() => {
    if (isAdminChildAdmin) {
      setRedirecting(true);
      router.push("/");
    }
  }, [isAdminChildAdmin, router]);

  if (loading || redirecting) {
    return <Loading />; 
  }
  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-8">
                    <p className="main-purple-text">
                      User Management {">"}{" "}
                      <Link
                        className="href_link"
                        href="/usermanagement/childAdmin"
                      >
                        Child Admin
                      </Link>{" "}
                    </p>
                  </div>
                  <Home />
                </div>
              </div>
            </div>
            <UserManagementComp />
            <ChildAdminsComp />
          </div>
        </div>
      </div>
    </main>
  );
};

export default ChildAdmin;
