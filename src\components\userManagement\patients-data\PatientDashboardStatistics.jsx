import React, { useCallback, useEffect, useState } from "react";
import { Doughnut } from "react-chartjs-2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import "../../userManagement/usermanagement.css";


const PatientDashboardStatistics = () => {
  const { data: session } = useSession();
  const adminId = session?.user?.id;

  const [loading, setLoading] = useState(true);
  const [patientsCount, setPatientsCount] = useState({
    total: 0,
    doctor: 0,
    influencer: 0,
    researcher: 0,
  });
  const [consultationCount, setConsultationCount] = useState({
    total_cancelled_consultations: 0,
    total_completed_consultations: 0,
    total_rescheduled_consultations: 0,
    total_unattended_consultations: 0,
    total_upcoming_consultations: 0,
  });
  const axiosAuth = useAxiosAuth();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      const data = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_PATIENTS_BY_TYPE}?user_id=${adminId}`
      );
      if (data?.data) {
        setPatientsCount({
          total: data?.data?.total_patients,
          doctor: data?.data?.total_doctor_patient,
          influencer: data?.data?.total_influencer_patient,
          researcher: data?.data?.total_researcher_patient,
        });
        setConsultationCount({
          total_cancelled_consultations:
            data?.data?.appointments_data?.total_cancelled_consultations,
          total_completed_consultations:
            data?.data?.appointments_data?.total_completed_consultations,
          total_rescheduled_consultations:
            data?.data?.appointments_data?.total_rescheduled_consultations,
          total_unattended_consultations:
            data?.data?.appointments_data?.total_unattended_consultations,
          total_upcoming_consultations:
            data?.data?.appointments_data?.total_upcoming_consultations,
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [adminId, axiosAuth]);

  useEffect(() => {
    if (adminId) {
      fetchData();
    }
  }, [fetchData, adminId]);

  const chartData = {
    datasets: [
      {
        label: "Number Of Consultations",
        // data: [60, 20, 20, 10],
        data: [
          consultationCount?.total_completed_consultations,
          consultationCount?.total_upcoming_consultations,
          consultationCount?.total_unattended_consultations,
          consultationCount?.total_rescheduled_consultations,
          consultationCount?.total_cancelled_consultations,
        ],
        backgroundColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
          "rgba(181, 0, 0, 1)",
        ],
        borderColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
          "rgba(181, 0, 0, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="">
      <p className="heading">Patients Data</p>
      <div className="row">
        <div className="col-sm-3 gx-1">
          <div className="custom-border-counting">
            <p className="text-center sub-heading mt-1 mb-1">Total Patients</p>
            <p className="text-center purple-num mb-2">
              {patientsCount?.total}
            </p>
          </div>
        </div>
        <div className="col-sm-3 gx-1">
          <div className="custom-border-counting">
            <p className="text-center sub-heading mt-1 mb-1">
              {"Doctor's Patient"}
            </p>
            <p className="text-center purple-num mb-2">
              {patientsCount?.doctor}
            </p>
          </div>
        </div>
        <div className="col-sm-3 gx-1">
          <div className="custom-border-counting">
            <p className="text-center sub-heading mt-1 mb-1 ">
              {" Influencer's Patient"}
            </p>
            <p className="text-center purple-num mb-2">
              {patientsCount?.influencer}
            </p>
          </div>
        </div>
        <div className="col-sm-3 gx-1">
          <div className="custom-border-counting">
            <p className="text-center sub-heading mt-1 mb-1 ">
              {" Researcher's Patient"}
            </p>
            <p className="text-center purple-num mb-2">
              {patientsCount?.researcher}
            </p>
          </div>
        </div>
      </div>
      <div className="row mt-3">
        <p className="heading mb-1">Total Appointment Request Rate</p>
        <div className="col-sm-4">
          <div className="pink-bg chartpadding">
            <Doughnut data={chartData} />
          </div>
        </div>
        <div className="col-sm-8">
          <div className="custom-border pink-bg">
            <div className="row">
              <p className="text-center sub-heading mb-1 mt-1">
                Appointment Consultations
              </p>
            </div>
            <div className="bg-white p-2 mb-1">
              <div className="row">
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Completed
                  </p>
                  <p className="text-center consultation-completed-count  mb-0">
                    {consultationCount?.total_completed_consultations}
                  </p>
                </div>
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Upcoming
                  </p>
                  <p className="text-center purple-count mb-0">
                    {consultationCount?.total_upcoming_consultations}
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Unattended
                  </p>
                  <p className="text-center consultation-unattended-count mb-0">
                    {consultationCount?.total_unattended_consultations}
                  </p>
                </div>
                <div className="col-sm-6">
                  <p
                    className="text-center mb-0 fw-light"
                    style={{ fontSize: "14px" }}
                  >
                    Rescheduled
                  </p>
                  <p className="text-center consultation-recheduled-count mb-0">
                    {consultationCount?.total_rescheduled_consultations}
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col-sm-12">
                  <p className="text-center mb-0 fw-light">Cancelled</p>
                  <p className="text-center consultation-cancelled-count mb-0">
                    {consultationCount?.total_cancelled_consultations}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDashboardStatistics;
