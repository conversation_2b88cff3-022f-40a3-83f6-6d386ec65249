"use client";
import React, { useState, useEffect } from "react";
import ChildMainProfile from "./ChildMainProfile";
import ChildAdminHead from "./ChildAdminHead";

const ToggleChildAdmin = () => {
  const [activeTab, setActiveTab] = useState("mainprofile");

  useEffect(() => {
    setActiveTab("childmainprofile");
  }, []);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };
  return (
    <>
      <ChildAdminHead />
      <div className="d-inline-flex gap-1 buttons-row mb-0">
        <button
          className={`btn btn-primary grey-btn ${
            activeTab === "childmainprofile" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("childmainprofile")}
        >
          Main Profile
        </button>
        {/* <button
                    className={`btn btn-primary grey-btn ${activeTab === "appointments" ? "activePatientsTab" : ""
                        }`}
                    onClick={() => handleTabChange("appointments")}
                >
                 
                </button> */}
        {/* <button
                    className={`btn btn-primary grey-btn ${activeTab === "uploads" ? "activeChildAdminsTab" : ""
                        }`}
                    onClick={() => handleTabChange("uploads")}
                >
                 
                </button> */}
      </div>

      {activeTab === "childmainprofile" && <ChildMainProfile />}
      {/* {activeTab === "appointments" && <UserAppointments />}
            {activeTab === "uploads" && <UserUploads />}
            {activeTab === "communication" && <UserCommunication />} */}
    </>
  );
};
export default ToggleChildAdmin;
