.navbar-parent {
  position: relative;
  height: 100vh; /* Full height for vertical centering */
}
.navbar-container {
  position: absolute;
  z-index: 99;
  left: 15%;
  transform: translate(-50%, -50%); /* Centers the Navbar */
}

.online {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  border: 1px solid rgba(227, 227, 227, 1);
  border-radius: 5px;
  opacity: 1;
  border-left: 7px solid rgba(58, 255, 91, 1);
  margin-bottom: 100px;
}
.custom-title {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  background-color: white;
  margin-bottom: 100px;
  width: 100%;
}

p.online-content {
  padding: 10px;
  font-size: 14px;
}

.nav-link.custom-nav-link {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  background-color: white;
  width: 310px;
  font-size: 12px;
  color: black;
}
.nav-link.custom-nav-link:hover,
.nav-link.custom-nav-link:focus,
.nav-link.custom-nav-link.active {
  background-color: #8107d1 !important;
  /* box-shadow: inset 0px 3px 6px #00000029; */
  /* border: 1px solid #e3e3e3; */
  border-radius: 5px;
  color: white;
}
.custom-active-link {
  background-color: #8107d1 !important;
}

.active-link {
  /* background-color: #8107d1 !important; */
  border-radius: 5px;
  color: white;
}

svg.custom-icon {
  color: #8107d1;
  font-size: 28px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  height: 39px;
  width: 59px;
  padding: 5px;
}
svg.custom-active-link {
  color: white;
  font-size: 28px;
  background-color: #8107d1;
  border-radius: 5px;
  height: 39px;
  width: 59px;
  padding: 5px;
}

.brandlogo {
  height: 50px;
  width: 50px;
  object-fit: contain;
}

.logout-btn {
  background-color: #b50000 !important;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3 !important;
  border-radius: 5px !important;
  opacity: 1;
  width: 42px;
  height: 44px;
  vertical-align: middle;
  color: white !important;
}
/* button.btn.logout-btn.logout-text-btn {
    color: white;
    width: 77%;
} */

/* YourComponent.css */
/* .sidebar {
    overflow: hidden;
    transition: width 0.3s ease;
  }
 
  .sidebar:hover {
    overflow: visible;
    transition: width 0.3s ease;
  } */
.sidebar {
  width: 100px; /* Initial collapsed width */
  padding: 25px;
  background: transparent linear-gradient(180deg, #ffffff 0%, #f1f1f1 100%) 0%
    0% no-repeat padding-box;
  overflow: hidden;
  transition: width 0.3s ease; /* Smooth expansion effect */
  position: fixed;
  z-index: 999;
  box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.16);
  height: 100vh;
}

.sidebar:hover {
  width: 300px; /* Expanded width on hover */
}
.sidebar:hover .logout-text-btn {
  display: block;
  opacity: 1;
}

/* Hide text content by default */
.sidebar .nav-link,
.sidebar .custom-title {
  display: none;
  transition: opacity 0.5s ease;
}

/* Show text content when sidebar is hovered */
.sidebar:hover .nav-link,
.sidebar:hover .custom-title {
  display: block;
}
/* Hide logout button by default */
.sidebar .logout-text-btn {
  display: none;
  transition: opacity 0.5s ease, width 0.3s ease;
  width: 200px;
}

/* Show logout button when sidebar is hovered */
.sidebar:hover .logout-text-btn {
  display: inline-block; /* Use "inline-block" or "block" based on your layout */
}
.custom-nav-item.active-link {
  /* background-color: #8107d1; */
  border-radius: 5px;
}

.custom-nav-item.active-link .custom-nav-link {
  color: white !important;
}
