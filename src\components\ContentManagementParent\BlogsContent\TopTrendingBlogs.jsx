import React, { useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { highlightText } from "../../../utils/helperfunction";
import noDataFound from "../../../../public/assets/noDataFound.png";
import ViewBlogsContentModal from "../BlogsContent/ViewBlogsContentModal";
import { Placeholder } from "react-bootstrap";
import NoDataFound from "../../noDataFound/NoDataFound";
import { toast } from "react-toastify";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 4 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "Ranking",
  "Expert Name",
  "Date of Upload",
  "Blog Title",
  "Expert Role",
  "Blog Views",
  "Blog Page",
  "Delete Ranking",
];

const TopTrendingBlogs = ({
  fetchAllBlogs,
  trendingBlogs,
  handleDeleteRanking,
  trendingLoading,
  fetchTrendingBlogs,
}) => {
  const [showBlogsModal, setShowBlogsModal] = useState(false);
  const [singleBlog, setSingleBlog] = useState({});
  const [deletingPodcastId, setDeletingPodcastId] = useState(null);

  const handleViewBlogsModal = (item) => {
    setSingleBlog(item);
    setShowBlogsModal(true);
  };

  const handleDeletePodcastRanking = async (id) => {
    try {
      setDeletingPodcastId(id);
      await handleDeleteRanking(id);
      await fetchTrendingBlogs();
      await fetchAllBlogs();
    } catch (err) {
      toast.error("Error in delete ranking");
    } finally {
      setDeletingPodcastId(null);
    }
  };

  return (
    <>
      {showBlogsModal && (
        <ViewBlogsContentModal
          showBlogsModal={showBlogsModal}
          setShowBlogsModal={setShowBlogsModal}
          singleBlog={singleBlog}
        />
      )}
      <div className="bg-color">
        <div className="row">
          <div className="col-sm-12">
            <table className="table mt-2">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-light text-center child-admin-list-heading ${
                        index === 3 ? "col-4" : "col"
                      }`}
                      style={{ fontSize: "14px" }}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>

              <tbody className="custom-border">
                {trendingLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {trendingBlogs &&
                    Array.isArray(trendingBlogs) &&
                    trendingBlogs?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                          <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      trendingBlogs &&
                      Array.isArray(trendingBlogs) &&
                      trendingBlogs?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row align-baseline table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {/* {index + 1} */}
                              {blog?.blog_details?.BlogRanking}
                            </td>
                            <td className="col purple-content text-capitalize">
                              {blog?.blog_details?.expert_details
                                ?.expert_profile_photo ? (
                                <Image
                                  src={`${blog?.blog_details?.expert_details?.expert_profile_photo}`}
                                  alt={`Dr ${blog?.blog_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.blog_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(
                                blog?.blog_details?.expert_details?.expert_name
                              )}
                            </td>
                            <td className="col custom-font text-center">
                              {blog?.blog_details?.BlogDateTime?.split("T")[0]}
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                              {blog?.blog_details?.BlogTitle}
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {blog?.blog_details?.expert_details?.expert_role}
                            </td>
                            <td className=" text-capitalize col custom-font">
                              {blog?.blog_details?.BlogViews}
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "#8107D1", fontSize: "15px" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleViewBlogsModal(blog?.blog_details)
                                }
                              >
                                view
                              </button>
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "red", fontSize: "15px" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleDeletePodcastRanking(
                                    blog?.blog_details?.id
                                  )
                                }
                                disabled={
                                  deletingPodcastId === blog?.blog_details?.id
                                }
                              >
                                {deletingPodcastId === blog?.blog_details?.id
                                  ? "Deleting..."
                                  : "Delete"}
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default TopTrendingBlogs;
