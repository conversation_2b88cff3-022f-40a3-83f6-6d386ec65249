import React from "react";
import { formatDate } from "../db";
import Image from "next/image";
import { FaFileCircleCheck } from "react-icons/fa6";

const AllDoctorsfeedback = ({
  loading,
  allFeedbacks,
  handleFeedbackPreview,
  selectedValue,
  handleChange,
  setShowNoPreviewText,
}) => {
  return (
    <>
      <div className=" mt-2">
        {loading === false ? (
          <>
            {!allFeedbacks ||
            (Array.isArray(allFeedbacks) && allFeedbacks.length === 0) ? (
              <>
                <h3
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "500px" }}
                >
                  <FaFileCircleCheck className="podcast-icon " /> &nbsp;
                  <span className="text-secondary">
                    {" "}
                    No Doctors Feedback Available
                  </span>
                </h3>
              </>
            ) : (
              <>
                <div className="col-3 mb-2">
                  <select
                    className="form-select form-select-sm custom-form-select"
                    aria-label=".form-select-sm example"
                    value={selectedValue}
                    onChange={handleChange}
                  >
                    <option selected value="all">
                      Select
                    </option>
                    <option value="2">Under Review</option>
                    <option value="1">Approved</option>
                    <option value="0">Rejected</option>
                  </select>
                </div>
                <div className="list-of-articles">
                  {Array.isArray(allFeedbacks) &&
                    allFeedbacks?.map((item, index) => {
                      return (
                        <div key={index} className="bg-color-border mb-2">
                          <div className="row">
                            <div className="col-sm-6 mb-0 d-flex justify-content-start align-items-center">
                              {item.doctor_photo ? (
                                <Image
                                  src={item.doctor_photo || profile}
                                  alt="feedback"
                                  height={35}
                                  width={35}
                                  style={{ borderRadius: "50%" }}
                                />
                              ) : (
                                <Image
                                  src="/images/profile.png" // Fallback image
                                  alt="fallback"
                                  height={35}
                                  width={35}
                                  className="testimonial_image"
                                />
                              )}
                              <p className="ps-2 mb-0 custom-font-size">
                                {item?.Feedback?.split(" ")
                                  .slice(0, 4)
                                  .join(" ")}
                              </p>
                            </div>
                            <div className="col-sm-4 mb-0 d-flex justify-content-evenly align-items-center">
                              <p className="purple-text mb-0 pe-2">
                                {formatDate(item.CurrentTime)}
                              </p>
                              <p
                                className={`blog-approval-status-${
                                  item.status === 2
                                    ? "under-review"
                                    : item.status === 1
                                    ? "approved"
                                    : item.status === 0
                                    ? "rejected"
                                    : ""
                                } mb-0`}
                              >
                                {item.status === 2
                                  ? "Under Review"
                                  : item.status === 1
                                  ? "Approved"
                                  : item.status === 0
                                  ? "Rejected"
                                  : ""}
                              </p>
                            </div>


                            <div className="col-sm-1">
                              {/* <button
                        type="button"
                        className="btn btn-transparent red-text fw-bold p-0"
                        // onClick={() =>
                        //   handleBlogDelete(item?.blog_details?.id)
                        // }
                      >
                        Delete
                      </button> */}
                            </div>

                            <div className="col-sm-1 d-flex justify-content-center align-items-center">
                              <button
                                type="button"
                                className="btn btn-transparent purple-text fw-bold p-0"
                                onClick={() => {
                                  handleFeedbackPreview(item);
                                  setShowNoPreviewText(false);
                                }}
                              >
                                View
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </>
            )}
          </>
        ) : (
          renderPlaceholders()
        )}
      </div>
    </>
  );
};

export default AllDoctorsfeedback;
