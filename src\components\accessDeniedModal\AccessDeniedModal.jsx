"use client";

import React, { useState } from "react";
import { Modal } from "react-bootstrap"; // Import Modal
import { HiOutlineLogout } from "react-icons/hi";
import { toast } from "react-toastify";
import "./access-denied.css";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";

const AccessDeniedModal = ({ userInitialApprove = true }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const { isPhoneVerified } = useAdminContext();

  const handleSignOut = async () => {
    try {
      const { signOut } = await import("next-auth/react");
      await signOut({
        redirect: true,
        callbackUrl: "/auth/login",
      });
      toast.success("Sign Out Successfully", {
        autoClose: 3000,
        position: "top-center",
      });
      if (typeof window !== "undefined") {
        localStorage.clear("redirect_url");
        window.location.href = "/auth/login";
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div style={{ width: "100%", margin: "auto" }}>
      <Modal
        show={!isPhoneVerified}
        dialogClassName="modal-90w"
        centered
        backdrop="static"
        style={{ backdropFilter: "blur(10px)" }}
      >
        <Modal.Body>
          <div className="d-flex justify-content-end">
            <HiOutlineLogout
              title="Logout"
              color="red"
              size={30}
              cursor={"pointer"}
              fontWeight={20}
              onClick={handleSignOut}
            />
          </div>
          <div>
            <h3 className="not_approved_title">Phone Verification Required</h3>
            <p className="not_approved_body">
              Your phone number is not verified. To proceed, please verify your
              phone number.
              <br />
              <p> We appreciate your cooperation. </p>
            </p>
            <div className="push_to_profile_setting_button">
              <button
                onClick={() => {
                  setLoading(true);
                  router.push("/auth/otp-request");
                }}
                disabled={loading}
              >
                {loading ? "Please wait..." : "Verify Phone"}
              </button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

// export default AccessDeniedModal;
export default dynamic(() => Promise.resolve(AccessDeniedModal), {
  ssr: false,
});
