import React from "react";

const PreviewConsent = ({ selectedConsent, doctorsData }) => {
  return (
    <div className="modal-content custom-modal-content-article">
      <p className="m-3 introPreview-heading">{`Doctor's`} Feedback Preview</p>
      <div>
        <div className="c">
          <div className="consentform-custom-overflow ">
            <div className="row consent-form-box mt-3 mx-auto">
              <form className="p-4">
                <div className="row">
                  <label
                    htmlFor="docname"
                    className="form-label custom-form-label mb-1 mt-2"
                  >
                    Consulting Doctor’s Name
                  </label>
                  <div className="col">
                    <input
                      type="text"
                      value={doctorsData?.name?.split(" ")?.[0]}
                      className="form-control custom-form-control"
                      placeholder="First name"
                      aria-label="First name"
                      readOnly
                    />
                  </div>
                  <div className="col">
                    <input
                      type="text"
                      className="form-control custom-form-control"
                      placeholder="Last name"
                      aria-label="Last name"
                      readOnly
                      value={doctorsData?.name?.split(" ")?.[1]}
                    />
                  </div>
                </div>

                <p className="custom-para mt-3">
                  I, the undersigned, acknowledge and consent to participate in
                  a teleconsultation session with the above-named doctor
                  specializing in oncology. I have been informed and understand
                  the following terms and conditions:
                </p>
                <div className="image-container overflow-hidden">
                  <div className="content-scroll consent-custom-overflow overflow-auto">
                    <div className="custom-para consent-addContent">
                      <p
                        className="doctors-consent-text"
                        dangerouslySetInnerHTML={{
                          __html: selectedConsent?.ConsentContent,
                        }}
                      ></p>
                    </div>
                  </div>
                </div>
              </form>
              {/* </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewConsent;
