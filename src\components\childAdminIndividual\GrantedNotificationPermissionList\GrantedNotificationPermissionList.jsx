import React from "react";
import { GiCheckMark } from "react-icons/gi";
import ToggleBtn from "../../../common/toggle-btn/ToggleBtn";

const GrantedNotificationPermissionList = ({
  userPermissions = [],
  toggleNotification,
}) => {
  return (
    <div className="row mt-2">
      <div className="notification-tab mb-2">
        <div className="row">
          <div className="col-sm-8">
            <p className="p-s-subheading">Notifications Permissions</p>
          </div>
          {/* <div className="col-sm-2">
            <p className="p-s-subheading ps-3">email</p>
          </div>
          <div className="col-sm-2">
            <p className="p-s-subheading text-center">In-app</p>
          </div> */}
        </div>
        <div className="overflow-hidden mb-3">
          <div className="content-scroll">
            {userPermissions &&
              userPermissions?.map((notification) => (
                <div
                  key={notification}
                  className="row mb-2 single-notification-tab "
                >
                  <div className="col-sm-8">
                    <span className="notification-name">{notification}</span>
                  </div>
                  {/* <div className="col-sm-2">
                    <div className="form-check form-switch">
                
                      <ToggleBtn
                        isActive={notification.ActiveStatus === 1}
                        onToggle={(type, status) =>
                          toggleNotification(notification.id, status)
                        }
                        type="E"
                      />
                    </div>
                  </div> */}
                  <div className="col-sm-2">
                    <div className="form-check form-switch">
                      {/* <GiCheckMark className=" tick-mark-userpermissions" /> */}
                      <ToggleBtn
                        isActive={notification.ActiveStatus === 1}
                        onToggle={(type, status) =>
                          toggleNotification(notification.id, status)
                        }
                        type="N"
                      />
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GrantedNotificationPermissionList;
