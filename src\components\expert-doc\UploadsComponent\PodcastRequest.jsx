"use client";
import React from "react";
import { FaSpotify, FaYoutube } from "react-icons/fa6";
import { SiApplepodcasts, SiPodcastaddict } from "react-icons/si";
import { formatDate } from "../../../utils/helperfunction";
import { FaCalendar, FaTimes } from "react-icons/fa";
import CustomPagination from "@/components/CustomPagination/CustomPagination";

const PodcastRequest = ({
  podcastList,
  handlePodcastPreview,
  handleDeletePodcast,
  selectedValue,
  handleChange,
  setShowNoPreviewText,
  current_page,
  setCurrent_Page,
  articleLoading,
}) => {
  return (
    <>
     {articleLoading === false &&
        podcastList?.items?.length > 0 && (
          <div className="row">
          <div className="col-3">
            <select
              className="form-select form-select-sm custom-form-select"
              aria-label=".form-select-sm example"
              value={selectedValue}
              onChange={handleChange}
            >
              <option selected value="all">
                Select
              </option>
              <option value="1">Requested</option>
              <option value="2">Published</option>
              {/* <option value="3">Rejected</option> */}
            </select>
          </div>
          
         </div>
        )}
      {podcastList && podcastList?.length === 0 ? (
        <>
          <div
            className="d-flex justify-content-center align-items-center"
            style={{ minHeight: "500px" }}
          >
            <SiApplepodcasts className="podcast-icon" />{" "}
            <span className="custom-font-size"> No Podcast Available</span>
          </div>
        </>
      ) : (
        <div className="list-of-podcast">
          {podcastList &&
            podcastList?.items?.map((item, index) => (
              <div key={index} className="bg-color mb-2">
                <div className="row">
                  <div className="col-sm-8 mb-0 d-flex align-items-center">
                    <SiPodcastaddict className="fixed-icon-size" />
                    <p className="ps-2 mb-0 custom-font-size">
                      {item.PodcastTopic}
                    </p>
                  </div>

                  <div className="col-sm-1 gx-0 mb-0 d-flex align-items-center">
                    <p
                      className={`podcast-approved-${
                        item.PodcastStatus === 1
                          ? "requested"
                          : item.PodcastStatus === 2
                          ? "published"
                          : ""
                      } mb-0`}
                    >
                      {item.PodcastStatus === 1
                        ? "Requested"
                        : item.PodcastStatus === 2
                        ? "Published"
                        : ""}
                    </p>
                  </div>
                  <div className="col-sm-3 mb-0 d-flex justify-content-between align-items-center">
                    <button
                      type="button"
                      className="btn btn-purple-text fw-bold p-0 w-100"
                      style={{ fontSize: "12px" }}
                      onClick={() => {

                        setShowNoPreviewText(false);
                        handlePodcastPreview(item);

                      }}
                    >
                      {/* {item.PodcastStatus === 1
                        ? "Upload"
                        : item.PodcastStatus === 2
                        ? "Update"
                        : ""} */}
                      View
                    </button>
                    <button
                      type="button"
                      className="btn btn-red-text fw-bold p-0 w-100"
                      style={{ fontSize: "12px" }}
                      onClick={() => handleDeletePodcast(item?.id)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
                <div className="row mt-2">
                  <div className="col-1"></div>
                  <div className="col-sm-1 mb-0 d-flex align-items-center">
                    <p className="light-grey-text text-wrap mb-0 custom-font-size">
                      {/* Corrected URL check */}
                      {item?.Platforms?.youtube?.url && (
                        <a
                          className="text-decoration-none"
                          href={item?.Platforms?.youtube?.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: "inherit" }}
                        >
                          <button className="podcast-click-icons border-0 bg-light">
                            <FaYoutube
                              style={{
                                color: "#ff0000",
                                fontSize: "20px",
                              }}
                            />
                          </button>
                        </a>
                      )}
                    </p>
                  </div>
                  <div className="col-sm-1 mb-0 d-flex align-items-center">
                    <p className="light-grey-text text-wrap mb-0 custom-font-size">
                      {/* {item.PodcastURL ? item.PodcastURL : "No url updated"} */}
                      {item?.Platforms?.spotify?.url && (
                        <a
                          className=" text-decoration-none"
                          href={item?.Platforms?.apple_podcast?.url}
                          target="_blank"
                          style={{ color: "inherit" }}
                        >
                          <button className="podcast-click-icons border-0 bg-light">
                            <SiApplepodcasts
                              style={{
                                color: "#B150E2",
                                fontSize: "20px",
                              }}
                            />
                          </button>
                        </a>
                      )}
                    </p>
                  </div>
                  <div className="col-sm-1 mb-0 d-flex align-items-center">
                    <p className="light-grey-text text-wrap mb-0 custom-font-size">
                      {/* {item.PodcastURL ? item.PodcastURL : "No url updated"} */}
                      {item?.Platforms?.apple_podcast?.url && (
                        <a
                          className=" text-decoration-none"
                          href={item?.Platforms?.spotify?.url}
                          target="_blank"
                          style={{ color: "inherit" }}
                        >
                          <button className="podcast-click-icons border-0 bg-light">
                            <FaSpotify
                              style={{
                                color: "#1ed761",
                                fontSize: "20px",
                              }}
                            />
                          </button>
                        </a>
                      )}
                    </p>
                  </div>
                  <div className="col-8">
                    <p style={{ fontSize: "12px" }} className="mb-0 float-end">
                      <span className="podcast-preview-title">
                        Posted on -{" "}
                      </span>
                      {item?.PodcastDate
                        ? formatDate(item.PodcastDate)
                        : "No date available"}
                    </p>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
      {podcastList?.total_pages !== 1 && (
        <div className="d-flex justify-content-center align-items-center mt-3">
          <div className="d-none d-xl-block">
            <CustomPagination
              total_pages={podcastList?.total_pages}
              current_page={current_page}
              setCurrent_Page={setCurrent_Page}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default PodcastRequest;
