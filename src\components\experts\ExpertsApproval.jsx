import React from "react";
import Image from "next/image";
import Link from "next/link";
import dummyProfile from "../../../public/images/dummy-avatar.jpg";
import nodataFound from "../../../public/images/nodata.png";
import NoDataFound from "../noDataFound/NoDataFound";

const ExpertsApproval = ({ expertsApprovals }) => {
  return (
    <div className="col-sm-5 custom-border experts-profile-approval-request-section">
      <div className="row">
        <div className="col-sm-12 text-center">
          <p className="no-expert-approval-record pt-2">Approvals Requested</p>
        </div>
      </div>
      <div className="custom-shadow2 px-2 mb-2 mx-3">
        <div className="row p-2">
        <div className="col-sm-5 d-flex align-items-center">
              <p className="purple-content mb-0 fw-bold text-capitalize ">
                Profile
              </p>
            </div>
            <div className="col-sm-2 d-flex align-items-center">
              <p className="mb-0 fw-bold gx-0 text-capitalize">Role</p>
            </div>
            <div className="col-sm-2 d-flex align-items-center">
              <p className="mb-0 fw-bold custom-date">Date</p>
            </div>
            <div className="col-sm-3  d-flex align-items-center ">
            <p className="mb-0 fw-bold custom-date">Action</p>
            </div>
          </div>
      </div>
      <div className=" overflow-hidden">
        <div
          style={{ maxHeight: "650px" }}
          className="content-scroll overflow-auto px-3"
        >
          {expertsApprovals && expertsApprovals?.length > 0 ? (
            expertsApprovals?.map((item, index) => (
              <div key={index} className="custom-shadow2 p-2 mb-3">
                <div className="row">
                  <div className="col-sm-5 d-flex align-items-center">
                    {item?.profile_photo ? (
                      <>
                        <Image
                          src={`${item?.profile_photo}`}
                          alt={`${item?.name}`}
                          width={35}
                          height={35}
                          className="expert_image"
                        />
                      </>
                    ) : (
                      <>
                        <Image
                          src={dummyProfile}
                          alt={`${item?.name}`}
                          width={35}
                          height={35}
                          className="expert_image"
                        />
                      </>
                    )}
                    <p className="expert-purple-content mb-0 text-capitalize">
                      {item.name}
                    </p>
                  </div>
                  <div className="col-sm-2 gx-0 d-flex align-items-center">
                    <p className="mb-0 text-capitalize custom-font-size">{item.user_role}</p>
                  </div>
                  <div className="col-sm-2 gx-1 d-flex align-items-center justify-content-center">
                    <p className="mb-0 custom-font-size">
                      {item.DateOfRegistration?.split("T")[0]}
                    </p>
                  </div>
                  <div className="col-sm-3 ">
                    <Link
                      href={`/usermanagement/experts/experts-doctor/${item.id}/${item.email}/${item.approval}`}
                      className="text-white text-decoration-none"
                    >
                      <button
                        type="button"
                        className="btn btn-primary black-btn p-1"
                      >
                        Review
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="no-data-found-margin">
            <NoDataFound />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExpertsApproval;
