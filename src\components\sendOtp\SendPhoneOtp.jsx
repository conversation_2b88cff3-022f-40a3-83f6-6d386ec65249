"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import adminLogo from "../../../public/assets/adminloginlogo.png";
import ellipseadmin from "../../../public/assets/ellipseadmin.png";
import ellipsetop from "../../../public/assets/ellipse-top.png";
import RightLogin from "../Login/rightLogin";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import "./otp.css";
import {
  encryptBase,
  getCountryCodeFromPhoneNumber,
  getPhoneNumberWithoutCountryCode,
} from "../../utils/helperfunction";
import Cookies from "js-cookie";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import axios from "axios";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useRouter } from "next/navigation";
import { debounce } from "lodash";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import { useGeolocated } from "react-geolocated";
import coordinateToCountry from "coordinate_to_country";

const SendPhoneOtp = () => {
  const [phone, setPhone] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const router = useRouter();
  const { isPhoneVerified } = useAdminContext();
  const [submitted, setSubmitted] = useState(false);

  const [errorMsg, setErrorMsg] = useState("");
  const [defaultCountry, setDefaultCountry] = useState("US");

  const user_id = session?.user?.id;
  const user_email = session?.user?.email;
  const user_phone = session?.user?.phone;

  const { coords, isGeolocationAvailable, isGeolocationEnabled } =
    useGeolocated({
      positionOptions: {
        enableHighAccuracy: false,
      },
      userDecisionTimeout: 5000,
    });

  // Set default country based on geolocation
  useEffect(() => {
    if (!isGeolocationAvailable) {
      setErrorMsg("Geolocation is not supported by your browser.");
      return;
    }

    if (!isGeolocationEnabled && !coords) {
      setErrorMsg("Location services are turned off. Please enable them.");
      return;
    }

    if (coords) {
      const countryCode = coordinateToCountry(
        coords.latitude,
        coords.longitude,
        true
      );

      setDefaultCountry(countryCode[0]?.toLowerCase());
    }
  }, [coords, isGeolocationAvailable, isGeolocationEnabled]);

  const handlePhoneChange = (value) => {
    setPhone(value);
    setError("");
  };

  useEffect(() => {
    if (isPhoneVerified) {
      router.push("/");
    }
  }, [isPhoneVerified, router]);

  const phoneNumberWithoutCountryCode = getPhoneNumberWithoutCountryCode(phone);
  const countryCode = getCountryCodeFromPhoneNumber(phone);

  const encryptedPhone = encryptBase(phone);
  const encodedPhone = encodeURIComponent(encryptedPhone);

  const handleSendOtp = async (e) => {
    e.preventDefault();
    setSubmitted(true);
    if (!phone) {
      toast.error("Phone number cannot be empty", {
        autoClose: 2000,
        theme: "colored",
        position: "top-center",
      });
      return;
    }
    const isValid = /^[1-9]\d{8,14}$/.test(phone);

    if (!isValid) {
      setError("Invalid phone number");
      return;
    }
    const formattedUserPhone = user_phone?.replace(/\D/g, "");
    const formattedPhone = phone?.replace(/\D/g, "");
    if (formattedUserPhone !== formattedPhone) {
      toast.error(
        "Entered phone number does not match with the user's phone number",
        {
          autoClose: 2000,
          theme: "colored",
          position: "top-center",
        }
      );
      return;
    }

    const formData = {
      email: user_email,
      phone: phoneNumberWithoutCountryCode,
      country_code: countryCode,
      user_role: "child_admin",
    };
    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_SEND_OTP}user_id=${user_id}`,
        formData
      );
      const message = response?.data?.message;

      console.log({ response });

      if (message === "OTP sent successfully.") {
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "endTime",
            new Date(Date.now() + 120 * 1000).toISOString()
          );
        }

        // Show success toast with backend message
        toast.success(message, {
          autoClose: 1500,
          theme: "colored",
          position: "top-center",
        });
        // Navigate to OTP verification screen
        router.replace(`/auth/verify-mobile/?xts=${encodedPhone}`);
      } else {
        toast.error(message || "Failed in sending OTP!", {
          autoClose: 1500,
          theme: "colored",
          position: "top-center",
        });
      }
    } catch (error) {
      const backendMessage =
        error?.response?.data?.message ||
        error?.response?.data ||
        "Something went wrong!";

      toast.error(backendMessage, {
        autoClose: 2000,
        theme: "colored",
        position: "top-center",
      });
      console.error("error in registering:", backendMessage);
    } finally {
      setLoading(false);
    }
  };

  //   if (response?.data?.message === "otp sent") {
  //     router.replace(`/auth/verify-mobile/?xts=${encodedPhone}`);
  //     const toastMessage = `OTP is Sent to ${phoneNumberWithoutCountryCode}`;
  //     toast.success(toastMessage, {
  //       autoClose: 1500,
  //       theme: "colored",
  //       position: "top-center",
  //     });
  //     if (typeof window !== "undefined") {
  //       localStorage.setItem(
  //         "endTime",
  //         new Date(Date.now() + 120 * 1000).toISOString()
  //       );
  //     }
  //   } else {
  //     toast.error("Failed In Sending Otp!", {
  //       autoClose: 1500,
  //       theme: "colored",
  //       position: "top-center",
  //     });
  //   }
  // } catch (error) {
  //   if (error?.response?.data?.phone) {
  //     toast.error("User with this phone already exists!", {
  //       autoClose: 2000,
  //       theme: "colored",
  //       position: "top-center",
  //     });
  //   } else
  //     toast.error(error?.data?.message, {
  //       autoClose: 2000,
  //       theme: "colored",
  //       position: "top-center",
  //     });
  // } finally {
  //   setLoading(false);
  // }

  return (
    <>
      <div className="row">
        <div className="col-sm-6 gx-0">
          <Image src={adminLogo} className="admin-logo ms-4" alt="" />
          <Image src={ellipsetop} className="ellipse-top" alt="" />
          <h5 className="login-heading ms-3">Verify Phone</h5>
          <div className="login-form">
            <form>
              <div className="row">
                <div className="col-sm-12">
                  <div className="form-group mt-2 mb-2">
                    <label
                      htmlFor="userEmail"
                      className="admin-login-label mb-2"
                    >
                      Enter your phone number
                    </label>
                    <div className="form-group">
                      <div
                        className="d-flex"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                      >
                        <PhoneInput
                          placeholder="Enter phone number"
                          style={{
                            cursor: "pointer",
                            borderColor: error ? "orangered" : "",
                          }}
                          country={defaultCountry}
                          className={`form-control email-container form-login-fields phone-input `}
                          inputStyle={{ border: "none" }}
                          value={phone}
                          onChange={handlePhoneChange}
                          defaultCountry="US"
                          required
                          //   title={errors.phone ? errors.phone : ""}
                          countrySelectComponent={({
                            country,
                            ...restProps
                          }) => (
                            // eslint-disable-next-line react/jsx-no-undef
                            <SelectCountry
                              value={country}
                              onChange={(val) => handleCountryChange(val)}
                              className={`country-container-input`}
                              style={{
                                cursor: "pointer",
                              }}
                              {...restProps}
                            />
                          )}
                        />
                      </div>
                      {error && (
                        <div className=" my-1 otp-val-error">{error}</div>
                      )}
                    </div>
                  </div>
                  <span className="admin-login-label">
                    Please enter the phone number where {"you'd"} like to
                    receive the OTP
                  </span>
                </div>
                <Link
                  onClick={handleSendOtp}
                  href=""
                  disabled={loading}
                  className="admin-continue mt-5"
                >
                  {loading ? "Loading..." : "Continue"}
                </Link>
              </div>
            </form>
          </div>
          <Image src={ellipseadmin} className="ellipse-down" alt="" />
        </div>
        <div className="col-sm-6 gx-0">
          <RightLogin />
        </div>
      </div>
    </>
  );
};
export default SendPhoneOtp;
