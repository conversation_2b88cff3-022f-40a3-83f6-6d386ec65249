.btn.grey-btn {
  background: #dfdfdf 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px !important;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #414146;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}
.form-control.custom-placeholder::placeholder {
  font-size: 12px;
}
.btn.approvals-grey-btn,
.btn.approvals-grey-btn:hover,
.approvals-grey-btn:active,
.approvals-grey-btn:focus {
  width: 169px;
  height: 38px;
  background: #e3e3e3;
  border-radius: 3px;
  font-size: 12px;
  color: #5b5b5b;
  font-weight: 500;
}

.activeApprovalsTab {
  width: 169px !important;
  height: 38px !important;
  background: #414146 0% 0% no-repeat padding-box !important;
  color: #fbfbfb !important;
}

.user-management-scroll {
  max-height: 606px;
  padding: 12px;
}

/* .active-page{
    background-color: rgb(217, 211, 211) !important;
    border-bottom: #04AB20;
} */
.child-admin-list-heading {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-size: 16px;
  font-weight: 700;
}

.expert-content-scroll {
  max-height: 650px;
  /* overflow-y: scroll; */
}

.content-scroll::-webkit-scrollbar {
  display: none;
}

.form-select.custom-form-select-patient {
  font-size: 12px;
}

.form-select.custom-form-select-patient:focus {
  box-shadow: none;
  border-color: transparent;
}

/* .form-select.custom-form-select-patient::placeholder{
  font-size: 12px;
} */

.input-group.search-input-patient {
  background: #ffffff !important;
  box-shadow: 0px 3px 6px #00000029;
  font-size: 12px;
  color: #5b5b5b;
  border-radius: 5px;
  height: 35px;
}

.input-group-text.custom-search-icon {
  background-color: white !important;
  border-left: none !important;
  border: none !important;
}

.calender-filter-container {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #dee2e6;
  border-radius: 3px;
}

span.date-filter-patient {
  background: #ffffff;
  /* box-shadow: 0px 3px 6px #00000029; */
  border-radius: 3px;
  /* color: #5b5b5b; */
  /* width: 242px; */
  padding: 6px 6px 7px;
  display: inline-flex;
  /* margin-top: 22px; */
  font-size: 14px;
}

span.date-filter-patient.search-shadow-box {
  box-shadow: 0px 3px 6px #00000029;
  padding: 8px;
  border-radius: 5px;
}

.cancel-patient-search-btn {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(-50%, -50%);
  font-size: 20px;
  font-weight: 600;
  cursor: pointer;
}

.cross-icon-calendar {
  color: #ff7700;
}

.calender-icon-calendar {
  color: #8107d1;
}

.calendar-icon-patient {
  position: relative;
  z-index: 99;
}

.dor {
  font-size: 14px !important;
  background-color: red;
}

.expert_image {
  border-radius: 50%;
  margin-right: 5%;
  object-fit: cover;
  width: 30px;
  height: 30px;
}

/* .page-link.active,
.active > .page-link {
  background-color: transparent !important;
  border-bottom: 2px solid #8107d1 !important;
  border: none;
  color: #8107d1;
} */

.active-index:hover {
  /* background-color: white !important; */
  color: #3489fd;
}

.active-index:focus {
  /* background-color: white !important; */
  color: #3489fd;
}

/* .btn.grey-btn {
    background: #dfdfdf 0% 0% no-repeat padding-box !important;
    box-shadow: inset 0px 3px 6px #00000029;
    border-radius: 3px 3px 0px 0px !important;
    opacity: 1;
    border: 1px solid #fae5ff !important;
    width: 169px;
    height: 45px;
    border-bottom: 2px solid #414146 !important;
    font-size: 14px !important;
    color: #5b5b5b !important;
    font-weight: 500 !important;
} */
.btn.grey-btn:hover,
.btn.grey-btn:active {
  background: #fae5ff 0% 0% no-repeat padding-box;

  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.btn.grey-btn:focus {
  background: #fae5ff 0% 0% no-repeat padding-box;
  /* box-shadow: inset 0px 3px 6px #00000029; */
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}
.btn.grey-btn.activeExpertsTab {
  background: #fae5ff !important;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px !important;
  opacity: 1;
  border: 1px solid #fae5ff !important;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1 !important;
  font-size: 14px !important;
  color: #5b5b5b !important;
  font-weight: 500 !important;
}

.activeDoctorsTab {
  color: white !important;
  border: 1px solid #8107d1 !important;
  background-color: #8107d1 !important;
}

.date-picker-patients-list {
  box-shadow: 0px 3px 6px #00000029;
  border: 0px !important;
  height: 30px;
  width: 100%;
  font-size: 12px;
  color: grey;
}

.date-picker-patients-list:hover {
  outline: none !important;
}

/* *****************************************************Experts******************************************** */

.bg-color {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  opacity: 1;
  padding: 10px;
  background-color: white;
}

.heading {
  letter-spacing: 0px;
  color: #5b5b5b !important;
  opacity: 1;
  font-size: 17px;
  font-weight: 600;
}

.experts-approval-request-section {
  min-height: 660px;
}

.experts-profile-approval-request-section {
  min-height: 500px;
}

.custom-border {
  /* border: 1px solid #e3e3e3; */
  border-radius: 5px;
  opacity: 1;
}
.custom-table {
  border-collapse: collapse; /* Ensures borders are not doubled */
}

.custom-table th,
.custom-table td {
  border: none; /* Removes borders from cells */
}

.sub-heading {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-weight: 600;
  font-size: 12px !important;
}

.purple-num {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  font-size: 40px;
}

.bottom-border {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  opacity: 1;
  border: 2px solid var(--unnamed-color-e3e3e3);
  border: 2px solid #e3e3e3;
  opacity: 1;
  height: 0px;
}

.green-num {
  letter-spacing: 0px;
  color: #04ab20;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.pink-bg {
  background: #fdf5ff 0% 0% no-repeat padding-box;
  border-radius: 5px;
  opacity: 1;
  /* border: 1px solid blue; */
}

.purple-count {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.orange-count {
  letter-spacing: 0px;
  color: #ff971a;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.green-count {
  letter-spacing: 0px;
  color: #04ab20;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.app-red-count {
  letter-spacing: 0px;
  color: #ff2e2e;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.app-black-count {
  letter-spacing: 0px;
  color: #414146;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.chartpadding {
  /* padding: 20px; */
  display: flex;
  justify-content: center;
  align-items: center;
  /* border: 1px solid red; */
}

.grey-container {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  opacity: 1;
  padding: 15px;
  min-height: 200px;
}

span.purple-span {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  font-weight: 400;
}

.purple-text {
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
}

.single-line {
  white-space: nowrap;
  /* Prevents text wrapping */
  overflow: hidden;
  /* Ensures no overflow issues */
  text-overflow: ellipsis;
  /* Adds "..." if the text is too long */
}

.doctor-patient-name {
  color: #8107d1 !important;
  font-size: 12px;
}

button.btn.btn-primary.purple-btn {
  width: 100%;
  box-shadow: 0px 3px 6px #00000014;
  border-radius: 3px;
  opacity: 1;
  border: 1px solid #8107d1;
  background-color: #8107d1;
  font-size: 14px;
}

.btn-primary.black-btn {
  width: 100%;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  background-color: #414146;
  padding: 10px;
  font-size: 12px;
}

.btn.transparent-btn {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  opacity: 1;
  background-color: #ffffff;
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  padding: 7px;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
}

.btn.transparent-btn:hover {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  opacity: 1;
  background-color: #8107d1;
  letter-spacing: 0px;
  color: #8107d1;
  opacity: 1;
  padding: 7px;
  width: 100%;
  color: white;
  font-size: 14px;
}

.btn.transparent-btn:active {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  opacity: 1;
  background-color: #8107d1 !important;
  letter-spacing: 0px;
  opacity: 1;
  padding: 7px;
  width: 100%;
  color: white !important;
  font-size: 14px;
}

tbody.custom-border {
  border: 1px solid white;
}

thead.custom-border {
  border: 1px solid white;
}

.custom-border-counting {
  border-radius: 5px;
  opacity: 1;
  background-color: white;
  box-shadow: 0px 3px 6px #00000012;

  border: 1px solid #e3e3e3;
  border-radius: 5px;
}

tr.custom-row {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  opacity: 1;
  margin-top: 2%;
}

.no-border-btn {
  background-color: white;
  border: none;
  color: #ff971a;
}

.custom-font-size {
  font-size: 12px;
}

.content-scroll-1 {
  max-height: 725px;
  /* overflow-y: scroll; */
}

.content-scroll-1::-webkit-scrollbar {
  display: none;
}

.content-scroll {
  max-height: 717px;
  /* overflow-y: scroll; */
}

.content-scroll::-webkit-scrollbar {
  display: none;
}

/* ******************************************************Patients****************************************************888 */
.grey-bg {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  background-color: #f6f6f6;
  padding: 20px;
}

.payment-back {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  background-color: white;
}

.purple-content {
  letter-spacing: 0px;
  color: #8107d1 !important;
  opacity: 1;
  font-size: 12px;
}

td.purple-content {
  letter-spacing: 0px;
  color: #8107d1 !important;
  opacity: 1;
  font-size: 12px;
  border: none;
}

td.custom-font {
  font-size: 12px;
}

a.main-purple-content {
  color: #8107d1;
  font-size: 12px;
}

td.green-text {
  letter-spacing: 0px;
  color: #04ab20;
  opacity: 1;
  font-weight: 500;
}

td.orange-text {
  letter-spacing: 0px;
  color: #ff971a;
  opacity: 1;
  font-weight: 500;
}

td.red-text {
  letter-spacing: 0px;
  color: #ff2e2e;
  opacity: 1;
  font-weight: 500;
}

td.grey-text {
  letter-spacing: 0px;
  color: #96969c;
  opacity: 1;
  font-weight: 500;
}

.custom-form-select-date {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  font-size: 14px;
  color: #5b5b5b;
}

.custom-form-select-date:focus {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  border-color: transparent;
}

.red-num {
  letter-spacing: 0px;
  color: #ff2e2e;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

ul.pagination.custom-pagination {
  --bs-pagination-border-color: white;
}

/* span.page-count {
  font-weight: 700;
  color: #8107d1;
  font-size: 17px;
}
.custom-page-link:hover {
  background-color: transparent;
  border: none;
}
.custom-page-link:focus {
  background-color: transparent;
  border: none;
  box-shadow: none;
} */
.form-select.select-font {
  font-size: 13px !important;
  box-shadow: 0px 3px 6px #00000029;
  height: 37px;
  border: none;
  border-radius: 0px;
}

.form-select.select-font:focus {
  border: none;
  box-shadow: 0px 3px 6px #00000029;
}

.form-control.search-input-focus {
  font-size: 12px;
  padding: 7px;
}

.form-control.search-input-focus:focus {
  border-color: transparent;
  box-shadow: none;
  font-size: 12px;
}

/* *******************************************Child Admin******************************** */
.light-grey-bg {
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border-radius: 5px;
  opacity: 1;
  padding: 10px;
}

.href_link {
  text-decoration: none;
  color: #8107d1;
}

.placeHolder_loading {
  margin-bottom: 4%;

  border-radius: 8px;
  /* Add rounded corners */
}

.approvals_request_title {
  /* border: 1px solid red; */
  text-align: center;
  color: #8107d1;
  font-weight: 600;
  font-size: 1.5rem;
}

.no-Patient-tickets-found {
  height: 100%;
  /* border: 1px solid red; */
}

.PiFolderNotchOpenFill_icon {
  color: #8107d1;
  size: 50;
}

.approvals_request_section {
  height: 100%;
}

.child-admin-name {
  text-decoration: none;
  color: #8107d1;
}

.total-child-admin {
  color: #8107d1;
}

.consultation-completed-count {
  letter-spacing: 0px;
  color: #04ab20;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.consultation-unattended-count {
  letter-spacing: 0px;
  color: #ff971a;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.consultation-recheduled-count {
  letter-spacing: 0px;
  color: #ff2e2e;
  opacity: 1;
  font-size: 20px;
  font-weight: 600;
}

.consultation-cancelled-count {
  letter-spacing: 0px;
  color: #b50000;
  opacity: 1;
  font-size: 25px;
  font-weight: 600;
}

.no-latest-appointments,
.no-expert-approval-record {
  color: #8107d1;
  opacity: 1;
  font-size: 20px;
  font-weight: 500;
}

.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  /* Adjust based on the content */
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  /* Position above the tooltip */
  left: 50%;
  margin-left: -100px;
  /* Center the tooltip */
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.by-expert-font {
  font-size: 13px !important;
  padding: 7px;
  color: #5b5b5b;
  box-shadow: 0px 3px 6px #00000029;
  border: none;
  height: 37px;
}

select.form-select.by-expert-font:focus {
  border: none;
  box-shadow: 0px 3px 6px #00000029;
}

.chart-pink-bg {
  background: #fdf5ff 0% 0% no-repeat padding-box;
  border-radius: 5px;
  opacity: 1;
  /* border: 1px solid blue; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.chartpadding {
  /* padding: 20px; */
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.warning-message-limit {
  color: orangered;
  font-size: 13px;
}

.patient-line-graph-heading {
  letter-spacing: 0px;
  color: #5b5b5b !important;
  opacity: 1;
  font-size: 17px;
  font-weight: 600;
}
