import Image from "next/image";
import React from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import HU_logoImage from "../../../../../public/images/white-logo.png";
import {
  capitalizeFullName,
  changeDateFormat,
  formatDate,
  splitPatientName,
  splitName,
  formatCustomDate,
  formatDateandTime,
} from "../../../../utils/helperfunction";
import { FaFileSignature } from "react-icons/fa";

const ConsentFormModal = ({
  showConsentModal,
  setShowConsentModal,
  consentFormData,
}) => {
  const { firstName, lastName } =
    consentFormData &&
    consentFormData?.expert_name &&
    splitName(consentFormData?.expert_name);

  return (
    <Modal
      show={showConsentModal}
      onHide={() => {
        setShowConsentModal(false);
      }}
      dialogClassName="viewConsent-modal-width"
      // backdrop="static"
      // keyboard={false}
      scrollable
    >
      <Modal.Header className="d-none d-xl-block" >
        <div className="model_title_doctor_consent">
          <div>Consent Form Preview</div>{" "}
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="bg-color consentform-custom-overflow ">
          <div className="row docPat-consent-form-box mt-3 mx-auto">
            <div className="bg-heading d-flex justify-content-around align-items-center p-2">
              <Image
                src={HU_logoImage}
                alt="Health Unwired Logo"
                className="cu-logo d-none d-xl-block mt-3"
              />
              &nbsp; &nbsp;
              <p className="heading mb-0 pe-2 text-white">
                TELECONSULTATION CONSENT FORM FOR ONCOLOGY CONSULTATIONS
              </p>
            </div>
            <form className="p-xl-4">
              <div className="row">
                <label
                  htmlFor="patientname"
                  className="form-label custom-form-label mb-1"
                >
                  Consulted Patient’s Name
                </label>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control custom-form-placeholder"
                    placeholder="Sandeep"
                    // value={patientFirstName}
                    value="Patient first name"
                    aria-label="First name"
                    readOnly
                    disabled
                  />
                </div>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control custom-form-placeholder"
                    placeholder="Nayak"
                    value="Patient last name"
                    aria-label="Last name"
                    readOnly
                    disabled
                  />
                </div>
                {/* ********************************* */}
                <div className="col d-xl-none">
                  <input
                    type="text"
                    className="form-control custom-form-control custom-form-placeholder"
                    placeholder="Sandeep"
                    // value={patientName}
                    value="Patient first name"
                    aria-label="First name"
                    readOnly
                    disabled
                  />
                </div>
                {/* ********************************* */}
              </div>
              <div className="row">
                <label
                  htmlFor="docname"
                  className="form-label custom-form-label mb-1 mt-2"
                >
                  Consulting{" "}
                  {capitalizeFullName(
                    consentFormData.expert_role
                      ? consentFormData.expert_role
                      : "expert role"
                  )}
                  ’s Name
                </label>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control"
                    placeholder="First name"
                    aria-label="First name"
                    value={
                      firstName
                        ? capitalizeFullName(firstName)
                        : "Expert first name"
                    }
                    disabled
                    // value="Nandhakumar"
                  />
                </div>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control"
                    placeholder="Last name"
                    aria-label="Last name"
                    value={
                      lastName
                        ? capitalizeFullName(lastName)
                        : "Expert Last name"
                    }
                    disabled
                    // value="Nandhakumar"
                  />
                </div>
                {/* ********************************* */}
                <div className="col d-xl-none">
                  <input
                    type="text"
                    className="form-control custom-form-control"
                    placeholder="First name"
                    aria-label="First name"
                    value={
                      firstName
                        ? capitalizeFullName(firstName)
                        : "Expert first name"
                    }
                    disabled
                  />
                </div>
                {/* ********************************* */}
              </div>

              <p className="custom-form-label mb-1 mt-2">
                Date of Consultation
              </p>
              <span className="fw-bold custom-para">
                {consentFormData?.DateOfConsentForm
                  ? formatDateandTime(consentFormData?.DateOfConsentForm)
                  : "05 / 07 / 2024"}
              </span>

              <p className="custom-para mt-3">
                I, the undersigned, acknowledge and consent to participate in a
                teleconsultation session with the above-named doctor
                specializing in oncology. I have been informed and understand
                the following terms and conditions:
              </p>
              <div className="image-container overflow-hidden">
                <div className="content-scroll consent-custom-overflow overflow-auto">
                  <div className="custom-para consent-addContent">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: consentFormData?.ConsentContent,
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="mb-2">
                <p className="custom-para">
                  <span className="fw-bold mt-3">Consent Duration</span>
                  <br /> This consent is valid for the specific teleconsultation
                  session mentioned above.
                  <br />
                  <br /> I, the undersigned, hereby acknowledge that I have read
                  and understood the terms and conditions outlined in this
                  Teleconsultation Consent Form. I willingly and voluntarily
                  consent to the teleconsultation with the specified oncology
                  doctor, granting access to my medical records for the purpose
                  of this consultation. I understand the potential risks and
                  benefits associated with teleconsultation.
                  <br />
                  <br />
                  <div className="d-flex">
                    <div className="col-sm-auto">
                      <p className="fw-bold">Doctor Signature</p>
                      {consentFormData && consentFormData?.expert_photo ? (
                        <>
                          <Image
                            src={consentFormData?.expert_sign}
                            alt=""
                            className="mb-2 user_signature rounded"
                            width={150}
                            height={150}
                          />
                        </>
                      ) : (
                        <div className="d-flex justify-content-center align-items-center  py-5 border rounded">
                          <p className="text-center text-secondary px-3">
                            Expert&apos;s signature will be
                            <br /> added here...
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="col-sm-auto ms-auto ">
                      <p className="fw-bold">patient Signature</p>
                      <div className="d-flex justify-content-center align-items-center py-5 border rounded">
                        {/* <FaFileSignature size={150} /> */}
                        <p className="text-center text-secondary px-3">
                          Patient&apos;s signature will be
                          <br /> added here...
                        </p>
                      </div>
                    </div>
                  </div>
                  <br />
                  <br />
                  <span className="fw-bold">
                    Healthcare Provider Confirmation:
                  </span>
                  <br /> I confirm that I have explained the nature, purpose,
                  and potential risks of the teleconsultation to the patient. I
                  have answered any questions the patient may have had, and they
                  have provided informed consent.
                  <br />
                  <br />{" "}
                  <span className="fw-bold">Healthcare Provider Name:</span>
                  <br /> Cancer Unwired Thank you for choosing Cancer Unwired
                  for your oncology care. If you have any further questions or
                  concerns, please do not hesitate to contact us at +852 8197
                  4746 or email <NAME_EMAIL>.
                </p>
              </div>

              <div className=" float-end mb-4 me-3"></div>
            </form>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button
          className="orange-btn"
          onClick={() => {
            setShowConsentModal(false);
          }}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ConsentFormModal;
