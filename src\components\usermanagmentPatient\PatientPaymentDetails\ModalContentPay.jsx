import React from "react";
import { Modal, Form } from "react-bootstrap";

const ModalContentPay = React.forwardRef(({ selectedPayment }, ref) => {
  return (
    <div ref={ref}>
      <Modal.Body>
        <Form>
          <div className="row">
            <Form.Group className="mb-3 col-md-12" controlId="inputId">
              <Form.Label className="purple-content">Id</Form.Label>
              <Form.Control
                type="text"
                value={selectedPayment?.invoice?.id}
                readOnly
              />
            </Form.Group>
            <Form.Group className="mb-3 col-sm-5" controlId="inputName">
              <Form.Label className="purple-content">Name</Form.Label>
              <Form.Control
                className="text-capitalize"
                type="text"
                value={selectedPayment?.invoice?.customer_name}
                readOnly
              />
            </Form.Group>
            <Form.Group className="mb-3 col-sm-7" controlId="inputEmail">
              <Form.Label className="purple-content">Email</Form.Label>
              <Form.Control
                type="email"
                value={selectedPayment?.invoice?.customer_email}
                readOnly
              />
            </Form.Group>
          </div>
          <div className="border p-2">
            <div className="border p-3">
              <div className="row">
                <div className="col-sm-6">
                  <p className="purple-content">Consultation</p>
                </div>
                <div className="col-sm-3">
                  <p className="float-end purple-content">Cost</p>
                </div>
                <div className="col-sm-3">
                  <p className="float-end purple-content">Price</p>
                </div>
              </div>
              <hr />
              <div className="row">
                <div className="col-sm-6">
                  <p className="">
                    {
                      selectedPayment?.invoice?.lines?.data[0]?.description?.split(
                        "_"
                      )[1]
                    }
                  </p>
                </div>
                <div className="col-sm-3">
                  <p className="float-end">${selectedPayment?.amount}</p>
                </div>
                <div className="col-sm-3">
                  <p className="float-end">${selectedPayment?.amount}</p>
                </div>
              </div>
            </div>
            <div className="row mt-2">
              <div className="col-sm-9">
                <p className="float-end fw-light mb-0">Subtotal</p>
              </div>
              <div className="col-sm-3">
                <p className="">${selectedPayment?.amount}</p>
              </div>
            </div>
            <div className="row mb-1">
              <div className="col-sm-9">
                <p className="float-end fw-light">Dues</p>
              </div>
              <div className="col-sm-3">
                <p className="green-text">Cleared</p>
              </div>
            </div>
          </div>
        </Form>
      </Modal.Body>
    </div>
  );
});

ModalContentPay.displayName = "ModalContentPay";
export default ModalContentPay;
