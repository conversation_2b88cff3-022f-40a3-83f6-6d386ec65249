"use client";
import React, {
  useState,
  useEffect,
  useContext,
  useRef,
  useCallback,
} from "react";
import "react-big-calendar/lib/css/react-big-calendar.css";
import "./appointmentmanage.css";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { AdminDetailsContext } from "../../Context/AdminContext/AdminContext";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
import { HiInformationCircle } from "react-icons/hi2";
import PrescriptionModal from "./PrescriptionModal/PrescriptionModal";
import noData from "../../../public/assets/noDataFound.png";
import Image from "next/image";
import {
  convertIntoTime,
  renderPlaceholders,
  calculateDuration,
  formatSelectedDate,
  capitalizeFullName,
} from "../../utils/helperfunction";
import DocPatConsentFormModal from "./PrescriptionModal/DocPatConsentFormModal";
import IRPrescriptionModal from "./PrescriptionModal/IRPrescriptionModal";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FaCalendar, FaTimes } from "react-icons/fa";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { useDebouncedCallback } from "use-debounce";
import CustomPagination from "../../components/CustomPagination/CustomPagination";
import Cookies from "js-cookie";
import NoDataFound from "../noDataFound/NoDataFound";
function withTooltip(abbr, fullform) {
  return (
    <>
      <OverlayTrigger
        key="top"
        placement="top"
        overlay={<Tooltip id="tooltip-top">{fullform}</Tooltip>}
      >
        <p className="mb-0">
          {abbr} &nbsp;
          <span>
            <HiInformationCircle color="black" />
          </span>
        </p>
      </OverlayTrigger>
    </>
  );
}
const headers = [
  { label: "DOA", tooltip: "Date of Appointment" },
  { label: "C-Id", tooltip: "Consultation Id" },
  { label: "Expert Name" },
  { label: "Expert Role" },
  { label: "Duration" },
  { label: "Patient Name" },
  { label: "Description" },
  { label: "DOB", tooltip: "Date of booking" },
  { label: "Status" },
  { label: "Prescription" },
  { label: "Consent Form" },
];

const ExpertsAppointmentsList = () => {
  const { session } = useContext(AdminDetailsContext);
  const admin_id = Cookies.get("userId") || session?.user.id;
  const axiosAuth = useAxiosAuth();
  const router = useRouter();

  const searchParams = useSearchParams();
  
  const queryRef = useRef(router.query);
  const [appointments, setAppointments] = useState([]);
  const [showModal, setShowModal] = useState(false);
  // const [consentFormshowModal, setConsentFormShowModal] = useState(false);
  const [openConsentModalIndex, setOpenConsentModalIndex] = useState(null);
  const [openPrescriptionIndex, setOpenPrescriptionIndex] = useState(null);
  const [prescriptionData, setPrescriptionData] = useState(null);
  const [consentFormData, setConsentFormData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState("");
  const [perPageData, setPerPageData] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [showPicker, setShowPicker] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [userRole, setUserRole] = useState(null);
  const searchMenu = useRef(null);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const cacheRef = useRef({});

  const fetchConsentFormData = async (expertId) => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_CONSENTFORMDATA}${expertId}/`
      );
      if (response.status === 200) {
        setConsentFormData(response.data);
      } else {
        console.error(
          "Error fetching data from the API: ",
          response.status,
          response.statusText
        );
      }
    } catch (error) {
      console.error("Error fetching data from the API: ", error);
      setLoading(false);
    }
  };

  const fetchPrescriptionData = async (prescriptionID) => {
    try {
      let apiUrl;

      if (userRole === "doctor") {
        apiUrl = `${process.env.NEXT_PUBLIC_GET_PRESCRIPTION}${prescriptionID}`;
      } else {
        apiUrl = `${process.env.NEXT_PUBLIC_GET_IRPRESCRIPTION}${prescriptionID}`;
      }

      const response = await axiosAuth.get(apiUrl);

      if (response.status === 200) {
        setPrescriptionData(response.data);
      } else {
        console.error(
          "Error fetching prescription data from the API: ",
          response.status,
          response.statusText
        );
        toast.warning("Prescription not yet added!", {
          position: "top-center",
          autoClose: 5000,
          theme: "dark",
        });
      }
    } catch (error) {
      console.error("Error fetching prescription data from the API: ", error);
    }
  };
  const fetchAppointmentData = useCallback(
    async (apiUrl) => {
      try {
        setLoading(true);
        const response = await axiosAuth.get(apiUrl);

        let items = response?.data?.items || [];
        let total_pages = response?.data?.total_pages || 0;
        let total_items = response?.data?.total_items || 0;

        setAppointments(items);
        setTotalPages(total_pages);

        cacheRef.current[current_page] = {
          items,
          total_pages,
          total_items,
        };
      } catch (error) {
        console.error("Error fetching data from the API: ", error);
      } finally {
        setLoading(false);
      }
    },
    [axiosAuth, current_page]
  );

  useEffect(() => {
    let url = `/calendar?`;

    if (statusFilter) url += `&status=${statusFilter}`;
    if (role) url += `&role=${role}`;
    if (startDate && endDate) {
      const formattedStartDate = formatSelectedDate(startDate);
      const formattedEndDate = formatSelectedDate(endDate);
      url += `&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
    }
    if (searchTerm) url += `&search=${searchTerm}`;

    router.push(url, { scroll: false });

    let api = process.env.NEXT_PUBLIC_GET_ALL_USER_APPOINTMENTS;
    let apiUrl = `${api}?user_id=${admin_id}&page=${current_page}&per_page=10`;

    // Fetch data if no filters are applied
    if (!role && !searchTerm && !endDate && !startDate && !statusFilter) {
      fetchAppointmentData(apiUrl);
      return; // Return to avoid further processing
    }

    let filterQuery = [
      `user_id=${admin_id}`,
      `page=${current_page}`,
      `per_page=${perPageData}`,
    ];

    // Build the query string based on applied filters
    if (role !== "") filterQuery.push(`role=${role}`);
    if (searchTerm) filterQuery.push(`name=${searchTerm}`);
    if (startDate && endDate) {
      const formattedStartDate = formatSelectedDate(startDate);
      const formattedEndDate = formatSelectedDate(endDate);
      filterQuery.push(
        `start_date=${formattedStartDate}&end_date=${formattedEndDate}`
      );
    }
    if (statusFilter) filterQuery.push(`app_status=${statusFilter}`);

    // Construct the final API URL
    apiUrl = `${api}?${filterQuery.join("&")}`;

    // Fetch data
    fetchAppointmentData(apiUrl);
  }, [
    admin_id,
    axiosAuth,
    role,
    searchTerm,
    startDate,
    endDate,
    statusFilter,
    router,
    fetchAppointmentData,
    current_page, // Add current_page to the dependency array
    perPageData,
  ]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);

    setStatusFilter(params.get("status") || "");
    setSearchTerm(params.get("search") || "");
    setRole(params.get("role") || "");
    // Restore startDate and endDate if they are present in the URL
    const startDateParam = params.get("start_date");
    const endDateParam = params.get("end_date");
    if (startDateParam && endDateParam) {
      setStartDate(formatSelectedDate(startDateParam));
      setEndDate(formatSelectedDate(endDateParam));
    }
  }, []);

  const handlePrescriptionButtonClick = (appointment) => {
    setShowModal(true);
    fetchPrescriptionData(appointment.prescriptions[0]);
  };

  const clearFilters = (filtersToClear) => {
    let url = `/calendar?`;

    // Remove the specified filters from the URL
    filtersToClear.forEach((filter) => {
      switch (filter) {
        case "role":
          setRole("");
          break;
        case "searchTerm":
          setSearchTerm("");
          break;
        case "statusFilter":
          setStatusFilter("");
          break;
        case "startDate":
          setStartDate("");
          break;
        case "endDate":
          setEndDate("");
          break;
        default:
          break;
      }
    });

    // Add the remaining filters to the URL
    if (role) url += `role=${role}&`;
    if (searchTerm) url += `search=${searchTerm}&`;
    if (statusFilter) url += `status=${statusFilter}&`;
    if (startDate && endDate)
      url += `start_date=${startDate}&end_date=${endDate}&`;

    // Remove the last '&' if present
    url = url.replace(/&$/, "");
    if (startDate && endDate) {
      setShowPicker(false);
    }
    router.push(url, { scroll: false });
  };

  const handleSearch = useDebouncedCallback((term) => {
    const lowercaseTerm = term.toLowerCase(); // Convert term to lowercase

    // Only update the state if the new value is different
    if (lowercaseTerm !== searchTerm) {
      setSearchTerm(lowercaseTerm);
    }
  }, 1000);

  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  return (
    <>
      <div className="calendar-back p-0">
        <div className="row ">
          <div className="col-sm-12 ">
            <div className="app-table-pink">
              <div className="row mb-2 mt-2">
                <div className="row">
                  <div className="col-sm-12">
                    <div className="row">
                      <div className="col-sm-3 ">
                        <div className="input-group input-group-sm selectDate ">
                          <select
                            className="form-select statusFilter"
                            aria-label="Default select example"
                            name="status"
                            value={statusFilter}
                            onChange={(event) => {
                              setStatusFilter(event.target.value);
                            }}
                            disabled={loading}
                          >
                            <option
                              value=""
                              defaultValue={"Select Meeting status"}
                            >
                              Select Appointment Status
                            </option>
                            <option value="Completed">Completed</option>
                            <option value="Rescheduled">Rescheduled</option>
                            <option value="Expired">Expired</option>
                            <option value="Upcoming">Upcoming</option>
                            <option value="Cancelled">Cancelled</option>
                            <option value="Cancellation Rejection">
                              Cancellation Rejection
                            </option>
                            <option value="Cancellation Pending">
                              Cancellation Pending
                            </option>
                          </select>
                          {statusFilter && (
                            <span
                              className="cross-container mx-2"
                              onClick={() => clearFilters(["statusFilter"])}
                            >
                              <span className="cross-symbol">&#10006;</span>
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="col-sm-3">
                        <div
                          ref={searchMenu}
                          className="calender-filter-container"
                        >
                          <span
                            className="date-filter-patient"
                            onClick={() => {
                              setShowPicker(!showPicker);
                            }}
                          >
                            {startDate
                              ? `${startDate.toLocaleDateString()} - ${
                                  endDate ? endDate.toLocaleDateString() : ""
                                }`
                              : "Select Date Range"}
                            <span className="calendar-icon-patient">
                              {startDate ? (
                                <FaTimes
                                  className=" cross-icon-calendar"
                                  onClick={() =>
                                    clearFilters(["startDate", "endDate"])
                                  }
                                />
                              ) : (
                                <FaCalendar className=" calender-icon-calendar" />
                              )}
                            </span>
                          </span>

                          {showPicker && (
                            <div style={{ position: "absolute", zIndex: 100 }}>
                              <DatePicker
                                selected={startDate}
                                startDate={startDate}
                                endDate={endDate}
                                selectsRange
                                inline
                                disabled={loading}
                                showTimeSelect={false}
                                onChange={(dates) => {
                                  setStartDate(dates[0]);
                                  setEndDate(dates[1]);
                                }}
                                shouldCloseOnSelect={true}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="col-sm-3">
                        <div className="input-group input-group-sm searchbar">
                          <input
                            type="text"
                            id="app-search"
                            className="form-control"
                            placeholder="Search Name..."
                            // value={searchTerm}
                            disabled={loading}
                            onChange={(event) => {
                              handleSearch(event.target.value);
                            }}
                            defaultValue={
                              searchParams.has("search")
                                ? searchParams.get("search")
                                : ""
                            }
                          />
                          {/* {searchTerm && (
                            <span
                              className="role-cross-container"
                              onClick={() => clearFilters(["searchTerm"])}
                            >
                              <span className="role-cross-symbol">
                                &#10006;
                              </span>
                            </span>
                          )} */}
                        </div>
                      </div>
                      <div className="col-sm-3">
                        <div className="input-group input-group-sm selectDate ">
                          <select
                            className="form-select statusFilter"
                            aria-label="Default select example"
                            name="role"
                            value={role}
                            disabled={loading}
                            onChange={(e) => setRole(e.target.value)}
                          >
                            <option
                              value=""
                              defaultValue={"Select Select Role"}
                            >
                              Select Role
                            </option>
                            <option value="doctor">Doctor</option>
                            <option value="researcher">Researcher</option>
                            <option value="influencer">Influencer</option>
                          </select>
                          {role && (
                            <span
                              className="role-cross-container"
                              onClick={() => clearFilters(["role"])}
                            >
                              <span className="role-cross-symbol">
                                &#10006;
                              </span>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row">
                <div className="allApp-scroll px-3">
                  <table className="table custom-bg-table">
                    <thead className="custom-table-head">
                      <tr>
                        {headers.map((header, index) => (
                          <th
                            scope="col"
                            className="appHead app-head-white text-center"
                            style={{fontSize: "14px"}}
                            key={index}
                          >
                            {header.tooltip
                              ? withTooltip(header.label, header.tooltip)
                              : header.label}
                          </th>
                        ))}
                      </tr>
                    </thead>

                    <tbody className="allApp-scroll px-3">
                      {loading ? (
                        <tr>
                          <td colSpan="11">
                            {renderPlaceholders(7, "50px", "10px")}
                          </td>
                        </tr>
                      ) : (
                        <>
                          {appointments &&
                          Array.isArray(appointments) &&
                          appointments?.length === 0 ? (
                            <>
                              <tr>
                                <td colSpan="11" className="custom-border-td">
                                  <div className=" d-flex justify-content-center align-items-center no-appointment-data-found mt-5">
                                    <NoDataFound />
                                  </div>
                                </td>
                              </tr>
                            </>
                          ) : (
                            appointments &&
                            Array.isArray(appointments) &&
                            appointments
                              ?.slice()
                              .reverse()
                              .map((appointment, index) => (
                                <React.Fragment key={index}>
                                  <tr className="custom-table-body text-center">
                                    <td
                                      className=" "
                                      style={{ fontSize: "12px",whiteSpace: "nowrap" }}
                                    >
                                      {appointment?.app_data?.DateOfFixingApp}
                                    </td>
                                    <td style={{fontSize: "12px"}}> {appointment?.app_data?.id}</td>
                                    <td className="" style={{fontSize: "12px"}}>{appointment?.doctor_details.name}</td>
                                    <td className="" style={{fontSize: "12px"}}>

                                      {capitalizeFullName(
                                        appointment?.expert_role
                                      )}
                                    </td>
                                    <td className="" style={{fontSize: "12px"}}>
                                      {convertIntoTime(
                                        appointment?.slot_details
                                          ?.schedule_start_time,
                                        appointment?.doctor_details?.TimeZone
                                      )}{" "}
                                      -{" "}
                                      {convertIntoTime(
                                        appointment?.slot_details
                                          ?.schedule_end_time,
                                        appointment?.doctor_details?.TimeZone
                                      )}
                                      &nbsp;&nbsp;<br/>
                                      <span
                                        className=""
                                        style={{ color: "#5B5B5B" }}
                                      >
                                        (
                                        {appointment?.meeting_session_details
                                          ? `${calculateDuration(
                                              appointment
                                                ?.meeting_session_details
                                                .SessionStartTime,
                                              appointment
                                                ?.meeting_session_details
                                                .SessionEndTime
                                            )}`
                                          : "00:00:00"}
                                        )
                                      </span>
                                    </td>

                                    <td className=" single-line-appointment" style={{fontSize: "12px"}}>
                                      {appointment?.patient_details?.name}
                                    </td>

                                    <td className="col-1  single-line-appointment" style={{fontSize: "12px"}}>
                                      {appointment?.app_data?.summary}
                                    </td>
                                    <td className=" single-line-appointment" style={{fontSize: "12px"}}>
                                      {appointment?.slot_details?.schedule_start_time.substring(
                                        0,
                                        10
                                      )}
                                    </td>
                                    <td
                                      className=" "
                                      style={{
                                        fontSize: "12px",
                                        color: (() => {
                                          switch (
                                            appointment?.app_data?.status
                                          ) {
                                            case "Completed":
                                              return "#04AB20";
                                            case "Onging":
                                              return "#F37721";
                                            case "Expired":
                                              return "#eb001b";
                                            case "Upcoming":
                                              return "#099CF1";
                                            case "Cancelled":
                                              return "#B50000";
                                            default:
                                              return "inherit";
                                          }
                                        })(),
                                        fontWeight: "bold",
                                      }}
                                    >
                                      {appointment.app_data.status}
                                    </td>
                                    <td
                                      className=""
                                      style={{ fontSize: "12px" }}
                                    >
                                      {appointment?.prescriptions &&
                                        appointment?.prescriptions.length ===
                                          0 &&
                                        appointment?.meeting_session_details
                                          .MeetingStatus !== 2 && (
                                          <p style={{ color: "#B5B2B2" }}>
                                            Not Issued
                                          </p>
                                        )}
                                      <div className="row">
                                        {appointment?.prescriptions &&
                                          appointment?.prescriptions.length !=
                                            0 &&
                                          appointment?.expert_role ===
                                            "doctor" && (
                                            <div className="col-sm-12">
                                              <button
                                                type="button"
                                                className="viewPres-btn mb-3"
                                                onClick={() => {
                                                  setUserRole(
                                                    appointment?.expert_role
                                                  );
                                                  setOpenPrescriptionIndex(
                                                    index
                                                  );
                                                  handlePrescriptionButtonClick(
                                                    appointment
                                                  );
                                                }}
                                              >
                                                view Prescription
                                              </button>
                                              {prescriptionData &&
                                                openPrescriptionIndex ===
                                                  index && (
                                                  <PrescriptionModal
                                                    show={showModal}
                                                    onClose={() => {
                                                      setShowModal(false);
                                                      setPrescriptionData(null);
                                                    }}
                                                    data={prescriptionData}
                                                  />
                                                )}
                                            </div>
                                          )}

                                        {appointment?.prescriptions &&
                                          appointment?.prescriptions.length !=
                                            0 &&
                                          appointment?.expert_role !==
                                            "doctor" && (
                                            <div className="col-sm-12">
                                              <button
                                                type="button"
                                                className="viewPres-btn mb-3"
                                                onClick={() => {
                                                  setUserRole(
                                                    appointment?.expert_role
                                                  );
                                                  setOpenPrescriptionIndex(
                                                    index
                                                  );
                                                  handlePrescriptionButtonClick(
                                                    appointment
                                                  );
                                                }}
                                              >
                                                view Prescription
                                              </button>
                                              {prescriptionData &&
                                                openPrescriptionIndex ===
                                                  index && (
                                                  <IRPrescriptionModal
                                                    show={showModal}
                                                    onClose={() => {
                                                      setShowModal(false);
                                                      setPrescriptionData(null);
                                                    }}
                                                    data={prescriptionData}
                                                  />
                                                )}
                                            </div>
                                          )}

                                        {appointment?.prescriptions &&
                                          appointment?.prescriptions.length ==
                                            0 &&
                                          appointment?.meeting_session_details
                                            .MeetingStatus == 2 && (
                                            <p style={{ color: "#B5B2B2" }}>
                                              Not Issued
                                            </p>
                                          )}
                                      </div>
                                    </td>
                                    <td
                                      className=""
                                      style={{ fontSize: "12px" }}
                                      s
                                    >
                                      {appointment?.consent_details &&
                                      appointment?.consent_details
                                        ?.PatientConsent_DoctorForm === 1 ? (
                                        <button
                                          type="button"
                                          className="viewConsent-btn"
                                          onClick={() => {
                                            setOpenConsentModalIndex(index);
                                            fetchConsentFormData(
                                              appointment.doctor_details.id
                                            );
                                          }}
                                        >
                                          View
                                        </button>
                                      ) : (
                                        <p style={{ color: "#B5B2B2" }}>
                                          No Consent Form
                                        </p>
                                      )}
                                      {openConsentModalIndex === index && (
                                        <DocPatConsentFormModal
                                          // show={consentFormshowModal}
                                          show={true}
                                          onClose={() => {
                                            setOpenConsentModalIndex(null);
                                          }}
                                          data={consentFormData}
                                          patientName={
                                            appointment?.patient_details?.name
                                          }
                                          doctorName={
                                            appointment?.doctor_details.name
                                          }
                                          doctorSignature={
                                            appointment?.doctor_Signature
                                            // "https://cdn.shopify.com/s/files/1/0594/4639/5086/files/Line_Through_Name.jpg?v=**********"
                                          }
                                          patientSignature={
                                            appointment?.patient_Signature
                                            // "https://cdn.shopify.com/s/files/1/0594/4639/5086/files/Line_Through_Name.jpg?v=**********"
                                          }
                                          appointmentDate={appointment?.slot_details?.schedule_start_time.substring(
                                            0,
                                            10
                                          )}
                                          style={{ zIndex: 9999 }}
                                        />
                                      )}
                                    </td>
                                  </tr>
                                </React.Fragment>
                              ))
                          )}
                        </>
                      )}
                    </tbody>
                  </table>
                </div>
                <div>
                  <CustomPagination
                    total_pages={totalPages}
                    current_page={current_page}
                    setCurrent_Page={setCurrent_Page}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ExpertsAppointmentsList;
