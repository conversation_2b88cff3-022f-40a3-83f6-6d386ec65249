"use client";
import React, { useState, useEffect, useCallback, useContext } from "react";
import { Calendar, momentLocalizer, Views } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import moment from "moment";
import { PiCircleFill } from "react-icons/pi";
import {
  changeDateFormat,
  convertIntoTime,
  getStartAndEndDate,
  getStartAndEndDatesForNavigateMonth,
} from "../../../utils/helperfunction";
import EventDetailModal from "./EventDetailModal/EventDetailModal";
// import AppointmentDetailModal from "./AppointmentDetailModal/AppointmentDetailModal";
import BookedAppointmentDetails from "./BookedAppointmentDetails/BookedAppointmentDetails";
import "./appointmentmanage.css";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useParams } from "next/navigation";
import Loading from "../../Loading/PageLoading/Loading";

const localizer = momentLocalizer(moment);
const formatDayName = (date) => {
  return moment(date).format("dddd");
};

const AppointmentManage = () => {
  const [date, setDate] = useState(moment());
  const [expertTimeZone, setExpertTimeZone] = useState("");
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [showModal1, setShowModal1] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [currentView, setCurrentView] = useState("month");
  const [allFreeSlots, setAllFreeSlots] = useState([]);
  const [busySlots, setBusySlots] = useState(null);
  const [calenderLoading, setCalenderLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(moment());
  const [navigateCurrentMonth, setNavigateCurrentMonth] = useState(
    currentDate.month() + 1
  );
  const axiosAuth = useAxiosAuth();
  const params = useParams();
  const expert_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";

  const fetchTestData = useCallback(
    async (id) => {
      try {
        const today_Date = new Date();
        // const currentYear = today_Date.getFullYear();
        const currentYear = moment(date).year();
        const currentMonth = today_Date.getMonth() + 1;

        const { todayDate, endDateFutureMonth } = getStartAndEndDate(
          currentYear,
          currentMonth
        );
        const { startDate1, endDate1 } = getStartAndEndDatesForNavigateMonth(
          currentYear,
          navigateCurrentMonth
        );

        // Construct the API URL
        const freeSlotUrl = `${process.env.NEXT_PUBLIC_DOCTOR_FREE_SLOT}/${id}/${todayDate}/${endDateFutureMonth}/`;
        const bookedSlotUrl = `${process.env.NEXT_PUBLIC_GET_USER_APPOINTMENTS}${id}/?start_date=${startDate1}&end_date=${endDate1}`;

        const [freeSlotResponse, appointmentsResponse] = await Promise.all([
          axiosAuth.get(freeSlotUrl),
          axiosAuth.get(bookedSlotUrl),
        ]);
        setAllFreeSlots(freeSlotResponse?.data);

        let allSlots = [
          ...freeSlotResponse?.data,
          ...appointmentsResponse?.data,
        ];

        setBusySlots(allSlots);
        setCalenderLoading(false);
      } catch (error) {
        console.error("Error fetching test data:", error);
        setCalenderLoading(false);
      }
    },
    [setBusySlots, axiosAuth, navigateCurrentMonth, date]
  );

  useEffect(() => {
    fetchTestData(expert_id); // Trigger API call
    const expert_timezone = JSON.parse(localStorage.getItem("expert_timezone"));
    setExpertTimeZone(expert_timezone);
  }, [fetchTestData, expert_id, navigateCurrentMonth]);

  const eventStyleGetter = (event, start, end, isSelected) => {
    let backgroundColor = "";
    if (event) {
      if (event.status === "B") {
        event.title = "Booked";
        backgroundColor = "#ff7700";
      } else if (event.status === "C") {
        event.title = "Cancelled";
        backgroundColor = "#B50000";
      } else if (event.status === "P") {
        event.title = "Pending";
        backgroundColor = "#FF971A";
      } else if (event.status === "R" && event.summary) {
        event.title = "Rescheduled";
        backgroundColor = "#F80D38";
      } else if (event.status === "R" && !event.summary) {
        event.title = "Not Available";
        backgroundColor = "#989393";
      } else {
        backgroundColor = "#763CEF";
      }
    }
    return {
      style: {
        backgroundColor,
      },
    };
  };

  const busyEventSlots = busySlots?.map((busy) => {
    let format = "";
    if (currentView === "month1") {
      format = "YYYY-MM-DD"; // Date only for month view
    }
    return {
      ...busy,
      title:
        busy.summary && busy.status === "B"
          ? "Booked"
          : busy.summary && busy.status === "C"
          ? "Cancelled"
          : busy.summary && busy.status === "P"
          ? "Pending"
          : busy.summary && busy.status === "R"
          ? "Rescheduled"
          : !busy.summary && busy.status === "R"
          ? "Not Available"
          : "Available",

      start: moment
        .tz(
          busy?.schedule_start_time || busy?.slot_start_time,
          format,
          expertTimeZone
        )
        .toDate(),
      end: moment
        .tz(
          busy?.schedule_end_time || busy?.slot_end_time,
          format,
          expertTimeZone
        )
        .toDate(),
    };
  });

  const handlePrev = () => {
    const newDate = moment(date).subtract(1, "month");
    setDate(newDate);
    const newMonth = newDate.month() + 1;
    setNavigateCurrentMonth(newMonth);
    setCurrentDate(moment(newDate));
  };

  const handleNext = () => {
    const newDate = moment(date).add(1, "month");
    setDate(newDate);
    const newMonth = newDate.month() + 1;
    setNavigateCurrentMonth(newMonth);
    setCurrentDate(moment(newDate));
  };

  const getMonth = (date) => {
    return moment(date).format("MMMM");
  };
  const getYear = (date) => {
    return moment(date).format("yy");
  };

  // Define custom toolbar with "Previous" and "Next" arrows and the month and year
  const CustomToolbar = (toolbar) => (
    <div className="row mt-4">
      <div className="rbc-toolbar">
        <div className="col-sm-5"></div>
        <div className="col-sm-7">
          <div className="row">
            <div className="col-sm-8">
              <button
                type="button"
                className="btn btn-arrows"
                onClick={handlePrev}
              >
                &lt;
              </button>
              <span className="rbc-toolbar-label month-color">
                {getMonth(toolbar.date)}
              </span>
              <button
                type="button"
                className="btn btn-arrows"
                onClick={handleNext}
              >
                &gt;
              </button>
            </div>
            <div className="col-sm-4 year-col">
              <div className="year-flow">
                <span className="yaer-cal">Year</span>
                <span className="year-annual mx-2">
                  {getYear(toolbar.date)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const handleCloseModal = () => {
    setShowModal(false);
  };
  const handleEventClick = (event) => {
    setSelectedEvent(event);
    if (
      event.status === "B" ||
      event.status === "P" ||
      event.status === "C" ||
      (event.status === "R" && event.summary)
    ) {
      setShowModal1(true);
    } else {
      setShowModal(true);
    }
  };
  const handleCloseModal1 = () => {
    setShowModal1(false);
  };

  let eventDate = changeDateFormat(
    selectedEvent?.slot_start_time?.split("T")[0]
  );

  return (
    <>
      {calenderLoading ? (
        <>
          <Loading />
        </>
      ) : (
        <>
          <div className="calendar-back">
            <div className="row">
              {/* <div className="col-sm-auto  gx-0">
                <button type="button" className="btn btn-back mt-4">
                  Back
                </button>
              </div> */}
              <div className="col-sm mx-3">
                <Calendar
                  localizer={localizer}
                  events={busyEventSlots}
                  eventPropGetter={eventStyleGetter}
                  startAccessor="start"
                  endAccessor="end"
                  style={{ height: "562px" }}
                  onSelectEvent={(event) => handleEventClick(event)}
                  date={date}
                  onNavigate={(newDate) => setDate(newDate)}
                  views={[Views.MONTH]}
                  defaultView={Views.MONTH}
                  popup
                  timeslots={2}
                  selectable
                  formats={{
                    dayHeaderFormat: (date, culture, localizer) =>
                      formatDayName(date),
                  }}
                  components={{ toolbar: CustomToolbar }}
                  tooltipAccessor={(event) => {
                    const startTime = event.schedule_start_time
                      ? convertIntoTime(
                          event?.schedule_start_time,
                          expertTimeZone
                        )
                      : convertIntoTime(event?.slot_start_time, expertTimeZone);

                    const endTime = event.schedule_end_time
                      ? convertIntoTime(
                          event?.schedule_end_time,
                          expertTimeZone
                        )
                      : convertIntoTime(event?.slot_end_time, expertTimeZone);

                    return `${startTime} - ${endTime}`;
                  }}
                />
                <div className="color-identification-back">
                  <span className="mx-5">
                    <PiCircleFill className="color-purple mx-2" />
                    AVAILABLE
                  </span>
                  <span className="mx-5">
                    <PiCircleFill
                      className="color-booked mx-2"
                      color="#ff7700"
                    />
                    BOOKED
                  </span>
                  <span className="mx-5">
                    <PiCircleFill className="color-yellow mx-2" />
                    PENDING
                  </span>
                  <span className="mx-5">
                    <PiCircleFill className="color-red mx-2" />
                    CANCELLED
                  </span>
                  <span>
                    <PiCircleFill
                      className="color-rechedule mx-2"
                      color=" #b50000"
                    />
                    RECHEDULED
                  </span>
                </div>
              </div>
            </div>
          </div>
          <EventDetailModal
            show={showModal}
            event={selectedEvent}
            onClose={handleCloseModal}
            expertTimeZone={expertTimeZone}
          />
          {showModal1 && (
            <BookedAppointmentDetails
              eventDate={eventDate}
              event={selectedEvent}
              show={showModal1}
              onClose={handleCloseModal1}
            />
          )}
        </>
      )}
    </>
  );
};

export default AppointmentManage;
