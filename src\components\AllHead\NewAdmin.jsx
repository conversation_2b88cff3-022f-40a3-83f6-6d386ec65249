"use client";
import React, { useState } from "react";
import { Modal, Button, Form, Row, Col } from "react-bootstrap";
import NewAdminForm from "../AllHead/NewAdminForm";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";

const NewAdmin = () => {
  const { isAdminChildAdmin } = useAdminContext();
  const [showModalAdmin, setShowModalAdmin] = useState(false);
  const handleShowModal = () => setShowModalAdmin(true);
  const handleClose = () => setShowModalAdmin(false);

  return (
    <>
      {!isAdminChildAdmin && (
        <button
          type="button"
          className="btn btn-newadmin"
          onClick={handleShowModal}
        >
          New Admin
        </button>
      )}

      <Modal show={showModalAdmin} onHide={handleClose}>
        <div className="modal-content custom-modal-content-admin modal-for-newadmin">
          <Modal.Header className="new-user-section">
            <Modal.Title className="title-edit">New Child Admin</Modal.Title>
            <div>
              <button
                type="button"
                className="btn btn-cancle-plan"
                onClick={handleClose}
              >
                Dismiss
              </button>
            </div>
          </Modal.Header>
          <Modal.Body>
            <div className="row">
              <div className="col-sm-12">
                <Form>
                  <Row>
                    <NewAdminForm handleClose={handleClose} />
                  </Row>
                </Form>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    </>
  );
};

export default NewAdmin;
