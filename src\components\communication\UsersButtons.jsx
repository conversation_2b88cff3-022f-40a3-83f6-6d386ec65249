import React, { useState, useEffect, useCallback } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Cookies from "js-cookie";
import PatientsTicket from "../../components/communication/PatientsTicket";
import DoctorsTicket from "../../components/communication/DoctorsTicket";
import ResearchersTickets from "../../components/communication/ResearchersTickets";
import InfluencersTicket from "../../components/communication/InfluencersTicket";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";

const UsersButtons = () => {
  const searchParams = useSearchParams();
  const initialTab = searchParams.get("tab");
  const [activeTab, setActiveTab] = useState(initialTab || "patient");
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true); // Initially set to false
  const pathname = usePathname();
  const router = useRouter();
  const admin_id = session?.user?.id;
  const [ticketsData, setTicketsData] = useState({});
  const [error, setError] = useState(false);

  const axiosAuth = useAxiosAuth();

  const handleTabChange = useCallback(
    (tab) => {
      const updatedPath = `${pathname}?tab=${tab}`;
      router.replace(updatedPath, { shallow: true });
      setActiveTab(tab);
      // Clear data for inactive tabs
      setTicketsData((prevData) => ({
        ...prevData,
        [tab]: prevData[tab] || null,
      }));
    },
    [pathname, router]
  );
  const fetchTickets = useCallback(async () => {
    try {
      setLoading(true); // Set loading to true before fetching data
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_TICKETS_BASED_BY_USER_ROLE}${activeTab}/all/?user_id=${admin_id}&page=1&per_page=10
`
      );
      setTicketsData((prevData) => ({
        ...prevData,
        [activeTab]: response?.data,
      }));
    } catch (error) {
      console.error("Error fetching tickets:", error);
      setError(true);
    } finally {
      setLoading(false);
    }
  }, [admin_id, axiosAuth, activeTab]);

  useEffect(() => {
    fetchTickets();
  }, [activeTab, fetchTickets]);

  return (
    <div>
      <p className="d-inline-flex gap-1 buttons-row mb-0">
        <button
          className={`btn grey-btn ${
            activeTab === "patient" && "activeExpertsTab"
          }`}
          onClick={() => handleTabChange("patient")}
        >
          Patient
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "doctor" && "activeExpertsTab"
          }`}
          onClick={() => handleTabChange("doctor")}
        >
          Doctor
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "researcher" && "activeExpertsTab"
          }`}
          onClick={() => handleTabChange("researcher")}
        >
          Researcher
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "influencer" && "activeExpertsTab"
          }`}
          onClick={() => handleTabChange("influencer")}
        >
          Influencer
        </button>
      </p>
      {activeTab === "patient" && (
        <PatientsTicket
          data={ticketsData.patient}
          loading={loading}
          activeTab={activeTab}
        />
      )}
      {activeTab === "doctor" && (
        <DoctorsTicket
          data={ticketsData.doctor}
          loading={loading}
          activeTab={activeTab}
        />
      )}
      {activeTab === "researcher" && (
        <ResearchersTickets
          data={ticketsData.researcher}
          loading={loading}
          activeTab={activeTab}
        />
      )}
      {activeTab === "influencer" && (
        <InfluencersTicket
          data={ticketsData.influencer}
          loading={loading}
          activeTab={activeTab}
        />
      )}
    </div>
  );
};

export default UsersButtons;
