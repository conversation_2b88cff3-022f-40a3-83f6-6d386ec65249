import React, { useCallback, useEffect, useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import Image from "next/image";
import ViewBlogModal from "./AllApprovalModals/ViewBlogModal.jsx";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { FaFile } from "react-icons/fa";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useParams } from "next/navigation";
import { Placeholder } from "react-bootstrap";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

const AllBlogs = () => {
  const [showBlogModal, setShowBlogModal] = useState(false);
  const [articleLoading, setArticleLoading] = useState(true);
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  const axiosAuth = useAxiosAuth();
  const params = useParams();
  const doctor_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";

  const fetchArticles = useCallback(async () => {
    try {
      setLoading(true);

      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERTS_BLOG}${doctor_id}/0/`
      );

      setArticles(response?.data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setArticleLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  useEffect(() => {
    if (doctor_id) {
      fetchArticles();
    }
  }, [doctor_id, fetchArticles]);
  return (
    <>
      <div className="col-sm-6">
        <div className="">
          <p className="fw-bold grey-text">Blogs</p>
          <div className="">
            {articleLoading === false ? (
              <>
                {articles && articles?.length === 0 ? (
                  <div className="fs-4 d-flex align-items-center justify-content-center no-articles-found">
                    <PiFolderNotchOpenFill color={"purple"} size={30} />
                    &nbsp; No Records Found
                  </div>
                ) : (
                  <div
                    style={{ maxHeight: "130px", overflowY: "auto" }}
                    className="content-scroll-testimonials overflow-auto"
                  >
                    {articles?.map((item, index) => (
                      <div key={index} className="bg-color mb-2">
                        <div className="row bg-grey upload-reviews">
                          <div className="col-sm-1">
                            <FaFile
                              style={{
                                fontSize: "30px",
                                color: "#8107D1",
                              }}
                            />
                          </div>
                          <div className="col-sm-3">
                            <p className="fw-semibold mb-0">
                              {formatDate(item?.blog_details?.BlogDateTime)}
                            </p>
                            <div className="">
                              <p className="custom-transperent-btn">
                                {timeDifference(item.blog_details.BlogDateTime)}
                              </p>
                            </div>
                          </div>
                          <div className="col-sm-7 mb-0">
                            <p> {item.blog_details?.BlogTitle}</p>
                          </div>
                          <div className="col-sm-1">
                            <button
                              type="button"
                              className="btn btn-yellow"
                              onClick={() => handleArticleModal(item)}
                            >
                              Review
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            ) : (
              renderPlaceholders()
            )}
          </div>
        </div>
      </div>
      <ViewBlogModal
        showBlogModal={showBlogModal}
        setShowBlogModal={setShowBlogModal}
      />
    </>
  );
};

export default AllBlogs;
