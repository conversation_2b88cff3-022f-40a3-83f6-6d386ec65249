"use client";
import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import Select from "react-select";
import Swal from "sweetalert2";
import { AiFillCamera } from "react-icons/ai";
import "../profileSetting.css";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Loading from "../../Loading/PageLoading/Loading";
import moment from "moment-timezone";
import { toast } from "react-toastify";
import { useAdminContext } from "../../../Context/AdminContext/AdminContext";
import ChildAdminGrantedPermissions from "./ChildAdminGrantedPermissions";
import { capitalizeFullName } from "../../../utils/helperfunction";
import PasswordReset from "../PasswordReset/PasswordReset";
import PhoneInput from "react-phone-input-2";
import parsePhoneNumberFromString from "libphonenumber-js";
const validatePhoneNumber = (phoneNumber) => {
  const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
  return parsedPhoneNumber && parsedPhoneNumber.isValid();
};
const customStyles = {
  // control: (provided, state) => ({
  //   ...provided,
  //   border: state.isFocused ? "2px solid white" : "1px solid white",
  //   boxShadow: state.isFocused ? "0 0 3px white" : "white",
  //   borderColor: state.isFocused ? "white" : "white",
  //   width: "100%",
  //   ":hover": {
  //     borderColor: "white",
  //   },
  //   backgroundColor: state.isDisabled ? "white" : provided.backgroundColor,
  // }),
  control: (provided, state) => ({
    ...provided,
    
     border: state.isFocused ? "1px solid #e3e3e3" : "1px solid #e3e3e3",
    fontSize: "12px",
    height: "30px",
    width: "100%",
    ":hover": {
      borderColor: "#e3e3e3", // Consistent border on hover
    },
    backgroundColor: state.isDisabled ? "#f1f1f1" : provided.backgroundColor, // Keep background logic
  }),

  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused ? "#9426b2" : "white",
    color: state.isFocused ? "white" : "#333",
    ":hover": {
      backgroundColor: "#9426b2",
      color: "white",
    },
    borderRadius: "5px",
  }),
};

const genderOptions = [
  { value: "", label: "Select Gender" },
  { value: "Female", label: "Female" },
  { value: "Male", label: "Male" },
  { value: "Others", label: "Others" },
];
const initialPasswordData = {
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
};
const ChildAdminSettings = () => {
  const { isAdminChildAdmin, userPermissions, session } = useAdminContext();
  const axiosAuth = useAxiosAuth();
  const [originalFormData, setOriginalFormData] = useState({});
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [notificationsList, setNotificationsList] = useState([]);
  const [formData, setFormData] = useState({});
  const [timezones, setTimezones] = useState([]);
  const [passwords, setPasswords] = useState(initialPasswordData);
  const isPermissible = userPermissions?.includes("cu_app.manage_notif");
  const userId = session?.user?.id;
  const userEmail = session?.user?.email;

  const fetchNotificationList = useCallback(async () => {
    try {
      if (userId !== undefined && userId !== null) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_FETCH_NOTIFICATION_STATUS}all/?user_id=${userId}`
        );
        setNotificationsList(response?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error in getting the Notifications List", err);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, userId]);

  const getUserData = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_USER_DATA}${userEmail}`
      );

      setFormData(response?.data?.user_data);
      setOriginalFormData(response?.data?.user_data);
    } catch (error) {
      console.error("Error in getting user data:", error);
    }
  }, [userEmail, axiosAuth]);

  useEffect(() => {
    const timezoneList = moment?.tz?.names();
    setTimezones(timezoneList);
  }, []);

  const handleEditButtonClick = async () => {
    if (!editMode) {
      const shouldEdit = await Swal.fire({
        title: "Confirm Edit",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
        customClass: {
          confirmButton: "swal-confirm-button-class",
          cancelButton: "swal-cancel-button-class",
        },
        buttonsStyling: false,
      });

      if (!shouldEdit.isConfirmed) {
        return;
      }
    }

    setErrors({});
    setEditMode(!editMode);
  };

  useEffect(() => {
    if (userId && userEmail) {
      fetchNotificationList();
      getUserData();
    }
  }, [userId, fetchNotificationList, getUserData, userEmail]);

  if (loading) {
    return (
      <>
        <Loading />
      </>
    );
  }

  const timeZoneOptions = timezones?.map((timezone) => ({
    value: timezone,
    label: timezone,
  }));

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "Address") {
      if (!value) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          Address: "Please enter address",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          Address: null,
        }));
      }

      setFormData((prevData) => ({
        ...prevData,
        admin_other_details: {
          ...prevData.admin_other_details,
          Address: value,
        },
      }));
    } else if (name === "ProfilePhoto") {
      // Handle both file and URL
      const newValue = e.target.files ? e.target.files[0] : value;
      setFormData((prevData) => ({
        ...prevData,
        admin_other_details: {
          ...prevData.admin_other_details,
          ProfilePhoto: newValue,
        },
      }));
    } else if (name === "date_of_birth") {
      const dob = moment(value, "YYYY-MM-DD");
      const age = moment().diff(dob, "years");
      const formattedDateOfBirth = dob.format("YYYY-MM-DD");

      if (age < 18) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          date_of_birth: "Age must be at least 18 years old",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          date_of_birth: null,
        }));
        setFormData((prevData) => ({
          ...prevData,
          date_of_birth: formattedDateOfBirth,
        }));
      }
    } else if (name === "sex") {
      if (!value || value == "" || value === null || value === undefined) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          sex: "Please select gender",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          sex: null,
        }));
        setFormData((prevData) => ({
          ...prevData,
          sex: value,
        }));
      }
    } else if (name === "City" || name === "Country") {
      if (!value) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          [name]: `Please enter ${name.toLowerCase()}`,
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          [name]: null,
        }));
      }

      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }));
    } else {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }));
    }
  };

  const handlePhoneChange = (value, country, e, formattedValue) => {
    setFormData((prevData) => ({ ...prevData, phone: formattedValue }));
  };

  const handleSaveChangesButtonClick = async () => {
    const errorField = Object.keys(errors).find((key) => errors[key] !== null);
    if (errorField) {
      toast.error(errors[errorField], {
        autoClose: 5000,
        theme: "colored",
        position: "top-center",
      });
      return;
    }
    if (!formData.date_of_birth) {
      toast.error("Enter Date of Birth");
      return;
    }
    try {
      const formDataObj = new FormData();
      formDataObj.append("name", formData.name);
      const isValidPhoneNumber = validatePhoneNumber(formData?.phone);
      if (!isValidPhoneNumber) {
        toast.error("Invalid phone number");
        return;
      }
      formDataObj.append("phone", formData?.phone);
      formDataObj.append("email", formData.email);
      formDataObj.append("sex", formData.sex);
      formDataObj.append("TimeZone", formData.TimeZone);
      formDataObj.append("Address", formData.admin_other_details.Address);
      formDataObj.append("Designation", "Designation");
      formDataObj.append("date_of_birth", formData.date_of_birth);
      formDataObj.append("City", formData.City);
      formDataObj.append("Country", formData.Country);
      if (passwords.currentPassword && passwords.newPassword) {
        formDataObj.append("current_password", passwords.currentPassword);
        formDataObj.append("new_password", passwords.newPassword);
      }

      if (formData?.admin_other_details?.ProfilePhoto) {
        formDataObj.append(
          "ProfilePhoto",
          formData?.admin_other_details?.ProfilePhoto
        );
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_USER_DATA}${userEmail}/?user_id=${userId}`,
        formDataObj
      );
      if (response?.data === '{"user_update_status": true}') {
        toast.success("Changes saved successfully", {
          autoClose: 5000,
          theme: "colored",
          position: "top-center",
        });
        setPasswords(initialPasswordData);
        getUserData();
        setEditMode(false);
      }
    } catch (error) {
      console.error("Error updating data:", error);

      let errorMessage = "An error occurred";
      if (error.response && error.response.data) {
        if (typeof error.response.data === "object") {
          const errorFields = Object.values(error.response.data).flat();
          errorMessage = errorFields.join(", ");
        } else {
          errorMessage = error.response.data[0];
        }
      }

      toast.error(errorMessage, {
        autoClose: 5000,
        theme: "colored",
        position: "top-center",
      });
    }
  };
  const handleCancelChanges = () => {
    setFormData(originalFormData);
    setEditMode(false);
  };

  return (
    <div class="overflow-hidden">
    <div className="main-contanier mt-2 childadmin-content-scroll overflow-auto">
      {/* --------------------------------------Profile settings 1----------------------*/}
      <div className="row ">
        <div className="col-sm-6">
          <div className="row">
            <div className="col-sm-6">
              <div className="profile-pic-wrapper">
                <div className="pic-holder border-1 border-black">
                  <Image
                    id="profilePic"
                    className="pic rounded-circle object-fit-contain"
                    src={
                      typeof formData?.admin_other_details?.ProfilePhoto ===
                      "string"
                        ? formData?.admin_other_details?.ProfilePhoto
                        : formData?.admin_other_details?.ProfilePhoto instanceof
                          File
                        ? URL.createObjectURL(
                            formData?.admin_other_details?.ProfilePhoto
                          )
                        : ""
                    }
                    width={150}
                    height={150}
                    alt="Profile"
                  />
                  {editMode && (
                    <>
                      <input
                        className="uploadProfileInput"
                        type="file"
                        name="ProfilePhoto"
                        id="newProfilePhoto"
                        accept="image/jpeg, image/png,  image/heif, image/heic"
                        onChange={handleInputChange}
                        style={{ display: "none" }}
                      />

                      <label
                        htmlFor="newProfilePhoto"
                        className="upload-file-block"
                      >
                        <div className="text-center">
                          <div className="mb-2">
                            <AiFillCamera />
                          </div>
                          <div className="text-uppercase" />
                        </div>
                      </label>
                    </>
                  )}
                </div>
                <span className="mx-2 fs-6"></span>
              </div>
              {editMode && errors.profilePic && (
                <span className=" text-danger error-msg-medicalrec">
                  {errors.profilePic}
                </span>
              )}
            </div>
            <div className="col-sm-6">
              <div className="d-flex justify-content-end align-items-center p-5 mt-2">
                {!editMode ? (
                  <button
                    className="btn mx-2 profile-d-btn"
                    type="button"
                    onClick={handleEditButtonClick}
                  >
                    Edit Form
                  </button>
                ) : (
                  <>
                    <button
                      className="btn mx-2 profile-d-btn"
                      type="button"
                      onClick={handleCancelChanges}
                    >
                      Cancel
                    </button>
                    <button
                      className="btn mx-2 profile-sub-btn"
                      type="button"
                      onClick={handleSaveChangesButtonClick}
                    >
                      Save Changes
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
          {/* -----------------------------------Profile settings 2-------------------------*/}

          {/* <p className="p-s-subheading">Personal Details</p> */}
          <form>
            <div className="form-group form-back ">
              <div className="form-p-style">
                <div className="row">
                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Name
                      {/* <span style={{ color: "red" }}> *</span> */}
                    </label>
                    <input
                      type="text"
                      className="form-control custom-input-control mb-2"
                      id="name"
                      name="name"
                      readOnly={!editMode}
                      value={capitalizeFullName(formData.name)}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      DOB
                    </label>
                    <input
                      type="date"
                      className="form-control custom-input-control mb-2"
                      id="date_of_birth"
                      name="date_of_birth"
                      readOnly={!editMode}
                      value={formData?.date_of_birth}
                      onChange={handleInputChange}
                    />
                    {editMode && errors.date_of_birth && (
                      <span className="text-danger error-msg-medicalrec">
                        {errors.date_of_birth}
                      </span>
                    )}
                  </div>

                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Phone Number<span style={{ color: "red" }}> *</span>
                    </label>
                    <PhoneInput
                      // ref={(itiRef) => setIti(itiRef)}
                      id="floatingInputPhone"
                      className="input-form-modal-phone mb-3 col-12"
                      country={"in"}
                      // required="required"
                      name="phone"
                      // readOnly={!editMode}
                      disabled={!editMode}
                      value={formData.phone}
                      onChange={handlePhoneChange}
                      inputStyle={{
                        height: "30px",
                        border: "none",
                        background: "#F1F1F1",
                        opacity: 1,
                        fontSize: "12px",
                      }}
                      buttonStyle={{
                        border: "none",
                        borderRight: 0,
                        background: "#F1F1F1",
                      }}
                    />
                  </div>
                  <div className=" col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Email<span style={{ color: "red" }}> *</span>
                    </label>

                    <div className="input-group mb-3">
                      <input
                        type="text"
                        id="email"
                        name="email"
                        readOnly={true}
                        disabled={true}
                        className="form-control custom-input-control"
                        aria-describedby="button-addon2"
                        value={formData.email}
                      />
                      {editMode && errors.email && (
                        <span className="text-danger error-msg-medicalrec">
                          {errors.email}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      City
                      {/* <span style={{ color: "red" }}> *</span> */}
                    </label>
                    <input
                      type="text"
                      className="form-control custom-input-control mb-2"
                      id="City"
                      name="City"
                      readOnly={!editMode}
                      value={capitalizeFullName(formData.City)}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Country
                      {/* <span style={{ color: "red" }}> *</span> */}
                    </label>
                    <input
                      type="text"
                      className="form-control custom-input-control mb-2"
                      id="Country"
                      name="Country"
                      readOnly={!editMode}
                      value={capitalizeFullName(formData.Country)}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="row">
                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Gender<span style={{ color: "red" }}> *</span>
                    </label>
                    <Select
                      className="custom-input-control"
                      aria-label="Gender"
                      id="sex"
                      name="sex"
                      isDisabled={!editMode}
                      styles={customStyles}
                      value={{ value: formData.sex, label: formData.sex }}
                      onChange={(selectedOption) =>
                        handleInputChange({
                          target: {
                            name: "sex",
                            value: selectedOption.value,
                          },
                        })
                      }
                      options={genderOptions}
                    />
                    {editMode && errors.sex && (
                      <span className="text-danger error-msg-medicalrec">
                        {errors.sex}
                      </span>
                    )}
                  </div>
                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Timezone<span style={{ color: "red" }}> *</span>
                    </label>
                    <Select
                      className="custom-input-control mb-2"
                      aria-label="Timezone"
                      id="timezone"
                      name="TimeZone"
                      isDisabled={!editMode}
                      isSearchable
                      styles={customStyles}
                      value={{
                        value: formData.TimeZone,
                        label: formData.TimeZone,
                      }}
                      onChange={(selectedOption) =>
                        handleInputChange({
                          target: {
                            name: "TimeZone",
                            value: selectedOption.value,
                          },
                        })
                      }
                      options={timeZoneOptions}
                    />
                    {editMode && errors.timezone && (
                      <span className="text-danger error-msg-medicalrec">
                        {errors.timezone}
                      </span>
                    )}
                  </div>
                </div>

                <div className=" mb-2">
                  <label htmlFor="name" className="purple-text mb-2">
                    Address
                    {/* <span style={{ color: 'red' }}> *</span> */}
                  </label>
                  <textarea
                    className="form-control profile-address"
                    id="address"
                    name="Address"
                    readOnly={!editMode}
                    rows={1}
                    value={formData?.admin_other_details?.Address}
                    onChange={handleInputChange}
                  />

                  {editMode && errors.address && (
                    <span className="text-danger error-msg-medicalrec">
                      {errors.address}
                    </span>
                  )}
                </div>
              </div>
            </div>
            {/* -----------------------------------Change Password-------------------------*/}
            <PasswordReset
              role={"child-admin"}
              setErrors={setErrors}
              errors={errors}
              setPasswords={setPasswords}
              passwords={passwords}
              editMode={editMode}
            />
          </form>
        </div>

        {/* --------------------------------------Notification settings ----------------------*/}
        {/* {(isAdminChildAdmin ? isPermissible : true) && ( */}
        <ChildAdminGrantedPermissions
          isAdminChildAdmin={isAdminChildAdmin}
          isPermissible={isPermissible}
          userPermissions={userPermissions}
          notificationsList={notificationsList}
          fetchNotificationList={fetchNotificationList}
        />
        {/* )} */}
      </div>
    </div>
    </div>
  );
};

export default ChildAdminSettings;
