"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { MdOutlineEdit } from "react-icons/md";
import profileimg from "../../../public/images/dummy-avatar.jpg";
import "react-datepicker/dist/react-datepicker.css";
import "./childadminindividual.css";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { formatDateTime } from "../../utils/helperfunction";
import { IoMdRefresh } from "react-icons/io";
import Loading from "../Loading/PageLoading/Loading";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import Link from "next/link";
import NotificationPermission from "./ListOfNotification/NotificationPermission";
import Swal from "sweetalert2";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import parsePhoneNumberFromString from "libphonenumber-js";
import { toast } from "react-toastify";
import { Country, State, City } from "country-state-city";
import Select from "react-select";
import { SiTimescale } from "react-icons/si";
import moment from "moment-timezone";
import makeAnimated from "react-select/animated";

const animatedComponents = makeAnimated();

const customStyles = {
  control: (provided, state) => ({
    ...provided,
    border: state.isFocused ? "2px solid white" : "1px solid white",
    boxShadow: state.isFocused ? "0 0 3px white" : "white",
    borderColor: state.isFocused ? "white" : "white",
    width: "200px",
    ":hover": {
      borderColor: "white",
    },
  }),
  menu: (provided) => ({
    ...provided,
    backgroundColor: "white",
    width: "100%",
    borderRadius: "15px",
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused ? "#9426b2" : "white",
    color: state.isFocused ? "white" : "#333",
    ":hover": {
      backgroundColor: "#9426b2",
      color: "white",
    },
    borderRadius: "5px",
  }),
};
const updateURLBasedOnStatus = (approvalStatus) => {
  const currentURL = new URL(window.location.href);
  const currentStatus = currentURL.pathname.split("/").pop().split("?")[0];

  if (currentStatus !== approvalStatus) {
    currentURL.pathname = currentURL.pathname.replace(
      currentStatus,
      approvalStatus
    );
    window.history.replaceState(null, "", currentURL);
  }
};
const ChildMainProfile = ({ params }) => {
  const { userPermissions, isAdminChildAdmin, session } = useAdminContext();
  // const isPermissible = userPermissions?.includes("cu_app.manage_notif");
  const childAdminId =
    params && params.user_id.length > 0 && params.user_id[0]
      ? params.user_id[0]
      : "";
  const childAdminEmail =
    params && params.user_id.length > 0 && params.user_id[1]
      ? params.user_id[1]
      : "";

  const admin_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const [selectedImage, setSelectedImage] = useState(null);
  const [childAdminData, setChildAdminData] = useState({});
  const [notificationsList, setNotificationsList] = useState([]);
  const [permissionsList, setPermissionsList] = useState([]);
  const [isEditable, setIsEditable] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [endDate, setEndDate] = useState(null);
  const [showPicker, setShowPicker] = useState(false);
  const fileInputRef = useRef(null);
  const [iti, setIti] = useState(null);
  const [packagetimezones, setPackagetimezones] = useState([]);
  const [childAdminDesignationList, setChildAdminDesignationList] = useState(
    []
  );

  const fetchChildAdminData = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_USER_DATA}${childAdminEmail}/`
      );
      setChildAdminData(response?.data?.user_data);
      updateURLBasedOnStatus(response?.data?.user_data?.approval);
      const cityValue = response?.data?.user_data?.City;
      if (cityValue) {
        const [city, state] = cityValue.split(",");
        setChildAdminData((prevDetails) => ({
          ...prevDetails,
          City: city?.trim(),
          State: state?.trim(),
        }));
      }

      setLoading(false);
    } catch (e) {
      console.log(e);
    }
  }, [axiosAuth, childAdminEmail]);

  const fetchGrantedUpdatePermissions = useCallback(async () => {
    try {
      const permissionResponse = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_FETCH_EDIT_PERMISSIONS}${childAdminId}/?user_id=${admin_id}`
      );
      setPermissionsList(permissionResponse?.data);
    } catch (e) {
      console.log(e, "permission error");
    }
  }, [axiosAuth, childAdminId, admin_id]);

  const getListOfChildAdminDesignationList = useCallback(async () => {
    try {
      if (admin_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}all/?Category=Designation&user_id=${admin_id}`
        );
        setChildAdminDesignationList(response?.data);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, admin_id]);

  useEffect(() => {
    if ((childAdminId, childAdminEmail)) {
      fetchChildAdminData();
    }
    if (admin_id) {
      fetchGrantedUpdatePermissions();
      getListOfChildAdminDesignationList();
    }
  }, [
    childAdminEmail,
    childAdminId,
    admin_id,
    fetchChildAdminData,
    fetchGrantedUpdatePermissions,
    getListOfChildAdminDesignationList,
  ]);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setChildAdminData({
      ...childAdminData,
      ProfilePhoto: file,
    });
    setSelectedImage(URL.createObjectURL(file));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setChildAdminData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handlePhoneChange = (value, country, e, formattedValue) => {
    setChildAdminData((prevData) => ({ ...prevData, phone: formattedValue }));
  };

  const validatePhoneNumber = (phoneNumber) => {
    const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
    return parsedPhoneNumber && parsedPhoneNumber.isValid();
  };
  const handleSaveChanges = async () => {
    try {
      const isValidPhoneNumber = validatePhoneNumber(childAdminData.phone);
      if (!isValidPhoneNumber) {
        toast.error("Invalid phone number", {
          autoClose: 3000,
          position: "top-center",
        });

        return;
      }

      // Create a new FormData object
      const formData = new FormData();
      formData.append("name", childAdminData.name);
      formData.append(
        "patientCode",
        childAdminData?.patientCode ||
          `CU_CA_${childAdminData?.id}` ||
          "CU_CA_00"
      );
      formData.append("phone", childAdminData.phone);
      formData.append("email", childAdminData.email);
      formData.append("TimeZone", childAdminData.TimeZone);
      formData.append("sex", childAdminData.sex);
      formData.append("Address", childAdminData?.admin_other_details?.Address);
      formData.append(
        "Designation",
        childAdminData?.admin_other_details?.Designation
      );
      formData.append("ProfilePhoto", childAdminData.ProfilePhoto);
      const stateName = childAdminData.State?.name
        ? childAdminData.State?.name
        : childAdminData.State;
      const cityName = childAdminData.City?.name
        ? childAdminData.City?.name
        : childAdminData.City;
      formData.append("City", cityName + ", " + stateName);

      formData.append(
        "Country",
        childAdminData.Country?.name
          ? childAdminData.Country?.name
          : childAdminData.Country
      );

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_USER_DATA}${childAdminEmail}/?user_id=${admin_id}`,
        formData
      );

      if (response.status == 200) {
        toast.success("Child Admin Profile Updated Successfully!", {
          autoClose: 3000,
          position: "top-center",
        });
      }

      setChildAdminData(childAdminData);
      setIsEditable(false);
    } catch (error) {
      console.error("Error updating data:", error);
    }
  };

  const handleTakeControlClick = async (admin_status) => {
    if (admin_status === "pending" || admin_status === "Deactivated") {
      toast.error("To make change profile should be approved...", {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      setIsEditable(false);
      return;
    }
    if (!isEditable) {
      const shouldEdit = await Swal.fire({
        title: "Confirm Edit",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
        customClass: {
          confirmButton: "swal-confirm-button-class",
          cancelButton: "swal-cancel-button-class",
        },
        buttonsStyling: false,
      });

      if (!shouldEdit.isConfirmed) {
        return;
      }
    }
    setIsEditable((prevIsEditable) => !prevIsEditable);
  };

  const toggleNotification = async (notificationId, newStatus) => {
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_FETCH_NOTIFICATION_STATUS_CHILD_ADMIN}${notificationId}/?user_id=${userId}`,
        {
          ActiveStatus: newStatus,
        }
      );
      fetchNotificationList();
    } catch (error) {
      console.error("Error updating notification status:", error);
    }
  };


  // ============ TO FETCH TIMEZONE FROM MOMENT ==========
  useEffect(() => {
    const timezoneList = moment.tz.names();
    setPackagetimezones(timezoneList);
  }, []);

  // Use the timezone list directly to generate options
  const timezonepackage = packagetimezones.map((timezone) => ({
    value: timezone,
    label: timezone,
  }));

  const timehandleChange = (selectedOptions) => {
    const selectedTimeZone = selectedOptions.label;
    setChildAdminData({
      ...childAdminData,
      TimeZone: selectedTimeZone,
    });
  };

  return (
    <>
      {loading ? (
        <>
          <Loading />
        </>
      ) : (
        <>
          <div className="payment-back">
            <div className=" overflow-hidden">
              <div className="user-management-scroll-child-admin overflow-auto">
                <div className="row">
                  <div className="col-sm-12 col-size">
                    <div className="row">
                      <div className="col-sm-6">
                        <div className="row">
                          {/* <div className="col-sm-12">
                            <button
                              type="button"
                              className="btn back-button"
                              onClick={() => {
                                if (typeof window !== "undefined") {
                                  window.history.back();
                                }
                              }}
                            >
                              {"<"} Back
                            </button>
                          </div> */}
                          <div className="col-sm-4 child-Admin-Top-Section">
                            {/* ------------profile photo---------- */}
                            {selectedImage ||
                            childAdminData?.admin_other_details
                              ?.ProfilePhoto ? (
                              <Image
                                src={
                                  selectedImage ||
                                  childAdminData?.admin_other_details
                                    ?.ProfilePhoto
                                }
                                alt="profile pic"
                                className=" object-fit-cover rounded-circle "
                                width={100}
                                height={100}
                              />
                            ) : (
                              <Image
                                src={profileimg}
                                alt="profile pic"
                                className="object-fit-cover rounded-circle"
                                width={100}
                                height={100}
                              />
                            )}
                            {isEditable && (
                              <>
                                <input
                                  type="file"
                                  id="modalProfileImage"
                                  accept="image/*"
                                  name="profilePicture"
                                  onChange={handleImageChange}
                                  ref={fileInputRef}
                                  style={{ display: "none" }}
                                />
                                <MdOutlineEdit
                                  className="editprofile"
                                  cursor={"pointer"}
                                  onClick={() =>
                                    fileInputRef.current &&
                                    fileInputRef.current.click()
                                  }
                                />
                              </>
                            )}
                       
                          </div>
                          {/* ---------------------- Child admin profile input fields ---------------------*/}
                          <div className="col-sm-8">
                            <div className="row">
                              <div className="col take-control d-flex justify-content-between align-items-center">
                                <div>
                              {childAdminData?.approval === "Approved" ? (
                              <button
                                type="button"
                                className="btn button-green custom-font-size"
                              >
                                Approved
                              </button>
                            ) : childAdminData?.approval === "Rejected" ? (
                              <button
                                type="button"
                                className="btn btn-danger custom-font-size"
                              >
                                Rejected
                              </button>
                            ) : childAdminData?.approval === "Deactivated" ? (
                              <button
                                type="button"
                                className="btn btn-danger custom-font-size"
                              >
                                Deactivated
                              </button>
                            ) : childAdminData?.approval ===
                              "Approval_requested" ? (
                              <button
                                type="button"
                                className="btn btn-warning custom-font-size"
                              >
                                Requested
                              </button>
                            ) : childAdminData?.approval === "Deleted" ? (
                              <button
                                type="button"
                                className="btn btn-danger custom-font-size"
                              >
                                Deleted
                              </button>
                            ) : (
                              <button type="button" className="btn btn-warning">
                                Pending
                              </button>
                            )}
                            </div>
                                <button
                                  type="button"
                                  className="btn control-profile"
                                  onClick={() => {
                                    handleTakeControlClick(
                                      childAdminData?.approval
                                    );
                                  }}
                                >
                                  {isEditable
                                    ? "Cancel"
                                    : "Take control of profile"}
                                </button>
                              </div>
                              <div className="col-sm-12 mt-2">
                                <div className="profile-bg-control">
                                  <span className="profile-last-edited mx-2">
                                    <span className="last-login">
                                      {" "}
                                      Last Login :{" "}
                                    </span>
                                    {formatDateTime(
                                      childAdminData?.DateOfRegistration,
                                      childAdminData?.TimeZone
                                    )}
                                  </span>

                                  <div className="child-amdin-refresh">
                                    <IoMdRefresh
                                      onClick={fetchChildAdminData}
                                    />
                                    Refresh
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-sm-6">
                            <div className="form-group mt-2">
                              <label
                                htmlFor="name"
                                className="form-label-style"
                              >
                                Name
                              </label>
                              <input
                                type="text"
                                name="name"
                                className="form-control form-fildes-read custom-font-size"
                                value={childAdminData?.name || ""}
                                onChange={handleInputChange}
                                readOnly={!isEditable}
                              />
                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="form-group mt-2">
                              <label
                                htmlFor="patientcode"
                                className="form-label-style"
                              >
                                Patient Code
                              </label>
                              <input
                                type="text"
                                className="form-control form-fildes-read custom-font-size"
                                value={
                                  childAdminData?.patientCode ||
                                  `HU_CA_${childAdminData?.id}` ||
                                  "HU_CA_00"
                                }
                                readOnly={!isEditable}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="form-group mt-2">
                              <label
                                htmlFor="phone"
                                className="form-label-style"
                              >
                                Phone Number
                              </label>
                              <div>
                                <PhoneInput
                                  ref={(itiRef) => setIti(itiRef)}
                                  id="floatingInputPhone"
                                  className="input-form-modal-phone custom-font-size"
                                  country={"in"}
                                  required="required"
                                  name="phone"
                                  value={childAdminData?.phone || ""}
                                  onChange={handlePhoneChange}
                                  disabled={!isEditable}
                                  inputStyle={{
                                    border: "1px solid #e3e3e3",
                                  }}
                                  buttonStyle={{
                                    border: "1px solid #e3e3e3",
                                    borderRight: 0,
                                    background: "white",
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="form-group mt-2">
                              <label
                                htmlFor="email"
                                className="form-label-style"
                              >
                                Email ID
                              </label>
                              <input
                                readOnly={!isEditable}
                                name="email"
                                type="email"
                                className="form-control form-fildes-read custom-font-size"
                                value={childAdminData?.email || ""}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="form-group mt-2">
                              <label
                                htmlFor="Time"
                                className="form-label-style"
                              >
                                Designation
                              </label>
                              <div className="input-group input-group-border mb-2">
                                <select
                                  id="childAdminDesignation"
                                  className="form-control form-fildes-read custom-font-size"
                                  disabled={!isEditable}
                                  value={
                                    childAdminData?.admin_other_details
                                      ?.Designation
                                  }
                                  onChange={(e) => {
                                    const selectedValue = e.target.value;
                                    setChildAdminData((prevState) => ({
                                      ...prevState,
                                      admin_other_details: {
                                        ...prevState.admin_other_details,
                                        Designation: selectedValue,
                                      },
                                    }));
                                  }}
                                >
                                  <option>Select Designation</option>
                                  {childAdminDesignationList &&
                                    Array.isArray(childAdminDesignationList) &&
                                    childAdminDesignationList.map((option) => (
                                      <option
                                        key={option}
                                        value={option?.Content}
                                      >
                                        {option?.Content}
                                      </option>
                                    ))}
                                </select>
                              </div>
                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="form-group mt-2">
                              <label
                                htmlFor="gender"
                                className="form-label-style"
                              >
                                Gender
                              </label>

                              <select
                                disabled={!isEditable}
                                name="sex"
                                className="form-control form-fildes-read custom-font-size"
                                value={childAdminData?.sex || ""}
                                onChange={handleInputChange}
                              >
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                                <option value="Others">Others</option>
                              </select>
                            </div>
                          </div>

                          <div className="col-6">
                            <label htmlFor="name" className="">
                              Location
                            </label>
                            <div className="input-group input-group-border mb-2">
                              <span className="input-group-text label-col">
                                {" "}
                                Country
                              </span>

                              <input
                                type="text"
                                className="form-control custom-inpu-p"
                                id="Country"
                                name="Country"
                                readOnly={!isEditable}
                                value={
                                  childAdminData?.Country?.name
                                    ? childAdminData?.Country?.name
                                    : childAdminData?.Country
                                }
                                onChange={(e) =>
                                  setChildAdminData({
                                    ...childAdminData,
                                    Country: e.target.value,
                                    State: "No states",
                                    City: "No cities",
                                  })
                                }
                              />

                              {isEditable && (
                                <Select
                                  id="Country"
                                  options={Country.getAllCountries()}
                                  getOptionLabel={(options) => {
                                    return options["name"];
                                  }}
                                  getOptionValue={(options) => {
                                    return options["name"];
                                  }}
                                  value={childAdminData?.Country}
                                  onChange={(item) => {
                                    const selectedCountry = item;
                                    setChildAdminData({
                                      ...childAdminData,
                                      Country: selectedCountry,
                                      State: "No states",
                                      City: "No cities",
                                    });
                                  }}
                                  className="child-admin-country"
                                />
                              )}
                            </div>
                          </div>
                          <div className="col-6">
                            <div className="form-group ">
                              <label
                                htmlFor="Time"
                                className="form-label-style"
                              >
                                Timezone
                              </label>
                              <div className="input-group input-group-border mb-2">
                                <span className="input-group-text label-col">
                                  {" "}
                                  &nbsp;
                                  <SiTimescale size={23} color={"#9426B2"} />
                                  &nbsp; Timezone
                                </span>
                                <input
                                  type="text"
                                  className="form-control custom-input-l"
                                  id="timezone"
                                  name="TimeZone"
                                  placeholder="Enter TimeZone"
                                  required
                                  readOnly={!isEditable}
                                  value={childAdminData?.TimeZone || ""}
                                  onChange={(e) =>
                                    setChildAdminData({
                                      ...childAdminData,
                                      TimeZone: e.target.value,
                                    })
                                  }
                                />
                                {isEditable ? (
                                  <>
                                    <Select
                                      id="TimeZone"
                                      name="TimeZone"
                                      required
                                      closeMenuOnSelect={false}
                                      components={animatedComponents}
                                      isDisabled={!isEditable}
                                      onChange={timehandleChange}
                                      options={timezonepackage}
                                      styles={customStyles}
                                      placeholder="Select timezone"
                                    />
                                  </>
                                ) : null}
                              </div>
                            </div>
                          </div>

                          <div className="col-sm-6">
                            <div className="input-group input-group-border mb-2">
                              <span className="input-group-text label-col">
                                {" "}
                                State
                              </span>

                              <input
                                type="text"
                                className="form-control custom-inpu-p"
                                id="Country"
                                name="State"
                                readOnly={!isEditable}
                                value={
                                  childAdminData?.State?.name
                                    ? childAdminData?.State?.name
                                    : childAdminData?.State
                                }
                                onChange={(e) =>
                                  setChildAdminData({
                                    ...childAdminData,
                                    State: e.target.value,
                                  })
                                }
                              />

                              {isEditable && (
                                <Select
                                  id="country"
                                  options={State?.getStatesOfCountry(
                                    childAdminData?.Country?.isoCode
                                  )}
                                  getOptionLabel={(options) => {
                                    return options["name"];
                                  }}
                                  getOptionValue={(options) => {
                                    return options["name"];
                                  }}
                                  value={childAdminData?.State}
                                  onChange={(item) => {
                                    const selectedState = item;
                                    setChildAdminData({
                                      ...childAdminData,
                                      State: selectedState,
                                    });
                                  }}
                                  className="child-admin-state"
                                />
                              )}
                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="input-group input-group-border mb-1">
                              <span className="input-group-text label-col">
                                {" "}
                                City
                              </span>

                              <input
                                type="text"
                                className="form-control custom-inpu-p"
                                id="Country"
                                name="City"
                                readOnly={!isEditable}
                                value={
                                  childAdminData?.City?.name
                                    ? childAdminData?.City?.name
                                    : childAdminData?.City
                                }
                                onChange={(e) =>
                                  setChildAdminData({
                                    ...childAdminData,
                                    City: e.target.value,
                                  })
                                }
                              />

                              {isEditable && (
                                <Select
                                  options={City.getCitiesOfState(
                                    childAdminData?.State?.countryCode,
                                    childAdminData?.State?.isoCode
                                  )}
                                  getOptionLabel={(options) => {
                                    return options["name"];
                                  }}
                                  getOptionValue={(options) => {
                                    return options["name"];
                                  }}
                                  value={childAdminData?.City}
                                  onChange={(item) => {
                                    const selectedCity = item;
                                    setChildAdminData({
                                      ...childAdminData,
                                      City: selectedCity,
                                    });
                                  }}
                                  className="child-admin-city"
                                />
                              )}
                            </div>
                          </div>

                          <div className="form-group mt-2">
                            <label
                              htmlFor="address"
                              className="form-label-style"
                            >
                              Address
                            </label>
                            <textarea
                              className="form-control text-form-fileds"
                              id="address"
                              name="address"
                              rows="5"
                              value={
                                childAdminData?.admin_other_details?.Address ||
                                ""
                              }
                              onChange={(e) =>
                                setChildAdminData({
                                  ...childAdminData,
                                  admin_other_details: {
                                    ...childAdminData.admin_other_details,
                                    Address: e.target.value,
                                  },
                                })
                              }
                            ></textarea>
                          </div>
                          <div className="col-sm-12 mt-3">
                            {isEditable ? (
                              <>
                                <button
                                  type="button"
                                  className="btn btn-save-changes"
                                  onClick={handleSaveChanges}
                                >
                                  Save changes
                                </button>
                              </>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                        <div className=" mb-5">
                          <div className="form-group form-back">
                            <div className="row">
                              <div className="col-sm-12">
                                <label
                                  htmlFor="name"
                                  className="purple-text-admin-profile mb-2"
                                >
                                  Current Password
                                  <span style={{ color: "red" }}> *</span>
                                </label>
                                <input
                                  type="text"
                                  className="form-control custom-input-control mb-2"
                                  id="pwd"
                                  name="password"
                                  readOnly
                                />
                              </div>
                            </div>
                            <div className="row">
                              <div className="col-sm-12">
                                <label
                                  htmlFor="name"
                                  className="purple-text-admin-profile mb-2"
                                >
                                  New Password
                                  <span style={{ color: "red" }}> *</span>
                                </label>
                                <input
                                  type="text"
                                  className="form-control custom-input-control mb-3"
                                  id="pwd2"
                                  name="password"
                                  readOnly
                                />
                              </div>
                            </div>
                            <div className="row">
                              <div className="col-sm-12">
                                <label
                                  htmlFor="name"
                                  className="purple-text-admin-profile mb-2"
                                >
                                  Re-enter New Password
                                  <span style={{ color: "red" }}> *</span>
                                </label>
                                <input
                                  type="text"
                                  className="form-control custom-input-control mb-2"
                                  id="pwd3"
                                  name="password"
                                  readOnly
                                />
                              </div>
                            </div>
                          </div>
                          <div className="d-flex justify-content-between align-items-center">
                            <Link
                              href="/"
                              className="forgot-pwd-link-prof-sett "
                            >
                              Forgot Password ?
                            </Link>
                            <button
                              className="profile-submit-btn-admin-sett mt-2"
                              type="button"
                            >
                              Submit
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="col-sm-6">
                        <div className="list-of-notiification ">
                          <NotificationPermission
                            permissionsList={permissionsList}
                            childAdminId={childAdminId}
                            admin_id={admin_id}
                            fetchGrantedUpdatePermissions={
                              fetchGrantedUpdatePermissions
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};
export default ChildMainProfile;
