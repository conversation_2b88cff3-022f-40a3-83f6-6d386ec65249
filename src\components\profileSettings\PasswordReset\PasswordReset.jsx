import React, { useState } from "react";
import { <PERSON><PERSON>utlineEye, AiOutlineEyeInvisible } from "react-icons/ai";

const PasswordReset = ({
  passwords,
  setPasswords,
  errors,
  setErrors,
  editMode,
}) => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const toggleShowCurrentPassword = () => {
    setShowCurrentPassword((prev) => !prev);
  };

  const toggleShowNewPassword = () => {
    setShowNewPassword((prev) => !prev);
  };

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword((prev) => !prev);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setPasswords((prevPasswords) => ({
      ...prevPasswords,
      [name]: value,
    }));

    if (name === "newPassword" || name === "confirmPassword") {
      if (
        passwords.newPassword !== passwords.confirmPassword &&
        passwords.confirmPassword?.length > 0
      ) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          newPassword: "New password and confirm password must match",
          confirmPassword: "New password and confirm password must match",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          newPassword: null,
          confirmPassword: null,
        }));
      }
    }

    if (
      name === "currentPassword" &&
      passwords.newPassword === value &&
      passwords.newPassword?.length > 0
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        newPassword: "New password must be different from current password",
      }));
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        newPassword: null,
      }));
    }

    if (
      name === "confirmPassword" &&
      value &&
      value !== passwords.newPassword
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        confirmPassword: "Passwords do not match",
      }));
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        confirmPassword: null,
      }));
    }
  };

  return (
    <div>
      <div className="form-group form-back">
        <div className="row">
          <div className="col-sm-12">
            <label htmlFor="pwd" className="purple-text mb-2">
              Current Password<span style={{ color: "red" }}> *</span>
            </label>
            <div className="input-group mb-2">
              <input
                type={showCurrentPassword ? "text" : "password"}
                className="form-control custom-input-control"
                id="pwd"
                readOnly={!editMode}
                name="currentPassword"
                value={passwords.currentPassword}
                onChange={handleChange}
              />
              <button
                className="password-eye"
                type="button"
                onClick={toggleShowCurrentPassword}
              >
                {showCurrentPassword ? (
                  <AiOutlineEyeInvisible size={25} color="#8107D1" />
                ) : (
                  <AiOutlineEye size={25} color="#8107D1" />
                )}
              </button>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-12">
            <label htmlFor="pwd2" className="purple-text mb-2">
              New Password<span style={{ color: "red" }}> *</span>
            </label>
            <div className="input-group mb-2">
              <input
                type={showNewPassword ? "text" : "password"}
                className="form-control custom-input-control"
                id="pwd2"
                name="newPassword"
                readOnly={!editMode}
                value={passwords.newPassword}
                onChange={handleChange}
              />
              <button
                className="password-eye"
                type="button"
                onClick={toggleShowNewPassword}
              >
                {showNewPassword ? (
                  <AiOutlineEyeInvisible size={25} color="#8107D1" />
                ) : (
                  <AiOutlineEye size={25} color="#8107D1" />
                )}
              </button>
            </div>
            {errors && errors.newPassword && (
              <div className="text-danger error-msg-medicalrec">
                {errors.newPassword}
              </div>
            )}
          </div>
        </div>
        <div className="row">
          <div className="col-sm-12">
            <label htmlFor="pwd3" className="purple-text mb-2">
              Re-enter New Password
              <span style={{ color: "red" }}> *</span>
            </label>
            <div className="input-group mb-2">
              <input
                type={showConfirmPassword ? "text" : "password"}
                className="form-control custom-input-control"
                id="pwd3"
                readOnly={!editMode}
                name="confirmPassword"
                value={passwords.confirmPassword}
                onChange={handleChange}
              />
              <button
                className="password-eye"
                type="button"
                onClick={toggleShowConfirmPassword}
              >
                {showConfirmPassword ? (
                  <AiOutlineEyeInvisible size={25} color="#8107D1" />
                ) : (
                  <AiOutlineEye size={25} color="#8107D1" />
                )}
              </button>
            </div>
            {errors && errors.confirmPassword && (
              <div className="text-danger error-msg-medicalrec">
                {errors.confirmPassword}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordReset;
