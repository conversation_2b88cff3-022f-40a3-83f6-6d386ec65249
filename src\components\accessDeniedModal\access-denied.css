/* .account_not_approved_modal {
  width: 100%;
  margin: auto;
} */

.not_approved_title {
    text-align: center;
    color: #9426b2;
    font-weight: 520;
  }
  
  .not_approved_body {
    text-align: center;
    font-size: 110%;
    color: #616161;
    margin-bottom: 5%;
  }
  .not_approved_body > p {
    text-align: center;
  }
  .push_to_profile_setting_button {
    display: flex;
    justify-content: center;
    margin-bottom: 1%;
  }
  .push_to_profile_setting_button > button {
    width: 235px;
    height: 44px;
    background: var(--unnamed-color-9426b2) 0% 0% no-repeat padding-box;
    border: 1px solid;
    background: #9426b2 0% 0% no-repeat padding-box;
    box-shadow: inset 0px 3px 6px #********;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    opacity: 1;
    color: white;
  }
  .account_request_head {
    text-align: center;
    color: #9426b2;
    font-weight: 520;
  }
  .account_request_subject {
    background: 0% 0% no-repeat padding-box;
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 2px solid #eeeeee;
    border-radius: 5px;
    opacity: 1;
    width: 100%;
    min-height: 50px;
    height: auto;
  }
  .account_request_text_area {
    background: 0% 0% no-repeat padding-box;
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 2px solid #eeeeee;
    border-radius: 5px;
    opacity: 1;
    width: 100%;
    min-height: 100px;
    height: auto;
  }
  textarea#account_request_subject,
  input#account_request_text_area {
    border: 0;
    border-radius: 0;
    box-shadow: none;
  }
  
  .account_request_cancel_button {
    width: 150px;
    height: 40px;
    background: 0% 0% no-repeat padding-box;
    border: 1px solid var(--unnamed-color-e3e3e3);
    background: #b50000 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #********;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    opacity: 1;
    color: white;
  }
  
  .account_request_submit_button {
    width: 150px;
    height: 40px;
    background: 0% 0% no-repeat padding-box;
    border: 1px solid var(--unnamed-color-e3e3e3);
    background: #9426b2 0% 0% no-repeat padding-box;
    box-shadow: inset 0px 3px 6px #********;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    opacity: 1;
    color: white;
  }
  
  .push_to_profile_setting_button_deactivation {
    display: flex;
    justify-content: center;
  }
  .push_to_profile_setting_button_deactivation > button {
    width: 235px;
    height: 44px;
    background: var(--unnamed-color-9426b2) 0% 0% no-repeat padding-box;
    border: 1px solid;
    background: red 0% 0% no-repeat padding-box;
    box-shadow: inset 0px 3px 6px #********;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    opacity: 1;
    color: white;
  }