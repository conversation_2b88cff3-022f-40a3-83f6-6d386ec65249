/* eslint-disable @next/next/no-img-element */
"use client";
import React, { useState, useEffect, useCallback } from "react";
import { debounce } from "lodash";
import { FaFile } from "react-icons/fa";
import { IoIosArrowBack } from "react-icons/io";
import { useParams, usePathname, useSearchParams } from "next/navigation";
import { formatDate, timeDifference } from "./db.js";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import Loading from "../Loading/PageLoading/Loading.js";
import ArticleModal from "./ArticleModal/ArticleModal.jsx";
import TestimonialModal from "./testimonialModal/TestimonialModal.jsx";
import PodcastApprovals from "./PodcastApprovals.jsx";
import FeedbackApprovals from "./FeedbackApprovals.jsx";
import TestimonialApproval from "./TestimonialApproval.jsx";
import ConsentFormApprovals from "./ConsentFormApprovals.jsx";
import ReviewsApproval from "./ReviewsApproval.jsx";
import ReviewModal from "./reviewsModal/ReviewModal.jsx";
import BlogsApproval from "./BlogsApproval.jsx";
import VideoApprovals from "./VideoApprovals.jsx";
import FeedbackModal from "./feedbackModal/FeedbackModal.jsx";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import profile from "../../../public/images/profile.png";
import Image from "next/image";
import { Button, Modal, Placeholder } from "react-bootstrap";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth.js";
import { useAdminContext } from "../../Context/AdminContext/AdminContext.js";
import PodcastModal from "./podcastModal/PodcastModal.jsx";
import DoctorConsentFormModal from "./doctorConsentModal/DoctorsConsentFormModal.jsx";
import ReactPlayer from "react-player";
import { useRouter } from "next/navigation";

import CU_logoImage from "../../../public/images/adminbglogo.png";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

const Approvals = ({
  testimonials,
  testimonialsLoading,
  noDtaError,
  handleTestmonialApprovals,
  showTestimonialModal,
  setShowTestimonialModal,
  UnApprovedFeedback,
  unApprovedFeedbackLoading,
  doctorsConsent,
  doctorsData,
  doctorConsentLoading,
  fetchDoctorConsent,
  fetchFeedback,
  setTestimonialsLoading,
  setDoctorConsentLoading,
}) => {
  const [videoRejectReason, setVideoRejectReason] = useState("");
  const searchParams = useSearchParams();
  const [inputFields, setInputFields] = useState([""]);
  const [loading, setLoading] = useState(true);
  const [articles, setArticles] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [podcasts, setPodcasts] = useState([]);
  const [singleArticle, setSingleArticles] = useState([]);
  const [singleReview, setSingleReview] = useState([]);
  const [singleFeedback, setSingleFeedback] = useState([]);
  const [singleTestimonial, setSingleTestimonial] = useState([]);
  const [singlePodcast, setSinglePodcast] = useState([]);
  const [showArticleModal, setShowArticleModal] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showPodcastModal, setshowPodcastModal] = useState(false);
  const [showConsentFormModal, setshowConsentFormModal] = useState(false);
  const [articleLoading, setArticleLoading] = useState(true);
  const pathname = usePathname();
  const initialTab = searchParams.get("tab");
  const initialSubtab = searchParams.get("subtab");
  const [activeSubTab, setActiveSubTab] = useState(
    initialSubtab || "introVideo"
  );

  const { session } = useAdminContext();
  const router = useRouter();

  const params = useParams();
  const doctor_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";
  const doctor_email1 =
    params && params?.user_id?.length > 0 && params?.user_id[1]
      ? params?.user_id[1]
      : "";

  const doctor_email = decodeURIComponent(doctor_email1);
  const admin_id = session?.user?.id;

  const axiosAuth = useAxiosAuth();

  const fetchArticles = useCallback(async () => {
    try {
      setLoading(true);

      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERTS_BLOG}${doctor_id}/0/`
      );

      setArticles(response?.data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setArticleLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  const fetchReviews = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERTS_EXTERNAL_REVIEWS}${doctor_id}/?user_id=${admin_id}`
      );
      setReviews(response?.data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }, [admin_id, axiosAuth, doctor_id]);

  const fetchPodcasts = useCallback(async () => {
    try {
      setLoading(true);
      const statusParam = "?status=1";
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERT_PODCASTS}${doctor_id}${statusParam}&per_page=20`
      );
      setPodcasts(response?.data);
      
    } catch (error) {
      console.log("Error in getting the podcasts", error);
    } finally {
      setLoading(false);
    }
  }, [doctor_id, axiosAuth]);

  useEffect(() => {
    if (doctor_id) {
      fetchArticles();
      fetchPodcasts();
      fetchReviews();
    }
    // if (doctor_email) {
    //   fetchDoctorDetails(doctor_email);
    // }
  }, [
    doctor_email,
    doctor_id,
    fetchArticles,
    // fetchDoctorDetails,
    fetchPodcasts,
    fetchReviews,
  ]);

  const handleAddMore = () => {
    setInputFields([...inputFields, ""]);
  };

  // const handleRejectVideo = (event) => {
  //   setVideoRejectReason(event.target.value);
  // };

  const calculateTextAreaHeight = () => {
    const lineHeight = 100;
    const rows = Math.ceil(videoRejectReason?.split("\n").length);
    return `${lineHeight * rows}px`;
  };

  const handleArticleModal = (item) => {
    setSingleArticles(item);
    setShowArticleModal(true);
  };
  const handleReviewModal = (item) => {
    setSingleReview(item);
    setShowReviewModal(true);
  };

  const handleTestimonialModal = (item) => {
    setSingleTestimonial(item);
    setShowTestimonialModal(true);
  };
  const handleConsentFormModal = () => {
    setshowConsentFormModal(true);

    // setShowTestimonialModal(true);
  };

  const handlePodcastApprovals = (item) => {
    setSinglePodcast(item);
    setshowPodcastModal(true);
  };

  const handleArticleApprovals = async (
    status,
    blog_id,
    blog_title,
    reason
  ) => {
    let blog_status;

    if (status === "reject") {
      blog_status = 2;
    } else if (status === "approve") {
      blog_status = 1;
    }
    try {
      let dataToSend = { BlogStatus: blog_status };

      // Include reason only if status is reject
      if (status === "reject") {
        dataToSend.Reason = reason;
      }
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CU_DOCTOR_ARTICLE_STATUS_BY_ADMIN}${blog_id}/?user_id=${admin_id}`,
        dataToSend
      );
      if (response.data?.message?.BlogStatus === 1) {
        toast.success(`${blog_title} Approved Successfully`);
        setShowArticleModal(false);
      } else if (response.data?.message?.BlogStatus === 2) {
        toast.success(`${blog_title} Rejected Successfully`);
        setShowArticleModal(false);
      }
      fetchArticles();
    } catch (error) {
      console.error(error);
    }
  };

  const handleReviewApprovals = async (status, blog_id, blog_title, reason) => {
    let blog_status;

    if (status === "reject") {
      blog_status = 3;
    } else if (status === "approve") {
      blog_status = 2;
    }
    try {
      let dataToSend = { ReviewStatus: blog_status };

      // Include reason only if status is reject
      if (status === "reject") {
        dataToSend.Reason = reason;
      }
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_GET_EXPERTS_EXTERNAL_REVIEW_APPROVAL}${blog_id}/?user_id=${admin_id}`,
        dataToSend
      );
      if (response.data?.message?.ReviewStatus === 2) {
        toast.success(`Review Approved Successfully`);

        setShowReviewModal(false);
      } else if (response.data?.message?.ReviewStatus === 3) {
        toast.success(`Review Rejected Successfully`);
        setShowReviewModal(false);
      }
      fetchReviews();
    } catch (error) {
      console.error(error);
    }
  };

  const debouncedHandleArticleApprovals = debounce(
    handleArticleApprovals,
    1000
  );
  const debouncedHandleReviewApprovals = debounce(handleReviewApprovals, 1000);

  // const handleVideoRejection = () => {
  //   debouncedHandleIntroVideoStatus("reject");
  // };

  // const handleTabChange = (tab) => {
  //   const updatedPath = `${pathname}?tab=${tab}`;
  //   router.replace(updatedPath, { shallow: true });
  //   setActiveTab(tab);
  // };
  const handleSubtabChange = (subtab) => {
    const updatedPath = `${pathname}?tab=${initialTab}&subtab=${subtab}`;
    router.replace(updatedPath, { shallow: true });
    setActiveSubTab(subtab);
  };

  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <>
          <div className="allApproval-container">
            <p className="d-inline-flex gap-1 buttons-row mb-0 mb-0">
              <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "introVideo" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("introVideo")}
              >
                Videos Upload
              </button>
              <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "approvals" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("approvals")}
              >
                Testimonials
              </button>
              <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "consent" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("consent")}
              >
                Consent form
              </button>
              <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "feedback" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("feedback")}
              >
                Feedback
              </button>
              <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "reviews" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("reviews")}
              >
                Reviews
              </button>
              <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "blogs" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("blogs")}
              >
                Blogs
              </button>
              {/* <button
                className={`btn approvals-grey-btn ${
                  activeSubTab === "podcasts" ? "activeApprovalsTab" : ""
                }`}
                onClick={() => handleSubtabChange("podcasts")}
              >
                Podcast
              </button> */}
            </p>

            {activeSubTab === "introVideo" && <VideoApprovals />}
            {activeSubTab === "approvals" && (
              <TestimonialApproval
                testimonials={testimonials}
                testimonialsLoading={testimonialsLoading}
                handleTestimonialModal={handleTestimonialModal}
                noDtaError={noDtaError}
                renderPlaceholders={renderPlaceholders}
              />
            )}
            {activeSubTab === "consent" && (
              <ConsentFormApprovals
                doctorConsentLoading={doctorConsentLoading}
                doctorsConsent={doctorsConsent}
                renderPlaceholders={renderPlaceholders}
                setshowConsentFormModal={setshowConsentFormModal}
                doctorsData={doctorsData}
                noDtaError={noDtaError}
                handleConsentFormModal={handleConsentFormModal}
              />
            )}
            {activeSubTab === "feedback" && (
              <FeedbackApprovals
                renderPlaceholders={renderPlaceholders}
                unApprovedFeedbackLoading={unApprovedFeedbackLoading}
                UnApprovedFeedback={UnApprovedFeedback}
                singleFeedback={singleFeedback}
                noDtaError={noDtaError}
                fetchFeedback={fetchFeedback}
              />
            )}
            {activeSubTab === "reviews" && (
              <ReviewsApproval
                unApprovedFeedbackLoading={unApprovedFeedbackLoading}
                renderPlaceholders={renderPlaceholders}
                reviews={reviews}
                handleReviewModal={handleReviewModal}
                noDtaError={noDtaError}
              />
            )}
            {activeSubTab === "blogs" && (
              <BlogsApproval
                articleLoading={articleLoading}
                articles={articles}
                handleArticleModal={handleArticleModal}
                renderPlaceholders={renderPlaceholders}
                noDtaError={noDtaError}
              />
            )}
            {/* {activeSubTab === "podcasts" && (
              <PodcastApprovals
                loading={loading}
                podcasts={podcasts}
                renderPlaceholders={renderPlaceholders}
                noDtaError={noDtaError}
                fetchPodcasts={fetchPodcasts}
              />
            )} */}
          </div>

          {showConsentFormModal && (
            <DoctorConsentFormModal
              doctorsData={doctorsData}
              showConsentFormModal={showConsentFormModal}
              setDoctorConsentLoading={setDoctorConsentLoading}
              setshowConsentFormModal={setshowConsentFormModal}
              doctorsConsent={doctorsConsent}
              admin_id={admin_id}
              fetchDoctorConsent={fetchDoctorConsent}
              showButtons={true}
              handleConsentFormModal={handleConsentFormModal}
            />
          )}
          {showPodcastModal && (
            <PodcastModal
              showPodcastModal={showPodcastModal}
              setshowPodcastModal={setshowPodcastModal}
              singlePodcast={singlePodcast}
              doctorsData={doctorsData}
              handlePodcastApprovals={handlePodcastApprovals}
              showButtons={true}
              fetchPodcasts={fetchPodcasts}
            />
          )}
          {showReviewModal && (
            <ReviewModal
              showReviewModal={showReviewModal}
              setShowReviewModal={setShowReviewModal}
              singleReview={singleReview}
              debouncedHandleArticleApprovals={debouncedHandleReviewApprovals}
              showButtons={true}
              fetchReviews={fetchReviews}
            />
          )}

          {showArticleModal && (
            <ArticleModal
              showArticleModal={showArticleModal}
              setShowArticleModal={setShowArticleModal}
              singleArticle={singleArticle}
              debouncedHandleArticleApprovals={debouncedHandleArticleApprovals}
              showButtons={true}
              fetchArticles={fetchArticles}
            />
          )}
          {showTestimonialModal && (
            <TestimonialModal
              showTestimonialModal={showTestimonialModal}
              setShowTestimonialModal={setShowTestimonialModal}
              singleTestimonial={singleTestimonial}
              handleTestmonialApprovals={handleTestmonialApprovals}
              testimonialsLoading={testimonialsLoading}
              showButtons={true}
            />
          )}
        </>
      )}
    </>
  );
};

export default Approvals;
