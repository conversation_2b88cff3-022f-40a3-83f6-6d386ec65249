"use client";

import { useEffect, useRef, useState } from "react";

const CountdownTimer = ({ onResend, start = 60, startText, cbText }) => {
  const timerInterval = useRef(null);

  const [startTime, setStartTime] = useState(start);
  const [showResend, setShowResend] = useState(false);

  // Function for the countdown timer
  function showTimer() {
    setStartTime((prev) => {
      if (prev <= 0) {
        setShowResend(true);
        clearInterval(timerInterval.current);
        return 0; // Ensure it stays at 0
      }
      return prev - 1;
    });
  }

  // Function to restart the timer, hide the resend button, and call onResend
  function handleShowResend() {
    setShowResend(false);
    setStartTime(start); // Reset the timer
    clearInterval(timerInterval.current);
    timerInterval.current = setInterval(showTimer, 1000);
    onResend();
  }

  useEffect(() => {
    setStartTime(start); // Initialize the timer with the provided start value
    timerInterval.current = setInterval(showTimer, 1000);
    return () => {
      clearInterval(timerInterval.current);
    };
  }, [start]); // Depend on `start` to reset properly when it changes

  return (
    <>
      {!showResend && (
        <div className="d-flex align-items-center">
          <span className="me-2">{startText}</span>
          <span className="text-secondary">
            {`00:${startTime < 10 ? "0" + startTime : startTime}`}
          </span>
        </div>
      )}
      {showResend && (
        <span
          onClick={handleShowResend}
          className="text-primary text-decoration-underline"
          style={{ cursor: "pointer" }}
        >
          {cbText}
        </span>
      )}
    </>
  );
};

export default CountdownTimer;
