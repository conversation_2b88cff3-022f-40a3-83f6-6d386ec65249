import React, { useCallback, useEffect } from "react";
import {
  calculateMeetingDuration,
  extractTimeFromDateString,
  formatCustomDate,
} from "../../utils/helperfunction";
import nodataFound from "../../../public/images/nodata.png";
import { Placeholder } from "react-bootstrap";
import Link from "next/link";
import Image from "next/image";
import "react-datepicker/dist/react-datepicker.css";
import { debounce } from "lodash";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import AppointmentFilter from "./AppointmentFilter";
import { IoIosInformationCircle } from "react-icons/io";
import NoDataFound from "../noDataFound/NoDataFound";

const AppointmentListPlaceholder = ({}) => (
  <>
    {[...Array(5)].map((_, index) => (
      <tr key={index}>
        <td className="custom-td">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              style={{ height: "30px", borderRadius: "5px" }}
            />
          </Placeholder>
        </td>
        <td className="custom-td-purple">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              style={{ height: "30px", borderRadius: "5px" }}
            />
          </Placeholder>
        </td>
        <td className="custom-td-purple">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              style={{ height: "30px", borderRadius: "5px" }}
            />
          </Placeholder>
        </td>
        <td className="custom-td">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              style={{ height: "30px", borderRadius: "5px" }}
            />
          </Placeholder>
        </td>
        <td className="text-center">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              style={{ height: "30px", borderRadius: "5px" }}
            />
          </Placeholder>
        </td>
      </tr>
    ))}
  </>
);

const AppointmenstInformation = ({
  recent_appointments,
  setSearchQuery,
  searchQuery,
  loading,
}) => {
  const debouncedSearch = useCallback(
    debounce((query) => {
      setSearchQuery(query);
      // fetchData(query);
    }, 300),
    []
  );

  useEffect(() => {
    if (searchQuery) {
      debouncedSearch(searchQuery);
    }
  }, [searchQuery, debouncedSearch]);
  return (
    <div className="dashboard-content-border">
      <AppointmentFilter />

      <div className="row">
        <div className="col-sm-12">
          <div className="d-flex">
            <p className="home-heading">Recent Appointments</p>
            &nbsp;
            <span>
              <a
                data-tooltip-id="my-tooltip"
                data-tooltip-content="Last 10 Appointments"
              >
                <IoIosInformationCircle />
              </a>
              <Tooltip id="my-tooltip" />
            </span>
          </div>
          <div className="table-wrapper">
            <table className="table">
              <thead className="app-head">
                <tr>
                  <th className="th-custom">Date</th>
                  <th className="th-custom">Patient</th>
                  <th className="th-custom">Expert</th>
                  <th className="th-custom">Duration</th>
                  <th className="th-custom">Call Summary</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <AppointmentListPlaceholder />
                ) : recent_appointments?.recent_appointments?.length > 0 ? (
                  recent_appointments?.recent_appointments
                    ?.slice()
                    ?.reverse()
                    ?.map((appointment, index) => (
                      <tr key={index}>
                        <td className="custom-td">
                          {formatCustomDate(appointment?.appointmenent_date)}
                        </td>
                        <td className="custom-td-purple text-capitalize">
                          {/* {appointment?.patient_data?.name} */}
                          <Link
                            className="purple-patient-name"
                            href={`/usermanagement/patients/${appointment?.patient_data?.id}/${appointment?.patient_data?.email}/${appointment?.patient_data?.approval}/${appointment?.patient_data?.name}`}
                          >
                            {appointment?.patient_data?.name || "Unknow User"}
                          </Link>
                        </td>
                        <td className="custom-td-purple">
                          <Link
                            type="button"
                            className="purple-patient-name"
                            href={`/usermanagement/experts/experts-doctor/${appointment?.doctor_data?.id}/${appointment?.doctor_data?.email}/${appointment?.doctor_data?.approval}`}
                          >
                            {appointment?.doctor_data?.name || "Unknow User"}
                          </Link>
                        </td>
                        <td className="custom-td">
                          {calculateMeetingDuration(
                            appointment?.meeting_start_time,
                            appointment?.meeting_end_time
                          )}
                        </td>
                        <td className="text-center ">
                          <p className="view-prescription">
                            {appointment?.prescription_data
                              ?.prescription_details?.length > 0
                              ? "Prescribed"
                              : "Not Prescribed"}
                          </p>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan="4">
                      <div className="fs-4 d-flex flex-column align-items-center justify-content-center no-Patient-tickets-found ">
                        <span className="no-expert-approval-record text-center">
                          <NoDataFound />
                        </span>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmenstInformation;
