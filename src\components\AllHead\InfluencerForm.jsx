import React from "react";
import { FaTwitter } from "react-icons/fa";
import { Row, Col, Form } from 'react-bootstrap';
import { RiInstagramFill } from "react-icons/ri";
import { FaFacebookF } from "react-icons/fa";
import { FaLinkedinIn } from "react-icons/fa";
import { MdOutlineAccessTime } from "react-icons/md";
const InfluencerForm = () => {
    return (
        <>
            {/*---------------------------------------- Influencer Form------------------------------------ */}
            <Form>
                <Row>
                    <Col md={12} >
                        <Form.Group controlId="name">
                            <Form.Label className='label-edit mt-1'>Name</Form.Label>
                            {/* Add patient-specific form fields here */}
                            <Form.Control type="text" className='input-form-modal' />
                        </Form.Group>
                    </Col>
                    <Col md={6} >
                        <Form.Group controlId="phoneNo">
                            <Form.Label className='label-edit'>Phone Number</Form.Label>
                            <Form.Control type="text" className='input-form-modal' />
                        </Form.Group>
                    </Col>
                    <Col md={6}>
                        <Form.Group controlId="Email">
                            <Form.Label className='label-edit'>Email</Form.Label>
                            <Form.Control type="email" className='input-form-modal' />
                        </Form.Group>
                    </Col>
                    <Col md={6} >
                        <Form.Group controlId="TimeZone">
                       
                            <Form.Label className='label-edit'>Timezone</Form.Label>
                            <MdOutlineAccessTime className='timezone-icon' />
                            <Form.Control type="text" className='input-form-modal' />
                        </Form.Group>
                    </Col>
                    <Col md={6}>
                        <Form.Group controlId="gender">
                            <Form.Label className='label-edit'>Gender</Form.Label>
                            <Form.Control as="select" className='input-form-modal   '>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </Form.Control>
                        </Form.Group>
                    </Col>
                    <Col md={6} >
                        <Form.Group controlId="Position">
                            <Form.Label className='label-edit'>Position</Form.Label>
                            <Form.Control type="text" className='input-form-modal' placeholder="" />
                        </Form.Group>
                    </Col>
                    <Col md={6}>
                        <Form.Group controlId="Specialty">
                            <Form.Label className='label-edit'>Specialty</Form.Label>
                            <Form.Control type="text" className='input-form-modal' placeholder="" />
                        </Form.Group>
                    </Col>
                    <Col md={12} >

                   
                            <Form.Label className='label-edit'>Social Accounts</Form.Label>
                            <Form.Group controlId="Specialty">
                            <FaTwitter className='all-icon' /><span className="handle-text">Twitter Handle</span>
                            <Form.Control type="text" className='input-form-modal-1' placeholder="" />
                            </Form.Group>
                           
                            <Form.Group controlId="Specialty">
                            <RiInstagramFill className='all-icon' /><span className="handle-text">Instagram Handle</span>
                            <Form.Control type="text" className='input-form-modal-1 mt-1' placeholder="" />
                            </Form.Group>
                            
                            <Form.Group controlId="Specialty">
                            <FaFacebookF className='all-icon' /><span className="handle-text">Facebook Handle</span>
                            <Form.Control type="text" className='input-form-modal-1 mt-1' placeholder="" />
                            </Form.Group>
                            <Form.Group controlId="Specialty">
                            <FaLinkedinIn className='all-icon' /><span className="handle-text">LinkedIn Handle</span>
                            <Form.Control type="text" className='input-form-modal-1 mt-1' placeholder="" />
                            </Form.Group>
                    </Col>
                    <Col md={6} >
                        <Form.Group controlId="membership">
                            <Form.Label className='label-edit mt-2'>Member Code</Form.Label>
                            <Form.Control type="text" className='input-form-modal' placeholder="Member Code" />
                        </Form.Group>
                    </Col>
                    <Col md={4} className="offset-2">
                        <button type="button" className='btn purple-modal-btn' >Submit</button>
                    </Col>
                </Row>
            </Form>
        </>
    )
}
export default InfluencerForm;