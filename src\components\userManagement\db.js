function getCurrentDate() {
  let date = new Date();
  let year = date.getFullYear();
  let month = ("0" + (date.getMonth() + 1)).slice(-2); // Months are zero based
  let day = ("0" + date.getDate()).slice(-2);
  return year + "-" + month + "-" + day;
}

const formatDate = (timestamp) => {
  let date = new Date(timestamp);
  let day = String(date.getDate()).padStart(2, "0");
  let month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based in JavaScript
  let year = date.getFullYear();
  return year + "-" + month + "-" + day;
};

export { getCurrentDate, formatDate };
