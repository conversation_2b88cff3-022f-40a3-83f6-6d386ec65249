.switch {
    position: relative;
    height: 1.4rem;
    width: 2.8rem;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    border-radius: 9999px;
    background-color: rgba(100, 116, 139, 0.377);
    transition: all .3s ease;
  }
  
  .switch:checked {
    background-color: #8107d1;
  }
  
  .switch::before {
    outline: none;
    position: absolute;
    content: "";
    left: calc(1.6rem - 1.6rem);
    top: calc(1.6rem - 1.6rem);
    display: block;
    height: 1.4rem;
    width: 1.4rem;
    cursor: pointer;
    border: 1px solid rgba(100, 116, 139, 0.527);
    border-radius: 9999px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 3px 10px rgba(100, 116, 139, 0.327);
    transition: all .3s ease;
  }
  

  
  .switch:checked:before {
    transform: translateX(100%);
    border-color: #8107d1;
  }