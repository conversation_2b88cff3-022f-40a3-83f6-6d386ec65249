import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import axios from "axios";

const authOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      async authorize(credentials) {
        const apiUrl = process.env.NEXT_PUBLIC_LOGIN_API_ENDPOINT;
        const { email, password } = credentials;

        try {
          const response = await axios.post(apiUrl, {
            email,
            password,
            user_app: "admin" || "child_admin",
          });
          if (
            response.status === 200 &&
            response.data.login_status === "success"
          ) {
            const {
              user_details,
              "X-AUTH-TOKEN": { access, refresh },
            } = response.data;

            return {
              ...user_details,
              access_token: access,
              refresh_token: refresh,
            };
          } else {
            console.log("Login failed:", response.data.error_message);
            return null;
          }
        } catch (error) {
          console.error("Error during login:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.user = user;
      }

      // Update the token if it's a trigger from session update
      if (trigger === "update" && session.user) {
        token.user = {
          ...token.user,
          ...session.user,
        };
      }
      // Ensure the token contains unique fields
      return token;
    },
    async session({ session, token }) {
      // Assign token.user to session.user ensuring no duplicates
      session.user = {
        ...token.user,
      };

      return session;
    },
  },
};

export default authOptions;
