import React from "react";
import { Placeholder } from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";

const TestimonialPlaceholder = () => {
  const placeholders = Array.from({ length: 4 }).map((_, index) => (
    <div key={index} className="col-sm-6 p-2">
      <div className="introvideo-bg p-3">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <Placeholder
              as="p"
              animation="glow"
              className="allApproval-heading mb-3 fs-4"
            >
              <Placeholder xs={6} />
            </Placeholder>
          </div>
          <div>
            <Placeholder
              as="p"
              animation="glow"
              className="testimonial-formatedate"
            ></Placeholder>
          </div>
        </div>
        <div className="d-flex justify-content-between align-items-center">
          <div className="">
            <div className="col-sm-auto">
              <Placeholder animation="glow" className="allApproval-image">
                <Placeholder
                  style={{ width: 50, height: 50, borderRadius: "50%" }}
                />
              </Placeholder>
            </div>
          </div>
          <div className="">
            <div className="col-sm-auto float-end">
              <Placeholder animation="glow" className="allApproval-image">
                <Placeholder
                  style={{ width: 50, height: 50, borderRadius: "50%" }}
                />
              </Placeholder>
            </div>
          </div>
        </div>
        <div className="row">
          <Placeholder
            as="p"
            animation="glow"
            className="allApproval-expert-para"
          >
            <Placeholder xs={12} />
            <Placeholder xs={10} />
            <Placeholder xs={8} />
          </Placeholder>
          <div className="modal-footer">
            <Placeholder.Button variant="secondary" xs={2} />
            <Placeholder.Button variant="secondary" xs={2} className="ms-2" />
          </div>
        </div>
      </div>
    </div>
  ));

  return (
    <div className="row overflow-auto allApproval-tab-scroll">
      {placeholders}
    </div>
  );
};

export default TestimonialPlaceholder;
