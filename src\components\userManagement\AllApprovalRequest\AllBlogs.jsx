import React, { useCallback, useEffect, useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import Image from "next/image";
import ViewBlogModal from "./AllApprovalModals/ViewBlogModal.jsx";
import BlogRejectReasonModal from "./BlogRejectReasonModal";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { toast } from "react-toastify";
import FeedbackPlaceholder from "./FeedbackPlaceholder";
import CustomPagination from "../../CustomPagination/CustomPagination";
import {
  capitalizeFullName,
  formatCustomDate,
  timeDifference,
} from "../../../utils/helperfunction";
import NoDataFound from "@/components/noDataFound/NoDataFound";

const AllBlogs = ({ userId }) => {
  const [showBlogModal, setShowBlogModal] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [allBlogs, setAllBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [singleBlog, setSingleBlog] = useState({});
  const [total_Pages, setTotal_Pages] = useState([]);
  const [current_page, setCurrent_Page] = useState(1);
  const [noDataFound, setNoDataFound] = useState(false);
  const axiosAuth = useAxiosAuth();

  const fetchAllBlogs = useCallback(
    async (page) => {
      try {
        setLoading(true);
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_EXPERTS_BLOG}all/0/?page=${page}`
        );
      
        const items = response?.data?.items;
        const totalPages = response?.data?.total_pages;
        setAllBlogs(items);
        setTotal_Pages(totalPages);
      } catch (error) {
        console.log(error);
        setNoDataFound(true);
      } finally {
        setLoading(false);
      }
    },
    [axiosAuth]
  );

  useEffect(() => {
    fetchAllBlogs(current_page);
  }, [fetchAllBlogs, current_page]);

  const handleArticleApprovals = async (status, blog_id, reason) => {
    let blog_status;

    if (status === "reject") {
      blog_status = 2;
    } else if (status === "approve") {
      blog_status = 1;
    }
    try {
      let dataToSend = { BlogStatus: blog_status };

      // Include reason only if status is reject
      if (status === "reject") {
        dataToSend.Reason = reason;
      }
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CU_DOCTOR_ARTICLE_STATUS_BY_ADMIN}${blog_id}/?user_id=${userId}`,
        dataToSend
      );

      if (response.data?.message?.BlogStatus === 1) {
        toast.success(`Blog Approved Successfully`);
        setShowReasonModal(false);
      } else if (response.data?.message?.BlogStatus === 2) {
        toast.success(`Blog Rejected Successfully`);
        setShowReasonModal(false);
      }
      fetchAllBlogs(1);
    } catch (error) {
      console.error(error);
    }
  };

  const handleViewBlog = (blog) => {
    setSingleBlog(blog?.blog_details);
    setShowBlogModal(true);
  };

  if (loading) {
    return <FeedbackPlaceholder />;
  }
  const handleShowReasonModal = (blog) => {
    setShowReasonModal(true);
    setSingleBlog(blog?.blog_details);
  };
  const hideReasonModal = () => {
    setShowReasonModal(false);
  };
  return (
    <>
      <div className="overflow-hidden">
        <div className="row overflow-auto allApproval-tab-scroll">
          {noDataFound == true ? (
            <NoDataFound />
          ) : Array.isArray(allBlogs) && allBlogs.length > 0 ? (
            allBlogs?.map((blog) => (
              <div key={blog?.blog_details?.id} className="col-sm-6 p-2">
                <div className="introvideo-bg p-3">
                  <div className="row">
                    <div className="col-sm-1">
                      <Image
                        src={
                          blog?.blog_details?.expert_details
                            ?.doctor_other_details?.ProfilePhoto
                        }
                        width={50}
                        height={50}
                        alt="cu logo"
                        className="allApproval-image"
                      />
                    </div>
                    <div className="col-sm-6">
                      <p className="allApproval-expert-name mb-0">
                        {blog?.blog_details?.expert_details?.name}
                      </p>
                      <p className="allApproval-expert-role">
                        {capitalizeFullName(
                          blog?.blog_details?.expert_details?.role
                            ? blog?.blog_details?.expert_details?.role
                            : "expert role"
                        )}
                      </p>
                      <p className="blog-formatedate">
                        {blog?.blog_details?.BlogDateTime &&
                          formatCustomDate(blog?.blog_details?.BlogDateTime)}
                        <span className="blog-time-difference">
                          {" "}
                          ({timeDifference(blog?.blog_details?.BlogDateTime)})
                        </span>
                      </p>
                    </div>
                    <div className="col-sm">
                      <p className="allApproval-expert-para content-scoll-section ">
                        {blog?.blog_details?.BlogTitle}
                      </p>
                    </div>
                    <div className="col-sm-1">
                      <span
                        className="allApproval-view-btn"
                        onClick={() => handleViewBlog(blog)}
                      >
                        View
                      </span>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn allApproval-reject-btn"
                      onClick={() => handleShowReasonModal(blog)}
                    >
                      Reject
                    </button>

                    <button
                      type="button"
                      className="btn allApproval-approve-btn ms-2"
                      onClick={() =>
                        handleArticleApprovals(
                          "approve",
                          blog?.blog_details?.id
                        )
                      }
                    >
                      Approve
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No other blogs available for approval.
              </h3>
            </div>
          )}
        </div>

        <div className="d-flex justify-content-center align-items-center">
          <div className="d-none d-xl-block">
            {total_Pages > 1 && (
              <CustomPagination
                total_pages={total_Pages}
                current_page={current_page}
                setCurrent_Page={setCurrent_Page}
              />
            )}
          </div>
        </div>
      </div>
      <ViewBlogModal
        showBlogModal={showBlogModal}
        setShowBlogModal={setShowBlogModal}
        singleBlog={singleBlog}
      />
      {showReasonModal && (
        <BlogRejectReasonModal
          show={handleShowReasonModal}
          onHide={hideReasonModal}
          showReasonModal={showReasonModal}
          handleArticleApprovals={handleArticleApprovals}
          singleBlog={singleBlog}
          fetchAllBlogs={fetchAllBlogs}
        />
      )}
    </>
  );
};

export default AllBlogs;
