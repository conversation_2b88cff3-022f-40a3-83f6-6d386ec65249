"use client";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { formatCustomDateAndTime } from "../../utils/helperfunction";
import { Button, Modal, Placeholder, Spinner } from "react-bootstrap";
import PdfViewerModal from "../expert-doc/pdfViewer/PdfViewerModal";
import CustomPagination from "../CustomPagination/CustomPagination";

const renderPlaceholders = () =>
  Array.from({ length: 8 }).map((_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size="lg"
          className="rounded-3"
          style={{ height: "50px" }}
        />
      </Placeholder>
    </div>
  ));

const PaymentHistoryModal = ({
  isOpen,
  setPaymentHistoryModal,
  expertIdForPaymentHistory,
}) => {
  const [expertPaymentHistory, setExpertPaymentHistory] = useState([]);
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);
  const [selectedInvoiceUrl, setSelectedInvoiceUrl] = useState("");
  const [loading, setLoading] = useState(true);
  const { expertId, expertWalletBalanceAmount } =
    expertIdForPaymentHistory || {};
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const paymentHistoryCache = useRef({});
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // const fetchExpertClearedAmount = useCallback(async () => {
  //   if (paymentHistoryCache.current[expertId]) {
  //     setExpertPaymentHistory(paymentHistoryCache.current[expertId]);
  //     setLoading(false);
  //     return;
  //   }

  //   try {
  //     const response = await axiosAuth.get(
  //       `${process.env.NEXT_PUBLIC_GET_ALL_EXPERT_PAYMENT}${expertId}/all/?user_id=${admin_id}&page=${current_page}`
  //     );
  //     let items = response?.data?.items || [];
  //     let total_pages = response?.data?.total_pages || 0;
  //     let total_items = response?.data?.total_items || 0;
  //     setTotalPages(total_pages);

  //     paymentHistoryCache.current[expertId] = response?.data;
  //     setExpertPaymentHistory(response?.data);
  //     setLoading(false);
  //   } catch (error) {
  //     console.error(error);
  //   }
  // }, [axiosAuth, admin_id, expertId]);
  const fetchExpertClearedAmount = useCallback(async () => {
    // Check if the data is already in cache
    const cacheKey = `${expertId}-${current_page}`;
    if (paymentHistoryCache.current[cacheKey]) {
      const { items, total_pages } = paymentHistoryCache.current[cacheKey];
      setExpertPaymentHistory(items);
      setTotalPages(total_pages);
      setLoading(false);
      return;
    }

    // Fetch data if not in cache
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_EXPERT_PAYMENT}${expertId}/all/?user_id=${admin_id}&page=${current_page}`
      );

      const items = response?.data?.items || [];
      const total_pages = response?.data?.total_pages || 0;

      // Cache the fetched data
      paymentHistoryCache.current[cacheKey] = {
        items,
        total_pages,
      };

      setExpertPaymentHistory(items);
      setTotalPages(total_pages);
    } catch (error) {
      console.error("Error fetching payment history: ", error);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, admin_id, expertId, current_page]);

  useEffect(() => {
    if (admin_id && isOpen) {
      fetchExpertClearedAmount();
    }
  }, [fetchExpertClearedAmount, admin_id, isOpen]);

  const handleViewReceipt = (invoiceUrl) => {
    setSelectedInvoiceUrl(invoiceUrl);
    setInvoiceModalOpen(true);
  };

  const handleCloseInvoiceModal = () => {
    setInvoiceModalOpen(false);
    setSelectedInvoiceUrl("");
  };

  const getTypeText = (transaction) => {
    const { TransactionType, PaymentStatus } = transaction;
    if (TransactionType === 1 && PaymentStatus === 0) return "Pending";
    if (TransactionType === 1 && PaymentStatus === 1) return "Debited";
    if (TransactionType === 0 && PaymentStatus === null) return "Credited";
    return TransactionType === 0 ? "Cr" : "Dr";
  };

  const doc = [_, selectedInvoiceUrl];

  return (
    <>
      <Modal
        show={isOpen}
        onHide={() => setPaymentHistoryModal(false)}
        centered
        scrollable
        dialogClassName="expert-payment-history-modal"
      >
        <Modal.Header className="d-flex justify-content-between align-items-center payment-history-modal-header px-3">
          <Modal.Title className="ps-4 fw-bold">Payment History</Modal.Title>
          <div
            className="pe-3 payment-history-modal-header-cancel-button"
            onClick={() => setPaymentHistoryModal(false)}
          >
            Cancel
          </div>
        </Modal.Header>
        <Modal.Body>
          <div className="d-flex justify-content-between align-items-center payment-history-balance-section fw-bold px-4 py-3">
            <span className="current-wallet">Current Wallet Balance</span>
            <span className="current-balance">
              {expertWalletBalanceAmount && `$ ${expertWalletBalanceAmount}.00`}
            </span>
          </div>
          <table className="table custom-table">
            <thead>
              <tr className="text-center">
                <th className="pending-row-custom">Sl No</th>
                <th className="pending-row-custom">Requested On</th>
                <th className="pending-row-custom">Transaction Amount</th>
                <th className="pending-row-custom">Balance Amount</th>
                <th className="pending-row-custom">Cleared On</th>
                <th className="pending-row-custom">View Transaction Receipt</th>
                <th className="pending-row-custom">
                  Download Transaction Receipt
                </th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={7}>{renderPlaceholders()}</td>
                </tr>
              ) : (
                <>
                  {Array.isArray(expertPaymentHistory) &&
                  expertPaymentHistory?.length > 0 ? (
                    <>
                      {expertPaymentHistory?.map((walletDetails, index) => (
                        <tr key={index}>
                          <td>{(current_page - 1) * 10 + index + 1}</td>
                          <td>
                            {walletDetails.TransactionDate &&
                              formatCustomDateAndTime(
                                walletDetails.TransactionDate
                              )}
                          </td>
                          <td className="text-justify">
                            {walletDetails.TransactionAmount
                              ? `$ ${walletDetails.TransactionAmount}.00`
                              : "$ 00.00"}{" "}
                            &nbsp;
                            <span
                              className={
                                walletDetails.TransactionType === 0
                                  ? "text-success fw-bold"
                                  : "text-danger fw-bold"
                              }
                            >
                              {getTypeText(walletDetails)}
                            </span>
                          </td>
                          <td>
                            {walletDetails.BalanceAmount
                              ? `$ ${walletDetails.BalanceAmount}.00`
                              : "$ 00.00"}
                          </td>
                          <td>
                            {walletDetails.PaymentStatus === 1 ? (
                              <>
                                {walletDetails.ClearedDate &&
                                  formatCustomDateAndTime(
                                    walletDetails.ClearedDate
                                  )}
                              </>
                            ) : (
                              "-- / -- / --"
                            )}
                          </td>
                          <td>
                            <button
                              className="border-0 bg-transparent text-purple fw-bold"
                              onClick={() =>
                                handleViewReceipt(walletDetails.Invoice)
                              }
                              disabled={walletDetails.PaymentStatus !== 1}
                            >
                              {walletDetails.PaymentStatus === 1
                                ? "View Receipt"
                                : "Not Available"}
                            </button>
                          </td>
                          <td>
                            <a
                              href={walletDetails.Invoice}
                              download
                              className="border-0 bg-transparent text-purple fw-bold"
                              disabled={walletDetails.PaymentStatus !== 1}
                            >
                              {walletDetails.PaymentStatus === 1 ? (
                                "Download Receipt"
                              ) : (
                                <span className="text-decoration-none">
                                  Not Available
                                </span>
                              )}
                            </a>
                          </td>
                        </tr>
                      ))}
                    </>
                  ) : (
                    <></>
                  )}
                </>
              )}
            </tbody>
          </table>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-center align-items-center">
          <CustomPagination
            total_pages={totalPages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </Modal.Footer>
      </Modal>

      <PdfViewerModal
        show={invoiceModalOpen}
        handleClosePdfModal={handleCloseInvoiceModal}
        document={doc}
      />
    </>
  );
};

export default PaymentHistoryModal;
