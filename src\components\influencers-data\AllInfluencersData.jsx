"use client";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import Traffic from "../../components/experts/Traffic";
import DashboardStatistics from "../experts/DashboardStatistics";
import Pagination from "../../components/experts/Pagination";
import SearchDataBar from "../experts/SearchDataBar";
import ExpertsList from "../experts/ExpertsList";
import ExpertsApproval from "../experts/ExpertsApproval";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import Loading from "../Loading/PageLoading/Loading";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { formatDateToYMD } from "../../utils/helperfunction";
import _ from "lodash";
import CustomPagination from "../CustomPagination/CustomPagination";

const AllInfluencersData = () => {
  const [requestedApprovedExperts, setrequestedApprovedExperts] = useState();
  const [loading, setLoading] = useState(true);
  const [influencerData, setInfluencerData] = useState();
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [influencerCount, setInfluencerCount] = useState({
    approval_requested: 0,
    approved: 0,
    deactivated: 0,
    pending: 0,
    rejected: 0,
    deleted: 0,
    selfDeactivated: 0,
    totalDoctorNumber: 0,
  });
  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const fetchedDataRef = useRef({});
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const user_id = session?.user?.id;

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleStatusChange = (status) => {
    setSelectedStatus(status);
    setCurrent_Page(1);
  };

  const filteredDoctorsData = influencerData?.filter((doctor) => {
    const nameMatches = doctor.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const statusMatches =
      selectedStatus === "all" || doctor.approval === selectedStatus;
    return nameMatches && statusMatches;
  });

  const getAllApprovedUnapprovedExpertsList = useCallback(
    async (query) => {
      try {
        let url = `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}${selectedStatus}/influencer/?user_id=${user_id}`;

        if (startDate && endDate && user_id) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}&page=${current_page}&per_page=6`;
        } else if (query && user_id) {
          url += `&name=${query}&page=${current_page}&per_page=6`;
        } else if (!startDate && !endDate && !query) {
          url += `&page=${current_page}&per_page=6`;
        }
        // Check if data for this URL is already fetched
        if (fetchedDataRef.current[url]) {
          const cachedData = fetchedDataRef.current[url];
          setInfluencerData(cachedData?.experts_data);
          setInfluencerCount(cachedData?.influencer_no);
          setTotalPages(cachedData?.total_pages);

          const expertiseData = await axiosAuth.get(
            process.env.NEXT_PUBLIC_FETCH_DOCTOR_EXPERTISE
          );


          // Map expertise IDs to names
          const expertiseMap = {};
          expertiseData?.data?.forEach((expertise) => {
            expertiseMap[expertise.id] = expertise.name;
          });

          // Update doctors' expertise to include names
          const doctorsWithExpertiseNames = cachedData?.experts_data.map(
            (doctor) => {
              const expertises = doctor.expertise.map((id) => expertiseMap[id]);
              return { ...doctor, expertiseNames: expertises };
            }
          );
          setInfluencerData(doctorsWithExpertiseNames);
        } else {
          const data = await axiosAuth.get(url);
          fetchedDataRef.current[url] = data?.data; // Store fetched data in useRef
          // const reversedInfluencersData = data?.data?.experts_data
          //   ?.slice()
          //   .reverse();
          // setInfluencerData(reversedInfluencersData);
          setInfluencerData(data?.data?.experts_data);
          setInfluencerCount(data?.data?.influencer_no);
          setTotalPages(data?.data?.total_pages);

          // const requestApprovalinfluencersData =
          //   data?.data?.experts_data?.filter(
          //     (user) => user.approval === "Approval_requested"
          //   );
          const requestApprovalinfluencersData =
            data?.data?.approval_requested_data?.influencer_requests;
          setrequestedApprovedExperts(requestApprovalinfluencersData);

          // setInfluencerData(requestApprovalinfluencersData);
        }
      } catch (err) {
        console.log("error in fetching ", err);
      } finally {
        setLoading(false);
      }
    },
    [user_id, startDate, endDate, selectedStatus, axiosAuth, current_page]
  );

  const approveSingleExpert = async (expertId) => {
    const body = { approval: "Approved" };
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${expertId}/?user_id=${user_id}`,
        body
      );
      if (response?.data?.message === "approved") {
        toast.success("Expert approved successfully!", {
          theme: "colored",
          autoClose: 1500,
        });
        // approveDoctors();
      } else {
        toast.error("Failed to approve expert. Please try again.", {
          theme: "colored",
          autoClose: 1500,
        });
      }
    } catch (err) {
      console.log("error in fetching ", err);
    }
  };
  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      getAllApprovedUnapprovedExpertsList(query);
    }, 500);
  }, [getAllApprovedUnapprovedExpertsList]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  const InfHeaderTitles = [
    "Guide Name",
    "Speciality",
    "Date of Application",
    "Date of Activation",
    "Status",
  ];

  const InfstatsData = [
    {
      heading: "Health Guide",
      value: influencerCount?.["total influencer number"],
    },
    { heading: "Approved", value: influencerCount?.approved },
    {
      heading: "Requested",
      value: influencerCount?.approval_requested,
    },
    { heading: "Pending", value: influencerCount?.pending },
    { heading: "Rejected", value: influencerCount?.rejected },
    { heading: "Deactivated", value: influencerCount?.deactivated },
    { heading: "Deleted", value: influencerCount?.deleted },
  ];

  return (
    <>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="bg-color">
          <div className=" overflow-hidden">
            <div className="user-management-scroll overflow-auto">
              <SearchDataBar
                heading="Health Guide Data"
                onSearchChange={handleSearchChange}
                onStatusChange={handleStatusChange}
                selectedStatus={selectedStatus}
                searchQuery={searchQuery}
                startDate={startDate}
                endDate={endDate}
                setEndDate={setEndDate}
                setStartDate={setStartDate}
                setSearchQuery={setSearchQuery}
                totalExperts={InfstatsData[0]?.value}
                expert={"Influencer"}
              />
              <div className="row">
                <div className="col-sm-7">
                  <DashboardStatistics statsData={InfstatsData} />
                  <ExpertsList
                    expertiseListName="Health Guide's List"
                    expertsList={influencerData}
                    headerTitles={InfHeaderTitles}
                    searchQuery={searchQuery}
                  />

                  <div className="d-flex justify-content-center align-items-center">
                    <div className="d-none d-xl-block">
                      <CustomPagination
                        total_pages={totalPages}
                        current_page={current_page}
                        setCurrent_Page={setCurrent_Page}
                      />
                    </div>
                  </div>
                </div>
                <ExpertsApproval
                  loading={loading}
                  approveSingleExpert={approveSingleExpert}
                  expertsApprovals={requestedApprovedExperts}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AllInfluencersData;
