import React from "react";
import { Doughnut } from "react-chartjs-2";

const TotalAppReqRate = ({ allExpertsAppointmentsDetails = {} }) => {
  const {
    total_completed_consultations = 0,
    total_upcoming_consultations = 0,
    total_unattended_consultations = 0,
    total_rescheduled_consultations = 0,
    total_cancelled_consultations = 0,
  } = allExpertsAppointmentsDetails;

  const chartData = {
    datasets: [
      {
        label: "Consultations",
        data: [
          total_completed_consultations,
          total_upcoming_consultations,
          total_unattended_consultations,
          total_rescheduled_consultations,
          total_cancelled_consultations,
        ],
        backgroundColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
          "rgba(181, 0, 0, 1)",
        ],
        borderColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
          "rgba(181, 0, 0, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };
  return (
    <div className="row mt-2">
      <p className="heading mb-1">Total Appointment Request Rate</p>
      <div className="col-sm-4 pink-bg">
        <div className="pink-bg chartpadding">
          <Doughnut data={chartData} />
        </div>
      </div>
      <div className="col-sm-8">
        <div className="custom-border pink-bg">
          <div className="row">
            <p className="text-center sub-heading mb-1 mt-1">
              Appointment Consultations
            </p>
          </div>
          <div className="bg-white p-2 mb-1">
            <div className="row">
              <div className="col-sm-6">
                <p className="text-center mb-0 fw-light" style={{fontSize: "13px"}}>Completed</p>
                <p className="text-center consultation-completed-count mb-0">
                  {total_completed_consultations}
                </p>
              </div>
              <div className="col-sm-6">
                <p className="text-center mb-0 fw-light" style={{fontSize: "13px"}}>Upcoming</p>
                <p className="text-center purple-count mb-0">
                  {total_upcoming_consultations}
                </p>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                <p className="text-center mb-0 fw-light" style={{fontSize: "13px"}}>Unattended</p>
                <p className="text-center consultation-unattended-count mb-0">
                  {total_unattended_consultations}
                </p>
              </div>
              <div className="col-sm-6">
                <p className="text-center mb-0 fw-light" style={{fontSize: "13px"}}>Rescheduled</p>
                <p className="text-center consultation-recheduled-count mb-0">
                  {total_rescheduled_consultations}
                </p>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-12">
                <p className="text-center mb-0 fw-light"  style={{fontSize: "13px"}}>Cancelled</p>
                <p className="text-center consultation-cancelled-count mb-0">
                  {total_cancelled_consultations}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TotalAppReqRate;
