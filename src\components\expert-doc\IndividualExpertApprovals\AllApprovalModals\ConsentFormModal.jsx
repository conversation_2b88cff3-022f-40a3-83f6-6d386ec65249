import Image from "next/image";
import React from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import CU_logoImage from "../../../../../public/images/white-logo.png";

const ConsentFormModal = ({ showConsentModal, setShowConsentModal }) => {
  return (
    <Modal
      show={showConsentModal}
      onHide={() => {
        setShowConsentModal(false);
      }}
      dialogClassName="viewConsent-modal-width"
     >
      <Modal.Header className="d-none d-xl-block" >
        <Modal.Title>
          <div className="model_title_doctor_consent">
            <div>Consent Form Preview</div>{" "}
          </div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="bg-color consentform-custom-overflow ">
          <div className="row docPat-consent-form-box mt-3 mx-auto">
            <div className="bg-heading d-flex justify-content-around align-items-center p-2">
              <Image
                src={CU_logoImage}
                alt=""
                className="cu-logo d-none d-xl-block object-fit-none"
              />
              <p className="heading mb-0 pe-2">
                TELECONSULTATION CONSENT FORM FOR ONCOLOGY CONSULTATIONS
              </p>
            </div>
            <form className="p-xl-4">
              <div className="row">
                <label
                  htmlFor="patientname"
                  className="form-label custom-form-label mb-1"
                >
                  Consulted Patient’s Name
                </label>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control custom-form-placeholder"
                    placeholder="Sandeep"
                    // value={patientFirstName}
                    value="Nandhakumar"
                    aria-label="First name"
                    readOnly
                  />
                </div>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control custom-form-placeholder"
                    placeholder="Nayak"
                    // value={patientLastName}
                    value="Nandhakumar"
                    aria-label="Last name"
                    readOnly
                  />
                </div>
                {/* ********************************* */}
                <div className="col d-xl-none">
                  <input
                    type="text"
                    className="form-control custom-form-control custom-form-placeholder"
                    placeholder="Sandeep"
                    // value={patientName}
                    value="Nandhakumar"
                    aria-label="First name"
                    readOnly
                  />
                </div>
                {/* ********************************* */}
              </div>
              <div className="row">
                <label
                  htmlFor="docname"
                  className="form-label custom-form-label mb-1 mt-2"
                >
                  Consulting Doctor’s Name
                </label>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control"
                    placeholder="First name"
                    aria-label="First name"
                    // value={doctorFirstName}
                    value="Nandhakumar"
                  />
                </div>
                <div className="col d-none d-xl-block">
                  <input
                    type="text"
                    className="form-control custom-form-control"
                    placeholder="Last name"
                    aria-label="Last name"
                    // value={doctorLastName}
                    value="Nandhakumar"
                  />
                </div>
                {/* ********************************* */}
                <div className="col d-xl-none">
                  <input
                    type="text"
                    className="form-control custom-form-control"
                    placeholder="First name"
                    aria-label="First name"
                    // value={doctorName}
                    value="Nandhakumar"
                  />
                </div>
                {/* ********************************* */}
              </div>

              <p className="custom-form-label mb-1 mt-2">
                Date of Consultation
              </p>
              <span className="fw-bold custom-para">
                {/* {changeDateFormat(appointmentDate)} */}
                05/07/2024
              </span>

              <p className="custom-para mt-3">
                I, the undersigned, acknowledge and consent to participate in a
                teleconsultation session with the above-named doctor
                specializing in oncology. I have been informed and understand
                the following terms and conditions:
              </p>
              <div className="image-container overflow-hidden">
                <div className="content-scroll consent-custom-overflow overflow-auto">
                  <div className="custom-para consent-addContent">
                    <div
                    //   dangerouslySetInnerHTML={{
                    //     __html: data?.ConsentContent,
                    //   }}
                    />
                    Nandhakumar, the undersigned, acknowledge and consent to
                    participate in a teleconsultation session with the
                    above-named doctor specializing in oncology. I have been
                    informed and understand the following terms and conditions:
                  </div>
                </div>
              </div>

              <div className="mb-2">
                <p className="custom-para">
                  <span className="fw-bold">Consent Duration</span>
                  <br /> This consent is valid for the specific teleconsultation
                  session mentioned above.
                  <br />
                  <br /> I, the undersigned, hereby acknowledge that I have read
                  and understood the terms and conditions outlined in this
                  Teleconsultation Consent Form. I willingly and voluntarily
                  consent to the teleconsultation with the specified oncology
                  doctor, granting access to my medical records for the purpose
                  of this consultation. I understand the potential risks and
                  benefits associated with teleconsultation.
                  <br />
                  <br />
                  <div className="d-flex">
                    <div className="col-sm-auto">
                      <p className="fw-bold">Doctor Signature</p>

                      <Image
                        src={CU_logoImage}
                        alt=""
                        className="mb-2 user_signature"
                        width={150}
                        height={100}
                      />
                    </div>

                    <div className="col-sm-auto ms-auto">
                      <p className="fw-bold">patient Signature</p>

                      <Image
                        src={CU_logoImage}
                        alt=""
                        className="mb-2 user_signature"
                        width={150}
                        height={100}
                      />
                    </div>
                  </div>
                  <br />
                  <br />
                  <span className="fw-bold">
                    Healthcare Provider Confirmation:
                  </span>
                  <br /> I confirm that I have explained the nature, purpose,
                  and potential risks of the teleconsultation to the patient. I
                  have answered any questions the patient may have had, and they
                  have provided informed consent.
                  <br />
                  <br />{" "}
                  <span className="fw-bold">Healthcare Provider Name:</span>
                  <br /> Cancer Unwired Thank you for choosing Cancer Unwired
                  for your oncology care. If you have any further questions or
                  concerns, please do not hesitate to contact us at +852 8197
                  4746 or email <NAME_EMAIL>.
                </p>
              </div>

              <div className=" float-end mb-4 me-3"></div>
            </form>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button
          className="orange-btn"
          onClick={() => {
            setShowConsentModal(false);
          }}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ConsentFormModal;
