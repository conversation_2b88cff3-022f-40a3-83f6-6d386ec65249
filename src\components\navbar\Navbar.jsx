"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import logo from "../../../public/navbar/logobrand.png";
import { HiMiniHome } from "react-icons/hi2";
import { PiUsersThreeFill } from "react-icons/pi";
import { BsFillCalendarEventFill } from "react-icons/bs";
import { ImPriceTags } from "react-icons/im";
import { BsFillChatLeftTextFill } from "react-icons/bs";
import { IoListSharp } from "react-icons/io5";
import { ImSwitch } from "react-icons/im";
import "./navbar.css";
import Link from "next/link";
import { signOut, useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import Cookies from "js-cookie";
import { FaUserTie } from "react-icons/fa";

const Navbar = () => {
  const { isAdminChildAdmin } = useAdminContext();
  const [url, setUrl] = useState("");
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (typeof window !== "undefined") {
      const newUrl = new URL(window.location.href);
      const pathWithParams = `${newUrl.pathname}${newUrl.search}`;
      setUrl(pathWithParams);
    }
  }, [router]);

  const handleSignOut = async () => {
    try {
      await signOut({ redirect: false, callbackUrl: "/" });
      toast.success("Logged Out Successfully");
      Cookies.remove("secure_access");
      Cookies.remove("refresh_secure");
      Cookies.remove("user_id");
      setTimeout(() => {
        router.push(`/auth/login?returnUrl=${url}`);
      }, 2000);
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const links = [
    { href: "/", label: "Home", Icon: HiMiniHome },
    {
      href: "/usermanagement/experts",
      label: "User Management",
      Icon: PiUsersThreeFill,
    },
    {
      href: "/calendar",
      label: "Appointment Management",
      Icon: BsFillCalendarEventFill,
    },
    { href: "/payment", label: "Payments and Pricing", Icon: ImPriceTags },
    {
      href: "/communication",
      label: "Communication",
      Icon: BsFillChatLeftTextFill,
    },
    {
      href: "/contentmanagement",
      label: "Content Management",
      Icon: IoListSharp,
    },
  ];

  return (
    <div className="navbar-container">
      <div className="sidebar">
        <Link className="navbar-brand" href="#">
          <Image src={logo} alt="" className="brandlogo mb-4" />
        </Link>
        <div className="d-flex">
          <FaUserTie className="custom-icon me-2 text-center online me-2" />
          <div className="custom-title text-center">
            <p className="online-content mb-0">
              {isAdminChildAdmin ? "Child Admin" : "Super Admin"}
            </p>
          </div>
        </div>
        <ul className="navbar-nav me-auto mb-5 text-center">
          {links.map(({ href, label, Icon }) => {
            const isActive =
              href === "/" ? pathname === href : pathname.startsWith(href);

            return (
              <li
                key={href}
                className={`nav-item text-center custom-nav-item mb-3 ${
                  isActive ? "active-link" : ""
                }`}
              >
                <div className="d-flex">
                  <Icon
                    className={`custom-icon ${
                      isActive ? "custom-active-link" : ""
                    }`}
                  />
                  <Link
                    className={`nav-link custom-nav-link ms-2 ${
                      isActive ? "custom-active-link" : ""
                    }`}
                    href={href}
                  >
                    {label}
                  </Link>
                </div>
              </li>
            );
          })}
        </ul>
        <div className="d-flex">
          <button
            onClick={handleSignOut}
            type="button"
            className="btn logout-btn"
          >
            <ImSwitch />
          </button>
          <button
            onClick={handleSignOut}
            type="button"
            className="btn logout-btn logout-text-btn"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
