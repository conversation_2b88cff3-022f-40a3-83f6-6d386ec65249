import React, { useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../../public/images/dummy-avatar.jpg";
import noDataFound from "../../../../../public/assets/noDataFound.png";
import ViewBlogsContentModal from "../../BlogsContent/ViewBlogsContentModal";
import { Placeholder } from "react-bootstrap";
import { highlightText } from "../../../../utils/helperfunction";
import NoDataFound from "../../../noDataFound/NoDataFound";
import { toast } from "react-toastify";
// import { highlightText } from "../../../utils/helperfunction";

const headers = [
  "Ranking",
  "Expert Profile",
  "Date of Upload",
  "Blog Title",
  "Expert Role",
  "Blog Views",
  "Blog Page",
  "Delete Ranking",
];

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 4 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};
const featuredBlogs = [
  {
    blog_details: {
      BlogRanking: 1,
      expert_details: {
        expert_name: "Dr. Jane Smith",
        expert_profile_photo: "/images/dummy-avatar.jpg",
        expert_role: "Cardiologist",
      },
      BlogDateTime: "2024-08-15T12:34:56",
      BlogTitle: "Understanding Heart Health: Tips for a Stronger Heart",
      BlogViews: 1245,
      id: 1,
    },
  },
  {
    blog_details: {
      BlogRanking: 2,
      expert_details: {
        expert_name: "Dr. John Doe",
        expert_profile_photo: "/images/dummy-avatar.jpg",
        expert_role: "Neurologist",
      },
      BlogDateTime: "2024-08-10T09:12:34",
      BlogTitle: "The Importance of Brain Health and How to Maintain It",
      BlogViews: 980,
      id: 2,
    },
  },
  {
    blog_details: {
      BlogRanking: 3,
      expert_details: {
        expert_name: "Dr. Emily Johnson",
        expert_profile_photo: "/images/dummy-avatar.jpg",
        expert_role: "Dermatologist",
      },
      BlogDateTime: "2024-08-20T14:22:11",
      BlogTitle: "Skincare Myths: What You Need to Know for Healthy Skin",
      BlogViews: 1560,
      id: 3,
    },
  },
  {
    blog_details: {
      BlogRanking: 4,
      expert_details: {
        expert_name: "Dr. Michael Lee",
        expert_profile_photo: "/images/dummy-avatar.jpg",
        expert_role: "Orthopedic Surgeon",
      },
      BlogDateTime: "2024-08-05T08:45:30",
      BlogTitle: "Joint Pain Relief: Effective Treatments and Exercises",
      BlogViews: 800,
      id: 4,
    },
  },
  {
    blog_details: {
      BlogRanking: 5,
      expert_details: {
        expert_name: "Dr. Sarah Brown",
        expert_profile_photo: "/images/dummy-avatar.jpg",
        expert_role: "Pediatrician",
      },
      BlogDateTime: "2024-08-12T10:15:20",
      BlogTitle: "Child Health: Essential Tips for Parents",
      BlogViews: 1420,
      id: 5,
    },
  },
];

const SelectedSimilarBlogs = (
  {
    // fetchAllBlogs,
    // featuredBlogs,
    // featuredLoading,
    // fetchFeaturedBlogs,
    // handleDeleteRanking,
  }
) => {
  const [showBlogsModal, setShowBlogsModal] = useState(false);
  const [singleBlog, setSingleBlog] = useState({});
  const [deletingPodcastId, setDeletingPodcastId] = useState(null);

  const handleViewBlogsModal = (item) => {
    setSingleBlog(item);
    setShowBlogsModal(true);
  };

  const handleDeletePodcastRanking = async (id) => {
    try {
      setDeletingPodcastId(id);
      await handleDeleteRanking(id);
      // await fetchFeaturedBlogs();
      await fetchAllBlogs();
    } catch (err) {
      toast.error("Error in delete ranking");
    } finally {
      setDeletingPodcastId(null);
    }
  };
  const featuredLoading = false;

  return (
    <>
      {showBlogsModal && (
        <ViewBlogsContentModal
          showBlogsModal={showBlogsModal}
          setShowBlogsModal={setShowBlogsModal}
          singleBlog={singleBlog}
        />
      )}
      <div className="bg-color">
        <div className="row">
          <div className="col-sm-12">
            <table className="table mt-2">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-light text-center child-admin-list-heading ${
                        index === 3 ? "col-4" : "col"
                      }`}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>

              <tbody className="custom-border">
                {featuredLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {featuredBlogs &&
                    Array.isArray(featuredBlogs) &&
                    featuredBlogs?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                          <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      featuredBlogs &&
                      Array.isArray(featuredBlogs) &&
                      featuredBlogs?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row align-baseline table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {blog?.blog_details?.BlogRanking}
                            </td>
                            <td className="col purple-content text-capitalize">
                              {blog?.blog_details?.expert_details
                                ?.expert_profile_photo ? (
                                <Image
                                  src={`${blog?.blog_details?.expert_details?.expert_profile_photo}`}
                                  alt={`Dr ${blog?.blog_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.blog_details?.expert_details?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(
                                blog?.blog_details?.expert_details?.expert_name
                              )}
                            </td>
                            <td className="col custom-font text-center">
                              {blog?.blog_details?.BlogDateTime?.split("T")[0]}
                            </td>

                            <td className=" text-capitalize fw-medium col-4 custom-font">
                              {blog?.blog_details?.BlogTitle}
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {blog?.blog_details?.expert_details?.expert_role}
                            </td>
                            <td className=" text-capitalize col custom-font">
                              {blog?.blog_details?.BlogViews}
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "#8107D1", fontSize: "15px" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleViewBlogsModal(blog?.blog_details)
                                }
                              >
                                view
                              </button>
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "red", fontSize: "15px" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleDeletePodcastRanking(
                                    blog?.blog_details?.id
                                  )
                                }
                                disabled={
                                  deletingPodcastId === blog?.blog_details?.id
                                }
                              >
                                {deletingPodcastId === blog?.blog_details?.id
                                  ? "Deleting..."
                                  : "Delete"}
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default SelectedSimilarBlogs;
