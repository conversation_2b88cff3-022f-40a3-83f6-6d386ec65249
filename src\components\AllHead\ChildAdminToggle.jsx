import React, { useState } from "react";
import "../../common/toggle-btn/toggle.css";

const ChildAdminToggle = ({ value, code, handleCheckboxChange }) => {
  const [checked, setChecked] = useState(false);

  const handleChange = (e) => {
    const isChecked = e.target.checked;
    setChecked(isChecked);
    handleCheckboxChange(value, code, isChecked);
  };

  return (
    <div className={`toggle-container `}>
      <input
        className="switch"
        type="checkbox"
        checked={checked}
        onChange={handleChange}
        id={`switch-${value}`}
      />
    </div>
  );
};

export default ChildAdminToggle;
