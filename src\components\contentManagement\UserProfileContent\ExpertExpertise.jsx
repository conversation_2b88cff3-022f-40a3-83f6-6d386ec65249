import React, { useState, useEffect, useCallback } from "react";
import { toast } from "react-toastify";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Loader from "../../loader/Loader";
import { MdDelete, MdEdit } from "react-icons/md";
import Swal from "sweetalert2";
import { Modal, Form, Button } from "react-bootstrap";
import ContentManPlaceholder from "./ContentManPlaceholder";

const ExpertExpertise = () => {
  const [cancerType, setCancerType] = useState("");
  const [expertiseName, setExpertiseName] = useState("");
  const [expertise, setExpertise] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showDesignationEditModal, setShowDesignationEditModal] =
    useState(false);
  const [formData, setFormData] = useState({ id: "", type: "" });
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const handleCancerTypeChange = (e) => {
    setCancerType(e.target.value);
  };

  const handleExpertiseNameChange = (e) => {
    setExpertiseName(e.target.value);
  };

  const addExperts = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_ADD_EXPERTS_BY_ADMIN}`
        );

        setExpertise(response?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const response = await axiosAuth.post(
        process.env.NEXT_PUBLIC_ADD_EXPERTS_BY_ADMIN,
        {
          type: cancerType,
          name: expertiseName,
        }
      );
      setLoading(false);
      if (response?.data) {
        toast.success(`Expertise Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
      }
      addExperts();
      setCancerType("");
      setExpertiseName("");
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  const handleDeleteClick = async (reportId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_CRUD_EXPERTS_BY_ADMIN}${reportId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Designation Deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            addExperts();
          }
        } catch (error) {
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleEditClick = (report) => {
    setFormData({
      id: report.id,
      type: report.type,
      name: report.name,
    });
    setShowDesignationEditModal(true);
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CRUD_EXPERTS_BY_ADMIN}${formData.id}/?user_id=${user_id}`,
        {
          type: formData.type,
          name: formData.name,
        }
      );
      if (response?.status) {
        toast.success(`Designation Updated Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        setShowDesignationEditModal(false);
        addExperts();
      }
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  useEffect(() => {
    addExperts();
  }, [user_id, addExperts, axiosAuth]);

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <>
      <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
        Add Cancer Types
      </h5>
      <div className="row">
        <div className="col-sm-4">
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label htmlFor="cancerType" className="form-label custom-label">
                Type of Cancer
              </label>
              <input
                type="text"
                className="form-control custom-form-control"
                id="cancerType"
                value={cancerType}
                onChange={handleCancerTypeChange}
                placeholder="Enter type of cancer"
              />
            </div>

            <div className="mb-3">
              <label
                htmlFor="expertiseName"
                className="form-label custom-label"
              >
                Name of Expertise
              </label>
              <input
                type="text"
                className="form-control custom-form-control"
                id="expertiseName"
                value={expertiseName}
                onChange={handleExpertiseNameChange}
                placeholder="Enter name of expertise"
              />
            </div>

            <button
              disabled={loading}
              type="submit"
              className="btn purple-button"
            >
              {loading ? "Submitting" : "Submit"}
            </button>
          </form>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">Experts Name</th>
                  <th scope="col">Type of Cancer</th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {expertise &&
                  expertise.map((item, index) => (
                    <tr key={index}>
                      <th scope="row" className="custom-font-size">
                        {index + 1}
                      </th>
                      <td className="custom-font-size">{item.name}</td>
                      <td className="custom-font-size">{item.type}</td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm purple-button"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit className="custom-font-size" />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item.id)}
                        >
                          <MdDelete className="custom-font-size" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      <Modal
        show={showDesignationEditModal}
        onHide={() => setShowDesignationEditModal(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Edit Designation</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group controlId="formDesignationType">
              <Form.Label>Designation Type</Form.Label>
              <Form.Control
                type="text"
                name="type"
                value={formData.type}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group controlId="formDesignationName">
              <Form.Label>Designation Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
              />
            </Form.Group>
            <Button type="submit" className="mt-3 edit-update-button">
              Update
            </Button>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default ExpertExpertise;
