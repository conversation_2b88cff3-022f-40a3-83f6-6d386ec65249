import React from "react";
import ToggleBtn from "../../../common/toggle-btn/ToggleBtn";
import { GiCheckMark } from "react-icons/gi";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";

const ChildAdminGrantedPermissions = ({
  notificationsList,
  userPermissions,
  isPermissible,
  fetchNotificationList,
}) => {
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  const toggleNotification = async (notificationId, newStatus) => {
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_NOTIFICATION_STATUS}${notificationId}/?user_id=${admin_id}`,
        {
          ActiveStatus: newStatus,
        }
      );
      fetchNotificationList();
    } catch (error) {
      console.error("Error updating notification status:", error);
    }
  };
  return (
    <div className="col-sm-6 gx-5">
      <div className="row">
        <div className="notification-tab mb-2">
          <div className="row">
            <div className="col-sm-8">
              <p className="p-s-subheading">Permissions Granted by Admin</p>
            </div>
          </div>
          <div className="overflow-hidden mb-3">
            <div className="content-scroll overflow-auto">
              {userPermissions &&
                userPermissions.map((notification) => (
                  <div
                    key={notification}
                    className="row mb-2 single-notification-tab"
                  >
                    <div className="col-sm-8">
                      <span className="notification-name">{notification}</span>
                    </div>
                    <div className="col-sm-2">
                      <div className="form-check form-switch">
                        <GiCheckMark className="tick-mark-userpermissions" />
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
      {isPermissible && (
        <>
          <div className="row">
            <div className="col-sm-8">
              <p className="p-s-subheading">Notification Settings</p>
            </div>
            <div className="col-sm-2">
              <p className="p-s-subheading text-center">email</p>
            </div>
            <div className="col-sm-2">
              <p className="p-s-subheading text-center">In-app</p>
            </div>
          </div>
          <div className="overflow-hidden">
            <div className="content-scroll overflow-auto">
              <div className="notification-tab">
                {notificationsList &&
                  Array.isArray(notificationsList) &&
                  notificationsList.map((notificationCategory) => {
                    const category = Object.keys(notificationCategory)[0];
                    const notifications = notificationCategory[category];
                    const emailNotification = notifications.find(
                      (notification) => notification.CategoryType === "E"
                    );
                    const appNotification = notifications.find(
                      (notification) => notification.CategoryType === "N"
                    );
                    return (
                      <div
                        key={category}
                        className="row mb-2 single-notification-tab me-3"
                      >
                        <div className="col-sm-8">
                          <span className="notification-name">{category}</span>
                        </div>
                        <div className="col-sm-2">
                          <div className="form-check form-switch">
                            {emailNotification && (
                              <ToggleBtn
                                isActive={emailNotification?.ActiveStatus === 1}
                                onToggle={toggleNotification}
                                id={emailNotification?.id}
                                type="E"
                              />
                            )}
                          </div>
                        </div>
                        <div className="col-sm-2">
                          <div className="form-check form-switch">
                            {appNotification && (
                              <ToggleBtn
                                isActive={appNotification?.ActiveStatus === 1}
                                onToggle={toggleNotification}
                                id={appNotification?.id}
                                type="N"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ChildAdminGrantedPermissions;
