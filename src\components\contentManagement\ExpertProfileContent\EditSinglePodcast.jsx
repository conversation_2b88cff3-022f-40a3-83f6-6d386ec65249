import React, { useState, useEffect, useCallback } from "react";
import {
  Button,
  Figure,
  Form,
  InputGroup,
  Modal,
  Spinner,
} from "react-bootstrap";
import Image from "next/image";
import { toast } from "react-toastify";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import dummyVideoThumbnail from "../../../../public/images/blackscreen.jpg";
import { useSession } from "next-auth/react";
import { AiFillYoutube } from "react-icons/ai";
import { FaSpotify } from "react-icons/fa";
import { SiApplepodcasts } from "react-icons/si";
import { IoIosAddCircleOutline } from "react-icons/io";
import { MdDelete } from "react-icons/md";
import Link from "next/link";
import { GiDuration } from "react-icons/gi";
import { calculateWordCount } from "../../../utils/helperfunction";
import Swal from "sweetalert2";
import ModalImage from "react-modal-image";

// Helper function to get category ID by name
const getCategoryIdByName = (categories, categoryName) => {
  if (!categoryName) {
    return null;
  }
  const filteredCategories = categories?.filter(
    (cat) => cat.Category === categoryName
  );
  return filteredCategories.length > 0 ? filteredCategories[0].id : null;
};

const EditSinglePodcast = ({ podcast, setEditPodcast, fetchPodcasts }) => {
  const [timestamps, setTimestamps] = useState([]);
  const [categories, setCategories] = useState([]);
  const [formData, setFormData] = useState({
    podcastId: 0,
    PodcastTopic: "",
    YoutubePodcastURL: "",
    PodcastDescription: "",
    PodcastCategory: "",
    YoutubeThumbnailImage: null,
    YoutubePodcastTranscription: "",
    ApplePodcastURL: "",
    SpotifyPodcastURL: "",
    YoutubePodcastDuration: "",
  });
  const [helpfullLinks, setHelpfullLinks] = useState([{ title: "", url: "" }]);
  const [loading, setLoading] = useState(false);
  const [YoutubeThumbnailPreview, setYoutubeThumbnailPreview] = useState(null);
  const [wordCount, setWordCount] = useState(0);

  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  // Fetch categories from API
  const fetchCategories = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_POST_PODCAST_CATEGORY}?user_id=${admin_id}`
      );
      if (response.status === 200) {
        setCategories(response.data);
      } else {
        console.error("Error fetching categories: ", response.statusText);
      }
    } catch (error) {
      console.error("Error fetching categories: ", error);
    }
  }, [axiosAuth, admin_id]);

  // Initialize form data and categories when podcast data changes
  useEffect(() => {
    if (admin_id) {
      fetchCategories();
    }

    if (podcast) {
      const {
        id,
        Platforms,
        ThumbnailImage,
        PodcastTopic,
        PodcastDescription,
        PodcastCategoryVal,
        HelpfulLinks,
      } = podcast;

      const wordCount = calculateWordCount(PodcastDescription || "");

      setFormData({
        podcastId: id,
        PodcastTopic: PodcastTopic || "",
        YoutubePodcastURL: Platforms?.youtube?.url || "",
        PodcastDescription: PodcastDescription || "",
        PodcastCategory: PodcastCategoryVal || "",
        YoutubeThumbnailImage:
          ThumbnailImage || Platforms?.youtube?.thumbnail || null,
        YoutubePodcastTranscription: Platforms?.youtube?.transcript || "",
        ApplePodcastURL: Platforms?.apple_podcast?.url || "",
        SpotifyPodcastURL: Platforms?.spotify?.url || "",
        YoutubePodcastDuration: Platforms?.youtube?.duration || "",
      });

      setWordCount(wordCount);
      setTimestamps(Platforms?.youtube?.timestamps || []);
      setYoutubeThumbnailPreview(ThumbnailImage || null);
      setHelpfullLinks(HelpfulLinks || [{ title: "", url: "" }]);
    }
  }, [podcast, fetchCategories, admin_id]);

  // Handle saving the edited podcast
  const handleSaveEdit = async () => {
    const wordCount = formData.PodcastDescription.trim().split(/\s+/).length;
    if (wordCount < 50 || wordCount > 100) {
      toast.error("Description should be between 50 and 100 words");
      return;
    }
    setLoading(true);

    try {
      const platform = {
        youtube: {
          url: formData.YoutubePodcastURL,
          timestamps: timestamps,
          transcript: formData.YoutubePodcastTranscription,
          thumbnail: formData.YoutubeThumbnailImage,
          duration: formData.YoutubePodcastDuration,
        },
        apple_podcast: {
          url: formData.ApplePodcastURL,
        },
        spotify: {
          url: formData.SpotifyPodcastURL,
        },
      };
      const dataToSend = new FormData();
      dataToSend.append("PodcastTopic", formData.PodcastTopic);
      dataToSend.append("YoutubePodcastURL", formData.YoutubePodcastURL);
      dataToSend.append("PodcastDescription", formData.PodcastDescription);
      if (formData.YoutubeThumbnailImage) {
        dataToSend.append("ThumbnailImage", formData.YoutubeThumbnailImage);
      }
      const filteredLinks = helpfullLinks.filter(
        (link) => link.title && link.url
      );

      if (filteredLinks && filteredLinks?.length > 0) {
        dataToSend.append("HelpfulLinks", JSON.stringify(filteredLinks));
      }

      dataToSend.append("Platforms", JSON.stringify(platform));

      const categoryID = getCategoryIdByName(
        categories,
        formData.PodcastCategory
      );
      if (categoryID) {
        dataToSend.append("PodcastCategoryVal", categoryID);
      } else {
        toast.error("Invalid category selected");
        return;
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_PUBLISH_EXPERTS_PODCAST_REQUEST}${formData?.podcastId}/?user_id=${admin_id}`,
        dataToSend
      );

      if (response.status === 200) {
        toast.success("Podcast edited successfully");
        fetchPodcasts();
        setEditPodcast(null);
      } else {
        console.error("Error editing podcast:", response.statusText);
        toast.error("Failed to edit podcast");
      }
    } catch (error) {
      console.error("Error editing podcast:", error);
      toast.error("Error occurred while saving changes");
    } finally {
      setLoading(false);
    }
  };

  // Handle timestamp changes
  const handleTimestampChange = (index, field, value) => {
    const updatedTimestamps = [...timestamps];
    updatedTimestamps[index][field] = value;
    setTimestamps(updatedTimestamps);
  };

  // Add new timestamp
  const handleAddTimestamp = () => {
    setTimestamps((prevTimestamps) => [
      ...prevTimestamps,
      { time: "", title: "" },
    ]);
  };

  // Handle thumbnail change
  const handleThumbnailChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prevData) => ({ ...prevData, YoutubeThumbnailImage: file }));
      const reader = new FileReader();
      reader.onloadend = () => {
        setYoutubeThumbnailPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddHelpfullLinks = () => {
    setHelpfullLinks((prevHelpfullLink) => [
      ...prevHelpfullLink,
      { title: "", url: "" },
    ]);
  };

  const handleHelpfullLink = (index, field, value) => {
    const updatedHelpfulLinks = [...helpfullLinks];
    updatedHelpfulLinks[index][field] = value;
    setHelpfullLinks(updatedHelpfulLinks);
  };

  const handleEditClose = () => {
    Swal.fire({
      title: "Are you sure ?",
      text: "You won't be able to revert this!",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes, Close it",
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        setEditPodcast(null);
      }
    });
  };
  return (
    <Modal
      show={!!podcast}
      onHide={handleEditClose}
      size="xl"
      centered
      scrollable
      keyboard={false}
      backdrop="static"
    >
      <Modal.Header closeButton>
        <Modal.Title>Edit Podcast</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Form.Group className="mb-3">
            <Form.Label className="fw-bold">Podcast Topic</Form.Label>
            <Form.Control
              type="text"
              value={formData.PodcastTopic}
              onChange={(e) =>
                setFormData((prevData) => ({
                  ...prevData,
                  PodcastTopic: e.target.value,
                }))
              }
              required
            />
          </Form.Group>

          <Form.Group controlId="PodcastCategory">
            <Form.Label className="fw-bold">Podcast Category</Form.Label>
            <Form.Select
              value={formData.PodcastCategory}
              onChange={(e) =>
                setFormData((prevData) => ({
                  ...prevData,
                  PodcastCategory: e.target.value,
                }))
              }
            >
              <option value="">Select Podcast Category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.Category}>
                  {category.Category}
                </option>
              ))}
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label className="fw-bold">Podcast Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={formData.PodcastDescription}
              onChange={(e) => {
                const description = e.target.value;
                const newWordCount = calculateWordCount(description);

                // Update form data and word count dynamically
                setFormData((prevData) => ({
                  ...prevData,
                  PodcastDescription: description,
                }));
                setWordCount(newWordCount);
              }}
              placeholder="Enter podcast description (50-100 words)"
              required
            />
            {(wordCount < 50 || wordCount > 100) && (
              <Form.Text className="text-muted">
                {wordCount < 50
                  ? `${
                      50 - wordCount
                    } more words required to reach the minimum.`
                  : `Exceeded by ${wordCount - 100} words.`}
              </Form.Text>
            )}
          </Form.Group>

          <div className="border px-2 py-2 mt-2 rounded">
            <InputGroup className="mb-3">
              <InputGroup.Text>
                <AiFillYoutube color="#FF0000" className="fs-3" />
                &nbsp; Youtube Podcast URL
              </InputGroup.Text>
              <Form.Control
                placeholder="Enter Youtube Podcast URL"
                type="url"
                value={formData.YoutubePodcastURL}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    YoutubePodcastURL: e.target.value,
                  }))
                }
                required
              />
            </InputGroup>
            <InputGroup className="mb-3 mt-4">
              <InputGroup.Text id="basic-addon1">
                <GiDuration color="#FF0000" className="fs-3" />
                &nbsp; Youtube Podcast Duration
              </InputGroup.Text>
              <Form.Control
                placeholder="Enter Youtube Podcast Duration"
                type="text"
                aria-label="YoutubePodcastDuration"
                aria-describedby="basic-addon1"
                value={formData.YoutubePodcastDuration}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    YoutubePodcastDuration: e.target.value,
                  }))
                }
                required
              />
            </InputGroup>

            <Form.Group className="mb-3">
              <Form.Label className="fw-bold">
                {" "}
                Youtube Thumbnail Image
              </Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={handleThumbnailChange}
              />
              {YoutubeThumbnailPreview ? (
                <>
                  <div
                    className="mt-2 d-flex justify-content-center align-items-center"
                    style={{ width: "200px", height: "200px" }}
                  >
                    {/* <Image
                      src={YoutubeThumbnailPreview}
                      alt="Thumbnail Preview"
                      width={100}
                      height={100}
                    /> */}
                    <ModalImage
                      small={YoutubeThumbnailPreview}
                      large={YoutubeThumbnailPreview}
                      showRotate={true}
                      alt={"Thumbnail Preview"}
                      // className={"custom-small-image"}
                    />
                  </div>
                </>
              ) : (
                <>
                  {formData?.YoutubeThumbnailImage &&
                  formData?.YoutubeThumbnailImage ? (
                    <div className="mt-2 d-flex justify-content-center w-100 ">
                      {/* <Image
                        src={formData?.YoutubeThumbnailImage}
                        alt="Thumbnail Preview"
                        width={200}
                        height={200}
                      /> */}
                      <ModalImage
                        small={formData?.YoutubeThumbnailImage}
                        large={formData?.YoutubeThumbnailImage}
                        showRotate={true}
                        alt={"podcast thunbnail"}
                        // className={"custom-small-image"}
                        style={{ width: "200px", height: "200px" }}
                      />
                    </div>
                  ) : (
                    <div className="mt-2 d-flex justify-content-center ">
                      <Figure>
                        <Figure.Image
                          width={200}
                          height={200}
                          alt="200x200"
                          src="holder.js/200x200"
                        />
                        <Figure.Caption>
                          No Thumbnail Image Available
                        </Figure.Caption>
                      </Figure>
                    </div>
                  )}
                </>
              )}
            </Form.Group>

            <Form.Group className="mb-3">
              <div className="d-flex justify-content-between align-items-center">
                <Form.Label className="fw-bold">
                  Youtube Podcast Transcription
                </Form.Label>
                <Link
                  href="https://youtubetotranscript.com/"
                  className="text-black text-decoration-none fw-bold"
                  target="_blank"
                >
                  Get Transcript
                </Link>
              </div>
              <Form.Control
                as="textarea"
                rows={5}
                value={formData.YoutubePodcastTranscription}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    YoutubePodcastTranscription: e.target.value,
                  }))
                }
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label className="fw-bold">
                Youtube Podcast Timestamps
              </Form.Label>
              <div
                style={{
                  maxHeight: timestamps.length > 5 ? "200px" : "auto",
                  overflowY: timestamps.length > 5 ? "scroll" : "visible",
                }}
              >
                {" "}
                {timestamps.map((timestamp, index) => (
                  <InputGroup className="mb-2" key={index}>
                    <InputGroup.Text>Time</InputGroup.Text>
                    <Form.Control
                      type="text"
                      value={timestamp.time}
                      onChange={(e) =>
                        handleTimestampChange(index, "time", e.target.value)
                      }
                      placeholder="00:00"
                    />
                    <InputGroup.Text>Title</InputGroup.Text>
                    <Form.Control
                      type="text"
                      value={timestamp.title}
                      onChange={(e) =>
                        handleTimestampChange(index, "title", e.target.value)
                      }
                      placeholder="Title"
                    />
                    <Button
                      variant="outline-danger"
                      onClick={() =>
                        setTimestamps(timestamps.filter((_, i) => i !== index))
                      }
                    >
                      <MdDelete />
                    </Button>
                  </InputGroup>
                ))}
              </div>
              <Button variant="outline-success" onClick={handleAddTimestamp}>
                <IoIosAddCircleOutline /> Add Timestamp
              </Button>
            </Form.Group>
          </div>
          <Form.Group className="mb-3">
            <Form.Label className="fw-bold">Apple Podcast URL</Form.Label>
            <InputGroup>
              <InputGroup.Text>
                <SiApplepodcasts color="#FF0000" className="fs-3" />
              </InputGroup.Text>
              <Form.Control
                type="url"
                placeholder="Enter Apple Podcast URL"
                value={formData.ApplePodcastURL}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    ApplePodcastURL: e.target.value,
                  }))
                }
              />
            </InputGroup>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label className="fw-bold">Spotify Podcast URL</Form.Label>
            <InputGroup>
              <InputGroup.Text>
                <FaSpotify color="#1DB954" className="fs-3" />
              </InputGroup.Text>
              <Form.Control
                type="url"
                placeholder="Enter Spotify Podcast URL"
                value={formData.SpotifyPodcastURL}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    SpotifyPodcastURL: e.target.value,
                  }))
                }
              />
            </InputGroup>
          </Form.Group>
          <div className="timestamps-section mt-4 mb-4">
            <label htmlFor="helpfullLinks" className="form-label custom-label">
              Helpful Links
            </label>
            <div
              style={{
                maxHeight: helpfullLinks.length > 5 ? "200px" : "auto",
                overflowY: helpfullLinks.length > 5 ? "scroll" : "visible",
              }}
            >
              {helpfullLinks.map((helpfulLink, index) => (
                <div key={index} className="d-flex mb-2">
                  <InputGroup>
                    <InputGroup.Text id="basic-addon1">Title</InputGroup.Text>
                    <Form.Control
                      placeholder="enter the title..."
                      aria-label="helpful title"
                      aria-describedby="basic-addon1"
                      type="text"
                      value={helpfulLink.title}
                      onChange={(e) =>
                        handleHelpfullLink(index, "title", e.target.value)
                      }
                      required
                    />
                  </InputGroup>
                  <InputGroup>
                    <InputGroup.Text id="basic-addon1">URL</InputGroup.Text>
                    <Form.Control
                      aria-label="helpful link"
                      aria-describedby="basic-addon1"
                      type="text"
                      placeholder="enter the url..."
                      value={helpfulLink.url}
                      onChange={(e) =>
                        handleHelpfullLink(index, "url", e.target.value)
                      }
                      required
                    />
                  </InputGroup>

                  <div className="d-flex justify-content-between align-items-center">
                    <IoIosAddCircleOutline
                      className="fs-3"
                      onClick={handleAddHelpfullLinks}
                    />
                    {helpfullLinks?.length > 1 && (
                      <MdDelete
                        className="fs-3"
                        onClick={() => {
                          const newHelpfulLink = [...helpfullLinks];
                          newHelpfulLink.splice(index, 1);
                          setHelpfullLinks(newHelpfulLink);
                        }}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleEditClose}>
          Close
        </Button>
        <Button variant="primary" onClick={handleSaveEdit} disabled={loading}>
          {loading ? "Please Wait..." : "Save Changes"}
          &nbsp;
          {loading && (
            <Spinner
              as="span"
              animation="border"
              size="sm"
              role="status"
              aria-hidden="true"
            />
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EditSinglePodcast;
