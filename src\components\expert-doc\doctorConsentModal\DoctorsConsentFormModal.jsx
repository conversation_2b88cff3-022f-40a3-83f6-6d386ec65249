import React, { useEffect, useState } from "react";
import Image from "next/image";
// import Modal from "react-modal";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import { IoClose } from "react-icons/io5";
import { toast } from "react-toastify";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { Placeholder } from "react-bootstrap";
import cuLogo from "../../../../public/images/white-logo.png";

import "./consentForm.css";
import RejectReasonModal from "../RejectReasonModal";
import { debounce } from "lodash";

const DoctorConsentFormModal = ({
  showConsentFormModal,
  setshowConsentFormModal,
  doctorsConsent,
  doctorsData,
  showButtons,
  admin_id,
  fetchDoctorConsent,
  doctorConsentLoading,
  setDoctorConsentLoading,
}) => {
  const axiosAuth = useAxiosAuth();
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const handleShowReasonModal = () => {
    setShowReasonModal(true);
  };
  const hideReasonModal = () => {
    setShowReasonModal(false);
  };

  const handleConsentApprovals = async (status, reason) => {
    let blog_status;

    if (status === "reject") {
      blog_status = 0;
    } else if (status === "approve") {
      blog_status = 1;
    }
    try {
      setLoading(true);
      let dataToSend = { Status: blog_status };
      setDoctorConsentLoading(true);
      // Include reason only if status is reject
      if (status === "reject") {
        dataToSend.Reason = reason;
      }
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_GET_DOCTORS_CONSENT_FORM}${doctorsData?.id}/?user_id=${admin_id}`,
        dataToSend
      );
      if (response.data?.message?.Status === 1) {
        toast.success(`Consent Form Approved Successfully`);
        setshowConsentFormModal(false);
      } else if (response.data?.message?.Status === 0) {
        toast.success(`Consent Form Rejected Successfully`);
        setshowConsentFormModal(false);
      }
      fetchDoctorConsent();
    } catch (error) {
      console.error(error);
      toast.error("Something went wrong. Please try again!");
    } finally {
      setLoading(false);
    }
  };
  const debouncedHandleConsentForm = debounce(handleConsentApprovals, 1000);
  const handleClose = () => setshowConsentFormModal(!showConsentFormModal);

  return (
    <>
      <Modal
        show={showConsentFormModal}
        onHide={() => setshowConsentFormModal(!showConsentFormModal)}
        centered
        size="lg"
        scrollable
      >
        <Modal.Body>
          <div className="bg-heading d-flex justify-content-around align-items-center p-2 mb-3">
            <Image
              src={cuLogo}
              alt="CU Logo"
              className="cu-logo object-fit-contain"
            />
            <p className="heading mb-0 pe-2 text-white">
              TELECONSULTATION CONSENT FORM FOR ONCOLOGY CONSULTATIONS
            </p>
          </div>

          <form>
            <div className="row mb-3">
              <label
                htmlFor="docname"
                className="form-label custom-form-label mb-1 mt-2"
              >
                Consulting Doctor’s Name
              </label>
              <div className="col">
                <input
                  type="text"
                  value={doctorsData?.name?.split(" ")?.[0]}
                  className="form-control custom-form-control"
                  placeholder="First name"
                  aria-label="First name"
                />
              </div>
              <div className="col">
                <input
                  type="text"
                  className="form-control custom-form-control"
                  placeholder="Last name"
                  aria-label="Last name"
                  value={doctorsData?.name?.split(" ")?.[1]}
                />
              </div>
            </div>

            <p className="custom-para mt-3">
              I, the undersigned, acknowledge and consent to participate in a
              teleconsultation session with the above-named doctor specializing
              in oncology. I have been informed and understand the following
              terms and conditions:
            </p>
            <div className="image-container overflow-hidden">
              <div className="content-scroll consent-custom-overflow overflow-auto">
                <div className="custom-para consent-addContent">
                  <p
                    className="doctors-consent-text"
                    dangerouslySetInnerHTML={{
                      __html: doctorsConsent?.data?.[0]?.ConsentContent,
                    }}
                  >
                    {/* {doctorsConsent?.data?.[0]?.ConsentContent} */}
                  </p>
                </div>
              </div>
            </div>
          </form>
        </Modal.Body>

        {showButtons && (
          <Modal.Footer className="d-flex align-items-center justify-content-end">
            <Button
              variant="danger"
              className="rounded-1"
              onClick={handleShowReasonModal}
            >
              Reject
            </Button>
            <Button
              variant="success"
              className="ms-3 py-2"
              disabled={loading}
              onClick={() => handleConsentApprovals("approve")}
            >
              {loading ? "Processing..." : "Approve"}
            </Button>
          </Modal.Footer>
        )}
      </Modal>
      {showReasonModal && (
        <RejectReasonModal
          show={handleShowReasonModal}
          onHide={hideReasonModal}
          doctorConsentLoading={doctorConsentLoading}
          debouncedHandleArticleApprovals={debouncedHandleConsentForm}
          type="consent"
        />
      )}
    </>
  );
};

export default DoctorConsentFormModal;
