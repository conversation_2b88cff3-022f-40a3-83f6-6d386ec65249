import React from "react";
import { FaCirclePlay } from "react-icons/fa6";
import { capitalizeFullName} from "../../../utils/helperfunction";

const IntroductionVideoUploads = ({
  expertIntroVideo,
  setTestimonialPreview,
  setFeedbackPreview,
  setShowReviewPreview,
  setShowIntroVideoPreview,
  setShowArticlesPreview,
  setShowPodcastPreview,
  setSelectedIntroVideo,
  setShowNoPreviewText
}) => {
  return (
    <div>
      {expertIntroVideo?.videoUrl === null ||
      expertIntroVideo?.videoUrl?.length === 0 ? (
        <>
          <h3
            className="d-flex justify-content-center align-items-center"
            style={{ minHeight: "500px" }}
          >
            <FaCirclePlay className="podcast-icon" /> &nbsp;
            <span className="text-secondary"> No Intro Video Available</span>
          </h3>
        </>
      ) : (
        <div className="">
          {expertIntroVideo &&
            expertIntroVideo?.videoUrl?.map((item, index) => (
              <div className="bg-color mb-2" key={index}>
                <div className="row">
                  <div className="col-sm-6 mb-0 d-flex justify-content-start align-items-center">
                    <FaCirclePlay
                      className="podcast-icon"
                      style={{ fontSize: "30px" }}
                    />
                    <p className="ps-2 mb-0 custom-font-size">
                      {`Expert - ${capitalizeFullName(
                        expertIntroVideo?.expertName
                      )} Introduction video ${index + 1}`}
                    </p>
                  </div>
                  <div className="col-sm-5 mb-0 d-flex">
                    {/* <p className="light-grey-text mb-0">
                                    {expertIntroVideo?.introVideoStatus[index] === 1
                                      ? "Pending"
                                      : expertIntroVideo?.introVideoStatus[
                                          index
                                        ] === 2
                                      ? "Approved"
                                      : expertIntroVideo?.introVideoStatus[
                                          index
                                        ] === 3
                                      ? "Rejected"
                                      : ""}
                                  </p> */}
                    <p
                      className={`blog-approval-status-${
                        expertIntroVideo?.introVideoStatus[index] === 1
                          ? "under-review"
                          : expertIntroVideo?.introVideoStatus[index] === 2
                          ? "approved"
                          : expertIntroVideo?.introVideoStatus[index] === 3
                          ? "rejected"
                          : ""
                      } mb-0`}
                    >
                      {expertIntroVideo?.introVideoStatus[index] === 1
                        ? "Pending"
                        : expertIntroVideo?.introVideoStatus[index] === 2
                        ? "Approved"
                        : expertIntroVideo?.introVideoStatus[index] === 3
                        ? "Rejected"
                        : ""}
                    </p>
                  </div>
                  <div className="col-sm-1">
                    <button
                      type="button"
                      className="btn btn-transparent purple-text fw-bold p-0"
                      onClick={() => {
                        setTestimonialPreview(false);
                        setFeedbackPreview(false);
                        setShowReviewPreview(false);
                        setShowIntroVideoPreview(false);
                        setShowArticlesPreview(false);
                        setShowPodcastPreview(false);
                        setShowIntroVideoPreview(true);
                        setShowNoPreviewText(false)
                        setSelectedIntroVideo({
                          url: item,
                          index: index + 1,
                        });
                      }}
                    >
                      View
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};

export default IntroductionVideoUploads;
