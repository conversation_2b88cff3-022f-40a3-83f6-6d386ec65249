import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import {
  capitalizeFullName,
  highlightText,
} from "../../../utils/helperfunction";
import { IoMdAddCircleOutline } from "react-icons/io";
import { AdminDetailsContext } from "../../../Context/AdminContext/AdminContext";
import ViewPodcastList from "../ExpertProfileContent/ViewPodcastList.jsx";
import { FaEye } from "react-icons/fa";
import ContentManPlaceholder from "../UserProfileContent/ContentManPlaceholder";
import AddPodcastForSelectedExpert from "./AddPodcastForSelectedExpert";
import AddBlogForSelectedExpert from "./AddBlogForSelectedExpert";
import axios from "axios";
import { GrSearch } from "react-icons/gr";
import { IoCloseSharp } from "react-icons/io5";
import CustomPagination from "../../CustomPagination/CustomPagination";

const AddPodcast = () => {
  const axiosAuth = useAxiosAuth();
  const { session } = useContext(AdminDetailsContext);
  const admin_id = session?.user.id;
  const [loading, setLoading] = useState(true);
  const [expertsData, setExpertsData] = useState([]);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [addPodcastModal, setAddPodcastModal] = useState(false);
  const [addBlogModal, setAddBlogModal] = useState(false);
  const [viewExpertPodcast, setViewExpertPodcast] = useState(null);
  const [viewExpertBlog, setViewExpertBlog] = useState(null);
  const [showPodcastModal, setShowPodcastModal] = useState(false);
  const [showBlogModal, setShowBlogModal] = useState(false);
  const [currentPodcastView, setCurrentPodcastView] = useState(null);
  const [currentBlogView, setCurrentBlogView] = useState(null);
  const [current_page, setCurrent_Page] = useState(1);
  const [initialLoading, setInitialLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  const fetchExpertsData = useCallback(
    async (query) => {
      try {
        setLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_EXPERTS_BY_FILTER}?page=${current_page}`;
        if (query) {
          url += `&name=${query}`;
        }
        const response = await axios.get(url);
        setExpertsData(response?.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching Experts: ", error);
      } finally {
        setLoading(false);
      }
    },
    [current_page]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      fetchExpertsData(query);
    }, 500);
  }, [fetchExpertsData]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [debouncedFetchData, searchQuery]);

  const handleSelectExpert = (expert, type) => {
    if (type === "podcast") {
      setSelectedDoctor(expert);
      setAddPodcastModal(true);
    } else if (type === "blog") {
      setSelectedDoctor(expert);
      setAddBlogModal(true);
    }
  };
  const handleClearSearch = () => {
    setSearchQuery("");
  };

  const handleViewPodcast = (item, index) => {
    setCurrentPodcastView(index);
    setViewExpertPodcast(item);
    setShowPodcastModal(true);
  };

  const handleViewBlog = (item, index) => {
    setCurrentBlogView(index);
    setViewExpertBlog(item);
    setShowBlogModal(true);
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <div className="">
      <div className="row">
        <AddPodcastForSelectedExpert
          addPodcastModal={addPodcastModal}
          setAddPodcastModal={setAddPodcastModal}
          selectedDoctor={selectedDoctor}
          setSelectedDoctor={setSelectedDoctor}
          from="contentManagement"
        />
        <AddBlogForSelectedExpert
          setAddBlogModal={setAddBlogModal}
          addBlogModal={addBlogModal}
          selectedDoctor={selectedDoctor}
          setSelectedDoctor={setSelectedDoctor}
        />
        <div className="row ">
          <h5 className=" col-sm-8 admin-add-blog-list-header fw-semibold ">
            Upload Podcast For Expert
          </h5>
          <div className="col-sm-4 ">
            <div className="input-group mb-3 search-input-patient calender-filter-container">
              <input
                type="text"
                style={{
                  height: "38px",
                  borderRadius: "3px",
                  border: "none",
                }}
                className="form-control search-input-focus"
                placeholder="Search Expert name"
                aria-label="Recipient's username"
                aria-describedby="button-addon2"
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
                value={searchQuery}
              />

              <span
                className="input-group-text custom-search-icon"
                id="button-addon2"
                style={{borderRadius: "5px"}}
              >
                {!searchQuery && (
                  <GrSearch
                    style={{
                      color: "#7B009C",
                    }}
                  />
                )}
              </span>

              <style jsx>{`
                ::placeholder {
                  color: #212529;
                }
              `}</style>
              {!loading && searchQuery && (
                <IoCloseSharp
                  className="clear-search-icon position-absolute"
                  style={{
                    fontSize: "20px",
                    cursor: "pointer",
                    right: "6px",
                    top: "10px",
                    color: "#7B009C",
                  }}
                  onClick={handleClearSearch}
                />
              )}
            </div>
          </div>
        </div>
        <div className="row">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr className="text-center">
                  <th scope="col" style={{ width: "60px", fontSize: "14px" }} className="fw-semibold">
                    Sl No
                  </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Expert ID	
                  </th>
                  <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Expert Profile</th>
                  <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Expert Role</th>
                  <th scope="col" style={{ width: "150px", fontSize: "14px" }} className="fw-semibold">
                    Add Podcast
                  </th>
                  <th scope="col" style={{ width: "150px", fontSize: "14px" }} className="fw-semibold">
                    View Podcast
                  </th>
                  {/* <th scope="col" style={{ width: "150px" }}>
                    Add Blog
                  </th>
                  <th scope="col" style={{ width: "150px" }}>
                    View Blog
                  </th> */}
                </tr>
              </thead>
              <tbody>
                {expertsData &&
                  expertsData?.experts_data?.map((item, index) => (
                    <tr key={item?.expert_details?.id}>
                      <td scope="row" className="col custom-font ">{index + 1}</td>
                      <td className="custom-font">{item?.expert_details?.id}</td>
                      <td className="text-start purple-content pr-4">
                        {item?.expert_other_details?.ProfilePhoto ? (
                          <Image
                            src={item?.expert_other_details?.ProfilePhoto}
                            alt={item?.expert_details?.name}
                            width={35}
                            height={35}
                            className="expert_image"
                          />
                        ) : (
                          <Image
                            src={dummyProfile}
                            alt={item?.expert_details?.name}
                            width={35}
                            height={35}
                            className="expert_image"
                          />
                        )}
                        <span>
                          {item?.expert_details?.prefix}{" "}
                          {highlightText(
                            capitalizeFullName(item?.expert_details?.name),
                            searchQuery
                          )}
                        </span>
                      </td>
                      <td className="custom-font">
                        {capitalizeFullName(
                          item?.expert_role ? item?.expert_role : "Expert Role"
                        )}
                      </td>
                      <td>
                        <button
                          className="btn btn-light btn-sm bg-transparent border-0"
                          onClick={() => handleSelectExpert(item, "podcast")}
                        >
                          <IoMdAddCircleOutline color="purple" size={25} />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-light btn-sm bg-transparent border-0"
                          onClick={() => handleViewPodcast(item, index)}
                        >
                          <FaEye color="orange" size={25} />
                        </button>
                      </td>

                      {currentPodcastView === index && (
                        <ViewPodcastList
                          showPodcastModal={showPodcastModal}
                          setShowPodcastModal={setShowPodcastModal}
                          viewExpertPodcast={viewExpertPodcast}
                        />
                      )}
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {expertsData?.total_pages !== 1 && (
        <div className="d-flex justify-content-center align-items-center mt-3">
          <div className="d-none d-xl-block">
            <CustomPagination
              total_pages={expertsData?.total_pages}
              current_page={current_page}
              setCurrent_Page={setCurrent_Page}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AddPodcast;
