import React, { useState, useEffect, useCallback } from "react";
import { MdEdit, MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "react-bootstrap";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Swal from "sweetalert2";
import ContentManPlaceholder from "./ContentManPlaceholder";
import { capitalizeFullName } from "../../../utils/helperfunction";
import { RiH5 } from "react-icons/ri";

const AddCategory = () => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [categories, setCategories] = useState([]);
  const [newCategory, setNewCategory] = useState("");
  const [categoryDescription, setCategoryDescription] = useState("");
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    categoryType: "podcast",
    Description: "",
  });
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getCreateApiEndpoint = (type) => {
    switch (type) {
      case "blog":
        return process.env.NEXT_PUBLIC_GET_POST_BLOG_CATEGORY;
      case "podcast":
      default:
        return process.env.NEXT_PUBLIC_GET_POST_PODCAST_CATEGORY;
    }
  };

  const getUpdateApiEndpoint = (type) => {
    switch (type) {
      case "blog":
        return process.env.NEXT_PUBLIC_UPDATE_BLOGS_CATEGORY;
      case "podcast":
      default:
        return process.env.NEXT_PUBLIC_UPDATE_PODCAST_CATEGORY;
    }
  };

  const getCategories = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${getCreateApiEndpoint(formData.categoryType)}?user_id=${user_id}`
        );
        setCategories(response?.data || []);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error fetching categories", err);
    }
  }, [axiosAuth, user_id, formData.categoryType]);

  useEffect(() => {
    getCategories();
  }, [getCategories]);

  const handleAddCategory = async () => {
    if (
      !newCategory.trim() ||
      !formData.categoryType ||
      !categoryDescription.trim()
    ) {
      toast.error("Please enter a all the details.", {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
      return;
    }

    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${getCreateApiEndpoint(formData.categoryType)}?user_id=${user_id}`,
        {
          Category: newCategory,
          Description: categoryDescription,
        }
      );
      setLoading(false);
      if (response?.data) {
        toast.success(`Category Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getCategories();
      }
      setNewCategory("");
      setCategoryDescription("");
    } catch (error) {
      console.log("Error adding category", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  const handleEditClick = (category) => {
    setFormData({
      id: category.id,
      name: category.Category,
      categoryType: formData.categoryType,
      Description: category.Description,
    });
    setShowEditModal(true);
  };

  const handleClose = () => {
    setShowEditModal(false);
  };

  const handleDeleteClick = async (categoryId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${getUpdateApiEndpoint(
              formData.categoryType
            )}${categoryId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Category Deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getCategories();
          }
        } catch (error) {
          console.log("Error deleting category", error);
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast.error("Please enter a category.", {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
      return;
    }
    if (!formData.Description.trim()) {
      toast.error("Please enter a description.", {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
      return;
    }

    try {
      const response = await axiosAuth.put(
        `${getUpdateApiEndpoint(formData.categoryType)}${
          formData.id
        }/?user_id=${user_id}`,
        {
          Category: formData.name,
          Description: formData.Description,
        }
      );
      if (response?.data) {
        toast.success(`Category Updated Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getCategories();
      }
      setFormData({
        id: "",
        name: "",
        categoryType: formData.categoryType,
        Description: "",
      }); // Reset categoryType to default
    } catch (error) {
      console.log("Error updating category", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    handleClose();
  };

  const categoryOptions = [
    { label: "Podcast", value: "podcast" },
    { label: "Blog", value: "blog" },
  ];

  if (loading) {
    return <ContentManPlaceholder />;
  }
  return (
    <div className="p-2">
      <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
        Add Category For Podcasts and Blogs
      </h5>
      <div className="row">
        <div className="col-sm-4">
          <div className="mb-3">
            <input
              type="text"
              className="form-control custom-form-control mb-2"
              id="category"
              value={newCategory}
              onChange={(e) => setNewCategory(e.target.value)}
              placeholder="Enter Category Name"
            />
            <textarea
              type="text"
              className="form-control custom-form-control"
              id="categoryDescription"
              value={categoryDescription}
              onChange={(e) => setCategoryDescription(e.target.value)}
              placeholder="Enter Category Description"
            />
          </div>
          <div className="mb-3">
            <Form.Label>Select Category Type</Form.Label>
            {categoryOptions.map((option) => (
              <div
                className="d-flex justify-content-between align-items-center"
                key={option.value}
              >
                <Form.Check
                  type="radio"
                  id={`category-type-${option.value}`}
                  name="categoryType"
                  label={option.label}
                  value={option.value}
                  checked={formData.categoryType === option.value}
                  onChange={handleChange}
                />
              </div>
            ))}
          </div>
          <button
            disabled={loading}
            type="submit"
            onClick={handleAddCategory}
            className="btn purple-button"
          >
            {loading ? `Adding Category` : `Add Category`}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">
                    {capitalizeFullName(formData.categoryType)} Category
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              <tbody>
                {categories.map((item, index) => (
                  <tr key={item.id}>
                    <th scope="row" className="custom-font-size">{index + 1}</th>
                    <td className="custom-font-size">{item.Category}</td>
                    <td className="custom-font-size">
                      <button
                        className="btn btn-primary btn-sm"
                        onClick={() => handleEditClick(item)}
                      >
                        <MdEdit />
                      </button>
                    </td>
                    <td className="custom-font-size">
                      <button
                        className="btn btn-danger btn-sm"
                        onClick={() => handleDeleteClick(item.id)}
                      >
                        <MdDelete />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Modal show={showEditModal} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>
            Edit {capitalizeFullName(formData.categoryType)} Category
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="formCategoryName">
            <Form.Label>Category Name</Form.Label>
            <Form.Control
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="mb-3"
            />
            <Form.Label>Category Description</Form.Label>
            <Form.Control
              as="textarea"
              type="text"
              name="Description"
              value={formData.Description}
              onChange={handleChange}
            />
          </Form.Group>
          {/* <Form.Group className="mt-3">
            <Form.Label>Select Category Type</Form.Label>
            {categoryOptions.map((option) => (
              <div
                className="d-flex justify-content-between align-items-center"
                key={option.value}
              >
                <Form.Check
                  type="radio"
                  id={`edit-category-type-${option.value}`}
                  name="categoryType"
                  label={option.label}
                  value={option.value}
                  checked={formData.categoryType === option.value}
                  onChange={handleChange}
                />
              </div>
            ))}
          </Form.Group> */}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>
          <Button variant="primary" onClick={handleSubmit}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AddCategory;
