import React from "react";
import { MdReviews } from "react-icons/md";
import { formatDate, timeDifference } from "./db.js";
import NoDataFound from "../noDataFound/NoDataFound.jsx";

const ReviewsApproval = ({
  unApprovedFeedbackLoading,
  reviews,
  renderPlaceholders,
  handleReviewModal,
  noDtaError,
}) => {
  return (
    <>
      {unApprovedFeedbackLoading === false ? (
        <>
          {noDtaError ? (
            <div className="custom-margin-nodatafoud">
              <NoDataFound />
            </div>
          ) : reviews && reviews?.length > 0 ? (
            <div className="overflow-hidden">
              <div className="overflow-auto allApproval-tab-scroll">
                <div className="row">
                  {reviews?.map((item, index) => (
                    <div key={index} className="col-6 mb-2">
                      <div className="d-flex bg-grey upload-reviews">
                        <div className="col-sm-1 review-icon">
                          <MdReviews />
                        </div>
                        <div className="col-sm-2">
                          <p className="fw-semibold mb-0">
                            {formatDate(item?.ReviewGenTime)}
                          </p>
                          <p className="btn-transparent custom-transperent-btn">
                            {timeDifference(item?.ReviewGenTime)}
                          </p>
                        </div>
                        <div className="col-sm-8 mb-0">
                          <p>
                            {item?.Review.split(" ").slice(0, 4).join("")}...
                          </p>
                        </div>
                        <div className="col-sm-1">
                          <button
                            type="button"
                            className="btn btn-yellow"
                            onClick={() => handleReviewModal(item)}
                          >
                            Review
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No other Reviews available for approval.
              </h3>
            </div>
          )}
        </>
      ) : (
        renderPlaceholders()
      )}
    </>
  );
};

export default ReviewsApproval;
