.icon-setting {
  cursor: pointer;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  box-shadow: inset 0 0 1px grey;
  border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  /* background: transparent linear-gradient(180deg, #9d2ba5 0%, #ca00d9 100%) 0%
      0% no-repeat padding-box; */
  background: rgb(198, 197, 197);
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: rgb(198, 197, 197);
}

.modal {
  backdrop-filter: blur(10px);
}

.custom-purple-text-color {
  color: #8107d1;
}
.video-container {
 
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2); /* Soft shadow effect */
  overflow: hidden; /* Ensures rounded corners */
  padding: 5px; /* Adds some space around the player */
  background: #ffffff; /* Optional: White background */
  max-width: 800px; /* Adjust max width as needed */
}

.helpful-link {
  color: #1871ff;
  text-decoration: none;
  font-size: 14px;
}

.helpful-link:hover {
  text-decoration: underline;
}



.custom-purple-button {
  background-color: #8107d1 !important;
  border: 0px !important;
  color: white !important;
}

.cursor-pointer {
  cursor: pointer;
}

.bgColor-purple {
  background-color: #8107d1;
}

