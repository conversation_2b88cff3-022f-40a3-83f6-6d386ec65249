// import React, { useState } from "react";
// import { Worker, Viewer } from "@react-pdf-viewer/core";
// import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";

// const PdfViewer = ({ documentUrl = "https://example.com/default.pdf" }) => {
//   const [fileUrl, setFileUrl] = useState(documentUrl);

//   // Using default layout plugin
//   const defaultLayoutPluginInstance = defaultLayoutPlugin();

//   return (
//     <div className="pdf-viewer">
//       <Worker
//         workerUrl={`https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`}
//       >
//         <Viewer fileUrl={fileUrl} plugins={[defaultLayoutPluginInstance]} />
//       </Worker>
//     </div>
//   );
// };

// export default PdfViewer;
