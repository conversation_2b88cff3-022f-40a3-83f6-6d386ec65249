'use client'

import React from "react";
import "../../../components/userManagement/usermanagement.css";
import "../../../components/administratorDasboard/home.css";
import UserManagementComp from "../../../components/userManagement/UserManagement";
import Navbar from "../../../components/navbar/Navbar";
import Home from "../../../components/administratorDasboard/Home";
import PatientsAllData from "../../../components/userManagement/patients-data/PatientsAllData";
import Link from "next/link";
import withAuth from "../../../withAuth/withAuth";

const Patients = () => {
  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-8">
                    {/* <p className="main-purple-text">User Management</p> */}
                    <p className="main-purple-text">
                      <Link
                        className="href_link"
                        href="/usermanagement/experts"
                      >
                        User Management {">"}{" "}
                      </Link>
                      <Link
                        className="href_link"
                        href="/usermanagement/patients/"
                      >
                        Patients {">"}
                      </Link>{" "}
                    </p>
                  </div>
                  <Home />
                </div>
              </div>
            </div>
            <UserManagementComp />
            <PatientsAllData />
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(Patients);
