/* .progress{
    height: 45px;
    background-color: #414146;
    width: 100%;
    border-radius: 0px 3px 3px 0px;
    border: 1px solid #414146;
    padding: 10px;
    font-size: 13px;
} */
.custom-progress-bar .progress-bar-label {
  text-align: center !important;
  width: 100% !important;
}

.progress-bar {
  /* background-color: #414146; */
  background-color: #9426b2;
}

.progress {
  height: 42px;
  width: 100%;
}
.colored-rescheduled-btn {
  background-color: #9426b2 !important;
  color: white;
}
.black-btn-not-started {
  width: 100%;
  height: 42px;
  /* border: 1px solid #414146; */
  padding: 0px !important;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #414146; */
  background-color: #9426b2;
}

/* center the progress bar */

.progress-bar-container {
  width: 100%;
  position: relative;
}

.progress-button-status {
  height: 35px !important;
  width: 100%;
  padding: 0px !important;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
}

.progress-bar-label {
  position: absolute;
  width: 100%;
  text-align: center;
  font-size: 14px;
  top: 50%;
  color: white;
  transform: translateY(-50%);
}


.colored-completed-btn {
  background-color: rgb(4, 171, 32) !important;
  color: white;
}
.colored-ongoing-btn {
  background-color: #97012f !important;
  color: white;
}
.colored-upcoming-btn {
  background-color: #223645 !important;
  color: white;
}
.colored-expired-btn {
  background-color: #d6d6d6 !important;
  color: #5a5959;
}
.colored-cancelled-btn {
  background-color: rgb(200, 44, 44) !important;
  color: white;
}

.activate-progress-bar {
  height: 35px;
}
