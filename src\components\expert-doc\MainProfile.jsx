"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { PiArticleNyTimesFill, PiFolderNotchOpenFill } from "react-icons/pi";

import CalenderFilter from "./CalenderFilter";
import Image from "next/image";
import { Md<PERSON><PERSON><PERSON>, MdOutlineEdit } from "react-icons/md";
import { IoStarHalfOutline, IoClose } from "react-icons/io5";
import { FaFile, FaFileUpload, FaStar } from "react-icons/fa";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "../../components/usermanagmentPatient/usermanagementpatient.css";
import { IoIosArrowBack } from "react-icons/io";
import { useParams } from "next/navigation";
import Loading from "../Loading/PageLoading/Loading";
import { capitalizeFullName, formatPhoneNumber, formatDate } from "./db.js";
import { toast } from "react-toastify";
import profileimg from "../../../public/assets/doctorprof.jpg";
import ArticleModal from "./ArticleModal/ArticleModal";
import { useSession } from "next-auth/react";
import profile from "../../../public/images/profile.png";
import TestimonialModal from "./testimonialModal/TestimonialModal";
import FeedbackModal from "./feedbackModal/FeedbackModal";
import { formatDateTime, getImageSrc } from "../../utils/helperfunction.js";
import { Button, Form, InputGroup, Modal, Placeholder } from "react-bootstrap";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import parsePhoneNumberFromString from "libphonenumber-js";
import Swal from "sweetalert2";
import CertificateViewer from "./CertificateViewer.jsx";
import RejectionModal from "./RejectionModal/RejectionModal";
import ProfileReactivationModal from "./ProfileReactivationModal/ProfileReactivationModal";
import { TbPointFilled } from "react-icons/tb";
import moment from "moment-timezone";
import Select from "react-select";
import { Country, State, City } from "country-state-city";
import SocialMediaLinks from "./SocialMediaLinks.jsx";
import { FiPlusCircle } from "react-icons/fi";
import { DocumentViewer } from "react-documents";
import PdfViewerModal from "./pdfViewer/PdfViewerModal";
import { useAdminContext } from "@/Context/AdminContext/AdminContext";

const renderPlaceholders = (value = 8) => {
  const placeholders = Array.from({ length: value }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

const renderStars = (rating) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  const stars = [];

  for (let i = 1; i <= fullStars; i++) {
    stars.push(<FaStar key={i} color="#FF971A" />);
  }

  if (hasHalfStar) {
    stars.push(<IoStarHalfOutline key="half" color="#FF971A" />);
  }

  const remainingStars = 5 - stars.length;

  for (let i = 1; i <= remainingStars; i++) {
    stars.push(<FaStar key={i + fullStars} />);
  }

  return stars;
};
const getStatusButton = (
  status,
  details = "",
  setShowRequestedForApprovalModal,
  setRejectModalAndDetails
) => {
  const getButton = (
    className,
    text,
    showModal = false,
    reason = "",
    date = "",
    type = ""
  ) => (
    <>
      <button type="button" className={`btn ${className}`}>
        {text}
        {showModal && (
          <>
            <br />
            <span
              onClick={() =>
                setRejectModalAndDetails({
                  rejectShowModal: true,
                  rejectedReason: reason,
                  rejectedDate: date,
                  type: type,
                })
              }
            >
              Why?
            </span>
          </>
        )}
      </button>
    </>
  );

  if (
    status === "self_deactivation" &&
    details?.self_reactivation_rejection_reason
  ) {
    return getButton(
      "btn-danger",
      "Rejected",
      true,
      details.self_reactivation_rejection_reason,
      details.self_reactivation_rejection_time,
      "rejected"
    );
  }
  if (status === "Deactivated" && details?.deactivated_reason) {
    return getButton(
      "btn-danger",
      "Deactivated",
      true,
      details.deactivated_reason,
      details.deactivated_time,
      "deactivating"
    );
  }
  if (status === "Rejected" && details?.rejected_reason) {
    return getButton(
      "btn-danger",
      "Rejected",
      true,
      details.rejected_reason,
      details.rejected_time,
      "rejected"
    );
  }

  switch (status) {
    case "Approved":
      return getButton("btn-green", "Approved");
    case "Rejected":
      return getButton("btn-danger", "Rejected");
    case "Deactivated":
      return getButton("btn-danger", "Deactivated");
    case "Deleted":
      return getButton("btn-danger", "Deleted");
    case "Approval_requested":
      return (
        <button
          type="button"
          className="btn btn-warning"
          style={{ fontSize: "12px" }}
        >
          Requested
          {details && (
            <span onClick={() => setShowRequestedForApprovalModal(true)}>
              &nbsp; Why?
            </span>
          )}
        </button>
      );
    case "self_deactivation":
      return getButton("btn-warning", "Self Deactivated");
    default:
      return getButton("btn-warning", "Pending");
  }
};

const initialState = {
  name: "",
  memberCode: "",
  phone: "",
  country_code: "",
  email: "",
  timezone: "",
  gender: "",
  address: "",
  practicingHospital: "",
  department: "",
  profilePicture: "",
  documents: "",
  professionalSummary: "",
  profileApproved: "",
  expertise: [],
  rating: 0,
};
const MainProfile = ({
  approvedTestimonials,
  handleTestmonialApprovals,
  approvedTestimonialsLoading,
  approvedFeedback,
  approvedFeedbackLoading,
}) => {
  const [doctorDetails, setDoctorDetails] = useState({});
  const [isEditable, setIsEditable] = useState(false);
  const [loading, setLoading] = useState(true);
  const [articles, setArticles] = useState([]);
  const [showArticleModal, setShowArticleModal] = useState(false);
  const [singleTestimonial, setSingleTestimonial] = useState([]);
  const [articleLoading, setArticleLoading] = useState(true);
  const [singleArticle, setSingleArticles] = useState([]);
  const [singleFeedback, setSingleFeedback] = useState([]);
  const [initialFormValues, setInitialFormValues] = useState(null);
  const [showRequestedForApprovalModal, setShowRequestedForApprovalModal] =
    useState(false);
  const [achivementInputGroups, setAchievementInputGroups] = useState(1);
  const [showTestimonialModal, setShowTestimonialModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [iti, setIti] = useState(null);
  const [showCertificate, setShowCertificate] = useState(false);
  const [currentCertificate, setCurrentCertificate] = useState(null);
  const [currentPdf, setCurrentPdf] = useState(null);
  const [timezones, setTimezones] = useState([]);
  const { data: session } = useSession();
  const [RejectModalAndDetails, setRejectModalAndDetails] = useState({
    rejectShowModal: false,
    rejectedReason: "",
    rejectedDate: null,
    type: "",
  });
  const [
    showOtherAchievementsDocumentModal,
    setShowOtherAchievementsDocumentModal,
  ] = useState(false);
  const fileInputRefs = useRef([]);
  const [achievements, setAchievements] = useState([]);
  const fileInputRef = useRef(null);
  const params = useParams();
  const admin_id = session && session?.user?.id;

  const doctor_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";
  const doctor_email1 =
    params && params?.user_id?.length > 0 && params?.user_id[1]
      ? params?.user_id[1]
      : "";

  // const doctor_id = 106;
  const doctor_Status =
    params && params?.user_id?.length > 0 && params?.user_id[2]
      ? params?.user_id[2]
      : "";

  const axiosAuth = useAxiosAuth();
  const doctor_email = decodeURIComponent(doctor_email1);
  const [editedFields, setEditedFields] = useState({});
  const [formValues, setFormValues] = useState(initialState);
  const [expertProfileStatus, setExpertProfileStatus] = useState({
    approval_status: "",
    details: "",
  });

  const { isAdminChildAdmin, userPermissions, isAdmin } = useAdminContext();
  const isEditPermissible = userPermissions?.includes("cu_app.change_cuuser");


  const updateURLBasedOnStatus = (approvalStatus) => {
    const currentURL = new URL(window.location.href);
    const currentStatus = currentURL?.pathname?.split("/").pop().split("?")[0];

    if (currentStatus !== approvalStatus) {
      currentURL.pathname = currentURL.pathname.replace(
        currentStatus,
        approvalStatus
      );
      window.history.replaceState(null, "", currentURL);
    }
  };
  const handleCloseInvoiceModal = () => {
    setShowCertificate(false);
  };

  const carrierAchievement = ["", currentPdf];

  const fetchDoctorDetails = useCallback(
    async (doctor_id) => {
      try {
        const { data } = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_DOCTOR_DATA}${doctor_email}/`
        );
        const userData = data?.user_data;
        const doctorDetails = userData?.doctor_other_details;
        setLoading(false);
        setDoctorDetails(userData);

        if (typeof window !== "undefined") {
          localStorage.setItem(
            "expert_timezone",
            JSON.stringify(userData?.TimeZone)
          );
        }

        const approvalStatus = userData?.approval;
        updateURLBasedOnStatus(approvalStatus);
        setExpertProfileStatus({
          approval_status: approvalStatus,
          details: userData?.approval_status_reason,
        });
        let member_code;

        if (doctorDetails?.MemberCode) {
          member_code = doctorDetails?.MemberCode;
        } else {
          let memberCodePrefix =
            userData?.role && userData?.role === "doctor"
              ? "D_"
              : role === "researcher"
              ? "R_"
              : "I_";
          member_code = `HU_${memberCodePrefix}${userData?.id}`;
        }

        const formValues = {
          name: userData.name,
          memberCode: member_code,
          phone: userData.phone,
          email: userData.email,
          timezone: userData?.TimeZone,
          City: userData?.City,
          State: "",
          Country: userData?.Country,
          country_code: userData?.country_code,
          gender: userData.sex,
          address: doctorDetails.Address,
          practicingHospital: doctorDetails.PractisingHospital,
          department: doctorDetails.Dept,
          profilePicture: doctorDetails.ProfilePhoto,
          professionalSummary: doctorDetails?.Summary,
          documents: doctorDetails?.Certificates,
          profileApproved: userData.approval,
          expertise: userData.expertise,
          last_login: userData.last_login,
          rating: userData?.expert_rating,
          ResearchPapers: doctorDetails?.ResearchPapers,
          SocialLinks: doctorDetails?.SocialLinks,
          OtherAchievements: doctorDetails?.OtherAchievements,
          ExperienceSummary: doctorDetails?.ExperienceSummary,
        };

        setFormValues(formValues);
        const initialAchievemnts = Array.isArray(
          doctorDetails?.OtherAchievements
        )
          ? doctorDetails?.OtherAchievements
          : [["", ""]];

        setAchievements(initialAchievemnts);
        const cityValue = userData?.City;
        if (cityValue) {
          const [city, state] = cityValue.split(",");
          setFormValues((prevDetails) => ({
            ...prevDetails,
            City: city?.trim(),
            State: state?.trim(),
          }));
        }
        setInitialFormValues(formValues);

        const { data: articleData } = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_DOCTOR_ARTICLE_DATA}${doctor_id}/1/`
        );
        setArticles(articleData);
      } catch (error) {
        console.log(error);
      } finally {
        setArticleLoading(false);
        setLoading(false);
      }
    },
    [
      doctor_email,
      setLoading,
      setDoctorDetails,
      setFormValues,
      setArticles,
      axiosAuth,
    ]
  );

  useEffect(() => {
    // Get a list of timezones using moment-timezone
    const timezoneList = moment.tz.names();
    setTimezones(timezoneList);

    if (doctor_email) {
      fetchDoctorDetails(doctor_id);
    }
  }, [doctor_email, doctor_id, fetchDoctorDetails]);

  const handleTakeControlClick = async () => {
    if (doctor_Status === "pending" || doctor_Status === "Deactivated") {
      toast.error("To make change profile should be approved...");
      setIsEditable(false);
      return;
    }
    if (!isEditable) {
      const shouldEdit = await Swal.fire({
        title: "Confirm Edit",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
        customClass: {
          confirmButton: "swal-confirm-button-class",
          cancelButton: "swal-cancel-button-class",
        },
        buttonsStyling: false,
      });

      if (!shouldEdit.isConfirmed) {
        return;
      }
    }
    setIsEditable((prevIsEditable) => !prevIsEditable);
  };

  const handleInputChange = (e) => {
    const { name, type } = e.target;
    let value;

    if (type === "file") {
      const file = e.target.files[0];
      value = file;

      if (file) {
        const previewURL = URL.createObjectURL(file);

        setFormValues((prevValues) => ({
          ...prevValues,
          [name]: previewURL,
        }));

        setEditedFields((prevEditedFields) => ({
          ...prevEditedFields,
          [name]: value,
        }));
      }
    } else {
      value = e.target.value;

      setFormValues((prevValues) => ({
        ...prevValues,
        [name]: value,
      }));

      setEditedFields((prevEditedFields) => ({
        ...prevEditedFields,
        [name]: value,
      }));
    }
  };

  const handlePhoneChange = (value, country, e, formattedValue) => {
    const country__Code = `+${country.dialCode}`;
    const phoneNumber = formattedValue?.replace(country__Code, "")?.trim();

    setFormValues((prevValues) => ({
      ...prevValues,
      phone: phoneNumber,
      country_code: country__Code,
    }));

    setEditedFields((prevEditedFields) => ({
      ...prevEditedFields,
      phone: phoneNumber,
      country_code: country__Code,
    }));
  };

  const validatePhoneNumber = (phoneNumber) => {
    const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
    return parsedPhoneNumber && parsedPhoneNumber.isValid();
  };

  const handleSaveChanges = async () => {
    const editedData = new FormData();
    const mapping = {
      name: "name",
      memberCode: "MemberCode",
      phone: "phone",
      email: "email",
      timezone: "TimeZone",
      gender: "sex",
      address: "Address",
      practicingHospital: "PractisingHospital",
      department: "Dept",
      professionalSummary: "Summary",
      documents: "Certificates",
      profilePicture: "ProfilePhoto",
    };

    Object.keys(editedFields).forEach((key) => {
      const mappedKey = mapping[key];
      if (mappedKey) {
        editedData.append(mappedKey, editedFields[key]);
      }
    });

    const stateName = formValues.State?.name
      ? formValues.State?.name
      : formValues.State;
    const cityName = formValues.City?.name
      ? formValues.City?.name
      : formValues.City;
    editedData.append("City", cityName + ", " + stateName);

    editedData.append(
      "Country",
      formValues.Country?.name ? formValues.Country?.name : formValues.Country
    );

    if ([...editedData.keys()].length > 0) {
      try {
        if ("phone" in editedFields) {
          const isValidPhoneNumber = validatePhoneNumber(
            `${formValues?.country_code}${formValues?.phone}`
          );

          if (!isValidPhoneNumber) {
            toast.error("Invalid phone number");
            return;
          }
        }

        if (doctor_Status === "pending" || doctor_Status === "Deactivated") {
          toast.error("To make changes, the profile should be approved...");
          setIsEditable(false);
          return;
        }
        let responseStatus = {
          message: "",
          toastStatus: "",
        };
        try {
          let adminUpdateExpertProfileResponse = await axiosAuth.put(
            `${process.env.NEXT_PUBLIC_UPDATE_DOCTOR_DATA_BY_ADMIN}${doctor_email}/?user_id=${admin_id}`,
            editedData
          );
          toast.success("Changes saved successfully");
        } catch (e) {
          console.error(e, "making the patch request failed");
          toast.error("Failed to save changes");
        }

        setIsEditable(false);
      } catch (error) {
        console.error(error);
        toast.error("Failed to save changes");

        setIsEditable(false);
      }
    }
  };

  const handleArticleModal = (item) => {
    setSingleArticles(item);
    setShowArticleModal(true);
  };

  const handleViewCertificate = (index) => {
    setCurrentCertificate(index);
    setShowCertificate(true);
  };

  const handleTestimonialModal = (item) => {
    setSingleTestimonial(item);
    setShowTestimonialModal(true);
  };

  const handleFeedbackModal = (item) => {
    setSingleFeedback(item);
    setShowFeedbackModal(true);
  };
  const handleCancelChanges = () => {
    setFormValues(initialFormValues);
    setEditedFields({});
    setIsEditable(false);
  };

  const addAchivementsRow = () => {
    setAchievements((prev) => [...prev, ["", ""]]); // Add a new empty achievement pair
    setAchievementInputGroups((prev) => prev + 1); // Increment the count

    setTimeout(() => {
      const innerScrollableElement = document.querySelector(
        ".achievement-custom-overflow"
      );
      if (innerScrollableElement) {
        innerScrollableElement.scrollTop = innerScrollableElement.scrollHeight; // Scroll to bottom
      }
    }, 0);
  };

  const deleteAchievementRow = (rowIndex) => {
    setAchievements((prev) => prev.filter((_, index) => index !== rowIndex)); // Remove the specific index
    setAchievementInputGroups((prev) => (prev > 1 ? prev - 1 : prev)); // Decrement count, ensuring minimum 1
  };

  const handleAchievementInputChange = (rowIndex, value) => {
    setAchievements((prevAchievements) => {
      const updatedAchievements = [...prevAchievements];
      updatedAchievements[rowIndex][0] = value;
      return updatedAchievements;
    });
  };
  const handleFileInputChange = (rowIndex, file) => {
    setAchievements((prevAchievements) => {
      const updatedAchievements = [...prevAchievements];
      updatedAchievements[rowIndex][1] = file;
      return updatedAchievements;
    });
  };

  const handleIconClick = (index) => {
    if (fileInputRefs.current[index]) {
      fileInputRefs.current[index].click();
    }
  };

  const handleViewClick = (pdf) => {
    setCurrentPdf(pdf);
    setShowOtherAchievementsDocumentModal(true);
  };

  const handleCloseModal = () => {
    setShowOtherAchievementsDocumentModal(false);
    setCurrentPdf(null);
  };

  const validResearchPapers = formValues?.ResearchPapers?.filter(
    (item) => item.url.trim() !== "" || item.title.trim() !== ""
  );

  
  return (
    <>
      {loading ? (
        <>
          <Loading />
        </>
      ) : (
        <>
          {showFeedbackModal && (
            <FeedbackModal
              showFeedbackModal={showFeedbackModal}
              setShowFeedbackModal={setShowFeedbackModal}
              singleFeedback={singleFeedback}
              showButtons={false}
            />
          )}
          {showArticleModal && (
            <ArticleModal
              showArticleModal={showArticleModal}
              setShowArticleModal={setShowArticleModal}
              singleArticle={singleArticle}
              showButtons={false}
            />
          )}
          {showTestimonialModal && (
            <TestimonialModal
              showTestimonialModal={showTestimonialModal}
              setShowTestimonialModal={setShowTestimonialModal}
              singleTestimonial={singleTestimonial}
              handleTestmonialApprovals={handleTestmonialApprovals}
              showButtons={false}
            />
          )}
          <div className="payment-back">
            <div className=" overflow-hidden">
              <div className="user-management-scroll overflow-auto">
                <div className="row">
                  <div className="col-sm-9 col-size">
                    <div className="row">
                      <div className="col-sm-6">
                        <div className="row">
                          <div className="col-sm-3 expert-image-section ">
                            <Image
                              src={formValues?.profilePicture || profileimg}
                              alt="doctor image"
                              width={100}
                              height={100}
                              className="object-fit-cover rounded-circle"
                            />{" "}
                            {isEditable ? (
                              <>
                                <input
                                  type="file"
                                  id="modalProfileImage"
                                  accept="image/*"
                                  name="profilePicture"
                                  onChange={handleInputChange}
                                  ref={fileInputRef}
                                  style={{ display: "none" }}
                                />

                                <MdOutlineEdit
                                  className="expert-editprofile"
                                  cursor={"pointer"}
                                  onClick={() =>
                                    fileInputRef.current &&
                                    fileInputRef.current.click()
                                  }
                                />
                              </>
                            ) : (
                              ""
                            )}
                          </div>
                          <div className="col-sm-9">
                            <div className="row">
                              <div className="col-sm-12">
                                <div className="row align-items-center">
                                  <div className="col-sm-4">
                                    {getStatusButton(
                                      expertProfileStatus?.approval_status,
                                      expertProfileStatus?.details,
                                      setShowRequestedForApprovalModal,
                                      setRejectModalAndDetails
                                    )}
                                  </div>
                                  <div className="col-sm-4 gx-0">
                                    <div className="ratings-div p-1 d-flex justify-content-evenly align-items-center">
                                      <span className="star-color">
                                        <span>{formValues?.rating}</span>
                                      </span>
                                      {renderStars(formValues?.rating).map(
                                        (star, index) => (
                                          <span
                                            key={index}
                                            style={{ cursor: "pointer" }}
                                            className="d-flex align-items-center"
                                          >
                                            {star}
                                          </span>
                                        )
                                      )}
                                    </div>
                                  </div>

                                  {isAdmin ||
                                  (isAdminChildAdmin && isEditPermissible) ? (
                                    <div className="col-sm-4">
                                      <button
                                        type="button"
                                        className="btn control-profile"
                                        onClick={
                                          isEditable
                                            ? handleCancelChanges
                                            : handleTakeControlClick
                                        }
                                      >
                                        {isEditable ? "Cancel" : "Edit Profile"}
                                      </button>
                                    </div>
                                  ) : null}
                                </div>
                              </div>
                              <div className="col-sm-12 mt-2">
                                <div className="profile-bg-control ">
                                  <span className="profile-active">
                                    Last Login{" "}
                                  </span>{" "}
                                  <span className="profile-last-edited mx-2">
                                    {formatDateTime(
                                      formValues?.last_login,
                                      formValues?.timezone
                                    )}
                                  </span>
                                  <button
                                    type="button"
                                    className="btn refresh-btn"
                                    onClick={() => fetchDoctorDetails()}
                                  >
                                    refresh
                                  </button>{" "}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="overflow-hidden">
                          <div className="content-scroll-profile overflow-auto">
                            <div className="row">
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="name"
                                    className="form-label-style"
                                  >
                                    Name
                                  </label>
                                  <input
                                    type="text"
                                    name="name"
                                    className="form-control form-fildes-read"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    value={capitalizeFullName(formValues?.name)}
                                    placeholder="Name"
                                  />
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="doctorcode"
                                    className="form-label-style"
                                  >
                                    Doctor Code
                                  </label>
                                  <input
                                    type="text"
                                    className="form-control form-fildes-read"
                                    name="memberCode"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    value={formValues?.memberCode}
                                    placeholder="Code"
                                  />
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="phone"
                                    className="form-label-style"
                                  >
                                    Phone Number
                                  </label>
                                  <div>
                                    <PhoneInput
                                      ref={(itiRef) => setIti(itiRef)}
                                      id="floatingInputPhone"
                                      className="input-form-modal-phone"
                                      required="required"
                                      name="phoneNumber"
                                      value={`${formValues?.country_code}${formValues?.phone}`}
                                      onChange={handlePhoneChange}
                                      disabled={!isEditable}
                                      inputStyle={{
                                        width: "100%",
                                        height: "auto",
                                        border: "1px solid #e3e3e3",
                                        boxShadow: "0px 3px 6px #00000029",
                                        background:
                                          "#ffffff 0% 0% no-repeat padding-box",
                                        fontSize: "14px",
                                      }}
                                      buttonStyle={{
                                        border: "1px solid #e3e3e3",
                                        borderRight: 0,
                                        background: "white",
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="email"
                                    className="form-label-style"
                                  >
                                    Email ID
                                  </label>
                                  <input
                                    type="email"
                                    name="email"
                                    className="form-control form-fildes-read"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    value={formValues?.email}
                                    placeholder="email"
                                  />
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="Time"
                                    className="form-label-style"
                                  >
                                    Timezone
                                  </label>
                                  <select
                                    className="form-select form-control form-fildes-read expert-select-timezone cancer-type-input"
                                    aria-label="Timezone"
                                    id="timezone"
                                    name="timezone"
                                    disabled={!isEditable}
                                    value={
                                      isEditable
                                        ? formValues.timezone
                                        : formValues?.timezone || ""
                                    }
                                    onChange={handleInputChange}
                                  >
                                    {timezones.map((timezone) => (
                                      <option key={timezone} value={timezone}>
                                        {timezone}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="gender"
                                    className="form-label-style"
                                  >
                                    Gender
                                  </label>
                                  <select
                                    className="form-select form-control form-fildes-read expert-gender-select cancer-type-input"
                                    aria-label="Gender"
                                    id="gender"
                                    name="gender"
                                    disabled={!isEditable}
                                    value={capitalizeFullName(
                                      formValues?.gender
                                    )}
                                    onChange={handleInputChange}
                                  >
                                    <option value="">Select Gender</option>
                                    <option value="Female">Female</option>
                                    <option value="Male">Male</option>
                                    <option value="Others">Others</option>
                                  </select>
                                </div>
                              </div>

                              <div className="row">
                                <label
                                  htmlFor="name"
                                  className="form-label-style mt-2"
                                >
                                  Location
                                </label>
                                <div className="col-sm-12">
                                  <div
                                    className="input-group input-group-border mb-2"
                                    style={{ width: "104%" }}
                                  >
                                    <span
                                      className="input-group-text label-col"
                                      style={{
                                        width: "100px",
                                        fontSize: "14px",
                                      }}
                                    >
                                      {" "}
                                      Country
                                    </span>

                                    <input
                                      type="text"
                                      className="form-control form-fildes-read1"
                                      style={{ fontSize: "14px" }}
                                      id="Country"
                                      name="Country"
                                      readOnly={!isEditable}
                                      value={
                                        formValues?.Country?.name
                                          ? formValues?.Country?.name
                                          : formValues?.Country
                                      }
                                      onChange={(e) =>
                                        setFormValues({
                                          ...formValues,
                                          Country: e.target.value,
                                          State: "No states",
                                          City: "No cities",
                                        })
                                      }
                                    />

                                    {isEditable && (
                                      <Select
                                        id="Country"
                                        options={Country.getAllCountries()}
                                        getOptionLabel={(options) => {
                                          return options["name"];
                                        }}
                                        getOptionValue={(options) => {
                                          return options["name"];
                                        }}
                                        value={formValues?.Country}
                                        onChange={(item) => {
                                          const selectedCountry = item;
                                          setFormValues({
                                            ...formValues,
                                            Country: selectedCountry,
                                            State: "No states",
                                            City: "No cities",
                                          });
                                        }}
                                      />
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="row">
                                <div className="col-sm-12">
                                  <div
                                    className="input-group input-group-border mb-2"
                                    style={{ width: "104%" }}
                                  >
                                    <span
                                      className="input-group-text label-col"
                                      style={{
                                        width: "100px",
                                        fontSize: "14px",
                                      }}
                                    >
                                      {" "}
                                      State
                                    </span>

                                    <input
                                      type="text"
                                      className="form-control form-fildes-read1"
                                      style={{ fontSize: "14px" }}
                                      id="Country"
                                      name="State"
                                      readOnly={!isEditable}
                                      value={
                                        formValues?.State?.name
                                          ? formValues?.State?.name
                                          : formValues?.State
                                      }
                                      onChange={(e) =>
                                        setFormValues({
                                          ...formValues,
                                          State: e.target.value,
                                        })
                                      }
                                    />

                                    {isEditable && (
                                      <Select
                                        id="country"
                                        options={State?.getStatesOfCountry(
                                          formValues?.Country?.isoCode
                                        )}
                                        getOptionLabel={(options) => {
                                          return options["name"];
                                        }}
                                        getOptionValue={(options) => {
                                          return options["name"];
                                        }}
                                        value={formValues?.State}
                                        onChange={(item) => {
                                          const selectedState = item;
                                          setFormValues({
                                            ...formValues,
                                            State: selectedState,
                                          });
                                        }}
                                      />
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="row">
                                <div className="col-sm-12">
                                  <div
                                    className="input-group input-group-border mb-1"
                                    style={{ width: "104%" }}
                                  >
                                    <span
                                      className="input-group-text label-col"
                                      style={{
                                        width: "100px",
                                        fontSize: "14px",
                                      }}
                                    >
                                      {" "}
                                      City
                                    </span>

                                    <input
                                      type="text"
                                      className="form-control form-fildes-read1"
                                      style={{ fontSize: "14px" }}
                                      id="Country"
                                      name="City"
                                      readOnly={!isEditable}
                                      value={
                                        formValues?.City?.name
                                          ? formValues?.City?.name
                                          : formValues?.City
                                      }
                                      onChange={(e) =>
                                        setFormValues({
                                          ...formValues,
                                          City: e.target.value,
                                        })
                                      }
                                    />

                                    {isEditable && (
                                      <Select
                                        options={City.getCitiesOfState(
                                          formValues?.State?.countryCode,
                                          formValues?.State?.isoCode
                                        )}
                                        getOptionLabel={(options) => {
                                          return options["name"];
                                        }}
                                        getOptionValue={(options) => {
                                          return options["name"];
                                        }}
                                        value={formValues?.City}
                                        onChange={(item) => {
                                          const selectedCity = item;
                                          setFormValues({
                                            ...formValues,
                                            City: selectedCity,
                                          });
                                        }}
                                      />
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="form-group mt-2">
                                <label
                                  htmlFor="address"
                                  className="form-label-style"
                                >
                                  Address
                                </label>
                                <textarea
                                  className="form-control text-form-fileds"
                                  id="address"
                                  rows="3"
                                  onChange={handleInputChange}
                                  name="address"
                                  readOnly={!isEditable}
                                  value={formValues?.address}
                                  placeholder="Add your address here..."
                                ></textarea>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="name"
                                    className="form-label-style mb-1"
                                  >
                                    Practicing Hospital
                                  </label>
                                  <input
                                    type="text"
                                    className="form-control form-fildes-read"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    name="practicingHospital"
                                    value={formValues?.practicingHospital}
                                    placeholder="Eg.St Bartholomews"
                                  />
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="doctorcode"
                                    className="form-label-style mb-1"
                                  >
                                    Department
                                  </label>
                                  <input
                                    type="text"
                                    name="department"
                                    className="form-control form-fildes-read"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    value={formValues?.department}
                                    placeholder="Eg. Oncology"
                                  />
                                </div>
                              </div>

                              <div className="form-group mt-2">
                                <label
                                  htmlFor="address"
                                  className="form-label-style"
                                >
                                  Address
                                </label>
                                <textarea
                                  className="form-control text-form-fileds"
                                  id="address"
                                  rows="3"
                                  onChange={handleInputChange}
                                  name="address"
                                  readOnly={!isEditable}
                                  value={formValues?.address}
                                  style={{ resize: "none" }}
                                  placeholder="Add your address here..."
                                ></textarea>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="name"
                                    className="form-label-style mb-1"
                                  >
                                    Practicing Hospital
                                  </label>
                                  <input
                                    type="text"
                                    className="form-control form-fildes-read"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    name="practicingHospital"
                                    value={formValues?.practicingHospital}
                                    placeholder="Eg.St Bartholomews"
                                  />
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <div className="form-group mt-2">
                                  <label
                                    htmlFor="doctorcode"
                                    className="form-label-style mb-1"
                                  >
                                    Department
                                  </label>
                                  <input
                                    type="text"
                                    name="department"
                                    className="form-control form-fildes-read"
                                    onChange={handleInputChange}
                                    readOnly={!isEditable}
                                    value={formValues?.department}
                                    placeholder="Eg. Oncology"
                                  />
                                </div>
                              </div>

                              <div>
                                <p className="grey-text mb-1 mt-2">
                                  Published Papers
                                </p>

                                {validResearchPapers &&
                                Array.isArray(validResearchPapers) &&
                                validResearchPapers?.length > 0 ? (
                                  <div className="overflow-hidden">
                                    <div className="content-scroll-3 overflow-auto">
                                      {validResearchPapers.map(
                                        (item, index) => (
                                          <div
                                            key={index}
                                            className="bg-color-white"
                                          >
                                            <div className="d-flex align-items-center">
                                              <p
                                                className="mb-0"
                                                style={{ fontSize: "14px" }}
                                              >
                                                {item.url}
                                              </p>
                                            </div>
                                          </div>
                                        )
                                      )}
                                    </div>
                                  </div>
                                ) : (
                                  <div className="text-center border rounded">
                                    <p className="my-5">
                                      No Research Papers Available
                                    </p>
                                  </div>
                                )}
                              </div>

                              <SocialMediaLinks
                                setUserDetails={setFormValues}
                                userDetails={formValues}
                                editMode={isEditable}
                              />
                              <div>
                                <p className="grey-text mb-1 ">
                                  Carrier Achievements
                                </p>
                                <div className="col-sm d-flex justify-content-end align-items-end">
                                  {isEditable && (
                                    <FiPlusCircle
                                      size={25}
                                      color="#9426B2"
                                      cursor={"pointer"}
                                      onClick={addAchivementsRow}
                                    />
                                  )}
                                </div>
                                <div className="overflow-hidden">
                                  <div className="content-scroll-3 overflow-auto">
                                    <>
                                      {Array.from(
                                        { length: achivementInputGroups },
                                        (_, index) => {
                                          return (
                                            <React.Fragment key={index}>
                                              <div className="input-group input-group-border d-flex justify-content-between align-items-center my-2">
                                                {/* Title Input with Form.Control */}
                                                <div
                                                  className={`d-flex justify-content-center align-items-center border-1 border-danger other-achievement-name`}
                                                  style={{
                                                    width:
                                                      isEditable === false
                                                        ? "85%"
                                                        : "60%",
                                                  }}
                                                >
                                                  <InputGroup className="mb-1">
                                                    <InputGroup.Text id="basic-addon1">
                                                      Title
                                                    </InputGroup.Text>
                                                    <Form.Control
                                                      placeholder={`${
                                                        achievements[index]?.[1]
                                                          ? "Enter your Achievements"
                                                          : "No Achievements Found."
                                                      }`}
                                                      aria-label="Achievement Title"
                                                      aria-describedby="basic-addon1"
                                                      readOnly={!isEditable}
                                                      value={
                                                        achievements[
                                                          index
                                                        ]?.[0] || ""
                                                      }
                                                      onChange={(e) =>
                                                        handleAchievementInputChange(
                                                          index,
                                                          e.target.value
                                                        )
                                                      }
                                                    />
                                                  </InputGroup>
                                                </div>

                                                {/* File Upload Section */}
                                                <div className="d-flex justify-content-center align-items-center">
                                                  <div className="d-flex justify-content-center align-items-center">
                                                    <span className="input-group-text label-col overflow-hidden text-nowrap p-0">
                                                      {isEditable ? (
                                                        <>
                                                          {achievements[
                                                            index
                                                          ]?.[1]
                                                            ? "Document Uploaded"
                                                            : "Upload document"}
                                                          &nbsp;
                                                          {!achievements[
                                                            index
                                                          ]?.[1] && (
                                                            <FaFileUpload
                                                              className="fs-4"
                                                              onClick={() =>
                                                                handleIconClick(
                                                                  index
                                                                )
                                                              }
                                                              style={{
                                                                cursor:
                                                                  "pointer",
                                                              }}
                                                            />
                                                          )}
                                                        </>
                                                      ) : achievements[
                                                          index
                                                        ]?.[1] ? (
                                                        <div className="d-flex align-items-center">
                                                          <button
                                                            className="bg-transparent border-0 text-black fw-bold custom-font-mobile"
                                                            onClick={() =>
                                                              handleViewClick(
                                                                achievements[
                                                                  index
                                                                ][1]
                                                              )
                                                            }
                                                          >
                                                            View
                                                          </button>
                                                        </div>
                                                      ) : null}
                                                    </span>

                                                    {isEditable && (
                                                      <Form.Control
                                                        type="file"
                                                        className="form-control d-none"
                                                        id={`achievement-file-${index}`}
                                                        name={`achievementFile${index}`}
                                                        accept=".pdf"
                                                        onChange={(e) =>
                                                          handleFileInputChange(
                                                            index,
                                                            e.target.files[0]
                                                          )
                                                        }
                                                        ref={(el) =>
                                                          (fileInputRefs.current[
                                                            index
                                                          ] = el)
                                                        }
                                                      />
                                                    )}
                                                  </div>
                                                  {isEditable &&
                                                    achivementInputGroups >
                                                      1 && (
                                                      <div className="px-2">
                                                        <button
                                                          type="button"
                                                          className="btn btn-link p-0"
                                                          onClick={() =>
                                                            deleteAchievementRow(
                                                              index
                                                            )
                                                          }
                                                        >
                                                          <MdDelete
                                                            color="#D80445"
                                                            className="fs-4"
                                                          />
                                                        </button>
                                                      </div>
                                                    )}
                                                </div>
                                              </div>
                                            </React.Fragment>
                                          );
                                        }
                                      )}
                                    </>
                                  </div>
                                </div>
                              </div>

                              <div>
                                <p className="grey-text mb-1 mt-3">
                                  Experience Summary
                                </p>
                                <div className="overflow-hidden">
                                  <div className="content-scroll-3 overflow-auto">
                                    {formValues?.ExperienceSummary?.length >
                                    0 ? (
                                      <>
                                        {formValues?.ExperienceSummary?.map(
                                          (item, index) => {
                                            const isIncomplete =
                                              !item.role ||
                                              !item.organisation_name ||
                                              !item.start_date ||
                                              !item.end_date ||
                                              !item.summary;

                                            return (
                                              <React.Fragment key={index}>
                                                <div>
                                                  {isIncomplete ? (
                                                    <div className="border rounded">
                                                      <p className="my-5 text-center">
                                                        Experience Not Added
                                                      </p>
                                                    </div>
                                                  ) : (
                                                    <>
                                                      <div className="d-flex justify-content-between">
                                                        <p
                                                          style={{
                                                            fontSize: "14px",
                                                          }}
                                                          className="mb-1 fw-medium"
                                                        >
                                                          <TbPointFilled />{" "}
                                                          {
                                                            item.organisation_name
                                                          }
                                                        </p>
                                                        <p
                                                          className="fw-medium mb-1 exp-date-range"
                                                          style={{
                                                            fontSize: "12px",
                                                          }}
                                                        >
                                                          {item.start_date} to{" "}
                                                          {item.end_date}
                                                        </p>
                                                      </div>
                                                      <p
                                                        className="ms-3 lh-sm"
                                                        style={{
                                                          fontSize: "14px",
                                                        }}
                                                      >
                                                        {item.summary}
                                                      </p>
                                                    </>
                                                  )}
                                                </div>
                                              </React.Fragment>
                                            );
                                          }
                                        )}
                                      </>
                                    ) : (
                                      <div className="border rounded">
                                        <p className="my-5 text-center">
                                          No Experience Summary Available
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                              {isEditable && (
                                <div className=" ">
                                  <button
                                    type="button"
                                    className={`${
                                      isEditable
                                        ? "btn expert-btn-save-changes-color-change"
                                        : "btn expert-btn-save-changes"
                                    } rounded`}
                                    onClick={handleSaveChanges}
                                  >
                                    Save Changes
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-sm-6 bg-grey">
                        <div className="form-group mt-2">
                          <label htmlFor="address" className="form-label-style">
                            Profile Summary
                          </label>
                          <textarea
                            className="form-control text-form-fileds"
                            id="address"
                            rows="5"
                            readOnly={!isEditable}
                            onChange={handleInputChange}
                            name="professionalSummary"
                            value={formValues?.professionalSummary}
                            style={{ resize: "none" }}
                            placeholder="Add your professional summary here..."
                          ></textarea>
                        </div>

                        <div className="form-group mt-2">
                          <label htmlFor="address" className="form-label-style">
                            Certificates
                          </label>

                          {formValues &&
                          Array.isArray(formValues?.documents) ? (
                            <div className="overflow-hidden">
                              <div
                                className={`${
                                  formValues?.documents?.length > 0
                                    ? "d-flex flex-wrap bg-white overflow-auto expertCertScroll"
                                    : ""
                                }`}
                              >
                                {formValues?.documents?.length > 0 ? (
                                  <>
                                    {formValues?.documents?.map(
                                      (certificate, index) => (
                                        <div
                                          key={index}
                                          className="bg-white py-1 px-1"
                                        >
                                          <span
                                            style={{
                                              cursor: "pointer",
                                              color: "#8107D1",
                                            }}
                                            // onClick={handleViewClick}
                                            onClick={() =>
                                              handleViewCertificate(index)
                                            }
                                            className=" badge rounded-pill custom-bg-light purple-text fw-light me-2"
                                          >
                                            <FaFile
                                              style={{
                                                fontSize: "15px",
                                                color: "#8107D1",
                                              }}
                                            />
                                            {certificate
                                              ? `certificate ${index + 1}`
                                              : "certificate"}
                                          </span>

                                          {currentCertificate === index && (
                                            <PdfViewerModal
                                              show={showCertificate}
                                              handleClosePdfModal={
                                                handleCloseInvoiceModal
                                              }
                                              document={certificate}
                                              index={index}
                                            />
                                          )}
                                        </div>
                                      )
                                    )}
                                  </>
                                ) : (
                                  <div className="border rounded">
                                    <p className="text-center my-2">
                                      No Certificates Uploaded
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="border rounded">
                              <p className="text-center my-5">
                                No Certificates Found
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="mt-2">
                          <p className="grey-text mb-1 mt-2">
                            Recent Testimonials
                          </p>
                          <div className=" overflow-hidden">
                            <div
                              className={`${
                                approvedTestimonials?.length > 0
                                  ? "content-scroll-3 overflow-auto"
                                  : ""
                              }`}
                            >
                              {approvedTestimonialsLoading === false ? (
                                <>
                                  {" "}
                                  {approvedTestimonials?.length === 0 ? (
                                    <div className="fs-5 d-flex align-items-center justify-content-center experts-no-articles-found">
                                      <PiFolderNotchOpenFill
                                        color={"#8107d1"}
                                        size={30}
                                      />
                                      &nbsp; No Testimonial Upoaded
                                    </div>
                                  ) : (
                                    <>
                                      {approvedTestimonials?.map(
                                        (item, index) => (
                                          <div
                                            key={index}
                                            className="bg-color-white mb-2"
                                          >
                                            <div className="d-flex align-items-center">
                                              <div className="col-sm-8 mb-0 d-flex align-items-center">
                                                <p className="ps-2 mb-0 custom-font-size">
                                                  {item?.CancerTreatmentType}
                                                </p>
                                              </div>
                                              <div className="col-sm-3 mb-0 d-flex">
                                                <p className="purple-text mb-0 pe-2">
                                                  {formatDate(
                                                    item?.CurrentTime
                                                  )}
                                                </p>
                                              </div>
                                              <div className="col-sm-1 gx-0">
                                                <button
                                                  type="button"
                                                  className="btn btn-transparent  purple-btn-transparent p-0"
                                                  onClick={() =>
                                                    handleTestimonialModal(item)
                                                  }
                                                >
                                                  View
                                                </button>
                                              </div>
                                            </div>
                                          </div>
                                        )
                                      )}
                                    </>
                                  )}
                                </>
                              ) : (
                                renderPlaceholders(2)
                              )}
                            </div>
                          </div>

                          <p className="grey-text mb-1 mt-2">Recent Articles</p>
                          <div className=" overflow-hidden">
                            <div
                              className={`${
                                articles.length !== 0
                                  ? "content-scroll-3 overflow-auto"
                                  : ""
                              }`}
                            >
                              {articleLoading === false ? (
                                <>
                                  {articles && articles?.length === 0 ? (
                                    <div className="fs-5 d-flex align-items-center justify-content-center experts-no-articles-found">
                                      <PiFolderNotchOpenFill
                                        color={"#8107d1"}
                                        size={30}
                                      />
                                      &nbsp; No Articles Uploaded
                                    </div>
                                  ) : (
                                    <>
                                      {articles &&
                                        articles?.map((item, index) => (
                                          <div
                                            key={index}
                                            className="bg-color-white mb-2"
                                          >
                                            <div className="d-flex align-items-center">
                                              <div className="col-sm-8 mb-0 d-flex align-items-center">
                                                <FaFile
                                                  style={{
                                                    fontSize: "25px",
                                                    color: "#8107D1",
                                                  }}
                                                />
                                                <p className="ps-2 mb-0 custom-font-size-recents single-line">
                                                  {item.blog_details?.BlogTitle}
                                                </p>
                                              </div>
                                              <div className="col-sm-3 mb-0 d-flex">
                                                <p className="purple-text mb-0 pe-2">
                                                  {formatDate(
                                                    item?.blog_details
                                                      ?.BlogDateTime
                                                  )}
                                                </p>
                                              </div>
                                              <div className="col-sm-1 gx-0">
                                                <button
                                                  type="button"
                                                  className="btn btn-transparent purple-btn-transparent p-0"
                                                  onClick={() =>
                                                    handleArticleModal(item)
                                                  }
                                                >
                                                  View
                                                </button>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                    </>
                                  )}
                                </>
                              ) : (
                                renderPlaceholders(2)
                              )}
                            </div>
                          </div>

                          <p className="grey-text mb-1 mt-2">Recent Feedback</p>
                          <div className=" overflow-hidden">
                            <div
                              className={`${
                                approvedFeedback?.length > 0
                                  ? "content-scroll-3 overflow-auto"
                                  : ""
                              }`}
                            >
                              {approvedFeedbackLoading === false ? (
                                <>
                                  {approvedFeedback?.length === 0 ? (
                                    <div className="fs-5 d-flex align-items-center justify-content-center experts-no-articles-found">
                                      <PiFolderNotchOpenFill
                                        color={"#8107d1"}
                                        size={30}
                                      />
                                      &nbsp; No Records Found
                                    </div>
                                  ) : (
                                    <>
                                      {approvedFeedback?.map((item, index) => (
                                        <div
                                          key={index}
                                          className="bg-color-white mb-2"
                                        >
                                          <div className="d-flex align-items-center">
                                            <div className="col-sm-8 mb-0 d-flex align-items-center">
                                              {item?.doctor_photo ? (
                                                <Image
                                                  src={
                                                    item?.doctor_photo ||
                                                    profile
                                                  }
                                                  alt="feedback"
                                                  height={35}
                                                  width={35}
                                                  className="next_image object-fit-cover"
                                                />
                                              ) : (
                                                <Image
                                                  src="/images/profile.png"
                                                  alt="feedback"
                                                  height={35}
                                                  width={35}
                                                  className="next_image object-fit-cover"
                                                />
                                              )}
                                              <p className="ps-2 mb-0 custom-font-size-recents">
                                                {item?.FeedbackCategory}
                                              </p>
                                            </div>
                                            <div className="col-sm-3 mb-0 d-flex">
                                              <p className="purple-text mb-0 pe-2">
                                                {formatDate(item?.CurrentTime)}
                                              </p>
                                            </div>
                                            <div className="col-sm-1 gx-0">
                                              <button
                                                type="button"
                                                className="btn btn-transparent purple-btn-transparent p-0"
                                                onClick={() =>
                                                  handleFeedbackModal(item)
                                                }
                                              >
                                                View
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                    </>
                                  )}
                                </>
                              ) : (
                                renderPlaceholders(2)
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-sm-3">
                    <CalenderFilter />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {showRequestedForApprovalModal && (
            <ProfileReactivationModal
              showRequestedForApprovalModal={showRequestedForApprovalModal}
              setShowRequestedForApprovalModal={
                setShowRequestedForApprovalModal
              }
              expertProfileStatus={expertProfileStatus}
              fetchDoctorDetails={fetchDoctorDetails}
            />
          )}
          {RejectModalAndDetails?.rejectShowModal && (
            <RejectionModal
              RejectModalAndDetails={RejectModalAndDetails}
              setRejectModalAndDetails={setRejectModalAndDetails}
            />
          )}
        </>
      )}

      <PdfViewerModal
        show={showOtherAchievementsDocumentModal}
        handleClosePdfModal={handleCloseModal}
        document={carrierAchievement}
      />
    </>
  );
};

export default MainProfile;
