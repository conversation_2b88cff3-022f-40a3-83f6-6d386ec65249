'use client'
import React from "react";
import ChildMainProfile from "../../components/childAdminIndividual/ChildMainProfile";
import withAuth from "../../withAuth/withAuth";
import "../../components/childAdminIndividual/childadminindividual.css";
const Page = () => {
  return (
    <div>
      <main>
        <div className="container-fluid">
          <div className="row">
            <div className="col-sm-1 navbar-parent">{/* <Navbar/> */}</div>
            <div className="col-sm-11">
              <ChildMainProfile />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default withAuth(Page);
