"use client";
import React from "react";
import { FaFileCircleCheck } from "react-icons/fa6";
import { MdReviews } from "react-icons/md";
import { formatDate } from "../db";

const AllReviews = ({
  handlePreviewView,
  loading,
  reviews,
  handleDeleteReview,
  selectedValue,
  handleChange,
  setShowNoPreviewText,
}) => {
  return (
    <>
      <div className=" mt-2">
        {loading === false ? (
          <>
            {!reviews || (Array.isArray(reviews) && reviews.length === 0) ? (
              <>
                <h3
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "500px" }}
                >
                  <FaFileCircleCheck className="podcast-icon " />
                  &nbsp;
                  <span className="text-secondary"> No Reviews Available</span>
                </h3>
              </>
            ) : (
              <>
                <div className="col-2 mb-2">
                  <select
                    className="form-select form-select-sm custom-form-select"
                    aria-label=".form-select-sm example"
                    value={selectedValue}
                    onChange={handleChange}
                  >
                    <option selected value="all">
                      Select
                    </option>
                    <option value="0">Under Review</option>
                    <option value="1">Approved</option>
                    <option value="2">Rejected</option>
                  </select>
                </div>
                <div className=" list-of-articles">
                  {Array.isArray(reviews) &&
                    reviews?.map((item, index) => {
                      return (
                        <div key={index} className="bg-color-border mb-2">
                          <div className="row">
                            <div className="col-sm-6 mb-0 d-flex justify-content-start align-items-center">
                              <MdReviews
                                style={{
                                  fontSize: "30px",
                                  color: "#8107D1",
                                }}
                              />
                              <p className="ps-2 mb-0 custom-font-size">
                                {" "}
                                {item?.Review.split(" ").slice(0, 4).join(" ")}
                              </p>
                            </div>
                            <div className="col-sm-4 mb-0 d-flex justify-content-evenly align-items-center">
                              <p className="purple-text mb-0 pe-2">
                                {formatDate(item.ReviewGenTime)}
                              </p>
                              <p
                                className={`blog-approval-status-${
                                  item.ReviewStatus === 1
                                    ? "under-review"
                                    : item.ReviewStatus === 2
                                    ? "approved"
                                    : item.ReviewStatus === 3
                                    ? "rejected"
                                    : ""
                                } mb-0`}
                              >
                                {item.ReviewStatus === 1
                                  ? "Under Review"

                                  : item.ReviewStatus === 2
                                  ? "Approved"
                                  : item.ReviewStatus === 3
                                  ? "Rejected"
                                  : ""}
                              </p>
                            </div>

                            <div className="col-sm-1 gx-0 d-flex justify-content-center align-items-center">
                              <button
                                type="button"
                                className="btn btn-transparent red-text fw-bold p-0"
                                onClick={() => handleDeleteReview(item?.id)}
                              >
                                Delete
                              </button>
                            </div>

                            <div className="col-sm-1 d-flex justify-content-center align-items-center">
                              <button
                                type="button"
                                className="btn btn-transparent purple-text fw-bold p-0"
                                onClick={() => {
                                  handlePreviewView(item);
                                  setShowNoPreviewText(false);
                                }}
                              >
                                View
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </>
            )}
          </>
        ) : (
          renderPlaceholders()
        )}
      </div>
    </>
  );
};

export default AllReviews;
