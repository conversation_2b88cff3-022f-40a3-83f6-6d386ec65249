"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import MainProfile from "./MainProfile";
import UserAppointments from "./UserApointments";
import UserUploads from "./UserUploads";
import UserCommunication from "./UserCommunication";
import PatientPayment from "./PatientPayment";
import PatientHead from "./PatientHead";
import PatientRefund from "./PatientRefund";
import { useSession } from "next-auth/react";

const ToggleUserPatient = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialTab = searchParams.get("tab");
  const [activeTab, setActiveTab] = useState(initialTab || "mainprofile");
  const pathname = usePathname();
  const { data: session } = useSession();
  const email = params.user_id[1];
  const id = params.user_id[0];

  useEffect(() => {
    const tab = searchParams.get("tab");
    setActiveTab(tab || "mainprofile");
  }, [id, email, router, searchParams]);

  const handleTabChange = (tab) => {
    const updatedPath = `${pathname}?tab=${tab}`;
    router.replace(updatedPath, { shallow: true });
    setActiveTab(tab);
  };

  return (
    <>
      <PatientHead />
      <div className="d-inline-flex gap-1 buttons-row mb-0">
        <button
          className={`btn grey-btn ${
            activeTab === "mainprofile" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("mainprofile")}
        >
          Main Profile
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "appointments" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("appointments")}
        >
          Appointments
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "uploads" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("uploads")}
        >
          Uploads
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "communication" ? "activeExpertsTab" : "" 
          }`}
          onClick={() => handleTabChange("communication")}
        >
          Communication
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "payment" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("payment")}
        >
          Payment
        </button>
        <button
          className={`btn grey-btn ${
            activeTab === "refund" ? "activeExpertsTab" : ""
          }`}
          onClick={() => handleTabChange("refund")}
        >
          Refunds
        </button>
      </div>

      {activeTab === "mainprofile" && <MainProfile email={email} id={id} />}
      {activeTab === "appointments" && <UserAppointments id={id} />}
      {activeTab === "uploads" && <UserUploads email={email} id={id} />}
      {activeTab === "communication" && <UserCommunication />}
      {activeTab === "payment" && <PatientPayment id={id} />}
      {activeTab === "refund" && <PatientRefund id={id} />}
    </>
  );
};

export default ToggleUserPatient;
