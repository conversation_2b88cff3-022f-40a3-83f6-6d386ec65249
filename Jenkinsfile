pipeline {
    agent any
    stages {
        stage('Build-dev') {
            when { branch 'dev'}
            steps {    
                sh 'echo "Start Development server deployment"'
                script{
                 app = docker.build("cu_admin_dev")
                }
            }   
        }
        stage('Deploy-dev') {
            when { branch 'dev'}
            steps {
                script{
                docker.withRegistry('https://671988968062.dkr.ecr.ap-south-1.amazonaws.com', 'ecr:ap-south-1:aws-dev-access') {
                app.push("${env.BUILD_NUMBER}")
                app.push("latest")
                    }
                }
                sh "docker system prune -f"
            }
                 }

        stage('Login to remote host-dev') {
            when { branch 'dev'}
            steps {
           sshagent(['***********']) {
           sh 'ssh -o StrictHostKeyChecking=no ubuntu@*********** "sh /home/<USER>/script/cu_admin_dev-start.sh"'
          // sh 'scp -r $WORKSPACE/* ubuntu@***********:/home/<USER>/cu_doctor_dev/'
          // sh 'ssh -o StrictHostKeyChecking=no ubuntu@*********** "sh /home/<USER>/script/pm2_cu_doctor_dev-restart.sh"'
           // sh 'ssh -o StrictHostKeyChecking=no scp *.* "ubuntu@***********:/home/<USER>/cu_frontend/cu-development/"'
           }
      }
    }
    }


post {
             always {
              cleanWs(cleanWhenNotBuilt: false,
                    deleteDirs: true,
                    disableDeferredWipeout: true,
                    notFailBuild: true,
                    patterns: [[pattern: '.gitignore', type: 'INCLUDE'],
                               [pattern: '.propsfile', type: 'EXCLUDE']])
        }


            }
        }
