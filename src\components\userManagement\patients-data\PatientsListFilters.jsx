import React, { useCallback, useEffect, useRef, useState } from "react";
import { FaCalendar, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import _ from "lodash";
import { GrSearch } from "react-icons/gr";

const PatientsListFilters = ({
  searchQuery,
  setSearchQuery,
  fetchData,
  adminId,
  startDate,
  endDate,
  expertSelect,
  setExpertSelected,
  setStatus,
  status,
  setStartDate,
  setEndDate,
  setCurrent_Page,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const searchMenu = useRef(null);

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handleDateFilterChange = (start, end) => {
    setStartDate(start);
    setEndDate(end);
    if (start && end) {
      setShowPicker(false);
    }
  };

  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  const handleClearFilter = (e) => {
    e.stopPropagation();
    handleDateFilterChange(null, null); // Clear the date filter
  };
  const handleClearQueryFilter = () => {
    setSearchQuery("");
    setCurrent_Page(1);
  };

  return (
    <div className="row">
      <div className="col-sm-12">
        <div className="grey-bg">
          <div className="row ">
            <div className="col-sm-1 d-flex justify-content-center align-items-center">
              <p className="heading mb-0">Filters</p>
            </div>
            <div className="col-sm-4">
              <div className="input-group search-input-patient">
                <input
                  type="text"
                  className="form-control search-input-focus custom-placeholder"
                  style={{
                    borderRight: "none",
                    fontSize: "12px",
                    border: "none",
                  }}
                  placeholder="Search by Name or Id"
                  aria-label="Recipient's username"
                  aria-describedby="button-addon2"
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setCurrent_Page(1);
                  }}
                  value={searchQuery}
                />
                {!searchQuery && (
                  <span
                    className="input-group-text custom-search-icon"
                    id="button-addon2"
                    style={{ borderRadius: "5px" }}
                  >
                    <GrSearch style={{ color: "#8107d1" }} />
                  </span>
                )}
                {searchQuery && (
                  <span
                    style={{ zIndex: 10 }}
                    className="cancel-patient-search-btn"
                  >
                    <FaTimes
                      style={{ fontSize: "13px" }}
                      className="cross-icon-calendar"
                      onClick={handleClearQueryFilter}
                    />
                  </span>
                )}
              </div>
            </div>
            <div className="col-sm-4">
              <div ref={searchMenu}>
                <span
                  className="date-filter-patient d-flex search-shadow-box"
                  onClick={handleCalendarClick}
                >
                  {startDate
                    ? `${startDate.toLocaleDateString()} - ${
                        endDate ? endDate.toLocaleDateString() : ""
                      }`
                    : "Select Date"}
                  <span className="calendar-icon-patient ms-auto calender-icon-place">
                    {startDate ? (
                      <FaTimes
                        className=" cross-icon-calendar"
                        onClick={handleClearFilter}
                      />
                    ) : (
                      <FaCalendar className=" calender-icon-calendar" />
                    )}
                  </span>
                </span>

                {showPicker && (
                  <div style={{ position: "absolute", zIndex: 1 }}>
                    <DatePicker
                      selected={startDate}
                      startDate={startDate}
                      endDate={endDate}
                      selectsRange
                      inline
                      showTimeSelect={false} // Disable time selection
                      onChange={(dates) => {
                        handleDateFilterChange(dates[0], dates[1]);
                        setCurrent_Page(1);
                      }}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="col-sm-3">
              <select
                className="form-select custom-form-select select-font"
                aria-label=".form-select-sm example"
                value={status}
                onChange={(e) => {
                  setStatus(e.target.value);
                  setCurrent_Page(1);
                }}
              >
                <option value="">By Status </option>
                <option value="Approved">Active</option>
                <option value="Deactivated">Deactivated</option>
                <option value="Deleted">Deleted</option>
                <option value="self_deleted">Self Deleted</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientsListFilters;
