import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import {
  capitalizeFullName,
  formatCustomDate,
  formatDate,
  timeDifference,
} from "../../../utils/helperfunction";
import { toast } from "react-toastify";
import FeedbackPlaceholder from "./FeedbackPlaceholder";
import CustomPagination from "../../CustomPagination/CustomPagination";
import NoDataFound from "@/components/noDataFound/NoDataFound";

const AllFeedback = ({ userId }) => {
  const [loading, setLoading] = useState(true);
  const [allFeedbacks, setAllFeedbacks] = useState([]);
  const [current_page, setCurrent_Page] = useState(1);
  const [noDtaError, setNoDtaError] = useState(false);
  const [total_Pages, setTotal_Pages] = useState([]);
  const axiosAuth = useAxiosAuth();

  const fetchAllFeedbacks = useCallback(
    async (page) => {
      try {
        setLoading(true);
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_FEEDBACK}all/2/?page=${page}`
        );
        const items = response?.data?.items;
        const totalPages = response?.data?.total_pages;
        setAllFeedbacks(items);
        setTotal_Pages(totalPages);
      } catch (err) {
        console.log("error in fetching feedbacks", err);
      } finally {
        setLoading(false);
      }
    },
    [axiosAuth]
  );
  useEffect(() => {
    fetchAllFeedbacks(current_page);
  }, [current_page, fetchAllFeedbacks]);

  const handleFeedbackApprovals = async (status, feedbackId) => {
    let blog_status;
    if (status === "reject") {
      blog_status = 0;

      try {
        const willCancel = await Swal.fire({
          title: "Are you sure?",
          text: "Once rejected, you will not be able to recover this Feedback",
          icon: "question",
          showCancelButton: true,
          confirmButtonText: "Yes, reject it!",
          cancelButtonText: "Cancel",
          confirmButtonColor: "#8107D1",
          cancelButtonColor: "#FF2E2E",
        });

        if (!willCancel.isConfirmed) {
          return; // Do nothing if the user cancels the confirmation
        }
      } catch (error) {
        console.error(error);
        return; // Exit the function if there's an error with Swal
      }
    } else if (status === "approve") {
      blog_status = 1;
    }

    try {
      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_APPROVE_REJECT_FEEDBACK}${feedbackId}/?user_id=${userId}`,
        { status: blog_status }
      );

      if (response?.data?.status === 1) {
        toast.success(`Feedback Approved Successfully.`);
        setShowFeedbackModal(false);
      } else if (response?.data?.status === 0) {
        toast.error(`Feedback Rejected Successfully`);
      }

      fetchAllFeedbacks(1);
    } catch (error) {
      console.error(error);
      setNoDtaError(true);
    }
  };

  if (loading) {
    return <FeedbackPlaceholder />;
  }

  return (
    <>
      <div className="mt-2 ms-2 text-secondary feedback-note">
        <b style={{ color: "#8107d1" }}> Note:</b> Feedback refers to opinions
        or insights shared by experts on the expert platform.
      </div>
      <div className="overflow-hidden ">
        <div className="overflow-auto allApproval-tab-scroll ">
          {noDtaError ? (
            <NoDataFound />
          ) : allFeedbacks?.length > 0 ? (
            allFeedbacks &&
            allFeedbacks?.map((feedback) => (
              <div key={feedback?.id} className="col-sm-6 p-3">
                <div className="introvideo-bg p-3">
                  <div className="row">
                    <div className="d-flex justify-content-between align-items-center ">
                      <div className="d-flex align-items-center">
                        <Image
                          src={feedback?.doctor_photo}
                          width={40}
                          height={40}
                          alt="profile-image"
                          className="allApproval-image"
                        />
                        <div className="col-sm-auto ">
                          <p className="allApproval-expert-name mb-0 fw-bold">
                            {feedback?.doctor_name}
                          </p>
                          <p className="allApproval-expert-role fw-bold mb-0">
                            {capitalizeFullName(
                              feedback?.expert_role
                                ? feedback?.expert_role
                                : "expert role"
                            )}
                          </p>
                        </div>
                      </div>

                      <div>
                        <p className="feedback-formatedate">
                          {feedback?.CurrentTime &&
                            formatCustomDate(feedback?.CurrentTime)}
                          <br />
                          <span className="feedback-time-difference">
                            {timeDifference(feedback?.CurrentTime)}
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="row px-1">
                    <p className="allApproval-expert-para text-justify">
                      {feedback?.Feedback}
                    </p>
                    <div className="modal-footer px-2">
                      <button
                        type="button"
                        className="btn allApproval-reject-btn"
                        onClick={() =>
                          handleFeedbackApprovals("reject", feedback?.id)
                        }
                      >
                        Reject
                      </button>

                      <button
                        type="button"
                        className="btn allApproval-approve-btn ms-2"
                        onClick={() =>
                          handleFeedbackApprovals("approve", feedback?.id)
                        }
                      >
                        Approve
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No other feedbacks available for approval.
              </h3>
            </div>
          )}
        </div>

        <div className="d-flex justify-content-center align-items-center">
          <div className="d-none d-xl-block">
            {total_Pages > 1 && (
              <CustomPagination
                total_pages={allFeedbacks?.total_Pages}
                current_page={current_page}
                setCurrent_Page={setCurrent_Page}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AllFeedback;
