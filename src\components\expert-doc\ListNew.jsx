import React from "react";

const doctors = [
  {
    name: "<PERSON>",
    specialty: "Cardiology",
    startDate: "24/05/2023",
    endDate: "26/05/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "Dermatology",
    startDate: "18/06/2023",
    endDate: "20/06/2023",
    status: "Inactive",
  },
  {
    name: "<PERSON>",
    specialty: "Orthopedics",
    startDate: "10/07/2023",
    endDate: "15/07/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "Neurology",
    startDate: "05/08/2023",
    endDate: "10/08/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "Pediatrics",
    startDate: "02/09/2023",
    endDate: "07/09/2023",
    status: "Inactive",
  },
  {
    name: "<PERSON>",
    specialty: "Ophthalmology",
    startDate: "29/09/2023",
    endDate: "03/10/2023",
    status: "Active",
  },
  {
    name: "<PERSON>",
    specialty: "General Medicine",
    startDate: "25/10/2023",
    endDate: "30/10/2023",
    status: "Active",
  },
];
const items = [
  {
    name: "<PERSON>",
    category: "Research",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Research",
    date: "24/06/2023",
  },
  {
    name: "Georgia Grant",
    category: "Research",
    date: "24/06/2023",
  },
  // Add more items as needed
];

const ListNew = () => {
  return (
    <div>
      <p>Doctors list </p>
    </div>
  );
};

export default ListNew;
