import React, { useCallback, useEffect, useRef, useState } from "react";
import { Placeholder } from "react-bootstrap";
import { FaCalendar, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import { formatDateToYMD } from "../../utils/helperfunction";

{
  [...Array(5)].map((_, index) => (
    <tr key={index}>
      <td className="custom-td">
        <Placeholder as="p" animation="glow">
          <Placeholder
            xs={12}
            style={{ height: "30px", borderRadius: "5px" }}
          />
        </Placeholder>
      </td>
      <td className="custom-td-purple">
        <Placeholder as="p" animation="glow">
          <Placeholder
            xs={12}
            style={{ height: "30px", borderRadius: "5px" }}
          />
        </Placeholder>
      </td>
      <td className="custom-td-purple">
        <Placeholder as="p" animation="glow">
          <Placeholder
            xs={12}
            style={{ height: "30px", borderRadius: "5px" }}
          />
        </Placeholder>
      </td>
      <td className="custom-td">
        <Placeholder as="p" animation="glow">
          <Placeholder
            xs={12}
            style={{ height: "30px", borderRadius: "5px" }}
          />
        </Placeholder>
      </td>
      <td className="text-center">
        <Placeholder as="p" animation="glow">
          <Placeholder
            xs={12}
            style={{ height: "30px", borderRadius: "5px" }}
          />
        </Placeholder>
      </td>
    </tr>
  ));
}

const HeadingPlaceHolder = () => {
  return (
    <Placeholder as="p" animation="glow">
      <Placeholder xs={12} style={{ height: "30px", borderRadius: "5px" }} />
    </Placeholder>
  );
};

const ValuePlaceholder = () => {
  return (
    <Placeholder as="p" animation="glow">
      <Placeholder xs={12} style={{ height: "30px", borderRadius: "5px" }} />
    </Placeholder>
  );
};

const AppointmentFilter = () => {
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [loading, setLoading] = useState(false);
  const [appointmentData, setAppointmentData] = useState({});
  const [showPicker, setShowPicker] = useState(false);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const dateMenu = useRef();
  const admin_id = session?.user?.id;

  const fetchAppointmentData = useCallback(async () => {
    try {
      setLoading(true);
      let url = `${process.env.NEXT_PUBLIC_APPOINTMENT_SEARCH}user_id=${admin_id}`;
      if (startDate && endDate && admin_id) {
        const startFormatted = formatDateToYMD(startDate);
        const endFormatted = formatDateToYMD(endDate);
        url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
      }
      const response = await axiosAuth.get(url);
      setAppointmentData(response?.data);
      setLoading(false);
    } catch (err) {
      console.log("Error in getting the appointmnent data", err);
    } finally {
      setLoading(false);
    }
  }, [admin_id, axiosAuth, endDate]);

  useEffect(() => {
    fetchAppointmentData();
  }, [fetchAppointmentData]);

  const {
    total_upcoming_consultations = 0,
    total_completed_consultations = 0,
    total_cancelled_consultations = 0,
    total_rescheduled_consultations = 0,
    total_unattended_consultations = 0,
  } = appointmentData || {};

  const closeOpenMenu = (e) => {
    if (showPicker && !dateMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };
  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeOpenMenu);
  }

  const handleDateFilterChange = (start, end) => {
    setStartDate(start);
    setEndDate(end);
    if (start && end) {
      setShowPicker(false); // Close the date picker when both dates are selected
    }
  };
  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handleClearFilter = (e) => {
    e.stopPropagation();
    handleDateFilterChange(null, null); // Clear the date filter
    setShowPicker(false);
  };
  return (
    <div>
      <div className="row">
        <div className="col-sm-8">
          <p className="heading">Appointment Information</p>
        </div>
        <div className="col-sm-4">
          <div className=" calender-filter-container">
            <span className="date-filter-patient" onClick={handleCalendarClick}>
              {startDate
                ? `${startDate?.toLocaleDateString()} - ${
                    endDate ? endDate?.toLocaleDateString() : ""
                  }`
                : "Select Date Range"}
              <span className="calendar-icon-patient">
                {startDate ? (
                  <FaTimes
                    className=" cross-icon-calendar"
                    onClick={handleClearFilter}
                  />
                ) : (
                  <FaCalendar className=" calender-icon-calendar" />
                )}
              </span>
            </span>
            {showPicker && (
              <div ref={dateMenu} style={{ position: "absolute", zIndex: 999 }}>
                <DatePicker
                  selected={startDate}
                  startDate={startDate}
                  endDate={endDate}
                  selectsRange
                  inline
                  showTimeSelect={false} // Disable time selection
                  onChange={(dates) =>
                    handleDateFilterChange(dates[0], dates[1])
                  }
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <div></div>
      <div className="row">
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="green-count mb-2">{total_completed_consultations}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? (
            <HeadingPlaceHolder />
          ) : (
            <p className="dashboard-content mb-2">Completed Consultations</p>
          )}
        </div>
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="black-count mb-2">{total_upcoming_consultations}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? <HeadingPlaceHolder /> : <p className="dashboard-content mb-2">Upcoming Consultations</p>}
        </div>
      </div>

      <div className="row">
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="red-count mb-2">{total_cancelled_consultations}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? <HeadingPlaceHolder /> : <p className="dashboard-content mb-2">Cancelled Consultations</p>}
        </div>
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="black-count mb-2">{total_rescheduled_consultations}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? <HeadingPlaceHolder /> : <p className="dashboard-content mb-2">Rescheduled Consultations</p>}
        </div>
      </div>

      <div className="row">
        <div className=" d-flex align-items-center justify-content-center gap-2">
          <div className="col-sm-1">
            {loading ? (
              <ValuePlaceholder />
            ) : (
              <p className="black-count mb-2">{total_unattended_consultations}</p>
            )}
          </div>
          <div className="col-sm-5">
            {loading ? <HeadingPlaceHolder /> : <p className="dashboard-content mb-2">Unattended Consultations</p>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentFilter;
