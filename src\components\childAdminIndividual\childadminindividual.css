button.btn.Deactivate-Profile {
  background: #ff2e2e 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #f6f6f6;
  border-radius: 3px;
  color: #ffff;
  height: 38px;
  float: right;
  width: 177px;
}
.payment-back {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  background-color: white;
}
.user-management-scroll-child-admin {
  max-height: 665px;
  padding: 20px;
}

button.btn.Deactivate-Profile:focus {
  background: #ff2e2e 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #f6f6f6;
  border-radius: 3px;
  color: #ffff;
  height: 38px;
  float: right;
  width: 177px;
}
.graph-border {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
}
.calendar-container {
  position: absolute;
  top: 226px;
  /* left: 0; */
  z-index: 1000;
}
span.date-filter-child {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  color: #5b5b5b;
  width: 200px;
  height: 35px;
  padding: 6px;
  margin-top: 10px;
  display: inline-flex;
}
.request-graph {
  color: #5b5b5b;
  font-size: 16px;
  font-weight: normal;
}
.rate-g {
  font-size: 20px;
  font-weight: normal;
  color: #333333;
}
.date-day {
  font-weight: normal;
  font-size: 14px;
  letter-spacing: 0.44px;
  color: #b3b8bd;
}
.calendar-icon-child {
  color: #8107d1;
  margin-left: 170px;
  margin-top: -3px;
  position: absolute;
}
.form-control.custom-font-size{
  font-size: 14px;
}
.form-control.custom-font-size:focus{
  box-shadow: none;
  border-color: #dee2e6;
}
.react-tel-input .form-control {
  width: 100%;
}
.form-control.custom-inpu-p:focus{
  box-shadow: none;
  border-color: #dee2e6;
}
.form-control .custom-input-l:focus{
  box-shadow: none;
  border-color: #dee2e6;
}
.form-control .text-form-fileds:focus{
  box-shadow: none;
  border-color: #dee2e6;
}
.btn.button-green,
.btn.button-green:hover {
  background-color: #04ab20;
  color: white;
  font-size: 12px;
}

.child-Admin-Top-Section {
  display: flex;
  justify-content: space-between;
  align-items: start;
}

.btn.back-button,
.btn.back-button:hover {
  color: #8107d1;
  font-weight: bold;
}
.profile-bg-control {
  float: right;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid grey;
  padding: 10px;
}

.editprofile,
.child-amdin-refresh {
  color: #8107d1;
  cursor: pointer;
  size: 30;
}
.btn-save-changes {
  background-color: #8107d1;
  color: white;
}

.last-login {
  color: #8107d1;
  font-weight: bold;
}
.btn.control-profile,
.btn.control-profile:hover {
  background-color: #414146;
  /* width: 100%; */
  color: white;
  padding: 2%;
  border: none;
  font-size: 12px;
  float: right;
}

.content-scroll {
  max-height: 375px;
}

.content-scroll::-webkit-scrollbar {
  display: none;
}
.tick-mark-userpermissions {
  color: green;
}
.single-notification-tab {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  padding: 6px;
}

.p-s-subheading {
  letter-spacing: 0px;
  color: #5b5b5b;
  font-size: 16px;
  font-weight: 700;
}

.purple-text-admin-profile {
  letter-spacing: 0px;
  color: #9426b2;
  opacity: 1;
  font-size: 15px;
  font-weight: 500;
}
.forgot-pwd-link-prof-sett {
  letter-spacing: 0px;
  color: #f37721;
  font-size: 16px;
  font-weight: 600;
}
.profile-submit-btn-admin-sett {
  background: #f37721;
  color: white;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  opacity: 1;
  border: none;
  font-size: 14px;
  padding: 10px 20px 10px 20px;
}

.granted-notification {
  min-height: 500px;
}

.list-of-notiification {
  width: 100%;
}

/* ---------- swal design ---------- */
.swal-confirm-button-class {
  float: inline-end;
  background-color: #4caf50 !important;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  margin-right: auto; /* This will push the button to the right */
  margin-left: 15px;
}

.swal-confirm-button-class:focus-visible {
  outline: none;
}

.swal-cancel-button-class {
  background-color: #ef5f12;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  margin-right: 15px;
}

.swal2-actions {
  width: 100%;
}

.child-admin-country,
.child-admin-state,
.child-admin-city {
  border: 1px solid transparent;
  width: 50%;
}
