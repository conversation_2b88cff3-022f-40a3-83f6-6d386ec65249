import React, { useState } from "react";
import { formatCustomDateAndTime } from "../../utils/helperfunction";
import Image from "next/image";
import PaymentHistoryModal from "./PaymentHistoryModal";
import { Button } from "react-bootstrap";
const ClearedPayment = ({ expertClearedAmountDetails = {} }) => {
  const [paymentHistoryModal, setPaymentHistoryModal] = useState(false);
  const [expertIdForPaymentHistory, setExpertIdForPaymentHistory] = useState({
    expertId: 0,
    expertWalletBalanceAmount: 0,
  });

  return (
    <>
      <div>
        <table className="table custom-table mt-1">
          <thead>
            <tr className="text-center">
              <th className="pending-row-custom">Sl No</th>
              <th className="pending-row-custom">Expert Name</th>
              <th className="pending-row-custom">Last Requested Date</th>
              <th className="pending-row-custom">Last Cleared Amount</th>
              <th className="pending-row-custom">Last Cleared Date</th>
              <th className="pending-row-custom">Balance Amount</th>
              <th className="pending-row-custom">Payment History</th>
            </tr>
          </thead>
          <tbody>
            {expertClearedAmountDetails &&
              Array.isArray(expertClearedAmountDetails) &&
              expertClearedAmountDetails.map((walletDetails, index) => (
                <tr key={index}>
                  <td>{index + 1}</td>
                  <td className="expert-name">
                    {walletDetails?.expert_details?.expert_profile_photo ? (
                      <>
                        <Image
                          src={`${walletDetails?.expert_details?.expert_profile_photo}`}
                          alt={`Dr ${walletDetails?.expert_details?.expert_name}`}
                          width={35}
                          height={35}
                          className="wallet-details-expert-image"
                        />
                      </>
                    ) : (
                      <>
                        <Image
                          src={dummyProfile}
                          alt={`Dr ${walletDetails?.expert_details?.expert_name}`}
                          width={35}
                          height={35}
                          className="wallet-details-expert-image"
                        />
                      </>
                    )}
                    {walletDetails?.expert_details?.expert_name ||
                      "Expert Name"}
                  </td>
                  <td>
                    {walletDetails.TransactionDate &&
                      formatCustomDateAndTime(walletDetails.TransactionDate)}
                  </td>
                  <td>
                    {walletDetails.TransactionAmount
                      ? `$ ${walletDetails.TransactionAmount}.00`
                      : "00.00"}
                  </td>
                  <td>
                    {walletDetails.ClearedDate &&
                      formatCustomDateAndTime(walletDetails.ClearedDate)}
                  </td>
                  <td>
                    {walletDetails.BalanceAmount
                      ? `$ ${walletDetails.BalanceAmount}.00`
                      : "00.00"}
                  </td>
                  <td
                    onClick={() => {
                      setExpertIdForPaymentHistory({
                        expertId: walletDetails?.expert_details?.expert_id,
                        expertWalletBalanceAmount: walletDetails?.BalanceAmount,
                      });

                      setPaymentHistoryModal(true);
                    }}
                  >
                    {" "}
                    <Button className="purple-btn">View</Button>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      {paymentHistoryModal && (
        <PaymentHistoryModal
          isOpen={paymentHistoryModal}
          setPaymentHistoryModal={setPaymentHistoryModal}
          expertIdForPaymentHistory={expertIdForPaymentHistory}
        />
      )}
    </>
  );
};

export default ClearedPayment;
