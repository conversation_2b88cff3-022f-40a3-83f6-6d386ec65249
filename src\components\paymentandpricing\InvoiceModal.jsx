import Image from "next/image";
import React, { useState } from "react";
import { Modal } from "react-bootstrap";
import logo from "../../../public/images/HU_logo.png";
import InvoiceModalContent from "./InvoiceModalContent";
import "./paymentandprice.css";
import { toast } from "react-toastify";

const InvoiceModal = ({
  showInvoiceModal,
  setShowInvoiceModal,
  selectedPayment,
}) => {
  const [downLoading, setDownloading] = useState(false);
  
  const handleInvoiceDownload = () => {
    setDownloading(true);
    const invoiceLink = selectedPayment?.invoice?.invoice_pdf;
    if (invoiceLink) {
      window.location.href = invoiceLink;
    } else {
      toast.error("Invoice link not available");
    }
    setDownloading(false);
  };

  return (
    <Modal
      show={showInvoiceModal}
      onHide={() => setShowInvoiceModal(false)}
      centered
    >
      <div className="c">
        <Modal.Header closeButton 
         > <div className="col-sm-3">
         <Image className="invoice-logo" src={logo} alt="header" />
       </div>
       <div className="col-sm-8">
              <p className="purple-content-invoice mb-0">
                {/* <span className="black-content-invoice"> */}
                  {selectedPayment?.invoice?.account_name}
                {/* </span> */}
              </p>
            </div></Modal.Header>
        <Modal.Title className="px-4">
          <div className="d-flex justify-content-between align-items-center mt-2">
           
            
          <p className="invoice-date-heading">
                  Date{" "}
                  <span>
                    {/* {formatDateFromUnixTimestamp(selectedPayment?.created)} */}
                    {selectedPayment?.date?.split("T")[0]}
                  </span>
                </p>
              
                
                <button
                  type="button"
                  onClick={handleInvoiceDownload}
                  className="purple-download-button-invoice"
                >
                  {downLoading ? "downloading.." : "Download Invoice"}
                </button>
        
            
          </div>
        </Modal.Title>

        <InvoiceModalContent selectedPayment={selectedPayment} />
      </div>
    </Modal>
  );
};

export default InvoiceModal;
