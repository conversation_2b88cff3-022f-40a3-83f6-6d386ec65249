import React from "react";
import { FaFileCircleCheck } from "react-icons/fa6";
import { capitalizeFullName} from "../../../utils/helperfunction";

const ConsentFormUploads = ({
  doctorsConsent,
  handleConsentPreview,
  selectedValue,
  handleChange,
  setShowNoPreviewText,
}) => {
  return (
    <>
      {doctorsConsent?.data &&
      Array.isArray(doctorsConsent?.data) &&
      doctorsConsent?.data?.length === 0 ? (
        <>
          <h3
            className="d-flex justify-content-center align-items-center"
            style={{ minHeight: "500px" }}
          >
            <FaFileCircleCheck className="podcast-icon" />
            &nbsp;
            <span className="text-secondary"> No Consent Form Available</span>
          </h3>
        </>
      ) : (
        <>
          <div className="col-2">
            <select
              className="form-select form-select-sm custom-form-select"
              aria-label=".form-select-sm example"
              value={selectedValue}
              onChange={handleChange}
            >
              <option selected value="all">
                Select
              </option>
              <option value="0">Under Review</option>
              <option value="1">Approved</option>
              <option value="2">Rejected</option>
            </select>
          </div>
          {doctorsConsent?.data &&
            Array.isArray(doctorsConsent?.data) &&
            doctorsConsent?.data?.map((item, index) => {
              return (
                <div className="bg-color mb-2" key={index}>
                  <div className="row">
                    <div className="col-sm-6 mb-0  d-flex justify-content-start align-items-center">
                      <FaFileCircleCheck
                        className="podcast-icon"
                        style={{ fontSize: "30px" }}
                      />
                      <p
                        className="ps-2 mb-0 custom-font-size"
                        dangerouslySetInnerHTML={{
                          __html: `${capitalizeFullName(
                            item?.ConsentContent?.split(" ")
                              .slice(0, 4)
                              .join(" ")
                          )}`,
                        }}
                      ></p>
                    </div>
                    <div className="col-sm-4 mb-0 d-flex justify-content-evenly align-items-center">
                      <p className="purple-text mb-0 pe-2">
                        {item.DateOfConsentForm?.split("T")[0]}
                      </p>
                      <p
                        className={`blog-approval-status-${
                          item.Status === 2
                            ? "under-review"
                            : item.Status === 3
                            ? "under-review"
                            : item.Status === 1
                            ? "approved"
                            : item.Status === 0
                            ? "rejected"
                            : ""
                        } mb-0`}
                      >
                        {item.Status === 2
                          ? "Under Review"
                          : item.Status === 3
                          ? "Approval_requested"
                          : item.Status === 1
                          ? "Approved"
                          : item.Status === 0
                          ? "Rejected"
                          : ""}
                      </p>
                    </div>

                    <div className="col-sm-1">
                      {/* <button
                                  type="button"
                                  className="btn btn-transparent red-text fw-bold p-0"
                                  // onClick={() =>
                                  //   handleBlogDelete(item?.blog_details?.id)
                                  // }
                                >
                                  Delete
                                </button> */}
                    </div>
                    <div className="col-sm-1 d-flex justify-content-center align-items-center">
                      <button
                        type="button"
                        className="btn btn-transparent purple-text fw-bold p-0"
                        onClick={() => {
                          handleConsentPreview(item);
                          setShowNoPreviewText(false);
                        }}
                      >
                        View
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
        </>
      )}
    </>
  );
};

export default ConsentFormUploads;
