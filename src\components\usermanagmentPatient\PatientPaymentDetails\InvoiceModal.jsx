import React, { useRef } from "react";
import { Modal } from "react-bootstrap";
// import ReactToPrint from "react-to-print";
import ModalContentPay from "./ModalContentPay";
import Image from "next/image";
import headerhover from "../../../../public/assets/adminloginlogo.png";
import { formatDateFromUnixTimestamp } from "../../../utils/helperfunction";

const InvoiceModal = ({ showModal, handleClose, selectedPayment }) => {
  let componentRef = useRef();

  const reactToPrintContent = React.useCallback(() => {
    return componentRef.current;
  }, [componentRef]);

  return (
    <Modal show={showModal} onHide={handleClose} centered size="lg">
      <div className="c" ref={(el) => (componentRef = el)}>
        <Modal.Header closeButton />
        <Modal.Title className="px-4">
          <div className="row mt-2">
            <div className="col-sm-3">
              <Image
                className="headerImage1"
                // src="/header-footer-image/header-hover.png"
                src={headerhover}
                alt="header"
              />
            </div>
            <div className="col-sm-6 gx-0">
              <p className="purple-content-2">
                <span className="black-content-2">
                  {selectedPayment?.invoice?.account_name}
                </span>
              </p>
            </div>
            <div className="col-sm-3 gx-0">
              <div className=" d-flex flex-column ms-auto float-end">
                <p className="date">
                  Date{" "}
                  <span>
                    {/* {formatDateFromUnixTimestamp(selectedPayment?.created)} */}
                    {formatDateFromUnixTimestamp(
                      selectedPayment?.invoice?.created
                    )}
                  </span>
                </p>
                <button
                  type="button"
                  onClick={() => {
                    const invoiceLink = selectedPayment?.invoice?.invoice_pdf;
                    if (invoiceLink) {
                      window.location.href = invoiceLink;
                    } else {
                      console.log("Invoice link not available");
                    }
                  }}
                  className="purple-download-button"
                >
                  Download Invoice
                </button>
              </div>
            </div>
          </div>
        </Modal.Title>

        <ModalContentPay selectedPayment={selectedPayment} />
      </div>
    </Modal>
  );
};

export default InvoiceModal;
