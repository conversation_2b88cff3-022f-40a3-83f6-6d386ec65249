import React, { useState, useEffect, useCallback } from "react";
import { MdEdit } from "react-icons/md";
import { MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import ContentManPlaceholder from "../../../contentManagement/UserProfileContent/ContentManPlaceholder";
import EditModal from "../../../contentManagement/EditModal";
import useAxiosAuth from "../../../../lib/hooks/useAxiosAuth";

const CommonTopics = () => {
  const [showModal, setShowModal] = useState(false);
  const [currentTopic, setCurrentTopic] = useState(null);
  const [commonTopic, setCommonTopic] = useState("");
  const [topics, setTopics] = useState("");
  const [loading, setloading] = useState(true);
  const [warningMessage, setWarningMessage] = useState("");
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();
  const maxWords = 10;

  const getCommonTopics = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_UPDATE_COMMON_TOPICS}all/?user_id=${user_id}`
        );
        setTopics(response?.data);
        setloading(false);
      }
    } catch (err) {
      console.log("error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  const handleCreateUpdateCommonTopic = async (crud, id, body) => {
    if (crud === "post" && commonTopic.trim() === "") {
      toast.error("Common topic cannot be empty", {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      return; // Exit the function if validation fails
    } else if (crud === "put" && body.trim() === "") {
      toast.error("Common topic edit cannot be empty", {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      return; // Exit the function if validation fails
    }

    try {
      setloading(true);

      const baseUrl = process.env.NEXT_PUBLIC_CREATE_COMMON_TOPICS;
      const baseUrl2 = process.env.NEXT_PUBLIC_GET_UPDATE_COMMON_TOPICS;

      // Add the user ID to the URL
      const url =
        crud === "post"
          ? `${baseUrl}?user_id=${user_id}`
          : `${baseUrl2}${id}/?user_id=${user_id}`; // Include the id for PUT

      const requestBody = { common_topics: commonTopic };
      const requestBody2 = { common_topics: body };

      // Use await to get the response
      const response =
        crud === "post"
          ? await axiosAuth.post(url, requestBody)
          : await axiosAuth.put(url, requestBody2);

      setloading(false);
      if (response?.data === "Common topic added successfully.") {
        toast.success(`Common Topic Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getCommonTopics();
      } else if (response?.data === "Successfully updated") {
        toast.success(`Common Topic Updated Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getCommonTopics();
      }

      setCommonTopic(" ");
      setShowModal(false);
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  useEffect(() => {
    getCommonTopics();
  }, [user_id, getCommonTopics, axiosAuth]);

  const handleEditClick = (reportItem) => {
    setCurrentTopic(reportItem);
    setShowModal(true);
  };

  const handleClose = () => {
    setShowModal(false);
    setCurrentTopic(null);
  };

  const handleDeleteClick = async (topicId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          // setloading(true);
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_GET_UPDATE_COMMON_TOPICS}${topicId}/?user_id=${user_id}`
          );
          if (response?.status === 204) {
            toast.success(`Common Topic deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getCommonTopics();
          }
        } catch (error) {
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleInputChange = (e) => {
    const inputValue = e.target.value;
    const words = inputValue
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    const wordCount = words.length;
    if (wordCount <= maxWords) {
      setCommonTopic(inputValue);
      setWarningMessage("");
    } else {
      setWarningMessage(`You can only type up to ${maxWords} words.`);
    }
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }
  return (
    <div className="">
      <h5 className=" mb-2 admin-add-blog-list-header fw-semibold">
        Add Common Topics for Ask Ruchika
      </h5>
      <div className="row">
        <div className="col-sm-4">
          <div className="mb-3">
            {/* <label htmlFor="cancerType" className="form-label custom-label">
              Common Topics
            </label> */}
            <input
              type="text"
              className="form-control custom-form-control"
              id="cancerType"
              value={commonTopic}
              onChange={handleInputChange}
              placeholder="Enter common topics"
              required
            />
          </div>
          {warningMessage && (
            <p className=" warning-message-limit">{warningMessage}</p>
          )}

          <button
            disabled={loading}
            type="submit"
            onClick={() => handleCreateUpdateCommonTopic("post")}
            className="btn purple-button"
          >
            {loading ? "Submitting" : "Submit"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">Common Topics</th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {topics &&
                  topics?.map((item, index) => (
                    <tr key={index}>
                      <th scope="row" className="custom-font-size">
                        {index + 1}
                      </th>
                      <td className="custom-font-size">{item.common_topics}</td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm purple-button"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit className="custom-font-size" />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item?.id)}
                        >
                          <MdDelete className="custom-font-size" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      {currentTopic && (
        <EditModal
          show={showModal}
          handleClose={handleClose}
          initialData={currentTopic}
          axiosAuth={axiosAuth}
          user_id={user_id}
          dataFetch={getCommonTopics}
          handleCreateUpdateCommonTopic={handleCreateUpdateCommonTopic}
          commonTopic={true}
        />
      )}
    </div>
  );
};

export default CommonTopics;
