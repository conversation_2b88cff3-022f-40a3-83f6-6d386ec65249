import React, { useEffect, useRef, useState } from "react";
import { MdCancel } from "react-icons/md";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FaCalendar, FaTimes } from "react-icons/fa";

const PaymentFilter = ({
  setFilters,
  filters,
  clearFilter,
  setStatusFilter,
  searchQuery,
  startDate,
  endDate,
  setEndDate,
  setStartDate,
  setSearchQuery,
  statusFilter,
  loading,
  handleClearQueryFilter,
  pageLimit,
  setPageLimit,
  setCurrentPage,
  payments,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const searchMenu = useRef(null);

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };

  const handlePageLimit = (e) => {
    const status = e.target.value;
    setPageLimit(status);
  };

  const handleStatusChange = (e) => {
    const status = e.target.value;
    setStatusFilter(status);
  };

  const handleDateFilterChange = (start, end) => {
    setStartDate(start);
    setEndDate(end);
    setCurrentPage(1);
  };

  const handleClearDateFilter = async () => {
    handleDateFilterChange(null, null);
    setShowPicker(false);
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prevFilters) => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  const handleSearchQuery = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  return (
    <div className="filters">
      <div className="row">
        <div className="col-sm-12">
          <p className="custom-heading">Filters</p>
        </div>
      </div>
      <div className="row mx-0">
        <div className="mb-3 expert-search-bar p-0 ps-0">
          <div className=" position-relative" style={{ height: "45px" }}>
            <input
              type="text"
              className="form-control app-search-filter"
              style={{ outline: "none", border: "0px", borderRadius: "3px" }}
              placeholder="Search Expert Name"
              aria-label="Recipient's username"
              aria-describedby="button-addon2"
              value={searchQuery}
              disabled={payments?.items?.length == 0 || loading}
              onChange={handleSearchQuery}
            />
            <span style={{ zIndex: 9999 }} className="cancel-expert-search-btn">
              {!loading && searchQuery && (
                <FaTimes
                  style={{ marginTop: "11px" }}
                  className=" cross-icon-calendar"
                  onClick={handleClearQueryFilter}
                />
              )}
            </span>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-sm-12 mb-3">
          <select
            style={{ height: "45px" }}
            className="form-select form-select-sm custom-select"
            aria-label="Select Prescription"
            name="statusFilter"
            disabled={payments?.items?.length == 0 || loading}
            value={filters?.statusFilter}
            // onChange={handleFilterChange}
          >
            <option value="">All</option>
            <option value="Success">Success</option>
            <option value="Cancelled">Cancelled</option>
            <option value="Failed">Failed</option>
            {/* <option value="Expired">Expired</option> */}
          </select>
        </div>
      </div>
      <div className="row">
        <div ref={searchMenu} className="">
          <div className=" calender-filter-container">
            <span
              className="date-filter-expert w-100"
              onClick={handleCalendarClick}
            >
              {startDate
                ? `${startDate.toLocaleDateString()} - ${
                    endDate ? endDate.toLocaleDateString() : ""
                  }`
                : "Select Date"}
              <span
                style={{ zIndex: 9999 }}
                className="calendar-icon-expert ms-auto"
              >
                {startDate ? (
                  <FaTimes
                    className=" cross-icon-calendar"
                    onClick={handleClearDateFilter}
                  />
                ) : (
                  <FaCalendar className=" calender-icon-calendar" />
                )}
              </span>
            </span>

            {showPicker && (
              <div style={{ position: "absolute", zIndex: 1 }}>
                <DatePicker
                  selected={startDate}
                  startDate={startDate}
                  endDate={endDate}
                  selectsRange
                  inline
                  showTimeSelect={false} // Disable time selection
                  onChange={(dates) =>
                    handleDateFilterChange(dates[0], dates[1])
                  }
                />
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-sm-12 mt-3">
          <select
            style={{ height: "45px" }}
            className="form-select form-select-sm custom-select"
            aria-label="Select Prescription"
            name="statusFilter"
            value={pageLimit}
            onChange={handlePageLimit}
          >
            <option value="">Items Per Page</option>
            <option value="10">10</option>
            <option value="15">15</option>
            <option value="20">20</option>
          </select>
        </div>
      </div>

      <div className="row mt-3">
        <div className="col-sm-12">
          <div className="grey-bg">
            {Object.entries(filters ?? {}).map(
              ([filterName, filterValue]) =>
                filterValue && (
                  <span
                    key={filterName}
                    className="badge rounded-pill pill-bg mb-2 me-3 text-center"
                  >
                    {`${filterValue}`}
                    <MdCancel
                      onClick={() => clearFilter(filterName)}
                      className="ms-2"
                      style={{ fontSize: "24px" }}
                    />
                  </span>
                )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFilter;
