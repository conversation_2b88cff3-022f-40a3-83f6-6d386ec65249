"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import profile from "../../../public/images/profile.png";
import { MdModeEditOutline } from "react-icons/md";
import { MdDelete } from "react-icons/md";
import TableHead from "../experts/TableHead";
import FetchDoctorLoader from "../loaders/fetchLoader/FetchDoctorLoader";
import Cookies from "js-cookie";
import { BsFastForwardFill } from "react-icons/bs";
import { TbPlayerTrackPrevFilled } from "react-icons/tb";
import { BsFillCircleFill } from "react-icons/bs";
import "./usermanagement.css";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";

const Pagination = () => {
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const user_id = session?.user.id;
  const DrHeaderTitles = [
    "Doctor Name",
    "Department",
    "Date of Application",
    "Date of Onboarding",
    "Status",
    "Edit",
    "Delete",
  ];

  useEffect(() => {
    const getAllApprovedUnapprovedExpertsList = async () => {
      try {
        setLoading(true);
        const data = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}all/${user_id}`
        );
        setList(data?.data?.experts_data || []);
      } catch (err) {
        console.log("error in fetching ", err);
      } finally {
        setLoading(false);
      }
    };
    getAllApprovedUnapprovedExpertsList();
  }, [axiosAuth, user_id]);

  // Calculate the index of the last item to be displayed on the current page
  const indexOfLastItem = currentPage * itemsPerPage;
  // Calculate the index of the first item to be displayed on the current page
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  // Get the current items to be displayed on the page
  const currentItems = list?.slice(indexOfFirstItem, indexOfLastItem);

  const prevPage = () => {
    const newPage = Math.max(currentPage - 1, 1);
    setCurrentPage(newPage);
  };
  // Next page
  const nextPage = () => {
    const newPage = Math.min(
      currentPage + 1,
      Math.ceil(list.length / itemsPerPage)
    );
    setCurrentPage(newPage);
  };

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  return (
    <div>
      <div className="row mt-3">
        <p className="heading">Doctors</p>
      </div>
      <table className="table mt-2 ">
        <TableHead headerTitles={DrHeaderTitles} />
        {loading ? (
          <FetchDoctorLoader />
        ) : (
          <tbody className="custom-border">
            {currentItems &&
              currentItems?.map((expert, index) => {
                return (
                  <tr key={index} className="custom-row">
                    <td className="text-center purple-content d-flex">
                      <Image src={profile} alt="" />
                      <span>{expert?.name}</span>
                    </td>
                    <td className="custom-font text-center">
                      {expert?.specialty}
                    </td>
                    <td className="text-center">{expert?.startDate}</td>
                    <td className="text-center">{expert?.endDate}</td>
                    <td className="text-center green-text">{expert?.status}</td>
                    <td className="text-center">
                      <MdModeEditOutline
                        style={{ color: "#8107D1", fontSize: "20px" }}
                      />
                    </td>
                    <td className="text-center">
                      <MdDelete
                        style={{ color: "#FF2E2E", fontSize: "20px" }}
                      />
                    </td>
                  </tr>
                );
              })}
          </tbody>
        )}
      </table>

      <div>
        <nav aria-label="Page navigation example">
          <ul className="pagination justify-content-center custom-pagination">
            <li
              onClick={prevPage}
              className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
            >
              <a
                className="page-link custom-page-link"
                href="#"
                tabIndex="-1"
                aria-disabled="true"
              >
                <span className="page-count">1</span>
                <TbPlayerTrackPrevFilled style={{ color: "#96969C" }} />
              </a>
            </li>
            {Array.from({ length: Math.ceil(list?.length / itemsPerPage) }).map(
              (_, index) => (
                <li
                  key={index}
                  onClick={() => paginate(index + 1)}
                  className={`page-item ${
                    currentPage === index + 1 ? "active active-page" : ""
                  }`}
                >
                  <a
                    className="page-link custom-page-link active-index"
                    href="#"
                  >
                    {index + 1}
                  </a>
                </li>
              )
            )}
            <li
              onClick={nextPage}
              className={`page-item ${
                currentPage === Math.ceil(list?.length / itemsPerPage)
                  ? "disabled"
                  : ""
              }`}
            >
              <a className="page-link custom-page-link" href="#">
                <BsFastForwardFill style={{ color: "#96969C" }} />
                <span className="page-count">
                  {Math.ceil(list.length / itemsPerPage)}
                </span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default Pagination;

// import React from 'react'
// import { MdModeEditOutline } from "react-icons/md";
// import { MdDelete } from "react-icons/md";
// import { BsFastForwardFill } from "react-icons/bs";
// import { TbPlayerTrackPrevFilled } from "react-icons/tb";
// import { BsFillCircleFill } from "react-icons/bs";

// const Pagination = () => {
//   return (
//     <div>
//         <nav aria-label="Page navigation example">
//               <ul className="pagination justify-content-center custom-pagination">
//                 <li className="page-item">
//                   <a
//                     className="page-link custom-page-link"
//                     href="#"
//                     tabIndex="-1"
//                     aria-disabled="true"
//                   >
//                     <span className="page-count">1</span>
//                     <TbPlayerTrackPrevFilled style={{ color: "#96969C" }} />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFillCircleFill
//                       style={{ color: "#96969C", fontSize: "10px" }}
//                     />
//                   </a>
//                 </li>
//                 <li className="page-item">
//                   <a className="page-link custom-page-link" href="#">
//                     <BsFastForwardFill style={{ color: "#96969C" }} />
//                     <span className="page-count">10</span>
//                   </a>
//                 </li>
//               </ul>
//             </nav>
//     </div>
//   )
// }

// export default Pagination
