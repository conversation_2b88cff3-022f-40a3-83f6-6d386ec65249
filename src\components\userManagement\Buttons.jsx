import Link from "next/link";
import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
 
const Buttons = () => {
  const pathname = usePathname();
  useEffect(() => {
  }, [pathname]);
 
  return (
    <>
      <div className="row">
        <div className="col-sm-4">
          <Link
            href="/usermanagement/experts/experts-doctor"
            className={`btn transparent-btn ${
              pathname === "/usermanagement/experts/experts-doctor" ? "activeDoctorsTab" : ""
            }`}
          >
            Doctors
          </Link>
        </div>
        <div className="col-sm-4">
          <Link
            href="/usermanagement/experts/experts-researchers"
            className={`btn transparent-btn ${
              pathname === "/usermanagement/experts/experts-researchers"
                ? "activeDoctorsTab"
                : ""
            }`}
          >
            Researchers
          </Link>
        </div>
        <div className="col-sm-4">
          <Link
            href="/usermanagement/experts/experts-influencer"
            className={`btn transparent-btn ${
              pathname === "/usermanagement/experts/experts-influencer"
                ? "activeDoctorsTab"
                : ""
            }`}
          >
            Health Guides
          </Link>
        </div>
      </div>
    </>
  );
};
 
export default Buttons;