import React from "react";
import { Pagination } from "react-bootstrap";
import "./pagination.css";

const CustomPagination = ({
  total_pages = 1,
  current_page = 1,
  setCurrent_Page,
}) => {
  const ellipsisThreshold = 5;

  const handlePageClick = (page) => {
    setCurrent_Page(page);
  };

  return (
    <Pagination className="mb-0 border-0 ">
      <Pagination.First
        onClick={() => handlePageClick(1)}
        disabled={current_page === 1}
      />
      <Pagination.Prev
        className="border-0 "
        onClick={() => handlePageClick(current_page - 1)}
        disabled={current_page === 1}
      />
      {total_pages <= ellipsisThreshold ? (
        Array.from({ length: total_pages }, (_, i) => (
          <Pagination.Item
            key={i + 1}
            className="border-0 "
            active={i + 1 === current_page}
            onClick={() => handlePageClick(i + 1)}
          >
            {i + 1}
          </Pagination.Item>
        ))
      ) : (
        <>
          <Pagination.Item
            key={1}
            className="border-0 "
            active={1 === current_page}
            onClick={() => handlePageClick(1)}
          >
            {1}
          </Pagination.Item>
          {current_page > 3 && <Pagination.Ellipsis key="start-ellipsis" />}
          {current_page > 2 && (
            <Pagination.Item
              className="border-0 "
              key={current_page - 1}
              onClick={() => handlePageClick(current_page - 1)}
            >
              {current_page - 1}
            </Pagination.Item>
          )}
          {current_page !== 1 && current_page !== total_pages && (
            <Pagination.Item key={current_page} className="border-0 " active>
              {current_page}
            </Pagination.Item>
          )}
          {current_page < total_pages - 1 && (
            <Pagination.Item
              className="border-0 "
              key={current_page + 1}
              onClick={() => handlePageClick(current_page + 1)}
            >
              {current_page + 1}
            </Pagination.Item>
          )}
          {current_page < total_pages - 2 && (
            <Pagination.Ellipsis className="border-0 " key="end-ellipsis" />
          )}
          <Pagination.Item
            className="border-0 "
            key={total_pages}
            active={total_pages === current_page}
            onClick={() => handlePageClick(total_pages)}
          >
            {total_pages}
          </Pagination.Item>
        </>
      )}
      <Pagination.Next
        onClick={() => handlePageClick(current_page + 1)}
        disabled={current_page === total_pages}
        className="border-0 "
      />
      <Pagination.Last
        onClick={() => handlePageClick(total_pages)}
        disabled={current_page === total_pages}
        className="border-0 "
      />
    </Pagination>
  );
};

export default CustomPagination;
