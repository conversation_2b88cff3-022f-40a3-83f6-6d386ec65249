import React from "react";
import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";
import { IoClose } from "react-icons/io5";
import {
  capitalizeFullName,
  convertDateFormat,
  formatDateandTime,
} from "../../../utils/helperfunction";

const TestimonialModal = ({
  showTestimonialModal,
  setShowTestimonialModal,
  singleTestimonial,
  handleTestmonialApprovals,
  testimonialsLoading,
  showButtons,
}) => {
  const handleClose = () => setShowTestimonialModal(false);

  return (
    <Modal
      show={showTestimonialModal}
      onHide={handleClose}
      centered
      size="lg"
      dialogClassName="custom-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title style={{ color: "#8107D1" }}>
          Testimonial - {singleTestimonial?.id}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <h5>{singleTestimonial?.CancerTreatmentType}</h5>
        <div
          className="d-flex px-2"
          style={{ color: "#8107D1", fontWeight: "bold" }}
        >
          <p className="text-capitalize">
            Patient -{" "}
            <span>{capitalizeFullName(singleTestimonial?.patient_name)}</span>
          </p>
          <p className="ms-auto">
            {formatDateandTime(singleTestimonial?.CurrentTime)}
          </p>
        </div>
        <div>
          <p>{singleTestimonial?.ExperienceSummary}</p>
        </div>
      </Modal.Body>
      {showButtons && (
        <Modal.Footer>
          <Button
            variant="danger"
            disabled={testimonialsLoading}
            onClick={() =>
              handleTestmonialApprovals("reject", singleTestimonial?.id)
            }
            className="rounded-1"
          >
            Reject
          </Button>
          <Button
            variant="success"
            disabled={testimonialsLoading}
            onClick={() =>
              handleTestmonialApprovals("approve", singleTestimonial?.id)
            }
            className="ms-2"
          >
            {testimonialsLoading ? "Processing...." : "Approve"}
          </Button>
        </Modal.Footer>
      )}
    </Modal>
  );
};

export default TestimonialModal;
