import React, { useState, useEffect, useCallback } from "react";
import { MdEdit, MdDelete } from "react-icons/md";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "react-bootstrap";
import Loader from "../../loader/Loader";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Swal from "sweetalert2";
import ContentManPlaceholder from "../UserProfileContent/ContentManPlaceholder";

const AddChildAdminDesignation = () => {
  const [showDesignationEditModal, setShowDesignationEditModal] =
    useState(false);
  const [selectedDesignation, setSelectedDesignation] = useState(null);
  const [listOfChildAdminDesignation, setListOfChildAdminDesignation] =
    useState(null);
  const [addChildAdminDesignation, setAddChildAdminDesignation] = useState("");
  const [formData, setFormData] = useState({ id: "", type: "" });
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const getListOfChildAdminDesignation = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}all/?Category=Designation&user_id=${user_id}`
        );
        setListOfChildAdminDesignation(response?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  useEffect(() => {
    getListOfChildAdminDesignation();
  }, [user_id, getListOfChildAdminDesignation, axiosAuth]);

  const handleAddDesignation = async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_CONTENT_TYPE}?user_id=${user_id}`,
        {
          Content: addChildAdminDesignation,
          Category: "Designation",
        }
      );
      setLoading(false);
      if (response?.data) {
        toast.success(`Designation Added Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfChildAdminDesignation();
      }
      setAddChildAdminDesignation("");
    } catch (error) {
      console.log("Error in adding the designation", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
  };

  const handleEditClick = (designation) => {
    setSelectedDesignation(designation);
    setFormData({ id: designation.id, type: designation.Content });
    setShowDesignationEditModal(true);
  };

  const handleClose = () => {
    setShowDesignationEditModal(false);
    setSelectedDesignation(null);
  };

  const handleDeleteClick = async (designationId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${designationId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Designation Deleted Successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getListOfChildAdminDesignation();
          }
        } catch (error) {
          console.log("Error in deleting the designation", error);
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${formData.id}/?user_id=${user_id}`,
        {
          Content: formData.type,
          Category: "Designation",
        }
      );
      if (response?.data) {
        toast.success(`Designation Edited Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfChildAdminDesignation();
      }
      setFormData({ id: "", type: "" });
    } catch (error) {
      console.log("Error in editing the designation", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    handleClose();
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <>
      <div className="row">
        <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
          Add Child Admin Designations
        </h5>
        <div className="col-sm-4">
          <div className="mb-3">
            <input
              type="text"
              className="form-control custom-form-control"
              id="designation"
              value={addChildAdminDesignation}
              onChange={(e) => setAddChildAdminDesignation(e.target.value)}
              placeholder="Enter Child Admin Designation"
            />
          </div>
          <button
            disabled={loading}
            type="submit"
            onClick={handleAddDesignation}
            className="btn purple-button"
          >
            {loading ? "Adding Designation" : "Add Designation"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px", fontSize: "14px" }} className="fw-semibold">
                    Slno
                  </th>
                  <th scope="col" style={{ fontSize: "14px" }} className="fw-semibold">Child Admin Designations</th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px", fontSize: "14px" }} className="fw-semibold">
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {listOfChildAdminDesignation &&
                  listOfChildAdminDesignation.map((item, index) => (
                    <tr key={index}>
                      <td scope="row" className="custom-font">{index + 1}</td>
                      <td className="custom-font">{item.Content}</td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => handleEditClick(item)}
                        >
                          <MdEdit />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteClick(item.id)}
                        >
                          <MdDelete />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      <Modal show={showDesignationEditModal} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Designation</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="formDesignationType">
            <Form.Label>Designation Type</Form.Label>
            <Form.Control
              type="text"
              name="type"
              value={formData.type}
              onChange={handleChange}
            />
          </Form.Group>
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            onClick={handleSubmit}
          >
            Update
          </Button>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default AddChildAdminDesignation;
