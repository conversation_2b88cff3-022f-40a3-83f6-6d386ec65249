import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { useSession } from "next-auth/react";
import {
  capitalizeFullName,
  formatCustomDate,
  timeDifference,
} from "../../../utils/helperfunction";
import { toast } from "react-toastify"; 
import TestimonialPlaceholder from "./TestimonialPlaceholder";
import CustomPagination from "@/components/CustomPagination/CustomPagination";


const AllTestimonials = () => {
  const [allTestimonials, setAllTestimonials] = useState([]);
  const [expandedItems, setExpandedItems] = useState({});
  const [loading, setLoading] = useState(true);
  const [noDtaError, setNoDtaError] = useState(false);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  const fetchAllTestimonals = useCallback(async () => {
    try {
      setLoading(true);
      const testimonalResponse = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_TESTIMONALS}all/2/?page=${current_page}`
      );
      const items = testimonalResponse?.data?.items;
      const totalPages = testimonalResponse?.data?.total_pages;
      setAllTestimonials(items);
      setTotalPages(totalPages);
    } catch (error) {
      console.error(error); //no data found(state)
      setNoDtaError(true);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, current_page]);

  useEffect(() => {
    if (admin_id) {
      fetchAllTestimonals();
    }
  }, [fetchAllTestimonals, admin_id, current_page]);

  const handleTestmonialApprovals = async (status, testimonial_id) => {
    Swal.fire({
      title:
        status === "approve" ? "Approve Testimonial?" : "Reject Testimonial?",
      text: `Are you sure you want to ${status} this testimonial?`,
      icon: "question",
      showCancelButton: true,
      confirmButtonText: `Yes, ${status} it!`,
      cancelButtonText: "No, cancel",
      confirmButtonColor: "#8107D1",
      cancelButtonColor: "#FF2E2E",
    }).then(async (result) => {
      if (result.isConfirmed) {
        let testimonial_status;

        if (status === "reject") {
          testimonial_status = 0;
        } else if (status === "approve") {
          testimonial_status = 1;
        }

        try {
          let response = await axiosAuth.put(
            `${process.env.NEXT_PUBLIC_APPROVE_REJECT_TESTIMONIAL}${testimonial_id}/?user_id=${admin_id}`,
            { status: testimonial_status }
          );
          if (response?.data?.status === 1) {
            toast.success(`Testimonial Approved.`);
          } else if (response?.data?.status === 0) {
            toast.error(`Testimonial Rejected`);
          }

          fetchAllTestimonals();
        } catch (error) {
          console.error(error);
        }
      }
    });
  };

  const truncateContent = (content, wordLimit = 30) => {
    const words = content.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return content;
  };

  const handleReadMore = (id) => {
    setExpandedItems((prevState) => ({
      ...prevState,
      [id]: !prevState[id],
    }));
  };

  if (loading) {
    return <TestimonialPlaceholder />;
  }
  return (
    <>
      <div className="mt-2 ms-2 text-secondary feedback-note">
        <b style={{ color: "#8107d1" }}> Note:</b> Testimonials refers to
        feedbacks of patients after each appointments.
      </div>
      <div className="overflow-hidden">
        <div
          className={`${
            Array.isArray(allTestimonials) && allTestimonials?.length > 0
              ? "overflow-auto allApproval-tab-scroll"
              : ""
          }`}
        >
          <div className="row">
            {noDtaError ? (
              <div className="d-flex justify-content-center align-items-center mt-5">
                <Image
                  src={nodataFound}
                  alt="No data"
                  width={300}
                  height={300}
                  className="mt-5"
                />
              </div>
            ) : allTestimonials?.length > 0 ? (
              allTestimonials &&
              Array.isArray(allTestimonials) &&
              allTestimonials.map((testimonal) => (
                <div key={testimonal?.id} className="col-sm-6 p-2">
                  <div className="introvideo-bg p-3 px-4">
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <p className="allApproval-heading mb-1">
                          {testimonal?.CancerTreatmentType}
                        </p>
                      </div>
                      <div>
                        <p className="testimonial-formatedate mb-1">
                          {testimonal?.CurrentTime &&
                            formatCustomDate(testimonal?.CurrentTime)}
                          <br />
                          <span className="testimonal-time-difference">
                            {" "}
                            {timeDifference(testimonal?.CurrentTime)}
                          </span>
                        </p>
                      </div>
                    </div>
                    <div className="d-flex justify-content-between align-items-center">
                      <div className="row">
                        <div className="col-sm-auto">
                          {testimonal?.expert_photo ? (
                            <>
                              <Image
                                src={`${testimonal?.expert_photo}`}
                                width={30}
                                height={30}
                                alt="profile-image"
                                className="allApproval-image"
                              />
                            </>
                          ) : (
                            <>
                              <Image
                                src={dummyProfile}
                                width={50}
                                height={50}
                                alt="profile-image"
                                className="allApproval-image"
                              />
                            </>
                          )}
                        </div>
                        <div className="col-sm-auto p-0">
                          <p className="allApproval-expert-name mb-0">
                            {testimonal?.expert_name}
                          </p>
                          <p className="allApproval-expert-role">
                            {capitalizeFullName(
                              testimonal?.expert_role
                                ? testimonal?.expert_role
                                : "Expert Role"
                            )}
                          </p>
                        </div>
                      </div>
                      <div className="row">
                        <div className="col-sm-auto">
                          <Image
                            src={testimonal?.patient_photo}
                            width={30}
                            height={30}
                            alt="profile-image"
                            className="allApproval-image"
                          />
                        </div>
                        <div className="col-sm-auto p-0">
                          <p className="allApproval-expert-name mb-0 text-end">
                            {testimonal?.patient_name}
                          </p>
                          <p className="allApproval-expert-role text-end">
                            Patient
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="row">
                      {/* <p className="allApproval-expert-para text-justify">
                    {testimonal?.ExperienceSummary}
                  </p> */}
                      <div>
                        <p className="allApproval-expert-para text-justify">
                          {expandedItems[testimonal.id]
                            ? testimonal?.ExperienceSummary
                            : truncateContent(
                                testimonal?.ExperienceSummary,
                                30
                              )}
                          {testimonal?.ExperienceSummary.split(" ").length >
                            30 && (
                            <span
                              onClick={() => handleReadMore(testimonal.id)}
                              style={{
                                color: "#8107d1",
                                cursor: "pointer",
                                marginLeft: "5px",
                              }}
                            >
                              {expandedItems[testimonal.id]
                                ? "Read Less"
                                : "Read More"}
                            </span>
                          )}
                        </p>
                      </div>
                      <div className="modal-footer">
                        <button
                          type="button"
                          className="btn allApproval-reject-btn"
                          onClick={() =>
                            handleTestmonialApprovals("reject", testimonal?.id)
                          }
                        >
                          Reject
                        </button>

                        <button
                          type="button"
                          className="btn allApproval-approve-btn ms-2"
                          onClick={() =>
                            handleTestmonialApprovals("approve", testimonal?.id)
                          }
                        >
                          Approve
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
                <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                  No other testimonials available for approval.
                </h3>
              </div>
            )}
          </div>
        </div>
      </div>
      {totalPages > 1 && (
        <div className="mt-2">
          <CustomPagination
            total_pages={totalPages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </div>
      )}
    </>
  );
};

export default AllTestimonials;
