"use client";
import React, { useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";

const UserManagementComp = () => {
  const { isAdminChildAdmin } = useAdminContext();

  const pathname = usePathname();
  useEffect(() => {}, [pathname]);

  return (
    <>
      <p className="d-inline-flex gap-1 buttons-row mb-0 mb-0">
        <Link
          href="/usermanagement/experts"
          className={`btn grey-btn ${
            pathname === "/usermanagement/experts" ||
            pathname === "/usermanagement/experts/experts-doctor" ||
            pathname === "/usermanagement/experts/experts-researchers" ||
            pathname === "/usermanagement/experts/experts-influencer"
              ? "activeExpertsTab"
              : ""
          }`}
        >
          Experts
        </Link>
        <Link
          href="/usermanagement/patients"
          className={`btn grey-btn ${
            pathname === "/usermanagement/patients" ? "activeExpertsTab" : ""
          }`}
        >
          Patients
        </Link>

        {!isAdminChildAdmin && (
          <Link
            href="/usermanagement/childAdmin"
            className={`btn grey-btn ${
              pathname === "/usermanagement/childAdmin"
                ? "activeExpertsTab"
                : ""
            }`}
          >
            Child Admin
          </Link>
        )}

        <Link
          href="/usermanagement/allApprovalRequest"
          className={`btn grey-btn ${
            pathname === "/usermanagement/allApprovalRequest"
              ? "activeExpertsTab"
              : ""
          }`}
        >
          All Approvals
        </Link>
      </p>
    </>
  );
};

export default UserManagementComp;
