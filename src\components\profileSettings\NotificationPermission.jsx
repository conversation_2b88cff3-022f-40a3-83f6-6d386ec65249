import React from "react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import ToggleBtn from "../../common/toggle-btn/ToggleBtn";

const NotificationPermission = ({
  notificationsList,
  fetchNotificationList,
}) => {
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  const toggleNotification = async (notificationId, newStatus) => {
    if (notificationId) {
      try {
        await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_UPDATE_NOTIFICATION_STATUS}${notificationId}/?user_id=${admin_id}`,
          {
            ActiveStatus: newStatus,
          }
        );
        fetchNotificationList();
      } catch (error) {
        console.error("Error updating notification status:", error);
      }
    } else {
      console.error("notification id missing");
    }
  };


  return (
    <div className="col-sm-6 gx-5">
      <div className="row">
        <div className="col-sm-8">
          <p className="p-s-subheading">Notification Settings</p>
        </div>
        <div className="col-sm-2">
          <p className="p-s-subheading ps-3">Email</p>
        </div>
        <div className="col-sm-2">
          <p className="p-s-subheading">In-App</p>
        </div>
      </div>
      <div className="overflow-hidden">
        <div className="content-scroll overflow-auto">
          <div className="notification-tab">
            {notificationsList &&
              // Array?.isArray(notificationsList) &&
              notificationsList?.map((notificationCategory, index) => {
                const category = Object?.keys(notificationCategory)[0];
                const notifications = notificationCategory[category];
                const emailNotification = notifications?.find(
                  (notification) => notification.CategoryType === "E"
                );

                const appNotification = notifications?.find(
                  (notification) => notification.CategoryType === "N"
                );
                return (
                  <div
                    key={index}
                    className="row mb-2 single-notification-tab me-3"
                  >
                    <div className="col-sm-8">
                      <span className="notification-name">{category}</span>
                    </div>
                    <div className="col-sm-2">
                      <div className="form-check form-switch">

                        {emailNotification && (
                          <ToggleBtn
                            isActive={emailNotification?.ActiveStatus === 1}
                            onToggle={toggleNotification}
                            id={emailNotification?.id}
                            type="E"
                          />
                        )}

                      </div>
                    </div>
                    <div className="col-sm-2">
                      <div className="form-check form-switch">

                        {appNotification && (
                          <ToggleBtn
                            isActive={appNotification?.ActiveStatus === 1}
                            onToggle={toggleNotification}
                            id={appNotification?.id}
                            type="N"
                          />
                        )}

                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationPermission;
