/* eslint-disable @next/next/no-img-element */
import React, { useState } from "react";
import { Modal, Button } from "react-bootstrap";
import { IoClose } from "react-icons/io5";
import {
  capitalizeFullName,
  convertDateFormat,
  formatDateandTime,
} from "../../../utils/helperfunction";
import ModalImage from "react-modal-image";
import RejectReasonModal from "../RejectReasonModal";

const ArticleModal = ({
  showArticleModal,
  setShowArticleModal,
  singleArticle,
  debouncedHandleArticleApprovals,
  showButtons,
  fetchArticles,
}) => {
  const article = singleArticle?.blog_details;
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleClose = () => setShowArticleModal(false);
  const handleShowReasonModal = () => setShowReasonModal(true);
  const hideReasonModal = () => setShowReasonModal(false);

  return (
    <>
      <Modal
        show={showArticleModal}
        onHide={handleClose}
        size="xl"
        centered
        scrollable
        dialogClassName="custom-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title style={{ color: "#8107D1" }} className="fw-bold">
            Article No - {article?.id}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <h3 className="fw-bold">{article?.BlogTitle}</h3>
          <div
            className="d-flex px-2"
            style={{ color: "#8107D1", fontWeight: "bold" }}
          >
            <p className="text-capitalize">
              Doctor - {capitalizeFullName(article?.expert_details?.name)}
            </p>
            <p className="ms-auto">
              {/* {convertDateFormat(article?.BlogDateTime.split("T")[0])} */}
              {formatDateandTime(article?.BlogDateTime)}
            </p>
          </div>
          <p
            dangerouslySetInnerHTML={{
              __html: article?.BlogBody,
            }}
          ></p>
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "center",
            }}
          >
            {article &&
              Array.isArray(article?.BlogImages) &&
              article?.BlogImages.map((img, index) => {
                const imageUrl = `${img.trim()}`;
                return (
                  <div className="d-flex p-3" key={index}>
                    <ModalImage
                      small={imageUrl}
                      className={"custom-small-image "}
                      large={imageUrl}
                      showRotate={true}
                      alt={`art-${index}`}
                    />
                  </div>
                );
              })}
          </div>
        </Modal.Body>
        {showButtons && (
          <Modal.Footer>
            <Button
              variant="danger"
              onClick={handleShowReasonModal}
              className="rounded-1"
            >
              Reject
            </Button>
            <Button
              variant="success"
              onClick={() =>
                debouncedHandleArticleApprovals(
                  "approve",
                  singleArticle?.blog_details?.id,
                  singleArticle?.blog_details?.BlogTitle
                )
              }
              className="py-2 ms-2"
            >
              Approve
            </Button>
          </Modal.Footer>
        )}
      </Modal>

      {/* Reject Reason Modal */}
      {showReasonModal && (
        <RejectReasonModal
          show={showReasonModal}
          onHide={hideReasonModal}
          debouncedHandleArticleApprovals={debouncedHandleArticleApprovals}
          singleArticle={singleArticle}
          type="article"
          fetchData={fetchArticles}
        />
      )}
    </>
  );
};

export default ArticleModal;
