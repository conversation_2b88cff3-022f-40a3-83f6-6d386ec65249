.accordion-container {
  border: 2px solid #fbfafa !important;
  font-size: 1.2em;
  background-color: #f9f9f9;
}

.accordion-header-custom:focus {
  border: 2px solid #fbfafa !important;
  background-color: #f9f9f9;
  outline: none;
}

.accordion-container .accordion-button {
  box-shadow: 0px 3px 6px #00000029;
  background-color: white;
}

.accordion-container .accordion-button:focus {
  box-shadow: 0px 3px 6px #00000029;
  background-color: white;
}

.faq-answer-section {
  padding: 0px !important;
}

.blogs-content-input {
  height: 21px;
  width: 21px;
  display: flex;
  justify-content: center;
  background-color: #dfdfdf;
  box-shadow: inset 0px 3px 6px #00000029 !important;
  border: none !important;
  border-radius: 3px;
}

.expert_image_blogs_list {
  border-radius: 50%;
  margin-right: 3%;
}

.article-modal-cont {
  width: 65%;
  margin-right: auto;
  margin-left: auto;
  margin-top: 5%;
  z-index: 9999px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 10px #00000029;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
  padding: 15px;
}

.modal-content-deactivate-modal {
  /* background: #ffffff 0% 0% no-repeat padding-box; */
  box-shadow: 0px 3px 10px #00000029;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
}

/* tableconteent */
.fixTableHead {
  overflow-y: auto;
  /* height: 450px; */
}

.fixTableHead thead th {
  position: sticky;
  top: 0;
}

.table.custon-table-featured {
  border-collapse: collapse;
  width: 100%;
}

/* .table.custon-table-featured {
  border-collapse: collapse; 
}

.table.custon-table-featured th,
.table.custon-table-featured td {
  border: none; 
} */

/* Tabs styling */

.custom-content-tab .nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  background: #fae5ff !important;
  box-shadow: inset 0 3px 6px #00000029;
  border-radius: 3px 3px 0 0;
  border: 1px solid #fae5ff !important;
  border-bottom: 2px solid #8107d1 !important;
  color: #5b5b5b !important;
}
 

.custom-content-tab .nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link {
  /* width: 170px; */
  width: 208px;
  background: #dfdfdf 0 0 no-repeat padding-box;
  border-radius: 3px 3px 0 0;
  border: 1px solid #fae5ff;
  border-bottom: 2px solid #414146;
  color: #5b5b5b;
  box-shadow: inset 0 3px 6px #00000029;
}

.selected-blogs-number {
  background-color: #8107d1;
  font-weight: 700;
  font-size: 14px;
  color: white;
  padding: 2px 6px;
  border-radius: 100%;
}

span.date-filter-expert {
  background: #ffffff;
  border-radius: 3px;
  padding: 9px;
  display: inline-flex;
  font-size: 12px;
  justify-content: space-between;
}

.calendar-icon-expert {
  color: #8107d1;
}

.form-control.search-input-focus::placeholder {
  font-size: 12px;
}