import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

const AddPodcastModal = () => {
  return (
    <>
      <Modal
        show={showPodcastModal}
        onHide={() => {
          setShowPodcastModal(false);
        }}
        size="lg"
      >
        <Modal.Header>
          <Modal.Title>Podcasts List for {doctorName} </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="col-sm-4">
            <div className="mb-3">
              <div className="row form-label custom-label">
                <div className="col-sm-auto">
                  <span>Upload Podcast for Selected Expert111</span>
                </div>
                {/* {selectedDoctor !== null && (
                  <div className="col-sm d-flex justify-content-end align-items-end">
                    <span
                      onClick={handleCancelPodcast}
                      style={{ color: "red", cursor: "pointer" }}
                    >
                      Clear
                    </span>
                  </div>
                )} */}
              </div>
              <div className="cancellation-desclaimer-section">
                <strong>Disclaimer:</strong> Podcasts can be uploaded for
                individual experts.
              </div>
            </div>
            {selectedDoctor !== null && (
              <div>
                <p>
                  You have selected{" "}
                  <strong>{capitalizeFullName(selectedDoctor.role)}</strong>
                  -&nbsp;
                  <strong>
                    {capitalizeFullName(selectedDoctor.name)}
                  </strong>{" "}
                  with an ID {selectedDoctor.id}
                </p>
              </div>
            )}

            <div className="">
              <label
                htmlFor="percentageInput"
                className="form-label custom-label"
              >
                Podcast Topic
              </label>
              <input
                type="text"
                className="form-control custom-input"
                id="title"
                name="title"
                placeholder="Enter Podcast title"
                value={formData?.PodcastTopic}
                onChange={(e) =>
                  setFormData((prevData) => ({
                    ...prevData,
                    PodcastTopic: e.target.value,
                  }))
                }
                required
              />
            </div>

            <div className="">
              <label
                htmlFor="percentageInput"
                className="form-label custom-label"
              >
                Podcast URL
              </label>
              <input
                type="text"
                placeholder="Enter video URL"
                className="form-control custom-podcast-input-control"
                value={formData?.PodcastURL}
                //   onChange={handleInputUrlChange}
                onChange={(e) => {
                  setFormData((prevData) => ({
                    ...prevData,
                    PodcastURL: e.target.value,
                  }));
                  setVideoUrl(event.target.value);
                }}
              />
            </div>
            <div className="">
              <label
                htmlFor="percentageInput"
                className="form-label custom-label"
              >
                Podcast Video Thumbnail
              </label>
              <input
                type="text"
                placeholder="Enter video URL"
                className="form-control custom-podcast-input-control"
                value={formData?.PodcastURL}
                //   onChange={handleInputUrlChange}
                onChange={(e) => {
                  setFormData((prevData) => ({
                    ...prevData,
                    PodcastURL: e.target.value,
                  }));
                  setVideoUrl(event.target.value);
                }}
              />
            </div>
            <button
              disabled={podcastUploading}
              type="submit"
              onClick={handleAddPodcast}
              className="btn purple-button mt-2"
            >
              {podcastUploading ? "Adding Podcast" : "Add Podcast"}
            </button>
            {videoUrl !== "" && (
              <button
                disabled={podcastUploading}
                type="button"
                onClick={() => {
                  setShowPreviewModal(true);
                }}
                className="btn purple-button mt-2 ms-2"
              >
                Preview
              </button>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            onClick={() => {
              setShowPodcastModal(false);
            }}
          >
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default AddPodcastModal;
