.purple-btn-edit-payment {
  background-color: #8107d1;
  border-radius: 5px;
  color: white;
  border: none;
}
.btn.grey-btn {
  background: #dfdfdf 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #414146;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}
.btn.grey-btn:hover,
.btn.grey-btn:active {
  background: #fae5ff 0% 0% no-repeat padding-box;

  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.btn.grey-btn:focus {
  background: #fae5ff 0% 0% no-repeat padding-box;
  /* box-shadow: inset 0px 3px 6px #00000029; */
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

/* for fee */
.bg-color {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  opacity: 1;
  background-color: white;
}
button.btn.purple-btn {
  background-color: #8107d1;
  color: white;
  border: none;
  font-size: 12px;
}
label.form-label.custom-form-label {
  letter-spacing: 0.44px;
  color: #52575d;
  font-size: 14px;
  font-weight: 400;
}

.btn.grey-btn.activeExpertsTab {
  background: #fae5ff !important;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  border: 1px solid #fae5ff !important;
  width: 169px;
  height: 45px;
  border-bottom: 2px solid #8107d1 !important;
  font-size: 14px;
  color: #5b5b5b !important;
  font-weight: 500;
}
button.grey-btn {
  background: #dfdfdf;
  border-radius: 3px 3px 0px 0px;
  letter-spacing: 0.38px;
  color: #333333;
  font-size: 12px;
  border: none;
  font-weight: 600;
  width: 169px;
  height: 45px;
}
.activeExpertsPaymentTab {
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 3px 3px 0px 0px;
  opacity: 1;
  width: 169px;
  height: 45px;
  font-size: 14px;
  color: white !important;
  font-weight: 500;
  background: #414146 !important;
}
.line-below-buttons {
  height: 2px;
  background-color: #000; /* Change to desired color */
  width: 100%;
  /* position: absolute; */
  bottom: -5px; /* Adjust this to position the line correctly */
}
.content-scroll-payment {
  max-height:815px;

}

.payment-back {
  min-height: 615px;
}

.line-horizontal {
  border: none; /* Remove the default border */
  height: 4px; /* Adjust the thickness (e.g., 3px) as needed */
  background-color: #cfcece; /* Set the line color */
  margin-bottom: 25px; /* Remove any default margin */
  padding: 1px; /* Remove any default padding */
}
button.choose-payment-btn {
  background: #ff971a !important;
  border: none !important;
  font-size: 12px;
  border-radius: 0px;
}
button.choose-payment-btn.clear-btn {
  background: #04ab20 !important;
}
.payment-search-bgm {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  padding: 10px;
}

.payment-method-background {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  padding: 15px;
  /* margin-left: 10px; */
  width: 98%;
  margin-left: 15px;
}

img.card-payment {
  width: 100%;
  height: 125px;
  object-fit: cover;
}
button.btn.btn-deactive {
  /* display: flex;
    position: absolute; */
  margin-top: -14px;
  margin-left: 90px;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  color: #ffff;
  border-radius: 3px;
}
button.btn.btn-deactive:focus {
  /* display: flex;
    position: absolute; */
  margin-top: -14px;
  margin-left: 90px;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  color: #ffff;
  border-radius: 3px;
}
button.btn.btn-active {
  /* display: flex; */
  /* position: absolute; */
  margin-top: -14px;
  /* width: 100px; */
  margin-left: 95px;
  background: #04ab20 0% 0% no-repeat padding-box;
  color: #ffff;
  border-radius: 3px;
}
button.btn.btn-active:focus {
  /* display: flex; */
  /* position: absolute; */
  margin-top: -14px;
  /* width: 100px; */
  margin-left: 95px;
  background: #04ab20 0% 0% no-repeat padding-box;
  color: #ffff;
  border-radius: 3px;
}
.method-pay {
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.44px;
  color: #8107d1;
  float: right;
  cursor: pointer;
}
.method-use {
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.44px;
  color: #52575d;
}
.pay-history {
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 0.5px;
  color: #333333;
}
.refund-apply {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  /* height: 211px; */
}
.row.row-payment {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 3px;
  padding: 18px;
  /* max-height: 95%; */
}
button.btn.btn-reject {
  width: 100%;
  background: #dfdfdf 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  padding: 6px;
}
button.btn.btn-reject:focus {
  width: 100%;
  background: #dfdfdf 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  padding: 6px;
}
button.btn.btn-approve {
  width: 100%;
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  padding: 6px;
}
button.btn.btn-approve:focus {
  width: 100%;
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  padding: 6px;
}

img.profile-refund {
  width: 92%;
  height: 96%;
  object-fit: contain;
  padding: 8px 0 0 18px;
}
span.doctor-name-payment {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  /* top: 10px; */
  margin-top: 20px;
}
.refund-amount {
  color: #343434;
  font-size: 14px;
  font-weight: bold;
  padding-left: 12px;
}
.invoice-link-refund {
  padding-left: 12px;
  color: #8107d1;
  font-size: 16px;
  font-weight: 500;
}
.patient-represent {
  color: #100db1;
  font-size: 16px;
  font-weight: 400;
}
.patient-name-refund {
  color: #343434;
  font-size: 16px;
  font-weight: bold;
}

table.table.table-containern {
  border-style: hidden;
  border: none;
}
.table.table-containern {
  border-collapse: collapse; /* Ensures there are no gaps between table cells */
  border: none;
}

.table.table-containern th,
.table.table-containern td {
  border: none; /* Removes borders from table cells */
}
.header-color {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
}
.sticky-header th {
  font-weight: 300;
  text-align: center;
  /* background-color: #4CAF50; */
  color: #414146;
  font-size: 15px;
}
.table > tbody {
  vertical-align: inherit;
  text-align-last: center;
  font-size: 14px;
  font-weight: 300;
  --bs-table-color-type: #52575d;
  /* color: #265ea1; */
}
button.btn.btn-purple-refund {
  border: 2px solid #8107d1;
  border-radius: 3px;
  letter-spacing: 0.38px;
  color: #8107d1;
  font-size: 15px;
  font-weight: 500;
}
button.btn.btn-purple-refund:active {
  border: 2px solid #8107d1;
  border-radius: 3px;
  letter-spacing: 0.38px;
  color: #8107d1;
}
button.btn.btn-purple-refund:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
}
.failed-indication {
  color: #ed4c4c;
  font-size: 10px;
}
.succeeded-indication {
  color: #1bc167;
  font-size: 10px;
}
.onhold-indication {
  color: #f4db50;
  font-size: 10px;
}

.table-data-pay {
  width: 15%;
  height: 15%;
  object-fit: contain;
  padding: 3px;
}
.patient-name-payment {
  color: #343434 !important;
  font-size: 14px;
  font-weight: bold;
  text-align: start;
  cursor: pointer;
}
.green-text {
  color: #198754;
  font-weight: 500;
}
/* .patient-name-payment:hover {
  color: #8107d1;
  font-size: 14px;
  font-weight: bold;
} */
/* td.name-profile {
    display: flex;
    text-align: left;
} */
li.page-item.page-style.active {
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #8107d1;
  --bs-pagination-active-border-color: none;
  --bs-pagination-disabled-color: var(--bs-pagination-active-bg);
  --bs-pagination-border-color: #ffffff;
  /* --bs-pagination-color: #000000; */
  --bs-emphasis-color: #000;
  border: none;
}
.page-link.active,
.active > .page-link {
  border-radius: 0px;
}
li.page-item.page-style {
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #8107d1;
  --bs-pagination-active-border-color: none;
  border-radius: 4px;
  --bs-pagination-border-color: #ffffff;
  --bs-emphasis-color: #000;
  border: none;
  --bs-pagination-bg: transparent;
  background-color: transparent;
}
.page-link {
  color: #000;
  text-decoration: none;
}
.page-link:hover {
  text-decoration: none;
  color: #000;
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid
    var(--bs-pagination-border-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.page-link:focus {
  box-shadow: none;
}
.pagination-icon {
  color: #7d8185;
}
button.btn.btn-renew {
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  width: 100%;
}
button.btn.btn-renew:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  width: 100%;
}
textarea.form-control.reason-textarea {
  border: none;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  height: 70px;
}
textarea.form-control.reason-textarea:focus {
  box-shadow: none;
}
textarea.form-control.expire-textarea {
  border: none;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  height: 100px;
}
textarea.form-control.expire-textarea:focus {
  box-shadow: none;
}
.starter {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  width: 100%;
  height: 110px;
}
span.month-plan {
  letter-spacing: 0.66px;
  color: #52575d;
  font-weight: bold;
  font-size: 21px;
  display: flex;
  justify-content: center;
}
.starter-paln {
  letter-spacing: 0.44px;
  color: #52575d;
  font-size: 14px;
  padding: 10px;
}
.subscription-plans {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  width: 90%;
  height: 354px;
}
button.btn.edit-plan {
  margin: 10px;
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  width: 94%;
  display: flex;
  justify-content: center;
}
button.btn.edit-plan:focus {
  color: #ffff;
  background: #8107d1 0% 0% no-repeat padding-box;
}
span.starter-plans {
  letter-spacing: 0.66px;
  color: #52575d;
  font-weight: bold;
  font-size: 21px;
  padding: 10px;
}
span.delete-plan {
  float: right;
  padding: 10px;
  letter-spacing: 0.44px;
  color: #ff2e2e;
  font-size: 14px;
  font-weight: bold;
}
.monthly-plan-cost {
  letter-spacing: 1.06px;
  color: #52575d;
  font-size: 34px;
  font-weight: bold;
  padding: 10px;
}
.sub-plans-heading {
  letter-spacing: 0.5px;
  color: #333333;
  font-size: 16px;
  font-weight: bold;
  align-items: flex-start;
  margin-top: 108px;
}
.user-per-month {
  padding: 10px;
}
.list-feature {
  font-size: 14px;
  letter-spacing: 0.44px;
  color: #52575d;
  font-weight: 500;
  margin: 15px;
}
.all-plans {
  letter-spacing: 0.44px;
  color: #52575d;
  font-size: 14px;
  font-weight: 400;
}
.all-users {
  letter-spacing: 0.44px;
  color: #8107d1;
  margin-top: -10px;
  font-size: 14px;
  font-weight: bold;
}
.days-of-expire {
  letter-spacing: 0.44px;
  color: #52575d;
  font-size: 14px;
  font-weight: 400;
  float: right;
  margin-right: 30px;
}
/* External CSS */
.custom-progress {
  width: 50%; /* Set the width to control the progress level */
  background: #8107d1 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 8px;
  color: #fff; /* Change the text color inside the progress bar */
  font-weight: bold;
  text-align: center;
  --bs-progress-bg: #ffffff;
}
.custom-progress-2 {
  width: 80%; /* Set the width to control the progress level */
  background: #8107d1 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 8px;
  color: #fff; /* Change the text color inside the progress bar */
  font-weight: bold;
  text-align: center;
  --bs-progress-bg: #ffffff;
}
.progress.progress-back {
  --bs-progress-bg: #fff;
  height: 10px;
  width: 93%;
}
.custom-progress::before {
  position: absolute;
  width: 100%;
}

.custom-progress::after {
  display: block;
  text-align: center;
  margin-top: 5px;
}
.col-sm-6.borde-right-style {
  border-radius: 1p;
  border-right: 1px solid #dfdfdf;
}
.custom-modal-content-pricing {
  width: 558px;
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 10px #00000029;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
  height: 738px;
}
.modal-header-payment {
  height: 57px;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 5px 5px 0px 0px;
}
.title-edit.modal-title.h4 {
  font-size: 18px;
  font-weight: bold;
  color: #5b5b5b;
}
button.btn.btn-cancle-plan {
  font-size: 18px;
  font-weight: 500;
  color: #ff2e2e;
  border: none;
}
button.btn.btn-cancle-plan:focus {
  font-size: 18px;
  font-weight: 500;
  color: #ff2e2e;
  border: none;
}
h6.edit-plans-pricing {
  color: #333333;
  font-size: 16px;
  font-weight: bold;
}
span.values-cost {
  font-size: 30px;
  font-weight: bold;
  color: #52575d;
}
label.label-edit.form-label {
  font-weight: bold;
  color: #333333;
  font-size: 16px;
}
form.form-bg-edit {
  padding: 15px;
  height: 254px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}
input#planName {
  box-shadow: none;
  height: 49px;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
}
input#planvalue {
  box-shadow: none;
  height: 49px;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
}
button.btn.btn-confirm-edit.btn.btn-primary {
  width: 100%;
  /* text-decoration: none; */
  background: #ff971a 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  height: 49px;
  border: none;
}
.plans-info-bg {
  padding: 6px;
  height: 248px;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}
button.btn.btn-save-edit {
  width: 40%;
  height: 49px;
  float: right;
  border: none;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
}
button.btn.btn-save-edit:focus {
  width: 40%;
  height: 49px;
  border: none;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
}
.point-bg {
  height: 38px;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border-radius: 3px;
}
.point-bg-1 {
  font-size: 24px;
  height: 38px;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border-radius: 3px;
}
/* Add this to your styles or a separate CSS file */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: purple;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded switch styles */
.switch-round .slider {
  border-radius: 34px;
}

.switch-round .slider:before {
  border-radius: 50%;
}
.form-switch .form-check-input {
  width: 55px;
  height: 25px;
  /* float: left; */
  background-image: var(--bs-form-switch-bg);
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
:root {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23FF0000'/%3e%3c/svg%3e");
}
label.label-select {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}
.plan-select-flow.row {
  position: relative;
  float: right;
}

.invoice-logo {
  width: 90px;
  height: 41px;
}
.purple-content{
  color: #713c8f !important;
  font-weight: 600;
}

.purple-content-invoice {
  color: #713c8f;
  font-size: 14px;
  font-weight: 600;
}
.black-content-invoice {
  color: #5b5b5b;
  font-size: 12px;
  font-weight: 500;
}
.purple-content-admin-invoice {
  letter-spacing: 0px;
  color: #8d4f9f;
  opacity: 1;
  font-size: 16px;
  line-height: 17px;
}

.purple-download-button-invoice {
  border: none;
  background-color: #713c8f;
  color: white;
  padding: 0px;
  /* font-weight: 600; */
  font-size: 11px;
  padding: 5px;
  border-radius: 20px;
  padding-left: 10px;
  padding-right: 10px;
}
.invoice-date-heading {
  letter-spacing: 0px;
  color: #223645;
  opacity: 1;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px !important;
  /* width: 120%;
    margin-left: 40px; */
}

.refund-profiles-container {
  max-height: 660px;
}
.refund-profiles-container {
  overflow-y: auto;
  scrollbar-width: thin;
}

.refund-profiles-container::-webkit-scrollbar {
  width: 8px;
}

.refund-profiles-container::-webkit-scrollbar-thumb {
  background-color: #713c8f !important;
  border-radius: 4px;
}

.form-control.expert-search-bar {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  font-size: 14px;
  color: #5b5b5b;
  height: 40px;
  border: 1px solid #dee2e6;
}

.form-control.expert-search-bar :focus {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  opacity: 1;
  font-size: 14px;
  color: #5b5b5b;
  height: 42px;
  border: 1px solid #dee2e6;
}
.cross-icon-calendar {
  color: #ff7700;
}
.cancel-expert-search-btn {
  z-index: 9999;
  position: absolute;
  top: 0px;
  right: -5px;
}
/* ******************************** */
.custom-date-input {
  border: none !important;
  width: 160px !important;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px !important;
  font-size: 12px !important;
  background: #fbfbfb;
  color: #96969c !important;
}
.custom-date-input:focus {
  border: none;

  box-shadow: 0px 3px 50px #00000014 !important;
  border-radius: 3px;
  background: #fbfbfb;
  color: #96969c;
}
.custom-date-input::-webkit-calendar-picker-indicator {
  filter: invert(28%) sepia(85%) saturate(4350%) hue-rotate(237deg)
    brightness(91%) contrast(99%);
}
.custom-search-input {
  border: none !important;
  width: 220px !important;
  background-color: #f6f6f6 !important;
  font-size: 12px !important;
}

.custom-button {
  background-color: #6b238e !important;
  border: none !important;
  color: white;
  padding: 8px 20px !important;
  border-radius: 5px !important;
  font-weight: bold !important;
  font-size: 12px !important;
}

.date-range-label {
  margin-right: 45px;
  letter-spacing: 0px;
  color: #414146;
  font-size: 12px;
}

.custom-input-group {
  display: flex;
  align-items: center;
}

.custom-search-container {
  display: flex;
  align-items: center;
}
table.table.custom-table {
  width: 100%;
  background-color: #fff;
  border-collapse: separate;
  border-spacing: 0 15px;
  margin-bottom: 0px;
  border: none;
}

.table.custom-table th {
  color: #000;
  font-weight: bold;
  text-align: left;
  padding: 10px;
  border: none;
}

.table.custom-table td {
  background-color: #f6f6f6 !important;
  vertical-align: middle;
  font-size: 12px;
  font-weight: 500;
  border: none;
  padding: 7px;
}

.custom-table tr td:nth-child(3),
.custom-table tr td:nth-child(5) {
  color: #52575d;
}

.custom-table tr td:nth-child(5) {
  background-color: #dfdfdf !important;
  color: #8107d1;
  font-weight: bold;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  width: 20%;
}

/* .custom-table tr td:nth-child(6) {
  color: #ff9800; 
}

.custom-table tr td:nth-child(6).cleared {
  color: #4caf50; 
} */

.custom-table tr td {
  border-bottom: none;
}

.custom-table tr:last-child td {
  border-bottom: none;
}

.expert-name {
  color: #6b238e; /* Dark purple */
  font-weight: bold;
}
.modal-content-custom {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  width: 80%;
  max-width: 800px;
  margin: auto;
}

.modal-header-custom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
  padding-bottom: 0;
}

.modal-title-custom {
  font-size: 1.5rem;
  font-weight: bold;
}

.modal-body-custom {
  padding-top: 0;
}

.table-custom th,
.table-custom td {
  padding: 1rem;
}

.button-custom {
  background-color: #800080;
  border-color: #800080;
  color: white;
}

.button-custom:focus {
  box-shadow: none;
}

.close-button-custom {
  background: none;
  border: none;
  font-size: 1rem;
  color: #ff0000;
  cursor: pointer;
}

.modal-footer-custom {
  display: flex;
  justify-content: flex-end;
}
/* *******************************************8 */
.table-row-pending {
  /* background-color: #0d6efd; */
  border-style: none;
  border-color: transparent;
}
th.pending-row-custom {
  letter-spacing: 0px;
  color: #414146;
  font-size: 12px;
  font-weight: 500;
  background: #f6f6f6 !important;
  text-align: center;
}
td.expert-name {
  letter-spacing: 0px;
  color: #8107d1;
  font-weight: 600 !important;
}
img.wallet-details-expert-image {
  border-radius: 50%;
  margin-right: 10px;
}
/* *************************Sort by filter******************** */
/* Container for the filter buttons */
.filter-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Label for the sort by text */
.sort-by-label {
  font-weight: bold;
  color: #6d00b5;
  margin-right: 10px;
}

/* Container for the buttons */
.buttons-container {
  display: flex;
  gap: 10px;
}

/* Style for each button */
.filter-button {
  display: flex;
  align-items: center;
  background-color: #f3e5f5;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 16px;
  color: #6d00b5;
  transition: background-color 0.3s;
}

/* Style for the active button */
.filter-button.active {
  background-color: #6d00b5;
  color: #fff;
}

/* Hide the default checkbox */
.filter-button input[type="checkbox"] {
  display: none;
}

/* Custom checkbox styling */
.filter-button::before {
  content: "☑";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  font-size: 16px;
  color: transparent;
  transition: color 0.3s;
  border: 2px solid #6d00b5;
}

/* Show the checkbox when the button is active */
.filter-button.active::before {
  color: #fff;
  position: relative;
  top: -9px;
  /* margin-bottom: 19px; */
  text-align: center;
  font-size: 20px;
}
/* Change background color on hover */
/* .filter-button:hover {
  background-color: #f3e5f5;
} */

/* Media query for responsiveness */
@media (max-width: 768px) {
  .buttons-container {
    flex-direction: column;
  }

  .filter-button {
    width: 100%;
    text-align: left;
  }
}

/* *****************************cleared modal************* */
.payment-history-balance-section {
  background: #fff8ff 0% 0% no-repeat padding-box;
  border-radius: 8px;
}
.current-wallet {
  letter-spacing: 0px;
  color: #6d00b5;
  font-size: 21px;
  font-weight: 600;
}
.current-balance {
  color: #6d00b5;
  font-size: 21px;
  font-weight: 600;
  text-align: right;
}
/* ********************Pending payment modal************* */
.custom-modal-header.modal-header {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 5px 5px 0px 0px;
}

.label-text {
  font-size: 14px;

  letter-spacing: 0.44px;
  color: #333333;
  font-weight: 500;
}
.value-text-grey {
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.5px;
  color: #96969c;
}
.value-text {
  font-size: 16px;
  font-weight: bold;
  color: #6d00b5;
}
.custom-modal-content-bg {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #f1f1f1;
  border-radius: 3px;
}
.expert-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.ml-3 {
  margin-left: 1rem;
}

.uploaded-documents {
  display: flex;
  flex-direction: column;
  /* border: 1px solid red; */
}
.no-transaction-receipt {
  color: #6d00b5;
}

.transction-receipt {
  width: 15%;
  display: flex;
  align-items: center;
  flex-direction: column;
  background-color: #f3e5f5;
  color: #6d00b5;
}

.custom-FaFilePdf-icon {
  color: #6d00b5;
  font-size: 2.2em;
}

.upload-label {
  display: block;
  width: 100%;
  cursor: pointer;
}

.upload-box {
  /* display: flex;
  align-items: center;
  justify-content: center; */
  background-color: #f9f9f9;
  border: 2px dashed #dee2e6;
  padding: 20px;
  border-radius: 5px;
  text-align: center;
  width: 100%;
  cursor: pointer;
}

.upload-box p {
  color: #96969c;
  font-size: 16px;
}

.file-input {
  display: none;
}

.submit-button {
  background-color: #6d00b5;
  border-color: #6d00b5;
  color: #fff;
}

.submit-button:hover {
  background-color: #5b0096;
  border-color: #5b0096;
}

.clear-payment-custom-IoMdCloudUpload-icon {
  color: #96969c;
  font-size: 4em;
}

.expert-payment-history-modal {
  max-width: 90% !important;
}

.payment-history-modal-header {
  background: #f3f1f1;
  color: #5b5b5b;
}
.payment-history-modal-header-cancel-button {
  color: #ff2e2e;
  cursor: pointer;
  font-weight: 600;
}

.juBESy {
  width: 100%;
}

.contentManage-custom-scroll-fee {
  max-height: 615px;
  padding: 12px;
}
.purple-content-fee {
  color: #713c8f;
  font-weight: 600;
  font-size: 12px;
}

.table-wrapper-scroll-y {
  display: block;
  max-height: 340px; /* Set a max height for the scrollable area */
  overflow-y: auto;
  overflow-x: hidden;
}

.my-custom-scrollbar {
  scrollbar-color: dark;
  scrollbar-width: thin;
}

/* Optional: Style for the scrollbar */
.table-wrapper-scroll-y::-webkit-scrollbar {
  width: 10px;
}

.table-wrapper-scroll-y::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.table-wrapper-scroll-y::-webkit-scrollbar-thumb:hover {
  background: #555;
}
