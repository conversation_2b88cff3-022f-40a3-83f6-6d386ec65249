import React, { useCallback, useContext, useEffect, useState } from "react";
import ContentManPlaceholder from "../contentManagement/UserProfileContent/ContentManPlaceholder";
import { AdminDetailsContext } from "../../Context/AdminContext/AdminContext";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import Image from "next/image";
import { IoMdAddCircleOutline } from "react-icons/io";
import { FaEye } from "react-icons/fa";
import { capitalizeFullName } from "../expert-doc/db";
import dummyProfile from "../../../public/images/dummy-avatar.jpg";
import { Table } from "react-bootstrap";
import Swal from "sweetalert2";

const ExpertCommission = () => {
  const [loading, setLoading] = useState(true);
  const [expertsData, setExpertsData] = useState([]);
  const axiosAuth = useAxiosAuth();
  const { session } = useContext(AdminDetailsContext);
  const admin_id = session?.user.id;

  const fetchExpertsData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}1/all/?user_id=${admin_id}`
      );
      if (response.status === 200) {
        setExpertsData(response?.data?.experts_data);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching data from the API: ", error);
      setLoading(false);
    }
  }, [axiosAuth, admin_id]);

  useEffect(() => {
    fetchExpertsData();
  }, [fetchExpertsData]);

  const handleAddOrEditCommission = async (expertId, existingCommission) => {
    const { value: commission } = await Swal.fire({
      title: existingCommission ? "Edit Commission" : "Add Commission",
      input: "text",
      inputLabel: "Enter Commission Amount",
      inputPlaceholder: "e.g. 10%",
      inputValue: existingCommission || "",
      showCancelButton: true,
      confirmButtonText: existingCommission ? "Update" : "Submit",
      cancelButtonText: "Cancel",
      inputValidator: (value) => {
        if (!value) {
          return "You need to enter a commission amount!";
        }
      },
    });

    if (commission) {
      // Post the commission data
      try {
        const response = await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_ADD_EXPERT_COMMISSION_API}${expertId}/?user_id=${admin_id}`,
          { CommissionPercentage: commission }
        );
        if (response.status === 200) {
          Swal.fire(
            "Success",
            `Commission ${
              existingCommission ? "updated" : "added"
            } successfully!`,
            "success"
          );
          fetchExpertsData(); // Refresh the expert data
        } else {
          Swal.fire(
            "Error",
            `Failed to ${existingCommission ? "update" : "add"} commission`,
            "error"
          );
        }
      } catch (error) {
        console.error(
          `Error ${existingCommission ? "updating" : "adding"} commission: `,
          error
        );
        Swal.fire(
          "Error",
          `Failed to ${existingCommission ? "update" : "add"} commission`,
          "error"
        );
      }
    }
  };

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <div>
      <h5>Add/Edit Commission to Selected Expert</h5>
      {/* Scrollable Table Wrapper */}
      <div className="table-wrapper-scroll-y my-custom-scrollbar overflow-x-hidden">
        <Table striped bordered style={{ fontSize: "12px" }}>
          <thead className="sticky-table-head">
            <tr className="text-center">
              <th scope="col" className="pending-row-custom fw-bold">
                Sl No
              </th>
              <th scope="col" className="pending-row-custom fw-bold">
                Expert Id
              </th>
              <th scope="col" className="pending-row-custom fw-bold">
                Expert Profile
              </th>
              <th scope="col" className="pending-row-custom fw-bold">
                Expert Role
              </th>
              <th scope="col" className="pending-row-custom fw-bold">
                Expert Consultation Fee
              </th>
              <th scope="col" className="pending-row-custom fw-bold">
                Expert Commission Percentage
              </th>
              <th scope="col" className="pending-row-custom fw-bold">
                Add/Edit Commission
              </th>
            </tr>
          </thead>
          <tbody className="text-center ">
            {expertsData &&
              expertsData.map((item, index) => (
                <tr key={index}>
                  <th scope="row">{index + 1}</th>
                  <td>{item?.id}</td>
                  <td className="d-flex justify-content-start align-items-center">
                    {item?.profile_photo ? (
                      <Image
                        src={item?.profile_photo}
                        alt={item?.name}
                        width={35}
                        height={35}
                        className="expert_image rounded-circle"
                      />
                    ) : (
                      <Image
                        src={dummyProfile}
                        alt={item?.name}
                        width={35}
                        height={35}
                        className="expert_image rounded-circle"
                      />
                    )}
                    &nbsp;&nbsp;
                    <span className="purple-content-fee">
                      {capitalizeFullName(item?.name || "Expert Name")}
                    </span>
                  </td>
                  <td>
                    {capitalizeFullName(item?.user_role || "Expert Role")}
                  </td>
                  <td>$ {item?.consultation_fees?.toFixed(2) || "00.00"}</td>
                  <td>
                    {item.commission_percentage
                      ? item.commission_percentage
                      : "0"}{" "}
                    %
                  </td>
                  <td>
                    <button
                      className=" py-2 px-5 purple-btn-edit-payment"
                      onClick={() =>
                        handleAddOrEditCommission(
                          item.id,
                          item.commission_percentage
                        )
                      }
                    >
                      {item.commission_percentage ? "Edit" : "Add"}
                    </button>
                  </td>
                </tr>
              ))}
          </tbody>
        </Table>
      </div>
    </div>
  );
};

export default ExpertCommission;
