import React from "react";
import { IoClose } from "react-icons/io5";
import Modal from "react-modal";
import ModalImage from "react-modal-image";
import { capitalizeFullName, convertDateFormat } from "../../../utils/helperfunction";

const ViewBlogsContentModal = ({
  showBlogsModal,
  setShowBlogsModal,
  singleBlog,
}) => {
  const handleClose = () => setShowBlogsModal(!showBlogsModal);

  return (
    <div>
      <Modal
        isOpen={showBlogsModal}
        onRequestClose={() => setShowBlogsModal(!showBlogsModal)}
        className=" article-modal-cont"
        // contentLabel="Example Modal"
      >
        <div className="modal-content custom-modal-content-article">
          <div className="d-flex justify-content-between ">
            <div style={{ color: "#8107D1" }}>
              <h2>Article no - {singleBlog.id}</h2>
            </div>
            <div>
              <IoClose
                onClick={handleClose}
                color="#8107D1"
                size={25}
                cursor={"pointer"}
              />
            </div>
          </div>
          <div
            className="modal-body-custom"
            style={{ maxHeight: "400px", overflowY: "auto" }}
          >
            <h5>{singleBlog.BlogTitle}</h5>
            <div
              className=" d-flex px-2"
              style={{ color: "#8107D1", fontWeight: "bold" }}
            >
              <p className=" text-capitalize">
                Doctor - {capitalizeFullName(singleBlog?.expert_details?.name)}
              </p>
              <p className=" ms-auto">
                {convertDateFormat(singleBlog?.BlogDateTime.split("T")[0])}
              </p>
            </div>
            <div className="c">
              <p
                dangerouslySetInnerHTML={{
                  __html: singleBlog?.BlogBody,
                }}
              ></p>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ViewBlogsContentModal;
