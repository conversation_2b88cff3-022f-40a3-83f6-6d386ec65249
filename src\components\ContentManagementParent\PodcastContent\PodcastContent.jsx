import React, { useCallback, useEffect, useMemo, useState } from "react";
import "../BlogsContent/blogs-content.css";
import Accordion from "react-bootstrap/Accordion";
import FeaturedPodcast from "../PodcastContent/FeaturedPodcast";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { Placeholder } from "react-bootstrap";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import PodcastMainList from "./PodcastMainList";
import TopPodcasts from "./TopPodcasts";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import { IoIosInformationCircle } from "react-icons/io";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import axios from "axios";
import AllBlogsFilter from "../BlogsContent/AllBlogsFilter";
import { formatDateToYMD } from "../../../utils/helperfunction";
import _ from "lodash";
import AddCategory from "../../contentManagement/UserProfileContent/AddCategory";

const PodcastContent = () => {
  const [loading, setLoading] = useState(true);
  const [current_page, setCurrent_Page] = useState(1);
  const [podcasts, setPodcasts] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [featuredLoading, setFeaturedLoading] = useState(true);
  const [topLoading, setTopLoading] = useState(true);
  const [featuredPodcasts, setFeaturedPodcasts] = useState([]);
  const [topPodcasts, setTopPodcasts] = useState([]);

  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const axiosAuth = useAxiosAuth();

  const fetchAllPodcasts = useCallback(
    async (query) => {
      try {
        setLoading(true);
        if (current_page === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_All_PUBLIC_PODCASTS}all/?page=${current_page}`;
        if (query) {
          url += `&search=${query}`;
        } else if (startDate && endDate) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
        }
        const response = await axios.get(url);
        setPodcasts(response?.data);
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      } catch (error) {
        console.error("Error fetching Blogs: ", error);
      } finally {
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      }
    },
    [current_page, startDate, endDate]
  );

  const debouncedFetchData = useMemo(
    (query) => {
      return _.debounce((query) => {
        fetchAllPodcasts(query);
      }, 500);
    },
    [fetchAllPodcasts]
  );

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  const fetchFeaturedPodcasts = useCallback(async () => {
    try {
      setFeaturedLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_GET_FEATURED_bLOGS}${"1"}/Featured/`
      );
      setFeaturedPodcasts(response?.data);
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
    } finally {
      setFeaturedLoading(false);
    }
  }, []);

  const fetchTopPodcasts = useCallback(async () => {
    try {
      setTopLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_GET_FEATURED_bLOGS}${"1"}/Top/`
      );
      setTopPodcasts(response?.data);
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
    } finally {
      setTopLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFeaturedPodcasts();
    fetchTopPodcasts();
  }, [fetchFeaturedPodcasts, fetchTopPodcasts]);

  const handleDeleteRanking = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete the ranking for this Podcast?",
      // icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });
    if (result.isConfirmed) {
      const body = {
        podcast_rank: 0,
      };

      try {
        setDeleteLoading(true);
        const response = await axiosAuth.delete(
          `${process.env.NEXT_PUBLIC_RANK_FEATURED_BLOGS}${id}/?user_id=${userId}`,
          { data: body }
        );
        if (response?.data === "successfully deleted") {
          toast.success(`The Rank has been ${response?.data}`, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        }
        setDeleteLoading(false);
      } catch (err) {
        toast.error(`Error in deleting the rank`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
    }
  };

  return (
    <div style={{ maxHeight: "820px" }} className=" overflow-y-auto">
      <h5 className=" my-3 admin-add-blog-list-header fw-semibold">
        Manage Top and Featuerd Podcasts
      </h5>
      <div className=" overflow-auto contentManage-custom-scroll-blog">
      <div className="row">
        <AllBlogsFilter
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          setSearchQuery={setSearchQuery}
          searchQuery={searchQuery}
          loading={loading}
        />
      </div>
      
        <PodcastMainList
          podcasts={podcasts}
          loading={loading}
          initialLoading={initialLoading}
          current_page={current_page}
          setCurrent_Page={setCurrent_Page}
          fetchAllPodcasts={fetchAllPodcasts}
          fetchFeaturedPodcasts={fetchFeaturedPodcasts}
          fetchTopPodcasts={fetchTopPodcasts}
          searchQuery={searchQuery}
        />

        <Accordion
        //   defaultActiveKey="0"
        >
          <Accordion.Item
            className=" accordion-container p-2 rounded"
            eventKey="0"
          >
            <Accordion.Header className=" accordion-header-custom">
              <div className=" d-flex w-100 align-items-center">
                <div className=" d-flex ">
                  <p className="c mb-0">Featured Podcast</p>
                  <span className=" selected-blogs-number ms-1">
                    {featuredPodcasts?.length}
                  </span>
                </div>
                <span className=" ms-auto me-5">
                  <a
                    data-tooltip-id="my-tooltip"
                    data-tooltip-content="You can only select 3 featured Podcasts"
                  >
                    <IoIosInformationCircle />
                  </a>
                  <Tooltip id="my-tooltip" />
                </span>
              </div>
            </Accordion.Header>
            <Accordion.Body className="faq-answer-section">
              <FeaturedPodcast
                handleDeleteRanking={handleDeleteRanking}
                fetchAllPodcasts={fetchAllPodcasts}
                featuredPodcasts={featuredPodcasts}
                featuredLoading={featuredLoading}
                fetchFeaturedPodcasts={fetchFeaturedPodcasts}
                deleteLoading={deleteLoading}
              />
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item
            className=" accordion-container p-2 rounded"
            eventKey="1"
          >
            <Accordion.Header>
              <div className=" d-flex w-100 align-items-center">
                <div className=" d-flex ">
                  <p className="c mb-0">Top Podcasts</p>
                  <span className=" selected-blogs-number ms-1">
                    {topPodcasts?.length}
                  </span>
                </div>
                <span className=" ms-auto me-5">
                  <a
                    data-tooltip-id="my-tooltip"
                    data-tooltip-content="You can only select 6 top Podcasts"
                  >
                    <IoIosInformationCircle />
                  </a>
                  <Tooltip id="my-tooltip" />
                </span>
              </div>
            </Accordion.Header>
            <Accordion.Body className="faq-answer-section">
              <TopPodcasts
                topLoading={topLoading}
                fetchAllPodcasts={fetchAllPodcasts}
                topPodcasts={topPodcasts}
                handleDeleteRanking={handleDeleteRanking}
                fetchTopPodcasts={fetchTopPodcasts}
                deleteLoading={deleteLoading}
              />
            </Accordion.Body>
          </Accordion.Item>
        </Accordion>
        <div className=" bg-color mb-4 mt-4">
          <AddCategory />
        </div>
      </div>
    </div>
  );
};

export default PodcastContent;
