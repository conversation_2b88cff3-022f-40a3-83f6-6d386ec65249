import Head from "next/head";
import "./globals.css";
import { Poppins } from "next/font/google";
import "bootstrap/dist/css/bootstrap.css";
import "bootstrap/dist/css/bootstrap.min.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Provider from "../components/sessionProvider/SessionProvider.jsx";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import AdminProvider from "../Context/AdminContext/AdminContext";
import "react-tooltip/dist/react-tooltip.css";
import { NotificationProvider } from "../Context/NotificationContext/NotificationContext";
import ServiceWorkerRegister from "@/components/ServiceWorker/ServiceWorkerRegister";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export default function RootLayout({ children }) {

  return (
    <html lang="en">
      {/* <Head> */}
      <title>Super Admin</title>
      <meta name="description" content="Cancer Unwired Admin Dashboard" />
      <meta name="msvalidate.01" content="5B64A670CAC5B6E3000E9F7F885093B7" />
      {/* </Head> */}
      <body
        className={poppins.className}
        style={{
          backgroundColor: "#FBFBFB",
          // height: "100vh",
          // overflowY: "auto",
        }}
      >
        <Provider>
          <AdminProvider>
            <ServiceWorkerRegister />
            <NotificationProvider>
              <ToastContainer
                theme="colored"
                position="top-center"
                autoClose={5000}
              />
              {children}
            </NotificationProvider>
          </AdminProvider>
        </Provider>
      </body>
    </html>
  );
}
