import React, { useCallback, useEffect, useMemo, useState } from "react";
import "./blogs-content.css";
import Accordion from "react-bootstrap/Accordion";
import FeaturedContent from "../BlogsContent/FeaturedContent";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import TopTrendingBlogs from "./TopTrendingBlogs";
import TopBlogsContent from "./TopBlogsContent";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import BlogsMainList from "./BlogsMainList";
import Swal from "sweetalert2";
import { toast } from "react-toastify";
import { IoIosInformationCircle } from "react-icons/io";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import axios from "axios";
import AllBlogsFilter from "./AllBlogsFilter";
import _ from "lodash";
import { formatDateToYMD } from "../../../utils/helperfunction";
import BlogByAdmin from "../../contentManagement/UserProfileContent/BlogByAdmin";

const BlogsContent = () => {
  const [loading, setLoading] = useState(true);
  const [current_page, setCurrent_Page] = useState(1);
  const [blogs, setBlogs] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(true);
  const [featuredLoading, setFeaturedLoading] = useState(true);
  const [topLoading, setTopLoading] = useState(true);
  const [trendingLoading, setTrendingLoading] = useState(true);
  const [featuredBlogs, setFeaturedBlogs] = useState([]);
  const [topBlogs, setTopBlogs] = useState([]);
  const [trendingBlogs, setTrendingBlogs] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();

  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const axiosAuth = useAxiosAuth();

  const fetchAllBlogs = useCallback(
    async (query) => {
      try {
        // setLoading(true);
        if (current_page === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_All_PUBLIC_BLOGS}all/?page=${current_page}`;
        if (query) {
          url += `&search=${query}`;
        } else if (startDate && endDate) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
        }
        const response = await axios.get(url);
        setBlogs(response?.data);
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      } catch (error) {
        console.error("Error fetching Blogs: ", error);
      } finally {
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      }
    },
    [current_page, startDate, endDate]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((query) => {
      fetchAllBlogs(query);
    }, 500);
  }, [fetchAllBlogs]);

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  const fetchFeaturedBlogs = useCallback(async () => {
    try {
      setFeaturedLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_GET_FEATURED_bLOGS}${"0"}/Featured/`
      );
      setFeaturedBlogs(response?.data);
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
    } finally {
      setFeaturedLoading(false);
    }
  }, []);

  const fetchTopBlogs = useCallback(async () => {
    try {
      setTopLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_GET_FEATURED_bLOGS}${"0"}/Top/`
      );
      setTopBlogs(response?.data);
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
    } finally {
      setTopLoading(false);
    }
  }, []);

  const fetchTrendingBlogs = useCallback(async () => {
    try {
      setTrendingLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_GET_FEATURED_bLOGS}${"0"}/Trending/`
      );
      setTrendingBlogs(response?.data);
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
    } finally {
      setTrendingLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFeaturedBlogs();
    fetchTopBlogs();
    fetchTrendingBlogs();
  }, [fetchFeaturedBlogs, fetchTopBlogs, fetchTrendingBlogs]);

  const handleDeleteRanking = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete the ranking for this blog?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
    });
    if (result.isConfirmed) {
      const body = {
        blog_rank: 0,
      };

      try {
        setDeleteLoading(true);
        const response = await axiosAuth.delete(
          `${process.env.NEXT_PUBLIC_RANK_FEATURED_BLOGS}${id}/?user_id=${userId}`,
          { data: body }
        );
        if (response?.data === "successfully deleted") {
          toast.success(`The Rank has been ${response?.data}`, {
            position: "top-center",
            theme: "colored",
            autoClose: 2500,
          });
        }
        setDeleteLoading(false);
        fetchAllBlogs();
      } catch (err) {
        toast.error(`Error in deleting the rank`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
    }
  };

  return (
    <div>
      <h5 className="my-3 admin-add-blog-list-header fw-semibold">
        Manage Top, Featured and Trending Blogs
      </h5>
      <div className="row">
        <AllBlogsFilter
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          setSearchQuery={setSearchQuery}
          searchQuery={searchQuery}
          loading={loading}
        />
      </div>

      <div className=" overflow-auto contentManage-custom-scroll-blog">
        <BlogsMainList
          blogs={blogs}
          loading={loading}
          initialLoading={initialLoading}
          current_page={current_page}
          setCurrent_Page={setCurrent_Page}
          fetchAllBlogs={fetchAllBlogs}
          fetchFeaturedBlogs={fetchFeaturedBlogs}
          fetchTopBlogs={fetchTopBlogs}
          fetchTrendingBlogs={fetchTrendingBlogs}
          searchQuery={searchQuery}
        />

        <Accordion>
          <Accordion.Item
            className=" accordion-container p-2 rounded"
            eventKey="0"
          >
            <Accordion.Header className=" accordion-header-custom">
              <div className=" d-flex w-100 align-items-center">
                <div className=" d-flex ">
                  <p className="c mb-0">Featured Blog </p>
                  <span className=" selected-blogs-number ms-1">
                    {featuredBlogs?.length}
                  </span>
                </div>
                <span className=" ms-auto me-5">
                  <a
                    data-tooltip-id="my-tooltip"
                    data-tooltip-content="You can only select 3 featured blogs"
                  >
                    <IoIosInformationCircle />
                  </a>
                  <Tooltip id="my-tooltip" />
                </span>
              </div>
            </Accordion.Header>
            <Accordion.Body className="faq-answer-section">
              <FeaturedContent
                fetchFeaturedBlogs={fetchFeaturedBlogs}
                featuredLoading={featuredLoading}
                fetchAllBlogs={fetchAllBlogs}
                featuredBlogs={featuredBlogs}
                handleDeleteRanking={handleDeleteRanking}
              />
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item
            className=" accordion-container p-2 rounded"
            eventKey="1"
          >
            <Accordion.Header>
              <div className=" d-flex w-100 align-items-center">
                <div className=" d-flex ">
                  <p className="c mb-0">Trending Blogs</p>
                  <span className=" selected-blogs-number ms-1">
                    {trendingBlogs?.length}
                  </span>
                </div>
                <span className=" ms-auto me-5">
                  <a
                    data-tooltip-id="my-tooltip"
                    data-tooltip-content="You can only select 4 trending blogs"
                  >
                    <IoIosInformationCircle />
                  </a>
                  <Tooltip id="my-tooltip" />
                </span>
              </div>
            </Accordion.Header>
            <Accordion.Body className="faq-answer-section">
              <TopTrendingBlogs
                handleDeleteRanking={handleDeleteRanking}
                trendingBlogs={trendingBlogs}
                fetchTrendingBlogs={fetchTrendingBlogs}
                fetchAllBlogs={fetchAllBlogs}
                trendingLoading={trendingLoading}
              />
            </Accordion.Body>
          </Accordion.Item>
          <Accordion.Item
            className=" accordion-container p-2 rounded"
            eventKey="2"
          >
            <Accordion.Header>
              <div className=" d-flex w-100 align-items-center">
                <div className=" d-flex ">
                  <p className="c mb-0">Top Blogs</p>
                  <span className=" selected-blogs-number ms-1">
                    {topBlogs?.length}
                  </span>
                </div>
                <span className=" ms-auto me-5">
                  <a
                    data-tooltip-id="my-tooltip"
                    data-tooltip-content="You can only select 7 top blogs"
                  >
                    <IoIosInformationCircle />
                  </a>
                  <Tooltip id="my-tooltip" />
                </span>
              </div>
            </Accordion.Header>
            <Accordion.Body className="faq-answer-section">
              <TopBlogsContent
                handleDeleteRanking={handleDeleteRanking}
                fetchAllBlogs={fetchAllBlogs}
                topBlogs={topBlogs}
                topLoading={topLoading}
                fetchTopBlogs={fetchTopBlogs}
              />
            </Accordion.Body>
          </Accordion.Item>
        </Accordion>
        <div className=" bg-color p-2 mb-4 mt-4">
          <BlogByAdmin />
        </div>
      </div>
    </div>
  );
};

export default BlogsContent;
