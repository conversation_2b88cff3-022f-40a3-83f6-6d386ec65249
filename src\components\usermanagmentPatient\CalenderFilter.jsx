import React, { useCallback, useEffect, useState } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import { IoIosRefresh } from "react-icons/io";
import { capitalizeFullName } from "../expert-doc/db";
import { useParams } from "next/navigation";
import Placeholder from "react-bootstrap/Placeholder";
import {
  formatCustomDateAndTime,
  formatDateandTime,
  formatDateRev,
  formatDateTimeConvert,
} from "../../utils/helperfunction";
import Link from "next/link";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { FaDownload } from "react-icons/fa6";
import * as FileSaver from "file-saver";
import * as XLSX from "xlsx";
import { toast } from "react-toastify";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import InfiniteScroll from "react-infinite-scroll-component";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};
const generateAndDownloadExcel = (data, fileName) => {
  try {
    if (!Array.isArray(data) || data?.length === 0) {
      throw new Error("No data available for export.");
    }

    const headers = [
      "Sl_No",
      "Appointment_id",
      "Status",
      "Summary",
      "Location",
      "Patient_name",
      "Patient_email",
      "Doctor_email",
      "Doctor_name",
      "Appointment_date",
      "Doctor_timezone",
    ];

    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet([headers]);

    // Convert data to sheet format
    XLSX.utils.sheet_add_json(ws, data, { origin: "A2", skipHeader: true });

    // Auto column width calculation
    const getMaxWidth = (data, columnIndex) => {
      const columnData = data.map(
        (row) => row[headers[columnIndex]]?.toString() || ""
      );
      const maxLength = Math.max(
        headers[columnIndex].length, // Header length
        ...columnData.map((str) => str.length) // Data length
      );
      return { wch: maxLength + 2 }; // Add padding
    };

    // Set dynamic column widths
    ws["!cols"] = headers.map((_, colIndex) => getMaxWidth(data, colIndex));

    // Ensure header row is bold
    if (!ws["!ref"]) throw new Error("Sheet reference missing!");

    const range = XLSX.utils.decode_range(ws["!ref"]);
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (!ws[cellAddress]) continue;
      ws[cellAddress].s = { font: { bold: true } }; // Apply bold styling
    }

    // Create workbook
    const wb = { Sheets: { data: ws }, SheetNames: ["data"] };

    // Generate Excel file
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blobData = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    FileSaver.saveAs(blobData, fileName);
  } catch (error) {
    console.error("Error generating Excel file:", error.message);
    toast.error(`Failed to generate Excel file: ${error.message}`);
  }
};
const CalenderFilter = ({ id }) => {
  const [dateRange, setDateRange] = useState([null, null]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [calendarDataLoading, setCalendarDataLoading] = useState(true);
  const [rotate, setRotate] = useState(true);
  const params = useParams();
  const { user_id } = params;
  const doctor_id = user_id[0];
  const axiosAuth = useAxiosAuth();
  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [downloadInProgress, setDownloadInProgress] = useState(false);

  const fetchAppointmentsForDateFilter = useCallback(async () => {
    try {
      setRotate(true);
      setCalendarDataLoading(true);

      let url = `${process.env.NEXT_PUBLIC_GET_PATIENT_APPOINTMENTS}${doctor_id}/?page=${pageNo}&per_page=7`;

      if (dateRange[0] && dateRange[1]) {
        const startFormatted = formatDateRev(dateRange[0]);
        const endFormatted = formatDateRev(dateRange[1]);
        url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
      }

      const response = await axiosAuth.get(url);
      const appointments = response?.data?.items || [];

      // Update state with new appointments
      setFilteredAppointments((prev) =>
        pageNo === 1 ? appointments : [...prev, ...appointments]
      );
      setHasMore(appointments.length > 0); // Mark if more data exists
    } catch (error) {
      console.error(error);
    } finally {
      setRotate(false);
      setCalendarDataLoading(false);
    }
  }, [doctor_id, dateRange, pageNo, axiosAuth]);

  useEffect(() => {
    fetchAppointmentsForDateFilter();
  }, [fetchAppointmentsForDateFilter]);

  const handleDateChange = (value) => {
    setFilteredAppointments([]); // Reset data on filter change
    setPageNo(1);
    setDateRange(value);
    setHasMore(true);
  };

  const clearDateRangeFilter = () => {
    setRotate(false);
    setCalendarDataLoading(true);
    setDateRange([null, null]);
  };

  const tileClassName = ({ date }) => {
    const [startDate, endDate] = dateRange;
    if (startDate && endDate) {
      if (date >= startDate && date <= endDate) {
        return "selected-range";
      }
    } else if (startDate && date.toDateString() === startDate.toDateString()) {
      return "start-date";
    } else if (endDate && date.toDateString() === endDate.toDateString()) {
      return "end-date";
    }
    return null;
  };

  const handleDownloadAppRecord = async () => {
    const fileType = "xlsx";
    let startFormatted = "";
    let endFormatted = "";

    try {
      // Display loading toast with progress bar
      setDownloadInProgress(true);
      const downloadToastId = toast.loading(
        "Generating and downloading file...",
        {
          progress: 0,
          autoClose: false, // Keeps the toast open until download is complete
        }
      );

      let url = `${process.env.NEXT_PUBLIC_GET_PATIENT_APPOINTMENTS}${doctor_id}/`;

      // Append date range filter if applied
      if (dateRange[0] && dateRange[1]) {
        // format the start and end date only if date range exists
        startFormatted = formatDateRev(dateRange[0]);
        endFormatted = formatDateRev(dateRange[1]);
        url += `?start_date=${startFormatted}&end_date=${endFormatted}`;
      }

      const response = await axiosAuth.get(url);
      const appointments = response?.data?.items || response?.data || [];

      // If no date range is selected and no records found, download entire data
      if (!dateRange[0] && !dateRange[1] && appointments.length === 0) {
        // Simulate downloading entire data with a success message
        toast.update(downloadToastId, {
          render: "No date range applied, downloading entire data...",
          type: "info",
          progress: 100,
        });

        const mappedData =
          appointments &&
          appointments?.map((appointment, index) => ({
            Sl_No: index + 1,
            Appointment_id: appointment.id,
            Status:
              appointment.status === "B"
                ? "Booked"
                : appointment.status === "C"
                ? "Cancelled"
                : "Pending",
            Summary: appointment?.summary,
            Location: appointment?.location,
            Patient_name: appointment?.patient.name,
            Patient_email: appointment?.patient.email,
            Doctor_email: appointment?.doctor_email,
            Doctor_name: appointment?.doctor_name,
            Appointment_date: formatCustomDateAndTime(
              appointment?.slot_start_time
            ),
            Doctor_timezone: appointment?.patient.TimeZone,
          }));

        generateAndDownloadExcel(mappedData, "No_Records_Found.xlsx");
        toast.update(downloadToastId, {
          render: "Download complete!",
          progress: 100,
          type: "success",
          autoClose: 3000,
        });

        toast.dismiss(downloadToastId); // Dismiss after download
        return;
      }

      // Handle the case where there are no records found (when date range is selected)
      if (appointments?.length === 0) {
        const noRecordsMessage = [
          {
            Sl_No: "N/A",
            Appointment_id: "N/A",
            Status: "No Records Found",
            Summary: "",
            Location: "",
            Patient_name: "",
            Patient_email: "",
            Doctor_email: "",
            Doctor_name: "",
            Appointment_date: "",
            Doctor_timezone: "",
          },
        ];

        toast.update(downloadToastId, {
          render: "No records found, downloading result...",
          type: "info",
          progress: 100,
        });

        generateAndDownloadExcel(noRecordsMessage, "No_Records_Found.xlsx");

        FileSaver.saveAs(data, "No_Records_Found.xlsx");

        toast.dismiss(downloadToastId); // Dismiss the toast after download
        return;
      }

      // Simulate progress for file generation when records are found
      const mappedData =
        appointments &&
        appointments?.map((appointment, index) => ({
          Sl_No: index + 1,
          Appointment_id: appointment.id,
          Status:
            appointment.status === "B"
              ? "Booked"
              : appointment.status === "C"
              ? "Cancelled"
              : "Pending",
          Summary: appointment?.summary,
          Location: appointment?.location,
          Patient_name: appointment?.patient.name,
          Patient_email: appointment?.patient.email,
          Doctor_email: appointment?.doctor_email,
          Doctor_name: appointment?.doctor_name,
          Appointment_date: formatCustomDateAndTime(
            appointment?.slot_start_time
          ),
          Doctor_timezone: appointment?.patient.TimeZone,
        }));

      toast.update(downloadToastId, {
        render: "Generating file...",
        progress: 50,
      });

      // Simulate some delay before file generation (optional for visual effect)
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const ws = XLSX.utils.json_to_sheet(mappedData);
      const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const data = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      toast.update(downloadToastId, {
        render: "Download ready. Saving file...",
        progress: 80,
      });

      const fileName = mappedData[0].Patient_name
        ? dateRange[0] && dateRange[1]
          ? `Patient - ${mappedData[0].Patient_name} appointments (${startFormatted} to ${endFormatted}).xlsx`
          : `Patient - ${mappedData[0].Patient_name} appointments.xlsx`
        : dateRange[0] && dateRange[1]
        ? `Appointments (${startFormatted} to ${endFormatted}).xlsx`
        : "Appointments.xlsx";

      // FileSaver.saveAs(data, fileName);
      generateAndDownloadExcel(mappedData, fileName);
      if (downloadToastId) {
        toast.update(downloadToastId, {
          render: "Download complete!",
          progress: 100,
          type: "success",
          autoClose: 3000,
        });
        toast.dismiss(downloadToastId);
      }
    } catch (error) {
      console.error("Error downloading appointments", error);
      if (downloadToastId) {
        toast.update(downloadToastId, {
          render: "Failed to download. Please try again.",
          type: "error",
          progress: 100,
        });
        toast.dismiss(downloadToastId);
      }
    } finally {
      setDownloadInProgress(false);
    }
  };

  return (
    <>
      <div className="row">
        <div className="col-sm-12">
          <div className="calander-patient">
            <div className="calender_title_selection">
              <span className="data-range float-start">Select Date Range</span>
              {dateRange[0] !== null && !calendarDataLoading && (
                <span
                  className="clear float-end"
                  onClick={clearDateRangeFilter}
                >
                  Clear
                </span>
              )}
            </div>
            <div className="class-span">
              {!calendarDataLoading ? (
                <Calendar
                  tileClassName={tileClassName}
                  onChange={handleDateChange}
                  selectRange
                  value={dateRange}
                />
              ) : (
                <Calendar
                  tileClassName={tileClassName}
                  onChange={() => {}}
                  selectRange={false}
                  value={dateRange}
                />
              )}
            </div>
            <div className="row">
              {filteredAppointments?.length > 0 && (
                <div className="col-sm-12 pe-4 d-flex justify-content-between">
                  <div className="download_section">
                    <button
                      className="download_appointments_records bg-transparent border-0 "
                      onClick={handleDownloadAppRecord}
                      style={{
                        pointerEvents:
                          calendarDataLoading || downloadInProgress
                            ? "none"
                            : "auto",
                        opacity:
                          calendarDataLoading || downloadInProgress ? 0.5 : 1,
                      }}
                      disabled={downloadInProgress}
                    >
                      &nbsp;{" "}
                      {downloadInProgress ? (
                        "Downloading... "
                      ) : (
                        <>
                          <FaDownload className="download_appointments_records_icon" />
                          Download
                        </>
                      )}
                    </button>
                  </div>

                  <button
                    type="button"
                    className="btn refresh-btn"
                    onClick={() => fetchAppointmentsForDateFilter()}
                  >
                    Refresh <IoIosRefresh className={rotate ? "rotate" : ""} />
                  </button>
                </div>
              )}
              <div className="image-container overflow-hidden">
                <div
                  className="content-scroll-2 overflow-auto "
                  id="scrollableDiv"
                >
                  {calendarDataLoading ? (
                    renderPlaceholders()
                  ) : (
                    <>
                      {filteredAppointments?.length === 0 ? (
                        <div className="fs-4 d-flex align-items-center justify-content-center no-articles-found">
                          <PiFolderNotchOpenFill color={"purple"} size={30} />
                          &nbsp; No Records Found
                        </div>
                      ) : (
                        <div className="col-sm-12">
                          <InfiniteScroll
                            dataLength={filteredAppointments?.length} // Reflects items rendered
                            next={() => setPageNo((prev) => prev + 1)} // Increments page
                            hasMore={hasMore} // If more data can be loaded
                            loader={renderPlaceholders()} // Placeholder
                            scrollableTarget="scrollableDiv"
                          >
                            {filteredAppointments &&
                              filteredAppointments?.map((items, index) => (
                                <div
                                  className="row"
                                  key={index + 1 || items?.id}
                                >
                                  <div className="col-sm-2">
                                    <div className="circle-time"></div>
                                    <div className="vertical-line"></div>
                                    <div className="circle-end"></div>
                                  </div>
                                  <div className="col-sm-10 mt-2">
                                    <span className="date-bold mb-3">
                                      {formatCustomDateAndTime(
                                        items?.slot_start_time
                                      )}
                                    </span>
                                    <br />
                                    <span className="custom-font-size">
                                      Appointment with{" "}
                                      <Link
                                        href={`/usermanagement/experts/experts-doctor/${items?.doctor_id}/${items?.doctor_email}/${items?.doctor_approval}`}
                                      >
                                        <span className="name-doctor text-decoration-underline">
                                          {items?.expert_prefix}{" "}
                                          {capitalizeFullName(
                                            items?.doctor_name
                                          ) || "Unknown"}
                                        </span>
                                      </Link>
                                    </span>
                                  </div>
                                </div>
                              ))}
                          </InfiniteScroll>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CalenderFilter;
