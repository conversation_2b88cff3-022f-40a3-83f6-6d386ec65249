"use client";
import React, { Suspense } from "react";
import AdminDashboard from "../../components/administratorDasboard/AdminDashboard";

import Navbar from "../../components/navbar/Navbar";
import withAuth from "../../withAuth/withAuth";

const Homepage = () => {
  return (
    <>
    <div className="container-fluid">
      <div className="row">
        <div className="col-sm-1 navbar-parent">
          <Navbar />
        </div>
        <div className="col-sm-11">
          <Suspense>
            <AdminDashboard />
          </Suspense>
        </div>
      </div>
      </div>
    </>
  );
};

export default withAuth(Homepage);
