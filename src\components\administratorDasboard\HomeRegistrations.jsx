import React from "react";
import { Placeholder } from "react-bootstrap";
import "react-tooltip/dist/react-tooltip.css";
import { IoIosInformationCircle } from "react-icons/io";
import { Tooltip } from "react-tooltip";

const HeadingPlaceHolder = () => {
  return (
    <Placeholder as="p" animation="glow">
      <Placeholder xs={12} style={{ height: "30px", borderRadius: "5px" }} />
    </Placeholder>
  );
};

const ValuePlaceholder = () => {
  return (
    <Placeholder as="p" animation="glow">
      <Placeholder xs={12} style={{ height: "30px", borderRadius: "5px" }} />
    </Placeholder>
  );
};

const HomeRegistrations = ({ registration_info, loading }) => {
  const {
    new_patients = 0,
    new_experts = 0,
    new_admin = 0,
    experts_deactivated = 0,
  } = registration_info;

  // const loading = true;
  return (
    <div className="dashboard-content-border mt-2">
      <div className="row">
        <div className="col-sm-10 d-flex">
          <p className="heading">Registrations</p>
          <span>
            <a
              data-tooltip-id="my-tooltip"
              data-tooltip-content="Total Registrations for this Month"
            >
              <IoIosInformationCircle />
            </a>
            <Tooltip id="my-tooltip" />
          </span>
        </div>
      </div>
      <div className="row">
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="green-count mb-2">{new_experts}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? <HeadingPlaceHolder /> : <p className="dashboard-content mb-2">New expert Registrations</p>}
        </div>
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="red-count mb-2">{new_admin}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? (
            <HeadingPlaceHolder />
          ) : (
            <p className="dashboard-content mb-2">New Child Admin Registrations</p>
          )}
        </div>
      </div>
      <div className="row">
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="green-count mb-2">{new_patients}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? <HeadingPlaceHolder /> : <p className="dashboard-content mb-2">New patient Registrations</p>}
        </div>
        <div className="col-sm-1">
          {loading ? (
            <ValuePlaceholder />
          ) : (
            <p className="red-count mb-2">{experts_deactivated}</p>
          )}
        </div>
        <div className="col-sm-5">
          {loading ? (
            <HeadingPlaceHolder />
          ) : (
            <p className="dashboard-content mb-2">Expert profile deactivations</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default HomeRegistrations;
