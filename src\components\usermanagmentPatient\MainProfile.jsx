/* eslint-disable @next/next/no-img-element */
import React, { useState, useEffect, useCallback } from "react";
import CalenderFilter from "./CalenderFilter";
import { IoIosArrowBack } from "react-icons/io";
import Swal from "sweetalert2";
import moment from "moment-timezone";
import { AiFillCamera } from "react-icons/ai";
import Loading from "../Loading/PageLoading/Loading";
import { toast } from "react-toastify";
import Select from "react-select";
import { formatDateTime } from "../../utils/helperfunction";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import parsePhoneNumberFromString from "libphonenumber-js";
import { Country, State, City } from "country-state-city";
import { MdOutlineEdit } from "react-icons/md";
import { useSession } from "next-auth/react";
import MainProfilePlaceholder from "./MainProfilePlaceholder";
import "../../components/usermanagmentPatient/usermanagementpatient.css";
import ProfileReactivationModal from "../expert-doc/ProfileReactivationModal/ProfileReactivationModal";
import RejectionModal from "../expert-doc/RejectionModal/RejectionModal";
import Image from "next/image";
import { useAdminContext } from "@/Context/AdminContext/AdminContext";

const initialState = {
  name: "",
  age: "",
  phone: "",
  email: "",
  sex: "",
  timezone: "",
  City: "",
  State: "",
  Country: "",
  address: "",
  height: "",
  weight: "",
  dietaryRestriction: "",
  cancerTypeMain: "",
  cancerTypeSub: "",
  cancerTypeLevel: "",
  existingIllnesses: "",
  pastIllnesses: "",
  notes: "",
  profilePic: null,
};

const updateURLBasedOnStatus = (approvalStatus = "") => {
  const currentURL = new URL(window.location.href);
  const pathSegments = currentURL.pathname.split("/");

  // Get the current status, which is the second-to-last segment
  const currentStatus = pathSegments[pathSegments.length - 2];

  // Update the status only if it is different from the current status
  if (currentStatus !== approvalStatus) {
    pathSegments[pathSegments.length - 2] = approvalStatus;
    currentURL.pathname = pathSegments.join("/");
    window.history.replaceState(null, "", currentURL);
  }
};

const getStatusButton = (
  status = "",
  details = "",
  setShowRequestedForApprovalModal,
  setRejectModalAndDetails
) => {
  const getButton = (
    className,
    text,
    showModal = false,
    reason = "",
    date = "",
    type = ""
  ) => (
    <>
      <button type="button" className={`btn ${className}`}>
        {text}
        {showModal && (
          <>
            <br />
            <span
              onClick={() =>
                setRejectModalAndDetails({
                  rejectShowModal: true,
                  rejectedReason: reason,
                  rejectedDate: date,
                  type: type,
                })
              }
            >
              Why?
            </span>
          </>
        )}
      </button>
    </>
  );

  if (status === "Deactivated" && details?.deactivated_reason) {
    return getButton(
      "btn-danger",
      "Deactivation",
      true,
      details.deactivated_reason,
      details.deactivated_time,
      "deactivating"
    );
  }

  if (status === "Rejected" && details?.rejected_reason) {
    return getButton(
      "btn-danger",
      "Rejected",
      true,
      details.rejected_reason,
      details.rejected_time,
      "rejected"
    );
  }

  switch (status) {
    case "Approved":
      return getButton("btn-success", "Approved");
    case "Rejected":
      return getButton("btn-danger", "Rejected");
    case "Deactivated":
      return getButton("btn-danger", "Deactivated");
    case "Deleted":
      return getButton("btn-danger", "Deleted");
    case "Approval_requested":
      return (
        <button type="button" className="btn btn-warning custom-font-size">
          Requested &nbsp;
          {details && (
            <span onClick={() => setShowRequestedForApprovalModal(true)}>
              Why?
            </span>
          )}
        </button>
      );
    case "self_deactivation":
      return getButton("btn-warning", "Self Deactivated");
    default:
      return getButton("btn-warning", "Pending");
  }
};
const MainProfile = ({ email, id }) => {
  const [profilePicFile, setProfilePicFile] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [timezones, setTimezones] = useState([]);
  const [originalFormData, setOriginalFormData] = useState({});
  const [cancerTypesData, setCancerTypesData] = useState();
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedMainType, setSelectedMainType] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("");
  const [categoraries, setCategories] = useState([]);
  const [userData, setUserData] = useState();
  const [iti, setIti] = useState(null);
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [formData, setFormData] = useState(initialState);
  const [patientDataLoading, setPatientDataLoading] = useState(true);
  const [showRequestedForApprovalModal, setShowRequestedForApprovalModal] =
    useState(false);
  const [profileStatus, setProfileStatus] = useState({
    approval_status: "",
    details: "",
  });
  const [RejectModalAndDetails, setRejectModalAndDetails] = useState({
    rejectShowModal: false,
    rejectedReason: "",
    rejectedDate: null,
    type: "",
  });
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;

  const { isAdminChildAdmin, userPermissions, isAdmin } = useAdminContext();
  const isEditPermissible = userPermissions?.includes("cu_app.change_cuuser");

  const fetchUserData = useCallback(async () => {
    try {
      setPatientDataLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_PATIENT_DATA}${email}/`
      );
      let patientDataResponse = response?.data?.user_data;
      setUserData(patientDataResponse);
      setPatientDataLoading(false);
      setProfileStatus({
        approval_status: patientDataResponse?.approval,
        details: patientDataResponse?.approval_status_reason,
      });
    } catch (error) {
      console.error(error);
    } finally {
      setPatientDataLoading(false);
    }
  }, [email, axiosAuth]);

  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  useEffect(() => {
    const cityValue = userData?.City;
    if (cityValue) {
      const [city, state] = cityValue.split(",");
      setCity(city?.trim());
      setState(state?.trim());
    }

    if (userData) {
      updateURLBasedOnStatus(userData?.approval);
    }
  }, [userData]);

  useEffect(() => {
    if (!editMode && userData?.cancer_type) {
      const [mainType, category, level] = userData.cancer_type;
      setSelectedMainType(mainType);
      setSelectedCategory(category);
      setSelectedLevel(level);
    }
  }, [editMode, userData]);

  useEffect(() => {
    const getCancerTypes = async () => {
      try {
        const resp = await axiosAuth.get(
          process.env.NEXT_PUBLIC_GET_CANCER_TYPES
        );
        setCancerTypesData(resp?.data);
      } catch (error) {
        console.error("Error fetching cancer types: ", error);
      }
    };

    getCancerTypes();
  }, [email, axiosAuth]);

  useEffect(() => {
    const fetchCategories = async () => {
      if (selectedMainType) {
        const response = await axiosAuth.post(
          process.env.NEXT_PUBLIC_GET_CANCER_TYPES_SUB_CATEGORY,
          {
            category: selectedMainType,
          }
        );
        setCategories(response?.data);
      }
    };

    fetchCategories();
  }, [selectedMainType, axiosAuth]);

  useEffect(() => {
    // Get a list of timezones using moment-timezone
    const timezoneList = moment.tz.names();
    setTimezones(timezoneList);
    setOriginalFormData({
      name: userData?.name || "",
      phone: userData?.phone || "",
      age: userData?.age || "",
      email: userData?.email || "",
      sex: userData?.sex || "",
      timezone: userData?.TimeZone || "",
      address: userData?.patient_other_details?.Address || "",
      height: userData?.patient_other_details?.Height || "",
      weight: userData?.patient_other_details?.Weight || "",
      dietaryRestriction:
        userData?.patient_other_details?.DietaryRestrictions || "",
      cancerTypeMain: userData?.cancer_type?.[0] || "",
      cancerTypeSub: userData?.cancer_type?.[1] || "",
      cancerTypeLevel: userData?.cancer_type?.[2] || "",
      existingIllnesses: userData?.patient_other_details?.ExistingIllness || "",
      pastIllnesses: userData?.patient_other_details?.PastIllness || "",
      notes: userData?.patient_other_details?.Notes || "",
      profilePic: userData?.patient_other_details?.ProfilePhoto || "",
    });

    // fetchAppointments();
  }, [userData, email]);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    setProfilePicFile(file);
  };

  const handleEditButtonClick = async () => {
    const shouldEdit = await Swal.fire({
      title: "Confirm Edit",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancel",
    });

    if (!shouldEdit.isConfirmed) {
      return;
    }
    setEditMode(true);

    setFormData({
      name: userData?.name || "",
      phone: userData?.phone || "",
      age: userData?.age || "",
      email: userData?.email || "",
      sex: userData?.sex || "",
      timezone: userData?.TimeZone || "Europe/London",
      City: userData?.City || "",
      Country: userData?.Country || "",
      address: userData?.patient_other_details?.Address || "",
      height: userData?.patient_other_details?.Height || "",
      weight: userData?.patient_other_details?.Weight || "",
      dietaryRestriction:
        userData?.patient_other_details?.DietaryRestrictions || "",
      // cancerType: userData?.cancer_type || "",
      cancerTypeMain: userData?.cancer_type?.[0] || "",
      cancerTypeSub: userData?.cancer_type?.[1] || "",
      cancerTypeLevel: userData?.cancer_type?.[2] || "",
      existingIllnesses: userData?.patient_other_details?.ExistingIllness || "",
      pastIllnesses: userData?.patient_other_details?.PastIllness || "",
      notes: userData?.patient_other_details?.Notes || "",
      profilePic: userData?.patient_other_details?.ProfilePhoto || "",
    });

    const cityValue = userData?.City;
    if (cityValue) {
      const [city, state] = cityValue.split(",");
      setFormData((prevDetails) => ({
        ...prevDetails,
        City: city?.trim(),
        State: state?.trim(),
      }));
    }
  };
  const isEqual = (obj1, obj2) => {
    for (const key in obj1) {
      if (key === "profilePic" && obj1[key] instanceof File) {
        // Skip file comparison for profilePic
        continue;
      }
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }
    return true;
  };

  const handlePhoneChange = (value, country, e, formattedValue) => {
    setFormData((prevData) => ({ ...prevData, phone: formattedValue }));
  };

  const validatePhoneNumber = (phoneNumber) => {
    const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
    return parsedPhoneNumber && parsedPhoneNumber.isValid();
  };

  const handleSaveChangesButtonClick = async () => {
    try {
      // const isValidPhoneNumber = validatePhoneNumber(formData.phone);
      // if (!isValidPhoneNumber) {
      //   toast.error("Invalid phone number", {
      //     autoClose: 3000,
      //     position: "top-center",
      //   });
      //   return;
      // }

      const formDataObj = new FormData();
      formDataObj.append("name", formData.name);
      formDataObj.append("phone", formData.phone);
      formDataObj.append("email", formData.email);
      formDataObj.append("sex", formData.sex);
      formDataObj.append("TimeZone", formData.timezone);

      const stateName = formData.State?.name
        ? formData.State?.name
        : formData.State;
      const cityName = formData.City?.name
        ? formData.City?.name
        : formData.City;
      formDataObj.append("City", `${cityName}, ${stateName}`);
      formDataObj.append(
        "Country",
        formData.Country?.name ? formData.Country?.name : formData.Country
      );
      formDataObj.append("Address", formData.address);
      formDataObj.append("Height", formData.height);
      formDataObj.append("Weight", formData.weight);
      formDataObj.append("age", formData.age);
      formDataObj.append("DietaryRestrictions", formData.dietaryRestriction);
      formDataObj.append("cancer_type_main", formData?.cancerType?.[0]);
      formDataObj.append(
        "cancer_type_sub",
        categoraries?.length > 0 ? formData?.cancerType?.[1] : "Cat not found"
      );
      formDataObj.append("cancer_type_level", formData.cancerType?.[2]);
      formDataObj.append("ExistingIllness", formData.existingIllnesses);
      formDataObj.append("PastIllness", formData.pastIllnesses);
      formDataObj.append("Notes", formData.notes);

      if (profilePicFile) {
        formDataObj.append("ProfilePhoto", profilePicFile);
      } else if (formData.profilePic) {
        formDataObj.append("ProfilePhoto", formData.profilePic);
      } else {
        formDataObj.delete("ProfilePhoto");
      }

      const changesMade =
        !isEqual(formData, originalFormData) || profilePicFile;

      if (changesMade) {
        try {
          const response = await axiosAuth.put(
            `${process.env.NEXT_PUBLIC_UPDATE_PATIENT_PROFILE}${email}/?user_id=${admin_id}`,
            formDataObj
          );

          toast.success("Changes saved successfully");
          fetchUserData();
        } catch (error) {
          toast.error("Failed to update profile.");
        }
      } else {
        toast.info("No changes made.", {
          autoClose: 3500,
        });
      }

      setEditMode(false);
    } catch (error) {
      console.error("Error updating data: ", error);
      toast.error("An error occurred while updating the data.");
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "allcancerType") {
      setSelectedMainType(value);
      setFormData((prevData) => ({
        ...prevData,
        cancerType: [value, selectedCategory, selectedLevel],
      }));
    } else if (name === "cancerType") {
      setSelectedCategory(value);
      setFormData((prevData) => ({
        ...prevData,
        cancerType: [selectedMainType, value, selectedLevel],
      }));
    } else if (name === "cancerLevel") {
      setSelectedLevel(value);
      setFormData((prevData) => ({
        ...prevData,
        cancerType: [selectedMainType, selectedCategory, value],
      }));
    } else {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }));
    }
  };

  const [showTooltip, setShowTooltip] = useState(false);

  const handleTooltipToggle = () => {
    setShowTooltip(!showTooltip);
  };

  const customStyles = {
    // Style for the entire Select container
    container: (provided) => ({
      ...provided,
      backgroundColor: editMode ? "white" : "white",
    }),
    // Style for the control (input, dropdown indicator, etc.)
    control: (provided) => ({
      ...provided,
      color: editMode ? "black" : "black",
      backgroundColor: editMode ? "white" : "white",
      color: editMode ? "black" : "rgba(0, 0, 0, 0.8)",
      border: "none",
    }),
  };

  return (
    <>
      <div className="payment-back">
        <div className=" overflow-hidden">
          <div className="user-management-scroll overflow-auto">
            <div className="row">
              <div className="col-sm-9">
                {patientDataLoading ? (
                  <MainProfilePlaceholder />
                ) : (
                  <div className="row">
                    <div className="col-sm-6">
                      <div className="row"></div>
                      <div className="row">
                        <div className="col-sm-3">
                          <Image
                            src={
                              profilePicFile
                                ? URL.createObjectURL(profilePicFile)
                                : userData?.patient_other_details?.ProfilePhoto
                                ? userData.patient_other_details.ProfilePhoto
                                : "/assets/doctorprof.jpg"
                            }
                            alt=""
                            width={120}
                            height={120}
                            className="object-fit-cover rounded-circle"
                          />
                          {editMode && (
                            <>
                              <input
                                className="uploadProfileInput"
                                type="file"
                                name="profile_pic"
                                id="newProfilePhoto"
                                accept="image/*"
                                onChange={handleFileChange}
                              />
                              <label
                                htmlFor="newProfilePhoto"
                                className="upload-file-block"
                              >
                                <div className="text-center">
                                  <div className="mb-2">
                                    <AiFillCamera />
                                  </div>
                                  <div className="text-uppercase" />
                                </div>
                              </label>
                            </>
                          )}
                        </div>

                        <div className="col-sm-9">
                          <div className="row">
                            <div className="col-sm-4">
                              {getStatusButton(
                                userData?.approval,
                                userData?.approval_status_reason,
                                setShowRequestedForApprovalModal,
                                setRejectModalAndDetails
                              )}
                            </div>
                            {(isAdmin ||
                              (isChildAdmin && isEditPermissible)) && (
                              <div className="col-sm-8">
                                {!editMode && (
                                  <button
                                    type="button"
                                    className="btn control-profile"
                                    onClick={handleEditButtonClick}
                                    disabled={editMode}
                                  >
                                    Take control of profile
                                  </button>
                                )}
                                {editMode && (
                                  <button
                                    type="button"
                                    className="btn control-profile"
                                    onClick={() => {
                                      setEditMode(false);
                                    }}
                                  >
                                    Cancel
                                  </button>
                                )}
                              </div>
                            )}
                            <div className="col-sm-12 mt-2">
                              <div className="profile-bg-control">
                                <span className="profile-active">
                                  Last Login{" "}
                                </span>{" "}
                                <span className="profile-last-edited mx-2">
                                  {formatDateTime(userData?.last_login)}
                                </span>
                                <button
                                  type="button"
                                  className="btn refresh-btn"
                                  onClick={() => fetchUserData()}
                                >
                                  refresh
                                </button>{" "}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="row">
                        <div className="col-sm-6">
                          <div className="form-group mt-2">
                            <label htmlFor="name" className="form-label-style">
                              Name
                            </label>
                            <input
                              id="name"
                              name="name"
                              readOnly={!editMode}
                              value={
                                editMode ? formData.name : userData?.name || ""
                              }
                              onChange={handleInputChange}
                              type="text"
                              className="form-control form-fildes-read"
                            />
                          </div>
                        </div>
                        <div className="col-sm-6">
                          <div className="form-group mt-2">
                            <label
                              htmlFor="patientcode"
                              className="form-label-style"
                            >
                              Patient Code
                            </label>
                            <input
                              type="text"
                              className="form-control form-fildes-read"
                              readOnly
                              value={userData?.id}
                              onChange={(e) => handleInputChange(e, "phone")}
                            />
                          </div>
                        </div>
                        <div className="col-sm-6">
                          <div className="form-group mt-2">
                            <label htmlFor="phone" className="form-label-style">
                              Phone Number
                            </label>
                            <div>
                              <PhoneInput
                                readOnly
                                ref={(itiRef) => setIti(itiRef)}
                                id="floatingInputPhone"
                                className="input-form-modal-phone"
                                country={"in"}
                                required="required"
                                name="phoneNumber"
                                value={
                                  editMode
                                    ? formData.phone
                                    : userData?.phone || ""
                                }
                                onChange={handlePhoneChange}
                                disabled={!editMode}
                                inputStyle={{
                                  width: "100%",
                                  height: "auto",
                                  border: "1px solid #e3e3e3",
                                  boxShadow: "0px 3px 6px #00000029",
                                }}
                                buttonStyle={{
                                  border: "1px solid #e3e3e3",
                                  borderRight: 0,
                                  background: "white",
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="col-sm-6">
                          <div className="form-group mt-2">
                            <label htmlFor="email" className="form-label-style">
                              Email ID
                            </label>
                            <input
                              id="email"
                              name="email"
                              aria-describedby="button-addon2"
                              value={
                                editMode
                                  ? formData.email
                                  : userData?.email || ""
                              }
                              type="email"
                              readOnly
                              className="form-control form-fildes-read"
                            />
                          </div>
                        </div>
                        <div className="col-sm-6">
                          <div className="form-group mt-2">
                            <label htmlFor="Time" className="form-label-style">
                              Timezone
                            </label>
                            <select
                              className="form-select form-control form-fildes-read cancer-type-input"
                              aria-label="Timezone"
                              id="timezone"
                              name="timezone"
                              disabled={!editMode}
                              value={
                                editMode
                                  ? formData.timezone
                                  : userData?.TimeZone || ""
                              }
                              onChange={handleInputChange}
                              // onClick={getCancerTypes}
                            >
                              {timezones.map((timezone) => (
                                <option key={timezone} value={timezone}>
                                  {timezone}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                        <div className="col-sm-6">
                          <div className="form-group mt-2">
                            <label
                              htmlFor="gender"
                              className="form-label-style"
                            >
                              Gender
                            </label>
                            <select
                              className="form-select form-control form-fildes-read cancer-type-input"
                              aria-label="Gender"
                              id="sex"
                              name="sex"
                              disabled={!editMode}
                              value={
                                editMode ? formData.sex : userData?.sex || ""
                              }
                              onChange={handleInputChange}
                            >
                              <option value="">Select Gender</option>
                              <option value="Female">Female</option>
                              <option value="Male">Male</option>
                              <option value="Others">Others</option>
                            </select>
                          </div>
                        </div>

                        <div className="row">
                          <label
                            htmlFor="name"
                            className="form-label-style mt-2"
                          >
                            Location
                          </label>
                          <div className="col-sm-12">
                            <div
                              className="input-group input-group-border mb-2"
                              style={{ width: "104%" }}
                            >
                              <span
                                className="input-group-text label-col"
                                style={{ width: "100px", fontSize: "14px" }}
                              >
                                {" "}
                                Country
                              </span>

                              <input
                                type="text"
                                className="form-control form-fildes-read1"
                                style={{ fontSize: "14px" }}
                                id="Country"
                                name="Country"
                                readOnly={!editMode}
                                value={
                                  editMode
                                    ? formData?.Country?.name
                                      ? formData?.Country?.name
                                      : formData?.Country
                                    : userData?.Country || ""
                                }
                                onChange={(e) =>
                                  setFormData({
                                    ...formData,
                                    Country: e.target.value,
                                    State: "No states",
                                    City: "No cities",
                                  })
                                }
                              />

                              {editMode && (
                                <Select
                                  id="Country"
                                  options={Country.getAllCountries()}
                                  getOptionLabel={(options) => {
                                    return options["name"];
                                  }}
                                  getOptionValue={(options) => {
                                    return options["name"];
                                  }}
                                  value={formData?.Country}
                                  onChange={(item) => {
                                    const selectedCountry = item;
                                    setFormData({
                                      ...formData,
                                      Country: selectedCountry,
                                      State: "No states",
                                      City: "No cities",
                                    });
                                  }}
                                />
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="row">
                          <div className="col-sm-12">
                            <div
                              className="input-group input-group-border mb-2"
                              style={{ width: "104%" }}
                            >
                              <span
                                className="input-group-text label-col"
                                style={{ width: "100px", fontSize: "14px" }}
                              >
                                {" "}
                                State
                              </span>

                              <input
                                type="text"
                                className="form-control form-fildes-read11"
                                style={{ fontSize: "14px" }}
                                id="Country"
                                name="State"
                                readOnly={!editMode}
                                value={
                                  editMode
                                    ? formData?.State?.name
                                      ? formData?.State?.name
                                      : formData?.State
                                    : state
                                }
                                onChange={(e) =>
                                  setFormData({
                                    ...formData,
                                    State: e.target.value,
                                  })
                                }
                              />

                              {editMode && (
                                <Select
                                  id="country"
                                  options={State?.getStatesOfCountry(
                                    formData?.Country?.isoCode
                                  )}
                                  getOptionLabel={(options) => {
                                    return options["name"];
                                  }}
                                  getOptionValue={(options) => {
                                    return options["name"];
                                  }}
                                  value={formData?.State}
                                  onChange={(item) => {
                                    const selectedState = item;
                                    setFormData({
                                      ...formData,
                                      State: selectedState,
                                    });
                                  }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-sm-12">
                            <div
                              className="input-group input-group-border mb-1"
                              style={{ width: "104%" }}
                            >
                              <span
                                className="input-group-text label-col"
                                style={{ width: "100px", fontSize: "14px" }}
                              >
                                {" "}
                                City
                              </span>

                              <input
                                type="text"
                                className="form-control form-fildes-read1"
                                style={{ fontSize: "14px" }}
                                id="Country"
                                name="City"
                                readOnly={!editMode}
                                value={
                                  editMode
                                    ? formData?.City?.name
                                      ? formData?.City?.name
                                      : formData?.City
                                    : city
                                }
                                onChange={(e) =>
                                  setFormData({
                                    ...formData,
                                    City: e.target.value,
                                  })
                                }
                              />

                              {editMode && (
                                <Select
                                  options={City.getCitiesOfState(
                                    formData?.State?.countryCode,
                                    formData?.State?.isoCode
                                  )}
                                  getOptionLabel={(options) => {
                                    return options["name"];
                                  }}
                                  getOptionValue={(options) => {
                                    return options["name"];
                                  }}
                                  value={formData?.City}
                                  onChange={(item) => {
                                    const selectedCity = item;
                                    setFormData({
                                      ...formData,
                                      City: selectedCity,
                                    });
                                  }}
                                />
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="form-group mt-1">
                          <label htmlFor="address" className="form-label-style">
                            Address
                          </label>
                          <textarea
                            className="form-control text-form-fileds"
                            id="address"
                            rows="5"
                            name="address"
                            readOnly={!editMode}
                            value={
                              editMode
                                ? formData.address
                                : userData?.patient_other_details?.Address || ""
                            }
                            onChange={handleInputChange}
                          ></textarea>
                        </div>
                        {editMode && (
                          <div className="col-sm-12 mt-3">
                            <button
                              type="button"
                              className={`btn btn-save-changes `}
                              onClick={handleSaveChangesButtonClick}
                              disabled={!editMode}
                            >
                              Save changes
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="col-sm-6 info-bg">
                      <div className="row">
                        <div className="col-sm-12">
                          <div className="row">
                            <div className="col-sm-12">
                              <span className="medical-info">
                                Medical Information
                              </span>
                              {/* <button type="button" className="btn btn-view-all">
                              View All
                            </button> */}
                            </div>
                            <div className="col-sm-6">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="height"
                                  className="form-label-style"
                                >
                                  Height
                                </label>
                                <input
                                  type="text"
                                  className="form-control form-fildes-read"
                                  id="height"
                                  name="height"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.height
                                      : userData?.patient_other_details
                                          ?.Height || ""
                                  }
                                  onChange={handleInputChange}
                                />
                              </div>
                            </div>
                            <div className="col-sm-6">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="weight"
                                  className="form-label-style"
                                >
                                  Weight
                                </label>
                                <input
                                  type="text"
                                  className="form-control form-fildes-read"
                                  id="weight"
                                  name="weight"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.weight
                                      : userData?.patient_other_details
                                          ?.Weight || ""
                                  }
                                  onChange={handleInputChange}
                                />
                              </div>
                            </div>
                            <div className="col-sm-6">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="dietary"
                                  className="form-label-style"
                                >
                                  Age
                                </label>
                                <input
                                  type="text"
                                  className="form-control form-fildes-read"
                                  id="age"
                                  name="age"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.age
                                      : userData?.age || ""
                                  }
                                  onChange={handleInputChange}
                                />
                              </div>
                            </div>
                            <div className="col-sm-6">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="dietary"
                                  className="form-label-style"
                                >
                                  Past Illnesses
                                </label>
                                <input
                                  type="text"
                                  className="form-control form-fildes-read"
                                  id="pastIllnesses"
                                  name="pastIllnesses"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.pastIllnesses
                                      : userData?.patient_other_details
                                          ?.PastIllness || ""
                                  }
                                  onChange={handleInputChange}
                                />
                              </div>
                            </div>

                            <div className="col-sm-6">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="cancerType"
                                  className="form-label-style"
                                >
                                  Cancer Type
                                </label>
                                <Select
                                  className="form-fildes-read category-select"
                                  isSearchable
                                  options={cancerTypesData?.map((item) => ({
                                    label: item,
                                    value: item,
                                  }))}
                                  value={
                                    selectedMainType
                                      ? {
                                          label: selectedMainType,
                                          value: selectedMainType,
                                        }
                                      : null
                                  }
                                  onChange={(selectedOption) =>
                                    setSelectedMainType(
                                      selectedOption?.value || ""
                                    )
                                  }
                                  isDisabled={!editMode}
                                  styles={customStyles}
                                />
                                {/* // </OverlayTrigger> */}
                              </div>
                            </div>

                            <div className="col-sm-6">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="category"
                                  className="form-label-style"
                                >
                                  Category
                                </label>
                                <select
                                  className="form-control category-select form-fildes-read"
                                  id="cancerType"
                                  name="cancerType"
                                  disabled={!editMode}
                                  // value={selectedCategory}
                                  value={
                                    editMode
                                      ? selectedCategory
                                      : userData?.cancer_type?.[1] || ""
                                  }
                                  onChange={handleInputChange}
                                >
                                  <option value="" disabled hidden>
                                    Select Cancer Category
                                  </option>
                                  {Array.isArray(categoraries) &&
                                  categoraries.length > 0 ? (
                                    categoraries.map((category, index) => (
                                      <option
                                        key={index}
                                        value={category?.name}
                                      >
                                        {category?.name}
                                      </option>
                                    ))
                                  ) : (
                                    <option value="">
                                      No categories available
                                    </option>
                                  )}
                                </select>
                              </div>
                            </div>

                            <div className="col-sm-12">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="illnesses"
                                  className="form-label-style"
                                >
                                  Existing illnesses
                                </label>
                                <input
                                  type="text"
                                  className="form-control form-fildes-read"
                                  id="existingIllnesses"
                                  name="existingIllnesses"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.existingIllnesses
                                      : userData?.patient_other_details
                                          ?.ExistingIllness || ""
                                  }
                                  onChange={handleInputChange}
                                />
                              </div>
                            </div>

                            <div className="col-sm-12">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="pastillness"
                                  className="form-label-style"
                                >
                                  Dietary restrictions
                                </label>
                                <input
                                  type="text"
                                  className="form-control form-fildes-read"
                                  id="dietaryRestriction"
                                  name="dietaryRestriction"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.dietaryRestriction
                                      : userData?.patient_other_details
                                          ?.DietaryRestrictions || ""
                                  }
                                  onChange={handleInputChange}
                                />
                              </div>
                            </div>
                            <div className="col-sm-12">
                              <div className="form-group mt-2">
                                <label
                                  htmlFor="note"
                                  className="form-label-style"
                                >
                                  Note
                                </label>
                                <textarea
                                  className="form-control text-form-fileds"
                                  rows="9"
                                  id="notes"
                                  name="notes"
                                  readOnly={!editMode}
                                  value={
                                    editMode
                                      ? formData.notes
                                      : userData?.patient_other_details
                                          ?.Notes || ""
                                  }
                                  onChange={handleInputChange}
                                ></textarea>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="col-sm-3">
                <CalenderFilter id={id} />
              </div>
            </div>
          </div>
        </div>
      </div>
      {showRequestedForApprovalModal && (
        <ProfileReactivationModal
          showRequestedForApprovalModal={showRequestedForApprovalModal}
          setShowRequestedForApprovalModal={setShowRequestedForApprovalModal}
          expertProfileStatus={profileStatus}
          fetchDoctorDetails={fetchUserData}
        />
      )}
      {RejectModalAndDetails?.rejectShowModal && (
        <RejectionModal
          RejectModalAndDetails={RejectModalAndDetails}
          setRejectModalAndDetails={setRejectModalAndDetails}
        />
      )}
    </>
  );
};
export default MainProfile;
