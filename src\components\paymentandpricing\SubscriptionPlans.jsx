import React, { useState } from 'react';
import { Modal, Button, Row, Col, Form } from 'react-bootstrap';
const SubscriptionPlans = (props) => {

  const [showModal, setShowModal] = useState(false);

  const handleShow = () => setShowModal(true);
  const handleClose = () => setShowModal(false);
  return (
    <>
      <h6 className='sub-plans-heading'>Subscription Plans</h6>
      <div className='row'>
        <div className='col-sm-4'>
          <div className='subscription-plans'>
            <span className='starter-plans'>Starter</span><span className='delete-plan'>Delete Plan</span><br />
            <span className='monthly-plan-cost'>35.99/month</span><br />
            <span className='user-per-month'>per user per month, billed monthly</span><br />
            <button type='button' className='btn edit-plan' variant="primary" onClick={handleShow}>Edit Plan</button>
            <ul className='list-feature'>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
            </ul>

          </div>
        </div>
        <div className='col-sm-4'>
          <div className='subscription-plans'>
            <span className='starter-plans'>Plus</span><span className='delete-plan'>Delete Plan</span><br />
            <span className='monthly-plan-cost'>600.99/month</span><br />
            <span className='user-per-month'>per user per month, billed monthly</span><br />
            <button type='button' className='btn edit-plan' variant="primary" onClick={handleShow}>Edit Plan</button>
            <ul className='list-feature'>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
            </ul>
          </div>
        </div>
        <div className='col-sm-4'>
          <div className='subscription-plans'>
            <span className='starter-plans'>Premium</span><span className='delete-plan'>Delete Plan</span><br />
            <span className='monthly-plan-cost'>109.99/month</span><br />
            <span className='user-per-month'>per user per month, billed monthly</span><br />
            <button type='button' className='btn edit-plan' variant="primary" onClick={handleShow}>Edit Plan</button>
            <ul className='list-feature'>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
              <li className='pt-2'>feature explained</li>
            </ul>
          </div>

        </div>
        <Modal show={showModal} onHide={handleClose} className='modal-for-editplan'>
        <div className="modal-content custom-modal-content-pricing">
          <Modal.Header className=' modal-header-payment'>

            <Modal.Title className='title-edit'>Edit Plan</Modal.Title>
            <button type="button" className="btn btn-cancle-plan" onClick={handleClose}>
              Cancel
            </button>
          </Modal.Header>
          <Modal.Body>
            <div className='row'>
              <div className='col-sm-6'>
                <h6 className='edit-plans-pricing'>Current Plan Value</h6>
                <span className='values-cost'>35.99/month</span>
              </div>
              <div className='col-sm-6'>
                <h6 className='edit-plans-pricing'>Current Plan Name</h6>
                <span className='values-cost'>Starter</span>
              </div>
            </div>
            {/* Your modal content goes here */}
            <Form className='form-bg-edit'>
              <Row className='plan-select-flow'>
                <Col xs="auto" >
                  <label className='label-select mt-1'>/Month</label>

                </Col>
                <Col xs="auto" className='gx-0'>

                  <Form.Check
                    type="switch"
                    id="custom-switch-year"

                  />
                </Col>
                <Col xs="auto">
                  <label className='label-select mt-1'>/Year</label>

                </Col>
              </Row>

              {/* Rounded switch */}


              <Form.Group controlId="planName">
                <Form.Label className='label-edit mt-5'>New Plan Name</Form.Label>
                <Form.Control type="text" className='form-edit-field' placeholder="Enter new plan name" />
              </Form.Group>

              <Form.Group controlId="planvalue">
                <Form.Label className='label-edit mt-2'>New Plan Value</Form.Label>
                <Row>
                  <Col md={9} >
                    <Form.Control type="text" className='form-edit-field' placeholder="Enter the new plan value" />
                  </Col>
                  <Col md={3}>
                    <Button type="button" className="btn btn-confirm-edit">
                      Confirm
                    </Button>
                  </Col>
                </Row>
              </Form.Group>


            </Form>
            <div
              className='plans-info-bg mt-2'>
              <h6 className='edit-plans-pricing' >Plan Information</h6>
              <ul className='point-bg-1 mt-2'>
                <li className='point-bg'>

                </li>
              </ul>
              <ul className='point-bg-1 mt-2'>
                <li className='point-bg'>

                </li>
              </ul>
              <ul className='point-bg-1 mt-2'>
                <li className='point-bg'>

                </li>
              </ul>
              <ul className='point-bg-1 mt-2'>
                <li className='point-bg'>

                </li>
              </ul>


            </div>
            <Button type='submit' className='btn btn-save-edit mt-3'>
              Submit
            </Button>
          </Modal.Body>
          </div>
        </Modal>
      </div>

    </>
  )
}
export default SubscriptionPlans;