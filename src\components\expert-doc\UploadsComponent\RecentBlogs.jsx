"use client";
import React from "react";
import { FaFile } from "react-icons/fa";
import { formatDate } from "../db.js";
import { FaFileCircleCheck } from "react-icons/fa6";

const RecentBlogs = ({
  articles,
  articleLoading,
  renderPlaceholders,
  handleView,
  setShowIntroVideoPreview,
  setShowArticlesPreview,
  setShowPodcastPreview,
  setShowConsentPreview,
  setShowReviewPreview,
  setFeedbackPreview,
  handleBlogDelete,
  selectedValue,
  handleChange,
  setShowNoPreviewText,
}) => {
  return (
    <>
      {articleLoading === false &&
        Array.isArray(articles) &&
        articles?.length > 0 && (
          <div className="col-3">
            <select
              className="form-select form-select-sm custom-form-select"
              aria-label=".form-select-sm example"
              value={selectedValue}
              onChange={handleChange}
            >
              <option selected value="all">
                Select
              </option>
              <option value="0">Under Review</option>
              <option value="1">Approved</option>
              <option value="2">Rejected</option>
            </select>
          </div>
        )}
      <div className="list-of-articles mt-3">
        {articleLoading === false ? (
          <>
            {!articles ||
            (Array.isArray(articles) && articles?.length === 0) ? (
              <>
                <h3
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "500px" }}
                >
                  <FaFileCircleCheck className="podcast-icon " />
                  &nbsp;
                  <span className="text-secondary"> No Blogs Available</span>
                </h3>
              </>
            ) : (
              <>
                {" "}
                {Array.isArray(articles) &&
                  articles?.map((item, index) => {
                    return (
                      <div key={index} className="bg-color-border mb-2">
                        <div className="row">
                          <div className="col-sm-6 mb-0 d-flex justify-content-start align-items-center">
                            <FaFile
                              style={{
                                fontSize: "30px",
                                color: "#8107D1",
                              }}
                            />
                            <p className="ps-2 mb-0 custom-font-size">
                              {item.blog_details?.BlogTitle}
                            </p>
                          </div>
                          <div className="col-sm-4 mb-0 d-flex justify-content-evenly align-items-center">
                            <p className="grey-text mb-0 ">
                              {formatDate(item?.blog_details?.BlogDateTime)}
                            </p>
                            <p
                              className={`blog-approval-status-${
                                item?.blog_details?.BlogStatus === 0
                                  ? "under-review"
                                  : item.blog_details?.BlogStatus === 1
                                  ? "approved"
                                  : item.blog_details?.BlogStatus === 2
                                  ? "rejected"
                                  : ""
                              } mb-0`}
                            >
                              {item?.blog_details?.BlogStatus === 0
                                ? "Under Review"
                                : item.blog_details?.BlogStatus === 1
                                ? "Approved"
                                : item.blog_details?.BlogStatus === 2
                                ? "Rejected"
                                : ""}
                            </p>
                          </div>

                          <div className="col-sm-1 gx-0 d-flex justify-content-center align-items-center">
                            {/* {item.blog_details?.BlogStatus === 2 && ( */}
                            <button
                              type="button"
                              className="btn btn-transparent btn-delete-text fw-medium p-0"
                              onClick={() =>
                                handleBlogDelete(item?.blog_details?.id)
                              }
                            >
                              Delete
                            </button>
                            {/* )} */}
                          </div>

                          <div className="col-sm-1 d-flex justify-content-center align-items-center">
                            <button
                              type="button"
                              className="btn btn-transparent btn-view-text fw-medium p-0"
                              onClick={() => {
                                setShowIntroVideoPreview(false);
                                setShowArticlesPreview(true);
                                setShowPodcastPreview(false);
                                setShowConsentPreview(false);
                                setShowReviewPreview(false);
                                setFeedbackPreview(false);
                                setShowNoPreviewText(false);
                                handleView(item);
                              }}
                            >
                              View
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </>
            )}
          </>
        ) : (
          renderPlaceholders()
        )}
      </div>
    </>
  );
};

export default RecentBlogs;
