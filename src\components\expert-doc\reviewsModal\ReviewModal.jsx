/* eslint-disable @next/next/no-img-element */
import Image from "next/image";
import React, { useState } from "react";
import { IoClose } from "react-icons/io5";
import { Modal, Button } from "react-bootstrap";
import {
  capitalizeFullName,
  convertDateFormat,
} from "../../../utils/helperfunction";
import ModalImage from "react-modal-image";
import RejectReasonModal from "../RejectReasonModal";

const ReviewsModal = ({
  showReviewModal,
  setShowReviewModal,
  singleReview,
  debouncedHandleArticleApprovals,
  showButtons,
  fetchReviews,
}) => {
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleClose = () => setShowReviewModal(!showReviewModal);
  const handleShowReasonModal = () => {
    setShowReasonModal(true);
  };
  const hideReasonModal = () => {
    setShowReasonModal(false);
  };

  return (
    <>
      {/* Main Review Modal */}
      <Modal show={showReviewModal} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title style={{ color: "#8107D1" }}>
            Review no - {singleReview?.id}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="d-flex justify-content-between">
            <p className="text-capitalize">
              Patient Email:{" "}
              {singleReview?.PatientEmail[0] || "Email not available"}
            </p>
            <p>
              {singleReview?.ReviewGenTime
                ? convertDateFormat(singleReview.ReviewGenTime.split("T")[0])
                : "Date not available"}
            </p>
          </div>
          <div>
            <p dangerouslySetInnerHTML={{ __html: singleReview?.Review }}></p>
          </div>
        </Modal.Body>
        {showButtons && (
          <Modal.Footer>
            <Button variant="danger" onClick={handleShowReasonModal}>
              Reject
            </Button>
            <Button
              variant="success"
              onClick={() =>
                debouncedHandleArticleApprovals(
                  "approve",
                  singleReview.id,
                  singleReview.Review.split(" ").slice(0, 4).join(" ")
                )
              }
            >
              Approve
            </Button>
          </Modal.Footer>
        )}
      </Modal>

      {/* Reject Reason Modal */}
      {showReasonModal && (
        <RejectReasonModal
          show={showReasonModal}
          onHide={hideReasonModal}
          debouncedHandleArticleApprovals={debouncedHandleArticleApprovals}
          singleArticle={singleReview}
          type="review"
          fetchData={fetchReviews}
        />
      )}
    </>
  );
};

export default ReviewsModal;
