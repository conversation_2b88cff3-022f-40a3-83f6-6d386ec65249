"use client";
import React, { useEffect, useState } from "react";
import AccessDeniedModal from "../components/accessDeniedModal/AccessDeniedModal";
import { useAdminContext } from "../Context/AdminContext/AdminContext";
import Loading from "../components/Loading/PageLoading/Loading";

const withAuth = (WrappedComponent) => {
  // eslint-disable-next-line react/display-name
  return (props) => {
    if (typeof window !== "undefined") {

      const { isPhoneVerified, loading, isAdminChildAdmin } = useAdminContext();
      const [hydrated, setHydrated] = useState(false);

      useEffect(() => {
        setHydrated(true); // Set hydrated state to true once the component is mounted
      }, []);

      if (typeof window === "undefined" || !hydrated) {
        return null;
      }

      if (loading) {
        return <Loading />;
      }
      if (!isPhoneVerified && isAdminChildAdmin) {
        return <AccessDeniedModal />;
      }

      return <WrappedComponent {...props} />;
    }
    return null;
  };
};

export default withAuth;
