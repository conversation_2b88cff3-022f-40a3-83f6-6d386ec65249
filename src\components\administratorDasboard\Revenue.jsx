import React, { useCallback, useEffect, useState } from "react";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import { <PERSON>hn<PERSON>, Line } from "react-chartjs-2";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import RevenuePlaceholder from "./RevenuePlaceholder";

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title
);

const monthLabels = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const Revenue = ({ admin_id }) => {
  const [chartData, setChartData] = useState([]);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(false);
  const axiosAuth = useAxiosAuth();

  const fetchChartsData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_CHART_DATA}${currentYear}/all/?user_id=${admin_id}`
      );
      const fetchedData = response.data;

      const mappedData = Array(12).fill(null);

      fetchedData.forEach((item) => {
        const month = parseInt(Object.keys(item)[0], 10) - 1; // Month as index (0-based)
        const value = parseInt(Object.values(item)[0], 10);
        mappedData[month] = value !== 0 ? value : null; // Plot non-zero values only
      });

      setChartData(mappedData);
      setLoading(false);
    } catch (err) {
      console.log("Error in getting the chart data", err);
    } finally {
      setLoading(false);
    }
  }, [admin_id, axiosAuth, currentYear]);

  useEffect(() => {
    if (admin_id) {
      fetchChartsData();
    }
  }, [fetchChartsData, admin_id]);

  const handleYearChange = (e) => setCurrentYear(e.target.value);

  const lineChartData = {
    labels: monthLabels,
    datasets: [
      {
        label: `Revenue`,
        data: chartData,
        borderColor: "#8107D1",
        tension: 0.5,
        pointRadius: 6,
        borderWidth: 3,
        pointHoverRadius: 8, // Increase this value to make the hover circle larger than pointRadius
        pointBorderWidth: 2, // Thickness of the border around the points
        pointHoverBorderWidth: 2,
        borderDashOffset: 0,
        pointBackgroundColor: "white",
        spanGaps: true,
      },
    ],
  };
  const chartOptions = {
    scales: {
      x: {
        grid: {
          display: false, // Disable vertical grid lines
        },
        title: {
          display: true,
          text: "Monthly", // Label for the x-axis
          color: "#5B5B5B", // Color of the label text
          font: {
            size: 16, // Adjust label font size
            weight: "600", // Make label text bold
          },
        },
        ticks: {
          font: {
            size: 14, // Adjust font size of x-axis ticks
          },
        },
      },
      y: {
        grid: {
          display: true, // Enable horizontal grid lines
          color: "#E3E3E3", // Color of horizontal grid lines
          lineWidth: 1, // Thickness of horizontal grid lines
          borderDash: [2, 2], // Dashed pattern: 2px dash, 2px gap
        },
        title: {
          display: true,
          text: "Revenue ($)", // Label for the y-axis with "$" symbol
          color: "#5B5B5B", // Color of the label text
          font: {
            size: 16, // Adjust label font size
            weight: "600", // Make label text bold
          },
        },
        ticks: {
          callback: function (value) {
            return `$${value}`; // Add "$" symbol to each tick value
          },
          font: {
            size: 14, // Adjust font size of y-axis ticks
          },
        },
      },
    },
    plugins: {
      legend: {
        display: false,
        position: "top",
        labels: {
          color: "#8107d1", // Change legend text color
          font: {
            size: 24, // Adjust legend font size
            weight: "bold",
          },
        },
        title: {
          color: "black", // Change legend title text color
          fontWeight: "bold", // Make legend title bold
          fontSize: 20, // Set legend title font size
        },
        backgroundColor: "#8107d1", // Change legend background color
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem) {
            let label = tooltipItem.formattedValue;
            return `Revenue : $ ${label}`; // Add "$" symbol to tooltip value
          },
        },
      },
    },
  };

  if (loading) {
    return <RevenuePlaceholder />;
  }
  return (
    <div>
      <div className="row line-chart-section">
        <div className="col-sm-11 chart-section-background">
          <div className="">
            <div className="year-navigation mt-2 ">
              <div className="chart-title fs-5">Total Revenue</div>
              <div className="year-select-chart">
                <select
                  id="year-select"
                  value={currentYear}
                  onChange={handleYearChange}
                  className="select-chart-year"
                >
                  <option>Select Year</option>
                  {Array.from({ length: 28 }, (_, i) => 2023 + i).map(
                    (year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    )
                  )}
                </select>
              </div>
            </div>
            <div className="p-5">
              <Line data={lineChartData} options={chartOptions} />
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-sm-6">
          <div className="border mt-2">
            <div className="row">
              {/* <div className="col-sm-6">
                          <Doughnut data={lineChartData} />
                        </div> */}
              <div className="col-sm-6">
                <p>Profit Information</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Revenue;
