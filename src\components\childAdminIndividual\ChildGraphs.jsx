import { useEffect } from 'react';

import * as d3 from 'd3';
const ChildGraph=() =>{
    const data = [
        { category: 1, value: 165 },
        { category: 2, value: 305 },
        { category: 3, value: 288 },
        { category: 4, value: 260 },
        { category: 5, value: 205 },
        { category: 6, value: 195 },
        { category: 7, value: 245 },
        { category: 8, value: 200 },
        { category: 9, value: 180 },
        { category: 10, value: 325 },
      ];
    
      useEffect(() => {
        const svg = d3.select("#lollipop-chart");
      
        // Set the maximum height for the chart
        const maxHeight = 200;
      
        // Find the maximum value in the data
        const maxValue = d3.max(data, (d) => d.value);
      
        // Create lollipop lines
        svg
          .selectAll(".lollipop-line")
          .data(data)
          .enter()
          .append("line")
          .attr("class", "lollipop-line")
          .attr("x1", (d) => d.category * 60)
          .attr("x2", (d) => d.category * 60)
          .attr("y1", maxHeight)
          .attr("y2", (d) => maxHeight - (d.value / maxValue) * maxHeight)
          .style("stroke", "#F7D8FF"); // Light Purple
      
        // Create lollipop circles
        svg
          .selectAll(".lollipop-circle")
          .data(data)
          .enter()
          .append("circle")
          .attr("class", "lollipop-circle")
          .attr("cx", (d) => d.category * 60)
          .attr("cy", (d) => maxHeight - (d.value / maxValue) * maxHeight)
          .attr("r", 3)
          .style("fill", "#6D00B5") // Dark Purple
          .style("background", "#6D00B5")
          .style("box-shadow", "0px 3px 6px #508FF429")
          .style("border", "4px solid #FFFFFF");
      }, [data]);
    return(
        <>
     
    
      <svg id="lollipop-chart" width="590" height="200">
     
      </svg>
    
 
        </>
    )
}
export default ChildGraph;