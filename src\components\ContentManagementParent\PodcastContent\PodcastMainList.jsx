import React, { useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { highlightText } from "../../../utils/helperfunction";
import noDataFound from "../../../../public/assets/noDataFound.png";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { Placeholder } from "react-bootstrap";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import RankingModal from "../BlogsContent/RankingModal";
import CustomPagination from "../../CustomPagination/CustomPagination";
import ViewPodcastDetailsModal from "./ViewPodcastDetailsModal";
import NoDataFound from "../../noDataFound/NoDataFound";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 7 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "S No",
  "Select Podcast",
  "Expert Profile",
  "Date of Upload",
  "Podcast Title",
  "Expert Role",
  "Podcast Views",
  "Podcast Page",
];

const PodcastMainList = ({
  loading,
  initialLoading,
  current_page,
  setCurrent_Page,
  podcasts,
  fetchAllPodcasts,
  fetchTopPodcasts,
  fetchFeaturedPodcasts,
  searchQuery,
}) => {
  const [rankingLoading, setRankingLoading] = useState(false);
  const [ranking, setRanking] = useState(1);
  const [section, setSection] = useState(" ");
  const [showModal, setShowModal] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState({});
  const [showPodcastModal, setShowPodcastModal] = useState(false);
  const [singlePodcast, setSinglePodcast] = useState({});

  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const handleAddRanking = async (id) => {
    if (!ranking || !section.trim()) {
      toast.error("Please select both ranking and section", {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      return;
    }
    const body = {
      ranking: Number(ranking),
      podcast_section: section,
    };
    try {
      setRankingLoading(true);
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_RANK_FEATURED_BLOGS}${id}/?user_id=${userId}`,
        body
      );
      const responseData = response?.data;

      if (responseData === "successfully ranked") {
        toast.success("The Podcast has been successfully ranked", {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      } else if (responseData === "Already have this ranking podcast") {
        toast.error("Already have this ranking podcast", {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      } else if (responseData === "Please select a valid rank") {
        toast.error("You have exceeded the ranking limit for this section", {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
      setRanking(1);
      setSection("");
      setShowModal(false);
      fetchAllPodcasts();
      {
        section === "Featured" ? fetchFeaturedPodcasts() : fetchTopPodcasts();
      }
    } catch (err) {
      toast.error("Error in ranking this blog", {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      console.error("Error in adding the ranking:", err);
    } finally {
      setRankingLoading(false);
    }
  };

  const handleOpenRankingModal = (blog) => {
    setSelectedBlog(blog);
    setShowModal(true);
  };

  const handleViewPodcastModal = (item) => {
    setSinglePodcast(item);
    setShowPodcastModal(true);
  };

  return (
    <>
      {showModal && (
        <RankingModal
          ranking={ranking}
          section={section}
          setSection={setSection}
          showModal={showModal}
          setShowModal={setShowModal}
          setRanking={setRanking}
          handleAddRanking={handleAddRanking}
          selectedBlog={selectedBlog}
          rankingLoading={rankingLoading}
          trending={false}
        />
      )}
      {showPodcastModal && (
        <ViewPodcastDetailsModal
          setShowPodcastModal={setShowPodcastModal}
          showPodcastModal={showPodcastModal}
          singlePodcast={singlePodcast}
        />
      )}
      <div className="bg-color">
        <div className="row">
          <div className="col-sm-12 fixTableHead">
            <table className="table mt-2 custon-table-featured">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-light text-center child-admin-list-heading ${
                        index === 4 ? "col-4" : "col"
                      }`}
                      style={{fontSize: "14px"}}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="custom-border">
                {initialLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {podcasts &&
                    Array.isArray(podcasts?.items) &&
                    podcasts?.items?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                            <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      podcasts &&
                      Array.isArray(podcasts?.items) &&
                      podcasts?.items?.map((podcast, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {(current_page - 1) * 10 + index + 1}
                            </td>

                            <td className="  col custom-font text-center">
                              <div className=" d-flex align-items-center">
                                <input
                                  className=" ms-5 form-check-input blogs-content-input"
                                  type="checkbox"
                                  name="category"
                                  id="expertCategory"
                                  value="Expert Rejection"
                                  checked={
                                    podcast?.podcast_details?.PodcastRanking !==
                                    null
                                  }
                                  onClick={() =>
                                    handleOpenRankingModal(
                                      podcast?.podcast_details
                                    )
                                  }
                                />
                                <div className=" ms-2 ">
                                  {podcast?.podcast_details?.PodcastRanking !==
                                    null && (
                                    <span className="badge badge-success bg-success">
                                      {
                                        podcast?.podcast_details
                                          ?.PodcastSectionName
                                      }{" "}
                                      -{" "}
                                      {podcast?.podcast_details?.PodcastRanking}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="col purple-content text-capitalize">
                              {podcast?.podcast_details?.expert_details
                                ?.doctor_other_details?.ProfilePhoto ? (
                                <Image
                                  src={`${podcast?.podcast_details?.expert_details?.doctor_other_details?.ProfilePhoto}`}
                                  alt={`Dr ${podcast?.podcast_details?.expert_details?.role}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${podcast?.podcast_details?.expert_details?.role}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(
                                podcast?.podcast_details?.expert_details?.name
                              )}
                              {/* </Link> */}
                            </td>
                            <td className="col custom-font text-center">
                              {
                                podcast?.podcast_details?.PodcastDate?.split(
                                  "T"
                                )[0]
                              }
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                              {highlightText(
                                podcast?.podcast_details?.PodcastTopic,
                                searchQuery
                              )}
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {podcast?.podcast_details?.expert_details?.role}
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {podcast?.podcast_details?.PodcastViews}
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "#8107D1" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleViewPodcastModal(
                                    podcast?.podcast_details
                                  )
                                }
                              >
                                view
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
                {loading && renderPlaceholders("load")}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="d-flex justify-content-center align-items-center mt-3">
        <div className="d-none d-xl-block">
          <CustomPagination
            total_pages={podcasts?.total_pages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </div>
      </div>
    </>
  );
};

export default PodcastMainList;
