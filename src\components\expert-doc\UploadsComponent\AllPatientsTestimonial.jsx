import Image from "next/image";
import React from "react";
import { FaFileCircleCheck } from "react-icons/fa6";
import { formatDate } from "../db";

const AllPatientsTestimonial = ({
  testimonials,
  loading,
  handleTestimonialPreview,
  handleDeleteTestimonial,
  selectedValue,
  handleChange,
  setShowNoPreviewText
}) => {
  return (
    <>
      <div className="col-2">
        <select
          className="form-select form-select-sm custom-form-select"
          aria-label=".form-select-sm example"
          value={selectedValue}
          onChange={handleChange}
        >
          <option selected value="all">
            Select
          </option>
          <option value="0">Under Review</option>
          <option value="1">Approved</option>
          <option value="2">Rejected</option>
        </select>
      </div>
      <div className=" mt-2">
        {loading === false ? (
          <>
            {!testimonials ||
            (Array.isArray(testimonials) && testimonials.length === 0) ? (
              <>
                <h3
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "500px" }}
                >
                  <FaFileCircleCheck className="podcast-icon" />
                  &nbsp;
                  <span className="text-secondary">
                    {" "}
                    No Testimonials Available
                  </span>
                </h3>
              </>
            ) : (
              <>
                <div className="list-of-articles">
                  {Array.isArray(testimonials) &&
                    testimonials?.map((item, index) => {
                      return (
                        <div key={index} className="bg-color-border mb-2">
                          <div className="row">
                            <div className="col-sm-6 mb-0 d-flex justify-content-start align-items-center">
                              {item?.patient_photo ? (
                                <Image
                                  src={item.patient_photo}
                                  alt="feedback"
                                  height={35}
                                  width={35}
                                  className="testimonial_image"
                                />
                              ) : (
                                <Image
                                  src="/images/profile.png" // Fallback image
                                  alt="fallback"
                                  height={35}
                                  width={35}
                                  className="testimonial_image"
                                />
                              )}
                              <p className="ps-2 mb-0 custom-font-size">
                                {item?.CancerTreatmentType}
                              </p>
                            </div>
                            <div className="col-sm-4 mb-0 d-flex justify-content-evenly align-items-center">
                              <p className="purple-text mb-0 pe-2">
                                {formatDate(item.CurrentTime)}
                              </p>
                              <p
                                className={`blog-approval-status-${
                                  item.status === 2
                                    ? "under-review"
                                    : item.status === 1
                                    ? "approved"
                                    : item.status === 0
                                    ? "rejected"
                                    : ""
                                } mb-0`}
                              >
                                {item.status === 2
                                  ? "Under Review"

                                  : item.status === 1
                                  ? "Approved"
                                  : item.status === 0
                                  ? "Rejected"
                                  : ""}
                              </p>
                            </div>
                            <div className="col-sm-1 d-flex justify-content-center align-items-center">
                              <button
                                type="button"
                                className="btn btn-transparent red-text fw-bold p-0"
                                onClick={() =>
                                  handleDeleteTestimonial(item?.id)
                                }
                              >
                                Delete
                              </button>
                            </div>

                            <div className="col-sm-1 d-flex justify-content-center align-items-center">
                              <button
                                type="button"
                                className="btn btn-transparent purple-text fw-bold p-0"
                                onClick={() => {
                                  handleTestimonialPreview(item);
                                  setShowNoPreviewText(false)
                                }}
                              >
                                View
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </>
            )}
          </>
        ) : (
          renderPlaceholders()
        )}
      </div>
    </>
  );
};

export default AllPatientsTestimonial;
