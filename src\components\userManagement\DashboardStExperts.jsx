import React, { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import Cookies from "js-cookie";

const DashboardStExperts = ({ startDate, endDate }) => {
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const user_id = session?.user?.id;
  const [expertCounts, setExpertCounts] = useState({
    doctor: 0,
    researcher: 0,
    influencer: 0,
  });

  const approveExperts = useCallback(async () => {
    try {
      setLoading(true);

      // const data = await axiosAuth.get(
      //   `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}all/?start_date=${startDate}&end_date=${endDate}&user_id=${user_id}`
      // );
      let apiUrl = `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}all/all/?user_id=${user_id}`;
      if (startDate && endDate) {
        apiUrl += `&start_date=${startDate}&end_date=${endDate}`;
      }

      const data = await axiosAuth.get(apiUrl);
      const filteredExperts = {
        doctor: data?.data?.experts_data?.filter(
          (user) => user?.user_role === "doctor"
        ).length,
        researcher: data?.data?.experts_data?.filter(
          (user) => user?.user_role === "researcher"
        ).length,
        influencer: data?.data?.experts_data?.filter(
          (user) => user?.user_role === "influencer"
        ).length,
      };
      setExpertCounts(filteredExperts);
    } catch (err) {
      console.log("error in fetching ", err);
    } finally {
      setLoading(false);
    }
  }, [user_id, axiosAuth, startDate, endDate]);

  useEffect(() => {
    if (user_id) {
      approveExperts();
    }
  }, [user_id, approveExperts]);

  return (
    <>
      <div className="row ">
        <div className="col-sm-4">
          <div className="custom-border">
            <p className="text-center sub-heading mt-1 mb-1">Doctors</p>
            <p className="text-center purple-num mb-2">
              {expertCounts?.doctor}
            </p>
            <div className="bottom-border"></div>
            {/* <p className="text-center green-num mb-1 mt-3">
                5%
                <BiSolidUpArrow />
              </p> */}
          </div>
        </div>
        <div className="col-sm-4">
          <div className="custom-border">
            <p className="text-center sub-heading mt-1 mb-1">Researchers</p>
            <p className="text-center purple-num mb-2">
              {expertCounts?.researcher}
            </p>
            <div className="bottom-border"></div>
            {/* <p className="text-center green-num mb-1 mt-3">
                5%
                <BiSolidUpArrow />
              </p> */}
          </div>
        </div>
        <div className="col-sm-4">
          <div className="custom-border">
            <p className="text-center sub-heading mt-1 mb-1">Health Guides</p>
            <p className="text-center purple-num mb-2">
              {expertCounts?.influencer}
            </p>
            <div className="bottom-border"></div>
            {/* <p className="text-center green-num mb-1 mt-3">
                5%
                <BiSolidUpArrow />
              </p> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default DashboardStExperts;
