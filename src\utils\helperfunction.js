import moment from "moment";
import moment1 from "moment-timezone";
import { FaStar } from "react-icons/fa";
import { Button, Placeholder } from "react-bootstrap";
import parsePhoneNumberFromString from "libphonenumber-js";
import { format, formatDistanceToNow, sub, parseISO, isValid } from "date-fns";
import { useEffect, useState } from "react";

export const capitalizeFullName = (name) => {
  if (typeof name === "undefined" || name === null) {
    return "";
  }
  if (name.includes(" ")) {
    let names = name.split(" ");
    for (let i = 0; i < names.length; i++) {
      names[i] = names[i].charAt(0).toUpperCase() + names[i].slice(1);
    }
    return names.join(" ");
  } else {
    return name.charAt(0).toUpperCase() + name.slice(1);
  }
};

export const formatPhoneNumber = (number) => {
  let phoneNumber = String(number);
  if (phoneNumber.startsWith("+")) {
    phoneNumber = phoneNumber.slice(1);
  }
  let countryCode = phoneNumber.slice(0, 2);
  let remainingNumber = phoneNumber.slice(2);
  return "+" + countryCode + " " + remainingNumber;
};
export const formatDate = (timestamp) => {
  let date = new Date(timestamp);
  let day = String(date.getDate()).padStart(2, "0");
  let month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based in JavaScript
  let year = date.getFullYear();
  return day + "-" + month + "-" + year;
};
export function timeDifference(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  const minute = 60;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30;
  const year = day * 365;
  const decade = year * 10;

  if (diffInSeconds < minute) {
    return `${diffInSeconds} seconds ago`;
  } else if (diffInSeconds < hour) {
    return `${Math.floor(diffInSeconds / minute)} minutes ago`;
  } else if (diffInSeconds < day) {
    return `${Math.floor(diffInSeconds / hour)} hours ago`;
  } else if (diffInSeconds < month) {
    return `${Math.floor(diffInSeconds / day)} days ago`;
  } else if (diffInSeconds < year) {
    return `${Math.floor(diffInSeconds / month)} months ago`;
  } else if (diffInSeconds < decade) {
    return `${Math.floor(diffInSeconds / year)} years ago`;
  } else {
    return `${Math.floor(diffInSeconds / decade)} decades ago`;
  }
}
export function convertDateFormat(inputDate) {
  // Create a new Date object
  let date = new Date(inputDate);

  // Get the day, month, and year
  let day = date.getDate();
  let month = date.toLocaleString("default", { month: "short" });
  let year = date.getFullYear();

  // Return the formatted date
  return `${day} ${month} ${year}`;
}

export function getCurrentDate() {
  let date = new Date();
  let year = date.getFullYear();
  let month = ("0" + (date.getMonth() + 1)).slice(-2); // Months are zero based
  let day = ("0" + date.getDate()).slice(-2);
  return year + "-" + month + "-" + day;
}
export const formatDateRev = (timestamp) => {
  let date = new Date(timestamp);
  let day = String(date.getDate()).padStart(2, "0");
  let month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based in JavaScript
  let year = date.getFullYear();
  return year + "-" + month + "-" + day;
};

export function mergeArraysBasedOnIndex(arr1, arr2) {
  // Ensure arr1 and arr2 are arrays with the length property
  const length1 = Array.isArray(arr1) ? arr1.length : 0;
  const length2 = Array.isArray(arr2) ? arr2.length : 0;

  // Determine the length of the merged array
  const mergedLength = Math.max(length1, length2);

  // Use map to iterate through the merged array
  const mergedArray = Array.from({ length: mergedLength }, (_, index) => {
    const itemFromArr1 = (arr1 && arr1[index]) || {}; // Default to an empty object if arr1 is shorter or undefined
    const itemFromArr2 = (arr2 && arr2[index]) || {}; // Default to an empty object if arr2 is shorter or undefined

    // Merge properties from both arrays
    return { ...itemFromArr1, ...itemFromArr2 };
  });

  return mergedArray;
}

export function calculateArrayCount(arr) {
  return arr?.length;
}

export function calculateUnreadReadCount(arr) {
  const unreadCount = arr.filter((item) => !item.read).length;
  const readCount = arr.length - unreadCount;
  return { unreadCount, readCount };
}

export function calculatePriorityCount(arr, priority) {
  return arr.filter((item) => item.priority === priority).length;
}

export function convert_time(dateString) {
  const date = moment(dateString);
  const formattedDate = date.format("DD MMM YYYY \n h:mm:ss A");
  return formattedDate;
}
export function convertime(dateString) {
  if (!dateString) {
    return { formattedDate: null, formattedTime: null };
  }

  const date = parseISO(dateString);
  if (!isValid(date)) {
    // Return null or custom fallback for invalid dates
    return { formattedDate: null, formattedTime: null };
  }

  const formattedDate = format(date, "dd MMM yyyy"); // Example: "29 Dec 2024"
  const formattedTime = format(date, "h:mm:ss a"); // Example: "4:30:15 PM"

  return { formattedDate, formattedTime };
}

export function formatNameInitials(fullName) {
  if (!fullName) {
    return ["", ""];
  }
  const names = fullName.trim().split(/\s+/);
  let initials = "";

  if (names.length >= 2) {
    const firstNameInitial = names[0].charAt(0).toUpperCase();
    const lastNameInitial = names[names.length - 1].charAt(0).toUpperCase();

    initials = `${firstNameInitial}${names
      .slice(1, -1)
      .map((name) => name.charAt(0).toUpperCase())
      .join("  ")}${lastNameInitial}`;
  }

  return initials;
}
// export function formatDateTime(dateTimeString, timeZone) {
//   const formattedDate = moment(dateTimeString)
//     .tz(timeZone)
//     .format("DD MMM YYYY [at] h:mmA z");

//   return formattedDate;
// }
// export function formatDateTime(dateTimeString, timeZone) {
//   const formattedDate = moment(dateTimeString)
//     .tz(timeZone)
//     .format("h:mm A [at] DD MMM YYYY");

//   return formattedDate;
// }

export function formatDateTime(timestamp, timeZone) {
  const date = new Date(timestamp);
  const options = {
    timeZone,
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  };
  const formattedDate = date?.toLocaleString("en-US", options);
  return formattedDate;
}

export function formatDateTimeConvert(dateTimeString) {
  // Parse the input ISO 8601 date string using Moment.js
  const dateTime = moment(dateTimeString);

  // Format the date part as 'DD MMM YYYY'
  const formattedDate = dateTime.format("DD MMM YYYY");

  // Format the time part as 'hh:mm A'
  const formattedTime = dateTime.format("hh:mm A");

  // Return the formatted date and time
  return formattedDate;
}

export const getFileExtension = (url) => {
  if (typeof url === "string") {
    const dotIndex = url.lastIndexOf(".");
    const questionMarkIndex = url.lastIndexOf("?");
    const extensionStart = dotIndex !== -1 ? dotIndex : 0;
    const extensionEnd =
      questionMarkIndex !== -1 ? questionMarkIndex : undefined;
    return url.substring(extensionStart + 1, extensionEnd);
  }
  return ""; // or any default value you prefer
};

export const highlightText = (text, searchTerm) => {
  const lowerCaseText = text?.toLowerCase();
  const lowerCaseSearchTerm = searchTerm?.toLowerCase();

  if (!searchTerm || !lowerCaseText?.includes(lowerCaseSearchTerm)) {
    return text;
  }

  const startIndex = lowerCaseText?.indexOf(lowerCaseSearchTerm);
  const endIndex = startIndex + searchTerm.length;

  return (
    <span>
      {text.substring(0, startIndex)}
      <span
        style={{ backgroundColor: "rgb(247,226,252)", borderRadius: "3px" }}
        className="px-1 fw-bold"
      >
        {text.substring(startIndex, endIndex)}
      </span>
      {text.substring(endIndex)}
    </span>
  );
};

export const highlightInteger = (number, searchTerm) => {
  if (
    typeof number !== "number" ||
    !Number.isInteger(number) ||
    !searchTerm ||
    isNaN(searchTerm)
  ) {
    return number;
  }

  const lowerCaseNumber = number.toString().toLowerCase();
  const lowerCaseSearchTerm = searchTerm.toString().toLowerCase();

  if (!lowerCaseNumber.includes(lowerCaseSearchTerm)) {
    return number;
  }

  const startIndex = lowerCaseNumber.indexOf(lowerCaseSearchTerm);
  const endIndex = startIndex + lowerCaseSearchTerm.length;

  return (
    <span>
      {number.toString().substring(0, startIndex)}
      <span
        style={{ backgroundColor: "rgb(193 170 197)", borderRadius: "3px" }}
      >
        {number.toString().substring(startIndex, endIndex)}
      </span>
      {number.toString().substring(endIndex)}
    </span>
  );
};

export function calculateAge(dob) {
  const today = new Date();
  const birthDate = new Date(dob);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // If the birth month has not occurred yet in this year, subtract 1 from age
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
}

export const callRefreshToken = async (refreshToken) => {
  const url = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
  const options = {
    method: "POST",
    // url: "https://nandhakumarsnk.pythonanywhere.com/api/token/refresh/",
    url: url,
    data: {
      refresh: refreshToken,
    },
  };
  try {
    const response = await axios.request(options);
    if (response.status < 200 || response.status >= 300) {
      throw new Error(
        `Failed to refresh access token. Status: ${response.status}`
      );
    }

    return response.data.access;
  } catch (error) {
    console.error("Error refreshing access token:", error);
    throw error;
  }
};

export const calculateMeetingDuration = (startTime, endTime) => {
  if (!startTime || !endTime) {
    return "Not available";
  }

  const start = new Date(startTime);
  const end = new Date(endTime);

  const durationInMilliseconds = end - start;
  const minutes = Math.floor(durationInMilliseconds / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  return `${hours}:${remainingMinutes < 10 ? "0" : ""}${remainingMinutes} mins`;
};

export const countUserRoles = (expertsData) => {
  // setLoading(true);
  return expertsData?.reduce(
    (counts, expert) => {
      const userRole = expert?.user_role?.toLowerCase(); // convert to lowercase for case-insensitivity

      // Increment the count for the corresponding user role
      counts[userRole + "Count"]++;
      // setLoading(false);
      return counts;
    },
    { doctorCount: 0, influencerCount: 0, researcherCount: 0 }
  );
};

export function generateRandomPassword() {
  const charset =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  let hasNumber = false;

  // Add at least one number to the password
  const randomIndex = Math.floor(Math.random() * 10);
  password += charset[randomIndex + 52]; // Start from index 52 to pick a number character

  // Generate the rest of the password
  for (let i = 0; i < 15; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
}

export const renderStarsApps = (ratings) => {
  const stars = [];
  for (let i = 0; i < ratings; i++) {
    stars.push(
      // <i key={i} className="fa fa-star yellow-star" />
      <FaStar className="star-in-app" />
    );
  }
  return stars;
};

export function getStartAndEndDate(year, month, monthsToAdd = 0) {
  // Create a new Date object with the given year and month (months are 0-indexed)
  const startDate = new Date(year, month - 1, 1); // Subtract 1 from the month since it's 0-indexed

  // Calculate the end date for this month
  const endDate = new Date(year, month, 0); // Set day to 0 to get the last day of the previous month

  // Calculate the end date for the specified future month
  const futureMonth = (month + monthsToAdd) % 12;
  const futureYear = year + Math.floor((month + monthsToAdd) / 12);
  const endDateFutureMonth = new Date(futureYear, futureMonth + 1, 0); // Set day to 0 to get the last day of the previous month

  // Format the dates as strings in "YYYY-MM-DD" format
  const formattedStartDate = startDate.toISOString().split("T")[0];
  const formattedEndDate = endDate.toISOString().split("T")[0];
  const formattedEndDateFutureMonth = endDateFutureMonth
    .toISOString()
    .split("T")[0];

  return {
    startDate: formattedStartDate,
    endDate: formattedEndDate,
    endDateFutureMonth: formattedEndDateFutureMonth,
    todayDate: new Date().toISOString().split("T")[0], // Include today's date
  };
}
export function convertIntoTime(timestamp = "", timeZone = "Asia/Kolkata") {
  const date = new Date(timestamp);

  const options = {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
    timeZone: timeZone,
  };

  const formattedTime = date?.toLocaleTimeString("en-US", options);
  return formattedTime;
}

export function changeDateFormat(inputDate) {
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  if (inputDate) {
    const dateParts = inputDate.split("-");

    if (dateParts.length === 3) {
      const [year, month, day] = dateParts;
      const formattedDate = `${months[month - 1]} ${day} ${year}`;
      return formattedDate;
    }
  }

  return "Invalid Date"; // Handle invalid or missing input
}

export function checkExpiry(startTime, endTime, timeZone) {
  try {
    const startMoment = moment.tz(startTime, timeZone);
    const endMoment = moment.tz(endTime, timeZone);
    const currentDate = moment.tz(timeZone);

    const fiveMinutesBeforeStart = startMoment.clone().subtract(5, "minutes");

    const isExpired = hasDatePassed(startMoment);
    if (isExpired) {
      return "Expired";
    } else {
      if (
        currentDate.isBetween(fiveMinutesBeforeStart, endMoment, null, "[]")
      ) {
        return "Start";
      } else if (currentDate.isBefore(fiveMinutesBeforeStart)) {
        return "Wait";
      } else {
        return "Expired";
      }
    }
  } catch (error) {
    console.error("Error in checkExpiry:", error);
    return "Expired"; // Return expired status if there's an error
  }
}

export const formatDateToYMD = (date) => {
  const year = date?.getFullYear();
  const month = String(date?.getMonth() + 1).padStart(2, "0");
  const day = String(date?.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

export const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) {
    return "00:00:00";
  }

  const start = new Date(startTime);
  const end = new Date(endTime);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return "Invalid timestamp format";
  }

  const duration = end - start;
  const hours = Math.floor(duration / (1000 * 60 * 60));
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((duration % (1000 * 60)) / 1000);

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
};

export const validateEmail = (email) => {
  // Regular expression for validating email format
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
};

export function splitDoctorFullName1(fullName = "sandeep reddy") {
  if (!fullName) {
    return ["", ""];
  }
  let nameParts = fullName.split(" ");
  let firstName = nameParts[1];
  let lastName = nameParts.slice(2).join(" ");

  // Capitalize the first letter of the first and last name
  firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
  lastName = lastName.charAt(0).toUpperCase() + lastName.slice(1);

  return {
    doctorFirstName: firstName,
    doctorLastName: lastName || "",
  };
}

export function splitPatientName(fullName = "sandeep reddy") {
  if (!fullName) {
    return ["", ""];
  }
  let nameParts = fullName.split(" ");
  let firstName = nameParts[0];
  let lastName = nameParts.slice(1).join(" ");

  // Capitalize the first letter of the first and last name
  firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
  lastName = lastName.charAt(0).toUpperCase() + lastName.slice(1);

  return {
    patientFirstName: firstName,
    patientLastName: lastName || "",
  };
}

export const renderPlaceholders = (length, height, borderRadius) => {
  const placeholders = Array.from({ length }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder xs={12} size={"lg"} style={{ height, borderRadius }} />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

export const formatSelectedDate = (date) => {
  const year = date?.getFullYear();
  const month = String(date?.getMonth() + 1).padStart(2, "0"); // Months are zero-based
  const day = String(date?.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};
export function getPhoneNumberWithoutCountryCode(phoneNumber) {
  const numberwithplus = `+${phoneNumber}`;
  const parsedPhoneNumber = parsePhoneNumberFromString(numberwithplus);
  if (parsedPhoneNumber) {
    return parsedPhoneNumber.nationalNumber;
  } else {
    // If parsing fails, return the original phone number
    return phoneNumber;
  }
}

export function getCountryCodeFromPhoneNumber(phoneNumber) {
  const numberwithplus = `+${phoneNumber}`;
  const parsedPhoneNumber = parsePhoneNumberFromString(numberwithplus);
  if (parsedPhoneNumber) {
    return parsedPhoneNumber.countryCallingCode;
  } else {
    return null;
  }
}

export function extractTimeFromDateString(datetimeString) {
  const datetime = new Date(datetimeString);
  const hour = datetime.getHours().toString().padStart(2, "0");
  const minute = datetime.getMinutes().toString().padStart(2, "0");
  return `${hour}:${minute}`;
}

export const getImageSrc = (image) => {
  let imageUrl = "";
  if (typeof image === "string") {
    imageUrl = `${image.trim()}`;
    // Check if the string is a valid URL
    if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
      return imageUrl;
    }
    // Ensure relative paths start with a leading slash
    if (!imageUrl.startsWith("/")) {
      imageUrl = `${imageUrl}`;
    }
  }
  // Handle file object
  else if (image instanceof File) {
    imageUrl = URL.createObjectURL(image);
  }
  // Return default profile image if no valid image is provided
  else {
    imageUrl = profileimg;
  }
  return imageUrl;
};

export const formatNotificationTime = (notificationTime) => {
  const parsedTime = new Date(notificationTime);
  const currentTime = new Date();
  const timeDifference = currentTime - parsedTime;

  if (timeDifference < 24 * 60 * 60 * 1000) {
    return format(parsedTime, "HH:mm");
  } else {
    return formatDistanceToNow(parsedTime, { addSuffix: true });
  }
};

export function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => setDebouncedValue(value), delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function formatDateFromUnixTimestamp(unixTimestamp) {
  const date = new Date(unixTimestamp * 1000); // Convert seconds to milliseconds
  const day = date.getDate();
  const month = date.getMonth() + 1; // Months are zero-based, so add 1
  const year = date.getFullYear();

  // Pad single-digit day and month with leading zeros
  const formattedDay = day < 10 ? `0${day}` : day;
  const formattedMonth = month < 10 ? `0${month}` : month;

  return `${formattedDay}-${formattedMonth}-${year}`;
}

export const skeletonLoader = (
  height = "10px",
  borderRadius = "2px",
  length
) => {
  const placeholders = Array.from({ length }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder xs={12} size={"lg"} style={{ height, borderRadius }} />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

export function splitName(name) {
  // Define a list of common prefixes
  const prefixes = ["Dr", "Mr", "Mrs", "Ms", "Prof"];

  // Split the name by spaces to analyze parts
  let nameParts = name.split(" ");

  // Remove any prefix found in the name parts
  if (prefixes.includes(nameParts[0].toLowerCase())) {
    nameParts.shift();
  }

  // Join the remaining parts to form the name without the prefix
  const nameWithoutPrefix = nameParts.join(" ");

  // Split the name without prefix to get first and last names
  const splitNames = nameWithoutPrefix.split(" ");

  // Handle cases with different lengths of split names
  let firstName, lastName;
  if (splitNames.length > 1) {
    firstName = splitNames.slice(0, -1).join(" ");
    lastName = splitNames.slice(-1).join(" ");
  } else {
    firstName = splitNames[0];
    lastName = "";
  }

  return { firstName, lastName };
}

export function formatCustomDate(datetime) {
  // Parse the datetime string
  const date = new Date(datetime);

  // Define an array of month names
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Extract day, month, and year
  const day = String(date.getDate()).padStart(2, "0");
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear();

  // Format the date
  const formattedDate = `${day} ${month}   ${year}`;

  return formattedDate;
}

export function formatCustomDateAndTime(datetime) {
  // Parse the datetime string
  const date = new Date(datetime);

  // Define an array of month names
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Extract day, month, and year
  const day = String(date.getDate()).padStart(2, "0");
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear();

  // Extract hours and minutes
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, "0");

  // Determine AM or PM
  const ampm = hours >= 12 ? "pm" : "am";

  // Convert 24-hour time to 12-hour time
  hours = hours % 12;
  hours = hours ? hours : 12; // The hour '0' should be '12'
  const formattedTime = `${hours}:${minutes} ${ampm}`;

  // Format the date and time
  const formattedDate = `${day} ${month} ${year}, ${formattedTime}`;

  return formattedDate;
}

export function formatDateandTime(isoString) {
  // Create a new Date object from the ISO string
  const date = new Date(isoString);

  // Extract date components
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Month is zero-based, so we add 1
  const day = String(date.getDate()).padStart(2, "0");

  // Extract time components in 24-hour format
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  // Format: YYYY-MM-DD HH:MM:SS
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
export const calculateWordCount = (description = "") =>
  description
    .trim()
    .split(/\s+/)
    .filter((word) => word !== "").length;

export function formatDatetoShortYear(dateString) {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0"); // Ensure 2 digits for day
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Get month and ensure 2 digits
  const year = String(date.getFullYear()).slice(2); // Get the last 2 digits of the year

  return `${day}-${month}-${year}`;
}

export function getStartAndEndDatesForNavigateMonth(year, month) {
  // Validate inputs
  if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
    throw new Error("Invalid year or month");
  }

  // Create moment objects for the given year and month
  const startDate1 = moment({ year, month: month - 1, day: 1 })
    .subtract(6, "days")
    .format("YYYY-MM-DD");
  const endDate1 = moment({ year, month: month - 1 })
    .endOf("month")
    .add(6, "days")
    .format("YYYY-MM-DD");

  return { startDate1, endDate1 };
}
export const truncateTitle = (title, maxLength) => {
  if (title?.length > maxLength) {
    return title.slice(0, maxLength) + "..";
  }
  return title;
};

export function isValidURL(url = "") {
  try {
    new URL(url);
    return true;
  } catch (_) {
    return false;
  }
}

const dataSecretKey = process.env.NEXT_PUBLIC_ENCRYPTION_DECYPTION_SECRET;

export const xorEncryptDecrypt = (input) => {
  if (!dataSecretKey) {
    throw new Error("Secret key is required");
  }
  let output = "";
  for (let i = 0; i < input.length; i++) {
    const inputCode = input.charCodeAt(i);
    const secretCode = dataSecretKey.charCodeAt(i % dataSecretKey.length);
    const xor = inputCode ^ secretCode;
    output += String.fromCharCode(xor);
  }
  return output;
};

export const encryptBase = (data) => {
  const stringified = JSON.stringify(data);
  const encrypted = xorEncryptDecrypt(stringified, dataSecretKey);
  return btoa(encrypted); // Base64 encode for safe transmission
};

export const decryptBase = (encodedData) => {
  try {
    const decoded = atob(encodedData); // Base64 decode
    const decrypted = xorEncryptDecrypt(decoded, dataSecretKey);
    return JSON.parse(decrypted);
  } catch (err) {
    console.error("Decryption failed:", err);
    return null;
  }
};
export const safeDecode = (value) => {
  try {
    return decodeURIComponent(value);
  } catch (err) {
    console.error("Malformed URI:", err);
    return null;
  }
};
