import Image from "next/image";
import React, { useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import ConsentFormModal from "./AllApprovalModals/ConsentFormModal.jsx";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import DoctorConsentFormModal from "../doctorConsentModal/DoctorsConsentFormModal";
import { useAdminContext } from "../../../Context/AdminContext/AdminContext";
import { formatDate, timeDifference } from "../db";

const AllConsentForm = ({
  doctorConsentLoading,
  doctorsConsent,
  doctorsData,
  fetchDoctorConsent,
}) => {
  const [showConsentFormModal, setshowConsentFormModal] = useState(false);

  const { session } = useAdminContext();
  const admin_id = session?.user?.id;
  return (
    <>
      <div className="col-sm-12">
        <p className="fw-bold">Consent Form</p>

        {doctorConsentLoading === false ? (
          <>
            {doctorsConsent &&
            Array.isArray(doctorsConsent) &&
            doctorsConsent?.length === 0 ? (
              <div className="fs-4 d-flex align-items-center justify-content-center no-articles-found">
                <PiFolderNotchOpenFill color={"#8107d1"} size={30} />
                &nbsp; No Records Found
              </div>
            ) : (
              <div className="introvideo-bg p-3">
                <div className="row">
                  <div className="col-sm-1">
                    {doctorsData?.doctor_other_details?.ProfilePhoto ? (
                      <Image
                        src={doctorsData?.doctor_other_details?.ProfilePhoto}
                        width={50}
                        height={50}
                        alt="cu logo"
                        className="allApproval-image"
                      />
                    ) : (
                      <Image
                        src="/images/profile.png"
                        alt="fallback"
                        height={35}
                        width={35}
                        className="testimonial_image"
                      />
                    )}
                  </div>

                  <div className="col-sm-2">
                    <p className="fw-semibold mb-0">
                      {formatDate(doctorsConsent[0]?.DateOfConsentForm)}
                    </p>
                    <p className="custom-transperent-btn">
                      {timeDifference(doctorsConsent[0]?.DateOfConsentForm)}
                    </p>
                  </div>

                  <div className="col-sm d-flex justify-content-end align-items-center">
                    <span
                      className="allApproval-view-btn"
                      onClick={() => setshowConsentFormModal(true)}
                    >
                      View
                    </span>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          renderPlaceholders()
        )}
      </div>
      {showConsentFormModal && (
        <DoctorConsentFormModal
          doctorsData={doctorsData}
          showConsentFormModal={showConsentFormModal}
          setshowConsentFormModal={setshowConsentFormModal}
          doctorsConsent={doctorsConsent}
          admin_id={admin_id}
          fetchDoctorConsent={fetchDoctorConsent}
          showButtons={true}
        />
      )}
    </>
  );
};

export default AllConsentForm;
