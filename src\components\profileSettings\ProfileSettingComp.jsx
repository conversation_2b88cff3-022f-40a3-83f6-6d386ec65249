"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useCallback, useEffect, useState } from "react";
import Select from "react-select";
import Swal from "sweetalert2";
import { FaEdit } from "react-icons/fa";
import { AiFillCamera } from "react-icons/ai";
import parsePhoneNumberFromString from "libphonenumber-js";
import "./profileSetting.css";
import { IoArrowBack } from "react-icons/io5";
import Cookies from "js-cookie";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import ToggleBtn from "../../common/toggle-btn/ToggleBtn";
import Loading from "../Loading/PageLoading/Loading";
import NotificationPermission from "./NotificationPermission";
import moment from "moment-timezone";
import { toast } from "react-toastify";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import { capitalizeFullName, validateEmail } from "../../utils/helperfunction";
import PasswordReset from "./PasswordReset/PasswordReset";
import PhoneInput from "react-phone-input-2";
import { Country, State, City } from "country-state-city";
import { handlePhoneUpdate } from "./phoneUpdateHandler";
import "./profileSetting.css";

const validatePhoneNumber = (phoneNumber) => {
  const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
  return parsedPhoneNumber && parsedPhoneNumber.isValid();
};
const customStyles = {
  control: (provided, state) => ({
    ...provided,

    border: state.isFocused ? "1px solid #e3e3e3" : "1px solid #e3e3e3",
    borderColor: state.isFocused ? "#ced4da" : "#e3e3e3",
    fontSize: "12px",
    height: "30px",
    minHeight: "30px",
    boxShadow: "0px 3px 6px #00000029",
    width: "100%",
    ":hover": {
      borderColor: "#e3e3e3", // Consistent border on hover
    },
    backgroundColor: state.isDisabled ? "#f1f1f1" : provided.backgroundColor, // Keep background logic
  }),

  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused ? "#e3e3e3" : "white",
    fontSize: "12px",
    color: state.isFocused ? "black" : "#333",
    ":hover": {
      backgroundColor: "#e3e3e3",
      color: "black",
    },
    borderRadius: "5px",
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    height: "30px", // Set the height for the container holding the indicators
    padding: "0", // Optional: Remove padding if needed
  }),
};

const genderOptions = [
  { value: "", label: "Select Gender" },
  { value: "Female", label: "Female" },
  { value: "Male", label: "Male" },
  { value: "Others", label: "Others" },
];
const initialPasswordData = {
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
};
const ProfileSettingComp = () => {
  const { isAdminChildAdmin, userPermissions, session } = useAdminContext();
  const axiosAuth = useAxiosAuth();
  const [originalFormData, setOriginalFormData] = useState({});
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [notificationsList, setNotificationsList] = useState([]);
  const [formData, setFormData] = useState({});
  const [timezones, setTimezones] = useState([]);
  const [passwords, setPasswords] = useState(initialPasswordData);
  const [tempPhone, setTempPhone] = useState("");
  const [tempCountryCode, setTempCountryCode] = useState("+1");

  useEffect(() => {
    if (formData?.phone) {
      setTempPhone(formData.phone);
    }
    if (formData?.country_code) {
      setTempCountryCode(formData.country_code);
    }
  }, [formData]);

  console.log("tempPhone", tempPhone);
  const isPermissible = userPermissions?.includes("cu_app.manage_notif");
  const admin_id = session?.user?.id;
  const userEmail = session?.user?.email;

  const fetchNotificationList = useCallback(async () => {
    try {
      if (admin_id !== undefined && admin_id !== null) {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_FETCH_NOTIFICATION_STATUS}all/?user_id=${admin_id}`
        );
        setNotificationsList(response?.data);
        setLoading(false);
      }
    } catch (err) {
      console.log("Error in getting the Notifications List", err);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, admin_id]);

  //Fetching the user data
  const getUserData = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_USER_DATA}${userEmail}`
      );

      setFormData(response?.data?.user_data);
      const cityValue = response?.data?.user_data?.City;
      if (cityValue) {
        const [city, state] = cityValue.split(",");

        setFormData((prevDetails) => ({
          ...prevDetails,
          City: city?.trim(),
          State: state?.trim(),
        }));
      }
      setOriginalFormData(response?.data?.user_data);
    } catch (error) {
      console.error("Error in getting user data:", error);
    }
  }, [userEmail, axiosAuth]);

  useEffect(() => {
    const timezoneList = moment?.tz?.names();
    setTimezones(timezoneList);
  }, []);

  const handleEditButtonClick = async () => {
    if (!editMode) {
      const shouldEdit = await Swal.fire({
        title: "Confirm Edit",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
        customClass: {
          confirmButton: "swal-confirm-button-class",
          cancelButton: "swal-cancel-button-class",
        },
        buttonsStyling: false,
      });

      if (!shouldEdit.isConfirmed) {
        return;
      }
    }

    setErrors({});
    setEditMode(!editMode);
  };

  useEffect(() => {
    if (admin_id && userEmail) {
      fetchNotificationList();
      getUserData();
    }
  }, [admin_id, fetchNotificationList, getUserData, userEmail]);

  if (loading) {
    return (
      <>
        <Loading />
      </>
    );
  }

  const timeZoneOptions = timezones?.map((timezone) => ({
    value: timezone,
    label: timezone,
  }));

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "Address") {
      setFormData((prevData) => ({
        ...prevData,
        admin_other_details: {
          ...prevData.admin_other_details,
          Address: value,
        },
      }));
    } else if (name === "ProfilePhoto") {
      const newValue = e.target.files ? e.target.files[0] : value;
      setFormData((prevData) => ({
        ...prevData,
        admin_other_details: {
          ...prevData.admin_other_details,
          ProfilePhoto: newValue,
        },
      }));
    } else if (name === "date_of_birth") {
      const dob = moment(value, "YYYY-MM-DD", true); // The true flag ensures strict parsing
      const age = moment().diff(dob, "years");

      if (!dob.isValid()) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          date_of_birth: "Date format should be YYYY-MM-DD",
        }));
      } else if (age < 18) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          date_of_birth: "Age must be at least 18 years old",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          date_of_birth: null,
        }));
        setFormData((prevData) => ({
          ...prevData,
          date_of_birth: dob.format("YYYY-MM-DD"),
        }));
      }
    } else if (name === "email") {
      const validEmail = validateEmail(value);
      if (!validEmail) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: "Please enter a valid email address",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: null,
        }));
        setFormData((prevData) => ({
          ...prevData,
          email: value,
        }));
      }
    } else if (name === "sex") {
      if (!value) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          sex: "Please select gender",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          sex: null,
        }));
        setFormData((prevData) => ({
          ...prevData,
          sex: value,
        }));
      }
    } else {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }));
    }
  };

  const handlePhoneChange = (value, country, e, formattedValue) => {
    setFormData((prevData) => ({ ...prevData, phone: formattedValue }));
  };
  const handleSaveChangesButtonClick = async () => {
    const errorField = Object.keys(errors).find((key) => errors[key] !== null);
    if (errorField) {
      toast.error(errors[errorField], {
        autoClose: 5000,
        theme: "colored",
        position: "top-center",
      });
      return;
    }

    try {
      const formDataObj = new FormData();
      formDataObj.append("name", formData?.name);
      const isValidPhoneNumber = validatePhoneNumber(formData?.phone);
      if (!isValidPhoneNumber) {
        toast.error("Invalid phone number", {
          autoClose: 5000,
          position: "top-center",
        });

        return;
      }
      formDataObj.append("phone", formData?.phone);
      formDataObj.append("email", formData?.email);
      formDataObj.append("sex", formData?.sex);
      formDataObj.append("TimeZone", formData?.TimeZone);
      formDataObj.append("Address", formData?.admin_other_details.Address);
      formDataObj.append("Designation", "Designation");
      formDataObj.append("date_of_birth", formData?.date_of_birth);
      // formDataObj.append("City", formData?.City);
      // formDataObj.append("Country", formData?.Country);

      const stateName = formData?.State?.name
        ? formData?.State?.name
        : formData?.State;
      const cityName = formData?.City?.name
        ? formData?.City?.name
        : formData?.City;
      formDataObj.append("City", cityName + ", " + stateName);

      formDataObj.append(
        "Country",
        formData?.Country?.name ? formData?.Country?.name : formData?.Country
      );

      if (passwords.currentPassword && passwords.newPassword) {
        formDataObj.append("current_password", passwords.currentPassword);
        formDataObj.append("new_password", passwords.newPassword);
      }

      if (formData?.admin_other_details?.ProfilePhoto) {
        formDataObj.append(
          "ProfilePhoto",
          formData?.admin_other_details?.ProfilePhoto
        );
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_USER_DATA}${userEmail}/?user_id=${admin_id}`,
        formDataObj
      );
      if (response?.data === '{"user_update_status": true}') {
        toast.success("Changes saved successfully", {
          autoClose: 5000,
          theme: "colored",
          position: "top-center",
        });
        setPasswords(initialPasswordData);
        getUserData();
        setEditMode(false);
      }
    } catch (error) {
      console.error("Error updating data:", error);
      toast.error(error?.response?.data || "Failed updating data...", {
        autoClose: 5000,
        theme: "colored",
        position: "top-center",
      });
    }
  };
  const handleCancelChanges = () => {
    setFormData(originalFormData);
    setEditMode(false);
  };

  const handleChangeClick = () => {
    handlePhoneUpdate({
      tempPhone,
      tempCountryCode,
      setTempPhone,
      setTempCountryCode,
      axiosAuth,
      setFormData,
      userEmail,
    });
  };

  return (
    <div class="overflow-hidden">
      <div className="main-contanier admin-content-scroll overflow-auto">
        <div className="row">
          <div
            onClick={() => {
              if (typeof window !== "undefined") {
                window.history.back();
              }
            }}
            className="profile-setting-heading"
          >
            <IoArrowBack /> Profile Settings
          </div>
          {/* --------------------------------------Profile settings 1----------------------*/}
          <div className="row ">
            <div className="col-sm-6">
              <div className="row">
                <div className="col-sm-6">
                  <div className="profile-pic-wrapper">
                    <div className="pic-holder border-1 border-black">
                      <Image
                        id="profilePic"
                        className="pic rounded-circle object-fit-contain"
                        src={
                          typeof formData?.admin_other_details?.ProfilePhoto ===
                          "string"
                            ? formData?.admin_other_details?.ProfilePhoto
                            : formData?.admin_other_details
                                ?.ProfilePhoto instanceof File
                            ? URL.createObjectURL(
                                formData?.admin_other_details?.ProfilePhoto
                              )
                            : ""
                        }
                        width={150}
                        height={150}
                        alt="Profile"
                      />

                      {editMode && (
                        <>
                          <input
                            className="uploadProfileInput"
                            type="file"
                            name="ProfilePhoto"
                            id="newProfilePhoto"
                            accept="image/jpeg, image/png,  image/heif, image/heic"
                            onChange={handleInputChange}
                            style={{ display: "none" }}
                          />

                          <label
                            htmlFor="newProfilePhoto"
                            className="upload-file-block"
                          >
                            <div className="text-center">
                              <div className="mb-2">
                                <AiFillCamera />
                              </div>
                              <div className="text-uppercase" />
                            </div>
                          </label>
                        </>
                      )}
                    </div>
                    <span className="mx-2 fs-6"></span>
                  </div>
                  {editMode && errors.profilePic && (
                    <span className=" text-danger error-msg-medicalrec">
                      {errors.profilePic}
                    </span>
                  )}
                </div>
                <div className="col-sm-6 d-flex justify-content-center align-items-center">
                  <div className="d-flex justify-content-end align-items-center mt-2">
                    {!editMode ? (
                      <button
                        className="btn mx-2 profile-d-btn"
                        type="button"
                        onClick={handleEditButtonClick}
                      >
                        Edit Form
                      </button>
                    ) : (
                      <>
                        <button
                          className="btn mx-2 profile-d-btn"
                          type="button"
                          onClick={handleCancelChanges}
                        >
                          Cancel
                        </button>
                        <button
                          className="btn mx-2 profile-sub-btn"
                          type="button"
                          onClick={handleSaveChangesButtonClick}
                        >
                          Save Changes
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              {/* -----------------------------------Profile settings 2-------------------------*/}

              <p className="p-s-subheading mb-0">Personal Details</p>
              <form>
                <div className="form-group form-back overflow-hidden">
                  <div className="form-p-style overflow-auto admin-custom-overflow p-3">
                    <div className="row">
                      <div className="col-sm-6">
                        <label htmlFor="name" className="purple-text mb-2">
                          Name
                          {/*  <span style={{ color: "red" }}> *</span> */}
                        </label>
                        <input
                          type="text"
                          className="form-control custom-input-control mb-2"
                          id="name"
                          name="name"
                          readOnly={!editMode}
                          value={capitalizeFullName(formData?.name)}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="col-sm-6">
                        <label htmlFor="name" className="purple-text mb-2">
                          DOB
                        </label>
                        <input
                          type="date"
                          className="form-control custom-input-control mb-2"
                          id="date_of_birth"
                          name="date_of_birth"
                          readOnly={!editMode}
                          value={formData?.date_of_birth}
                          onChange={handleInputChange}
                        />
                        {editMode && errors.date_of_birth && (
                          <span className="text-danger error-msg-medicalrec">
                            {errors.date_of_birth}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-sm-6">
                        <div className="col-sm-12">
                          <label htmlFor="name" className="purple-text mb-2">
                            Phone Number
                          </label>
                          <div className="d-flex justify-center align-items-center position-relative">
                            <PhoneInput
                              id="floatingInputPhone"
                              className="input-form-modal-phone mb-3"
                              country={"in"}
                              // required="required"
                              name="phone"
                              // readOnly={!editMode}
                              disabled
                              value={`${formData?.country_code}${formData?.phone}`}
                              onChange={handlePhoneChange}
                              inputStyle={{
                                height: "30px",
                                boxShadow: "0px 3px 6px #00000029",
                                border: "1px solid #e3e3e3",
                                opacity: 1,
                                fontSize: "12px",
                              }}
                              buttonStyle={{
                                border: "none",
                                borderRight: 0,
                                background: "#F1F1F1",
                              }}
                            />
                            {editMode && (
                              <span
                                className="position-absolute input-group-text phone-number-change-button label-col mb-3 right-0 "
                                onClick={handleChangeClick}
                              >
                                Change
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className=" col-sm-6">
                        <label htmlFor="name" className="purple-text mb-2">
                          Email
                        </label>

                        <div className="input-group mb-3">
                          <input
                            type="text"
                            id="email"
                            name="email"
                            readOnly={true}
                            disabled={true}
                            className="form-control custom-input-control"
                            aria-describedby="button-addon2"
                            value={formData?.email}
                          />
                        </div>
                        {editMode && errors.email && (
                          <span className="text-danger error-msg-medicalrec">
                            {errors.email}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="row">
                      <label htmlFor="name" className="purple-text mb-2">
                        Location
                      </label>
                      <div className="col-sm-12">
                        <div className="input-group input-group-border mb-2">
                          <span
                            className="input-group-text label-col"
                            style={{
                              fontSize: "12px",
                              boxShadow: "0px 3px 6px #00000029",
                              height: "30px",
                            }}
                          >
                            {" "}
                            Country
                          </span>

                          <input
                            type="text"
                            className="form-control custom-inpu-p"
                            id="Country"
                            name="Country"
                            readOnly={!editMode}
                            disabled={editMode}
                            value={
                              formData?.Country?.name
                                ? formData?.Country?.name
                                : formData?.Country
                            }
                            onChange={(e) =>
                              setFormData({
                                ...formData,
                                Country: e.target.value,
                                State: "No states",
                                City: "No cities",
                              })
                            }
                          />

                          {editMode && (
                            <Select
                              id="Country"
                              options={Country.getAllCountries()}
                              styles={customStyles}
                              className="w-50"
                              getOptionLabel={(options) => {
                                return options["name"];
                              }}
                              getOptionValue={(options) => {
                                return options["name"];
                              }}
                              value={formData?.Country}
                              onChange={(item) => {
                                const selectedCountry = item;
                                setFormData({
                                  ...formData,
                                  Country: selectedCountry,
                                  State: "No states",
                                  City: "No cities",
                                });
                              }}
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-sm-6">
                        <div className="input-group input-group-border mb-2">
                          <span
                            className="input-group-text label-col"
                            style={{
                              fontSize: "12px",
                              boxShadow: "0px 3px 6px #00000029",
                              height: "30px",
                            }}
                          >
                            {" "}
                            State
                          </span>

                          <input
                            type="text"
                            className="form-control custom-inpu-p"
                            id="state"
                            name="Country"
                            readOnly={!editMode}
                            disabled={editMode}
                            value={
                              formData?.State?.name
                                ? formData?.State?.name
                                : formData?.State
                            }
                            onChange={(e) =>
                              setFormData({
                                ...formData,
                                State: e.target.value,
                              })
                            }
                          />

                          {editMode && (
                            <Select
                              id="state"
                              styles={customStyles}
                              options={State?.getStatesOfCountry(
                                formData?.Country?.isoCode
                              )}
                              getOptionLabel={(options) => {
                                return options["name"];
                              }}
                              getOptionValue={(options) => {
                                return options["name"];
                              }}
                              value={formData?.State}
                              onChange={(item) => {
                                const selectedState = item;
                                setFormData({
                                  ...formData,
                                  State: selectedState,
                                });
                              }}
                            />
                          )}
                        </div>
                      </div>
                      <div className="col-sm-6">
                        <div className="input-group input-group-border mb-1">
                          <span
                            className="input-group-text label-col"
                            style={{
                              fontSize: "12px",
                              boxShadow: "0px 3px 6px #00000029",
                              height: "30px",
                            }}
                          >
                            {" "}
                            City
                          </span>

                          <input
                            type="text"
                            className="form-control custom-inpu-p"
                            id="city"
                            name="Country"
                            readOnly={!editMode}
                            disabled={editMode}
                            value={
                              formData?.City?.name
                                ? formData?.City?.name
                                : formData?.City
                            }
                            onChange={(e) =>
                              setFormData({
                                ...formData,
                                City: e.target.value,
                              })
                            }
                          />

                          {editMode && (
                            <Select
                              styles={customStyles}
                              options={City.getCitiesOfState(
                                formData?.State?.countryCode,
                                formData?.State?.isoCode
                              )}
                              getOptionLabel={(options) => {
                                return options["name"];
                              }}
                              getOptionValue={(options) => {
                                return options["name"];
                              }}
                              value={formData?.City}
                              onChange={(item) => {
                                const selectedCity = item;
                                setFormData({
                                  ...formData,
                                  City: selectedCity,
                                });
                              }}
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* <div className="row">
                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      City
                    </label>
                    <input
                      type="text"
                      className="form-control custom-input-control mb-2"
                      id="City"
                      name="City"
                      readOnly={!editMode}
                      value={capitalizeFullName(formData?.City)}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="col-sm-6">
                    <label htmlFor="name" className="purple-text mb-2">
                      Country
                    </label>
                    <input
                      type="text"
                      className="form-control custom-input-control mb-2"
                      id="Country"
                      name="Country"
                      readOnly={!editMode}
                      value={capitalizeFullName(formData?.Country)}
                      onChange={handleInputChange}
                    />
                  </div>
                </div> */}

                    {/* email verifivcation */}
                    <div className="row">
                      <div className="col-sm-6">
                        <label htmlFor="sex" className="purple-text mb-2">
                          Gender
                        </label>
                        <select
                          className={`form-select custom-input-control ${
                            editMode && errors.sex ? "is-invalid" : ""
                          }`}
                          id="sex"
                          name="sex"
                          aria-label="Gender"
                          disabled={!editMode}
                          value={formData?.sex || ""}
                          onChange={(e) =>
                            handleInputChange({
                              target: {
                                name: "sex",
                                value: e.target.value,
                              },
                            })
                          }
                        >
                          <option value="" disabled>
                            Select Gender
                          </option>
                          {genderOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        {editMode && errors.sex && (
                          <div className="invalid-feedback">{errors.sex}</div>
                        )}
                      </div>
                      <div className="col-sm-6">
                        <label htmlFor="timezone" className="purple-text mb-2">
                          Timezone
                        </label>
                        <select
                          className={`form-select custom-input-control mb-2 ${
                            editMode && errors.timezone ? "is-invalid" : ""
                          }`}
                          aria-label="Timezone"
                          id="timezone"
                          name="TimeZone"
                          disabled={!editMode}
                          value={formData?.TimeZone || ""}
                          onChange={(e) =>
                            handleInputChange({
                              target: {
                                name: "TimeZone",
                                value: e.target.value,
                              },
                            })
                          }
                        >
                          <option value="" disabled>
                            Select Timezone
                          </option>
                          {timeZoneOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        {editMode && errors.timezone && (
                          <div className="invalid-feedback">
                            {errors.timezone}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className=" mb-2">
                      <label htmlFor="name" className="purple-text mb-2">
                        Address
                        {/* <span style={{ color: 'red' }}> *</span> */}
                      </label>
                      <textarea
                        className="form-control profile-address"
                        id="address"
                        name="Address"
                        readOnly={!editMode}
                        rows={1}
                        value={formData?.admin_other_details?.Address}
                        onChange={handleInputChange}
                      />

                      {editMode && errors.address && (
                        <span className="text-danger error-msg-medicalrec">
                          {errors.address}
                        </span>
                      )}
                    </div>

                    <PasswordReset
                      role={"admin"}
                      setErrors={setErrors}
                      errors={errors}
                      setPasswords={setPasswords}
                      passwords={passwords}
                      editMode={editMode}
                    />
                  </div>
                </div>
                {/* -----------------------------------Change Password-------------------------*/}
                {/* <div className="form-group form-back">
              <div className="row">
                <div className="col-sm-12">
                  <label htmlFor="name" className="purple-text mb-2">
                    Current Password<span style={{ color: "red" }}> *</span>
                  </label>
                  <input
                    type="text"
                    className="form-control custom-input-control mb-2"
                    id="pwd"
                    name="password"
                    readOnly
                    //   value={editMode ? formData?.name : userData?.name || ""}
                    //   onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="row">
                <div className="col-sm-12">
                  <label htmlFor="name" className="purple-text mb-2">
                    New Password<span style={{ color: "red" }}> *</span>
                  </label>
                  <input
                    type="text"
                    className="form-control custom-input-control mb-3"
                    id="pwd2"
                    name="password"
                    readOnly
                    //   value={editMode ? formData?.name : userData?.name || ""}
                    //   onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="row">
                <div className="col-sm-12">
                  <label htmlFor="name" className="purple-text mb-2">
                    Re-enter New Password
                    <span style={{ color: "red" }}> *</span>
                  </label>
                  <input
                    type="text"
                    className="form-control custom-input-control mb-2"
                    id="pwd3"
                    name="password"
                    readOnly
                    //   value={editMode ? formData?.name : userData?.name || ""}
                    //   onChange={handleInputChange}
                  />
                </div>
              </div>
            </div>
            <div className="d-flex justify-content-between align-items-center">
              <Link href="/" className="forgot-pwd-link">
                Forgot Password ?
              </Link>
              <button className="profile-submit-btn mt-2" type="button">
                Submit
              </button>
            </div> */}
                {/* <div className="float-end">
              <button className="profile-submit-btn mt-2" type="button">
                Submit
              </button>
            </div> */}
              </form>
              {/* -----------------------------------Change Password-------------------------*/}
              {/* <PasswordReset
            role={"admin"}
            setErrors={setErrors}
            errors={errors}
            setPasswords={setPasswords}
            passwords={passwords}
            editMode={editMode}
          /> */}
            </div>

            <NotificationPermission
              notificationsList={notificationsList}
              fetchNotificationList={fetchNotificationList}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettingComp;
