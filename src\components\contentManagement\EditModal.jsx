import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form } from "react-bootstrap";
import { toast } from "react-toastify";

const EditModal = ({
  show,
  handleClose,
  initialData,
  axiosAuth,
  user_id,
  dataFetch,
  type,
  handleCreateUpdateCommonTopic,
  commonTopic,
}) => {
  const [formData, setFormData] = useState(initialData);

  useEffect(() => {
    setFormData(initialData);
  }, [initialData]);
  //   console.log("formdata", formData);
  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    try {
      //   setloading(true);
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_GET_CREATE_REPORT_TYPE}${formData.id}/?user_id=${user_id}`,
        {
          type: formData.type,
        }
      );
      //   setloading(false);
      if (response?.data) {
        toast.success(`Report edited Successfully`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        dataFetch();
      }
      setFormData(" ");
    } catch (error) {
      console.log("Error in adding the report", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    }
    handleClose();
  };


  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          Edit {commonTopic ? "Common Topic" : " Report"}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form.Group controlId="formReportType">
          <Form.Label>
            {commonTopic ? "Common Topic" : "Report Type"}
          </Form.Label>
          <Form.Control
            type="text"
            name={commonTopic ? "common_topics" : "type"}
            value={commonTopic ? formData.common_topics : formData.type}
            onChange={handleChange}
            required={true}
          />
        </Form.Group>
        {commonTopic ? (
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            // onClick={() => handleEditSubmit()}
            onClick={() =>
              handleCreateUpdateCommonTopic(
                "put",
                initialData.id,
                formData.common_topics
              )
            }
          >
            Save Changes
          </Button>
        ) : (
          <Button
            type="submit"
            className="mt-3 edit-update-button"
            // onClick={() => handleEditSubmit()}
            onClick={() => handleSubmit()}
          >
            Save Changes
          </Button>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default EditModal;
