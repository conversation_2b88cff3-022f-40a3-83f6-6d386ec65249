import React, { useCallback, useEffect, useMemo, useState } from "react";
import AllBlogsFilter from "../../BlogsContent/AllBlogsFilter";
import BlogsMainList from "../../BlogsContent/BlogsMainList";
import useAxiosAuth from "../../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import _ from "lodash";
import { formatDateToYMD } from "../../../../utils/helperfunction";
import axios from "../../../../lib/axios";
import Cookies from "js-cookie";
import { Accordion } from "react-bootstrap";
import { IoIosInformationCircle } from "react-icons/io";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import SelectedSimilarBlogs from "./SelectedSimilarBlogs";
import AllBlogsList from "./AllBlogsList";

const SimilarBlogTopics = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [loading, setLoading] = useState(true);
  const [current_page, setCurrent_Page] = useState(1);
  const [blogs, setBlogs] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);

  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const axiosAuth = useAxiosAuth();

  const fetchAllBlogs = useCallback(
    async (query) => {
      try {
        setLoading(true);
        if (current_page === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_All_PUBLIC_BLOGS}all/?page=${current_page}`;
        if (query) {
          url += `&search=${query}`;
        } else if (startDate && endDate) {
          const startFormatted = formatDateToYMD(startDate);
          const endFormatted = formatDateToYMD(endDate);
          url += `&start_date=${startFormatted}&end_date=${endFormatted}`;
        }
        const response = await axios.get(url);
        setBlogs(response?.data);
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      } catch (error) {
        console.error("Error fetching Blogs: ", error);
      } finally {
        setLoading(false);
        if (current_page === 1) setInitialLoading(false);
      }
    },
    [current_page, startDate, endDate]
  );

  const debouncedFetchData = useMemo(
    () => {
      return _.debounce((query) => {
        fetchAllBlogs(query);
      }, 500);
    },
    [fetchAllBlogs]
  );

  useEffect(() => {
    debouncedFetchData(searchQuery);
  }, [searchQuery, debouncedFetchData]);

  return (
    <>
      <div className="row">
        <AllBlogsFilter
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          setSearchQuery={setSearchQuery}
          searchQuery={searchQuery}
          loading={loading}
        />
      </div>
      <AllBlogsList
        blogs={blogs}
        loading={loading}
        initialLoading={initialLoading}
        current_page={current_page}
        setCurrent_Page={setCurrent_Page}
        fetchAllBlogs={fetchAllBlogs}
        // fetchFeaturedBlogs={fetchFeaturedBlogs}
        // fetchTopBlogs={fetchTopBlogs}
        // fetchTrendingBlogs={fetchTrendingBlogs}
        searchQuery={searchQuery}
      />
      <Accordion
      //   defaultActiveKey="0"
      >
        <Accordion.Item
          className=" accordion-container p-2 rounded"
          eventKey="0"
        >
          <Accordion.Header className=" accordion-header-custom">
            <div className=" d-flex w-100 align-items-center">
              <div className=" d-flex ">
                <p className="c mb-0">Similar Blogs </p>
                <span className=" selected-blogs-number ms-1">
                   10
                </span>
              </div>
              <span className=" ms-auto me-5">
                <a
                  data-tooltip-id="my-tooltip"
                  data-tooltip-content="You can only select 5 Similar blogs"
                >
                  <IoIosInformationCircle />
                </a>
                <Tooltip id="my-tooltip" />
              </span>
            </div>
          </Accordion.Header>
          <Accordion.Body className="faq-answer-section">
            <SelectedSimilarBlogs
            // fetchFeaturedBlogs={fetchFeaturedBlogs}
            // featuredLoading={featuredLoading}
            // fetchAllBlogs={fetchAllBlogs}
            // featuredBlogs={featuredBlogs}
            // handleDeleteRanking={handleDeleteRanking}
            />
          </Accordion.Body>
        </Accordion.Item>
      </Accordion>
    </>
  );
};

export default SimilarBlogTopics;
