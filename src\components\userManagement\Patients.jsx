"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import profile from "../../../public/images/profile.png";
import { BiSolidDownArrow } from "react-icons/bi";
import { MdModeEditOutline } from "react-icons/md";
import { MdDelete } from "react-icons/md";
import { BsFastForwardFill } from "react-icons/bs";
import { TbPlayerTrackPrevFilled } from "react-icons/tb";
import { BsFillCircleFill } from "react-icons/bs";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { Doughnut } from "react-chartjs-2";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import { BiSolidUpArrow } from "react-icons/bi";
import { toPadding } from "chart.js/helpers";

ChartJS.register(Arc<PERSON>lement, <PERSON><PERSON><PERSON>, Legend);
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const PatientsComp = () => {
  const chartData = {
    datasets: [
      {
        label: "# of Votes",
        data: [60, 20, 20, 10],
        backgroundColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
        ],
        borderColor: [
          "rgba(4, 171, 32, 1)",
          "rgba(129, 7, 209, 1)",
          "rgba(255, 151, 26, 1)",
          "rgba(255, 46, 46, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };
  return (
    <div className="payment-back">
      <div className="row">
        <div className="col-sm-5">
          <p className="heading">Patient Data by Expert</p>
          <div className="row">
            <div className="col-sm-3">
              <div className="custom-border">
                <p className="text-center sub-heading mt-1 mb-1">Doctors</p>
                <p className="text-center purple-num mb-2">200</p>
                <div className="bottom-border"></div>
                <p className="text-center green-num mb-1 mt-3">
                  5%
                  <BiSolidUpArrow />
                </p>
              </div>
            </div>
            <div className="col-sm-3">
              <div className="custom-border">
                <p className="text-center sub-heading mt-1 mb-1">Researchers</p>
                <p className="text-center purple-num mb-2">250</p>
                <div className="bottom-border"></div>
                <p className="text-center red-num mb-1 mt-3">
                  12%
                  <BiSolidDownArrow />
                </p>
              </div>
            </div>
            <div className="col-sm-3">
              <div className="custom-border">
                <p className="text-center sub-heading mt-1 mb-1">Influencers</p>
                <p className="text-center purple-num mb-2">50</p>
                <div className="bottom-border"></div>
                <p className="text-center green-num mb-1 mt-3">
                  5%
                  <BiSolidUpArrow />
                </p>
              </div>
            </div>
            <div className="col-sm-3">
              <div className="custom-border">
                <p className="text-center sub-heading mt-1 mb-1">Influencers</p>
                <p className="text-center purple-num mb-2">50</p>
                <div className="bottom-border"></div>
                <p className="text-center green-num mb-1 mt-3">
                  5%
                  <BiSolidUpArrow />
                </p>
              </div>
            </div>
          </div>
          <div className="row mt-4">
            <p className="heading mb-1">Total Appointment Request Rate</p>
            <div className="col-sm-4">
              <div className="pink-bg chartpadding">
                <Doughnut data={chartData} />
              </div>
            </div>
            <div className="col-sm-8">
              <div className="custom-border pink-bg">
                <div className="row">
                  <p className="text-center sub-heading mb-3 mt-3">
                    Appointment Requests
                  </p>
                </div>
                <div className="bg-white p-2 mb-1">
                  <div className="row">
                    <div className="col-sm-6">
                      <p className="text-center mb-2 fw-light mt-3">
                        Upcoming consultation
                      </p>
                      <p className="text-center purple-count mt-3">20</p>
                    </div>
                    <div className="col-sm-6">
                      <p className="text-center mb-2 fw-light mt-3">
                        Cancelled cansultation
                      </p>
                      <p className="text-center orange-count mt-3">20</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* *********************************************Total Expert Appointments********************************************** */}
        <div className="col-sm-7">
          <div className="row">
            <div className="col-sm-12">
              <div className="grey-bg">
                <div className="row ">
                  <div className="col-sm-3">
                    <p className="heading mb-0">Filters</p>
                  </div>
                  <div className="col-sm-3">
                    <div className="input-group mb-0 custom-form-select">
                      <input
                        type="date"
                        className="form-control"
                        aria-label="Sizing example input"
                        aria-describedby="inputGroup-sizing-sm"
                      />
                    </div>
                  </div>
                  <div className="col-sm-3">
                    <select
                      className="form-select  custom-form-select"
                      aria-label=".form-select-sm example"
                    >
                      <option selected>By Expert</option>
                      <option value="1">Doctor</option>
                      <option value="2">Researcher</option>
                      <option value="3">Influencer</option>
                    </select>
                  </div>
                  <div className="col-sm-3">
                    <select
                      className="form-select custom-form-select"
                      aria-label=".form-select-sm example"
                    >
                      <option selected>By Status </option>
                      <option value="1">One</option>
                      <option value="2">Two</option>
                      <option value="3">Three</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <table className="table mt-2">
            <thead className="custom-border">
              <tr className="custom-name">
                <th scope="col" className="fw-light text-center">
                  Patient Name
                </th>
                <th scope="col" className="fw-light text-center">
                  Assigned Expert
                  <BiSolidDownArrow
                    style={{ fontSize: "11px", color: "#04AB20" }}
                  />
                </th>
                <th scope="col" className="fw-light text-center">
                  Date of Joining
                  <BiSolidDownArrow
                    style={{ fontSize: "11px", color: "#04AB20" }}
                  />
                </th>
                <th scope="col" className="fw-light text-center">
                  Expert Name
                </th>
                <th scope="col" className="fw-light text-center">
                  Status
                </th>
                <th scope="col" className="fw-light text-center">
                  Edit
                </th>
                <th scope="col" className="fw-light text-center">
                  Delete
                </th>
              </tr>
            </thead>
            <tbody className="custom-border">
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center green-text">Active</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  green-text">Active</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  grey-text">Removed</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  red-text">Deleted</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  green-text">Active</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  orange-text">Restricted</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  green-text">Active</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
              <tr className="custom-row">
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Katy Rollins
                </td>
                <td className="custom-font text-center">Doctor</td>
                <td className="fw-bold text-center">24/05/2023</td>
                <td className="text-center purple-content">
                  <Image src={profile} alt="" />
                  Georgia Grant
                </td>
                <td className="text-center  green-text">Active</td>
                <td className="text-center">
                  <MdModeEditOutline
                    style={{ color: "#8107D1", fontSize: "20px" }}
                  />
                </td>
                <td className="text-center">
                  <MdDelete style={{ color: "#FF2E2E", fontSize: "20px" }} />
                </td>
              </tr>
            </tbody>
          </table>
          <nav aria-label="Page navigation example">
            <ul className="pagination justify-content-center custom-pagination">
              <li className="page-item">
                <a
                  className="page-link custom-page-link"
                  href="#"
                  tabIndex="-1"
                  aria-disabled="true"
                >
                  <span className="page-count">1</span>
                  <TbPlayerTrackPrevFilled style={{ color: "#96969C" }} />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFillCircleFill
                    style={{ color: "#96969C", fontSize: "10px" }}
                  />
                </a>
              </li>
              <li className="page-item">
                <a className="page-link custom-page-link" href="#">
                  <BsFastForwardFill style={{ color: "#96969C" }} />
                  <span className="page-count">10</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default PatientsComp;
