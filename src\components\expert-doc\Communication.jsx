import React, { useCallback, useEffect, useRef, useState } from "react";
import { IoMdRefresh } from "react-icons/io";
import { useParams } from "next/navigation";
import Loading from "../Loading/PageLoading/Loading";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { GrSearch } from "react-icons/gr";
import { convert_time } from "../../utils/helperfunction";
import { useSession } from "next-auth/react";
import SingleTicket from "./SingleTicket";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import CustomPagination from "../CustomPagination/CustomPagination";
import { Placeholder } from "react-bootstrap";
const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};
const queryTypes = [
  {
    label: "All Queries",
    status: "all",
    color: "queries-no-color-1",
    value: "total_tickets",
  },
  {
    label: "Open",
    status: "Open",
    color: "queries-no-color-3",
    value: "total_open_ticket",
  },
  {
    label: "Closed",
    status: "Closed",
    color: "queries-no-color-5",
    value: "total_closed_ticket",
  },
  {
    label: "On Hold",
    status: "On Hold",
    color: "queries-no-color-4",
    value: "total_on_hold_ticket",
  },
  {
    label: "Escalated",
    status: "Escalated",
    color: "queries-no-color-2",
    value: "total_escalated_ticket",
  },
];

const Communication = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [ticketsData, setTicketsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTicketDetails, setSelectedTicketDetails] = useState(null);
   const [showSingleMessage, setShowSingleMessage] = useState(false);
  const [originalTicketsData, setOriginalTicketsData] = useState(null);
  const [totalTicketsStatusCount, setTotalTicketsStatusCount] = useState({
    total_tickets: 0,
    total_open_ticket: 0,
    total_closed_ticket: 0,
    total_on_hold_ticket: 0,
    total_escalated_ticket: 0,
  });
  const [contentLoading, setContentLoading] = useState(true);
    const [selectedTicketData, setSelectedTicketData] = useState(null);
  const axiosAuth = useAxiosAuth();
  const params = useParams();

  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const fetchedDataRef = useRef({});

  const doctor_email =
    params && params?.user_id?.length > 0 && params?.user_id[1]
      ? decodeURIComponent(params?.user_id[1])
      : "";

  const fetchTickets = useCallback(async () => {
    try {
      setContentLoading(true);
      const url = `${process.env.NEXT_PUBLIC_FETCH_PATIENT_TICKETS}${doctor_email}/${selectedStatus}/?page=${current_page}&per_page=10`;

      // Check if data for this URL is already fetched
      if (fetchedDataRef.current[url]) {
        const cachedData = fetchedDataRef.current[url];
        const total_tickets = cachedData?.ticket_details?.slice(0, -1);
        setTotalPages(cachedData?.total_pages);
        setTotalTicketsStatusCount(
          cachedData?.ticket_details[cachedData?.ticket_details?.length - 1]
        );
        setTicketsData(total_tickets);
        setOriginalTicketsData(total_tickets);
      } else {
        const ticketResponse = await axiosAuth.get(url);
        fetchedDataRef.current[url] = ticketResponse.data; // Store fetched data in useRef
        let total_tickets = ticketResponse?.data?.ticket_details?.slice(0, -1);

        setTotalPages(ticketResponse?.data?.total_pages);
        setTotalTicketsStatusCount(
          ticketResponse?.data?.ticket_details[
            ticketResponse?.data?.ticket_details?.length - 1
          ]
        );
        setTicketsData(total_tickets);
        setOriginalTicketsData(total_tickets);
      }
    } catch (error) {
      console.error("Error fetching patient tickets:", error);
    } finally {
      setContentLoading(false);
      setLoading(false);
    }
  }, [doctor_email, selectedStatus, axiosAuth, current_page]);

  useEffect(() => {
    fetchTickets();
  }, [fetchTickets]);

  const handleTicketClick = (tickets) => {
    setSelectedTicketData(tickets);
    setShowSingleMessage(true);
  };
  const handleShowAllTickets = () => {
    setShowSingleMessage(false);
    setSelectedTicketData(null);
    setCurrent_Page(1);
  };

  // Filter tickets based on searchQuery
  useEffect(() => {
    if (searchQuery.trim() !== "") {
      const filteredTickets =
        originalTicketsData &&
        Array.isArray(originalTicketsData) &&
        originalTicketsData?.filter((ticketGroup) =>
          ticketGroup[0]?.ticket_details?.id
            .toLowerCase()
            .includes(searchQuery.trim().toLowerCase())
        );
      setTicketsData(filteredTickets);
    } else {
      setTicketsData(originalTicketsData);
    }
  }, [searchQuery, originalTicketsData]);

  return (
    <>
      {loading === true ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="payment-back">
          <div className="row">
            <div className="col-sm-auto">
              <div className="queries-background">
                {queryTypes &&
                  Array.isArray(queryTypes) &&
                  queryTypes.map((query, index) => {
                    const totalCount =
                      totalTicketsStatusCount &&
                      (query.status === "all"
                        ? totalTicketsStatusCount?.total_tickets
                        : totalTicketsStatusCount[query.value]);

                    // Trim and replace any extra whitespace in the query.label
                    const formattedLabel = query.label
                      .replace(/\s+/g, " ")
                      .trim();

                    return (
                      <div
                        key={index}
                        className={`box-color mt-${
                          index === 0 ? 4 : 2
                        } ticket-status`}
                        onClick={() => {
                          setSelectedStatus("all");
                  handleShowAllTickets();
                        }}
                      >
                        {formattedLabel}
                        <span className={query.color}>{totalCount || 0}</span>
                      </div>
                    );
                  })}
              </div>
            </div>
            <div className="col-sm">
              <div className="col-sm">
              {selectedTicketData !== null && showSingleMessage ? (
                  <>
                    <SingleTicket
                      // selectedTicketDetails={selectedTicketDetails}
                      // setSelectedTicketDetails={setSelectedTicketDetails}
                      // title={selectedTicketDetails[0]?.ticket_details?.subject}
                      // summary={
                      //   selectedTicketDetails[1]?.thread_data[0]?.summary
                      // }
                      // time={
                      //   selectedTicketDetails[1]?.thread_data[0]?.CreatedTime
                      // }
                      // ticketId={selectedTicketDetails[0]?.ticket_details?.id}
                      // threadData={selectedTicketDetails[1]?.thread_data}
                      // ticketStatus={
                      //   selectedTicketDetails[0]?.ticket_details?.status
                      // }
                      // profilephoto={
                      //   selectedTicketDetails[0]?.ticket_details
                      //     ?.profilephoto || ""
                      // }
                      // author_photo={
                      //   selectedTicketDetails[1]?.thread_data[0]
                      //     ?.author_photo || ""
                      // }
                      // attachements_data={
                      //   selectedTicketDetails[2]?.attachements_data
                      // }
                      ticketId={selectedTicketData[0]?.ticket_details?.id}
                handleShowAllTickets={handleShowAllTickets}
                time={selectedTicketData[1]?.thread_data[0]?.CreatedTime}
                title={selectedTicketData[0]?.ticket_details?.subject}
                    />
                  </>
                ) : (
                  <>
                    <div className="col-sm-6 mt-3  offset-sm-6 ml-auto d-flex justify-content-around align-items-center">
                      <div className="input-group mb-3 search-input-patient">
                        <input
                          type="text"
                          style={{
                            borderRadius: "3px",
                            border: "none",
                            fontSize: "14px",
                          }}
                          className="form-control search-input-focus"
                          placeholder="Search for Ticket ID"
                          aria-label="Search for Ticket ID"
                          aria-describedby="button-addon2"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                        <span
                          className="input-group-text custom-search-icon"
                          id="button-addon2"
                          style={{ borderRadius: "5px" }}
                        >
                          <GrSearch />
                        </span>
                      </div>
                      &nbsp;&nbsp;
                      <div
                        className="d-flex justify-content-between align-items-center mb-3"
                        onClick={() => {
                          setCurrent_Page(1);
                          fetchTickets();
                        }}
                      >
                        <div className="custom_refresh">Refresh</div>
                        <div>
                          {" "}
                          <IoMdRefresh
                            className="rotating-refresh-icon"
                            // style={{ transform: `rotate(${rotation}deg)` }}
                          />
                        </div>
                      </div>
                    </div>
                    {ticketsData && ticketsData?.length === 0 && !loading ? (
                      <div className="fs-4 d-flex align-items-center justify-content-center no-Patient-tickets-found">
                        <PiFolderNotchOpenFill className="PiFolderNotchOpenFill_icon" />
                        &nbsp; No {selectedStatus} tickets Found
                      </div>
                    ) : (
                      <div className=" overflow-hidden">
                        <div className="communication-scroll overflow-auto">
                          {!contentLoading &&
                            ticketsData &&
                            Array.isArray(ticketsData) &&
                            ticketsData.map((ticketGroup, index) => {
                              const ticket = ticketGroup[0]?.ticket_details;
                              const threadData = ticketGroup[1]?.thread_data;
                              return (
                                <div
                                  key={index}
                                  className={`line-color`}
                                  onClick={() =>
                                    handleTicketClick(ticketGroup)
                                  }
                                >
                                  <span className="remove-btn-style">
                                    {index + 1}
                                  </span>
                                  <span className="urgent-query">
                                    <span className="ticket-heading">
                                      Ticket Id{" "}
                                    </span>
                                    &nbsp;- {ticket?.id}
                                  </span>
                                  <span className="line-of-query">
                                    {ticket?.subject}
                                  </span>
                                  <span className="query-time">
                                    {convert_time(ticket?.CreatedTime)}
                                  </span>
                                  <span className="query-status px-auto">
                                    <span
                                      className={`query-status-text-badge ${
                                        ticket?.status === "Open"
                                          ? "bg-success"
                                          : ticket?.status === "Closed"
                                          ? "bg-danger"
                                          : ticket?.status === "Escalated"
                                          ? "bg-secondary"
                                          : ticket?.status === "On Hold"
                                          ? "bg-warning"
                                          : ""
                                      }`}
                                    >
                                      {ticket?.status}
                                    </span>
                                  </span>
                                </div>
                              );
                            })}

                          {contentLoading && renderPlaceholders()}

                          <div className="d-flex justify-content-center align-items-center mt-2">
                            <div className="d-none d-xl-block">
                              {!contentLoading && totalPages > 1 && (
                                <CustomPagination
                                  total_pages={totalPages}
                                  current_page={current_page}
                                  setCurrent_Page={setCurrent_Page}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Communication;
