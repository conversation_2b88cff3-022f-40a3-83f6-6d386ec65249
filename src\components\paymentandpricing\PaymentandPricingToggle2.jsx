import React, { useEffect, useState } from "react";
import PaymentHistory from "./PaymentHistory";
import ExpertPaymentsTable from "./ExpertPaymentsTable";
import {
  useParams,
  useSearchParams,
  usePathname,
  useRouter,
} from "next/navigation";

const PaymentandPricingToggle2 = () => {
  const searchParams = useSearchParams();
  const initialTab = searchParams.get("tab");
  const [paymentTab, setPaymentTab] = useState(initialTab || "patientPayments");
  const [expertRole, setExpertRole] = useState("doctor"); // Default to "doctor"
  const pathname = usePathname();
  const router = useRouter();

  const handleSelection = (category) => {
    const processedCategory = category.toLowerCase().replace(/s$/, "");
    setExpertRole(processedCategory);
  };

  const handlePaymentTabChange = (tab) => {
    const updatedPath = `${pathname}?tab=${tab}`;
    router.replace(updatedPath, { shallow: true });
    setPaymentTab(tab);
  };

  useEffect(() => {
    const tab = searchParams.get("tab");
    setPaymentTab(tab || "patientPayments");
  }, [searchParams]);

  return (
    <div className="payment-back">
      <div className="row">
        <div className="d-inline-flex justify-content-between mt-3 gx-5">
          <div className="d-inline-flex gap-1 buttons-row mb-0">
            <button
              className={`grey-btn ${
                paymentTab === "patientPayments"
                  ? "activeExpertsPaymentTab"
                  : ""
              }`}
              onClick={() => handlePaymentTabChange("patientPayments")}
            >
              Patient Payments
            </button>
            <button
              className={`grey-btn ${
                paymentTab === "expertPayments" ? "activeExpertsPaymentTab" : ""
              }`}
              onClick={() => handlePaymentTabChange("expertPayments")}
            >
              Expert Payments
            </button>
          </div>
          {paymentTab === "expertPayments" && (
            <div className="filter-container">
              <span className="sort-by-label">Sort By</span>
              <div className="buttons-container">
                {["Doctors", "Researchers", "Health Guides"].map((category) => (
                  <button
                    key={category}
                    className={`filter-button ${
                      expertRole === category.toLowerCase().replace(/s$/, "")
                        ? "active"
                        : ""
                    }`}
                    onClick={() => handleSelection(category)}
                  >
                    <input
                      type="checkbox"
                      checked={
                        expertRole === category.toLowerCase().replace(/s$/, "")
                      }
                      readOnly
                    />
                    <span>{category}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        <div className="gx-5">
          <div className="line-below-buttons "></div>

          {paymentTab === "patientPayments" && <PaymentHistory />}
          {paymentTab === "expertPayments" && (
            <ExpertPaymentsTable expertRole={expertRole} />
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentandPricingToggle2;
