import React, { useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../../public/images/dummy-avatar.jpg";
import { highlightText } from "../../../../utils/helperfunction";
import noDataFound from "../../../../../public/assets/noDataFound.png";
import useAxiosAuth from "../../../../lib/hooks/useAxiosAuth";
import { Placeholder } from "react-bootstrap";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import RankingModal from "../../BlogsContent/RankingModal";
import ViewBlogsContentModal from "../../BlogsContent/ViewBlogsContentModal";
import CustomPagination from "../../../CustomPagination/CustomPagination";
import NoDataFound from "../../../noDataFound/NoDataFound";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 7 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "S No",
  "Select Blog",
  "Expert Name",
  "Date of Upload",
  "Blog Title",
  "Expert Role",
  "Blog Views",
  "Blog Page",
];
// const blogs = {
//   items: [
//     {
//       blog_details: {
//         id: 1,
//         BlogSectionName: "similar",
//         BlogRanking: 1,
//         BlogTitle: "Understanding Heart Disease",
//         BlogDateTime: "2024-09-01T12:00:00Z",
//         BlogViews: 500,
//         expert_details: {
//           name: "Dr. John Doe",
//           role: "Cardiologist",
//           doctor_other_details: {
//             ProfilePhoto: "/images/doctor-john.jpg",
//           },
//         },
//       },
//     },
//     {
//       blog_details: {
//         id: 2,
//         BlogSectionName: "featured",
//         BlogRanking: 2,
//         BlogTitle: "The Importance of Mental Health",
//         BlogDateTime: "2024-08-25T09:30:00Z",
//         BlogViews: 350,
//         expert_details: {
//           name: "Dr. Jane Smith",
//           role: "Psychiatrist",
//           doctor_other_details: {
//             ProfilePhoto: "/images/doctor-jane.jpg",
//           },
//         },
//       },
//     },
//     {
//       blog_details: {
//         id: 3,
//         BlogSectionName: "similar",
//         BlogRanking: 3,
//         BlogTitle: "Managing Diabetes Effectively",
//         BlogDateTime: "2024-08-20T14:15:00Z",
//         BlogViews: 420,
//         expert_details: {
//           name: "Dr. Emily Johnson",
//           role: "Endocrinologist",
//           doctor_other_details: {
//             ProfilePhoto: "/images/doctor-emily.jpg",
//           },
//         },
//       },
//     },
//     {
//       blog_details: {
//         id: 4,
//         BlogSectionName: "trending",
//         BlogRanking: 4,
//         BlogTitle: "Advancements in Cancer Treatment",
//         BlogDateTime: "2024-08-18T11:00:00Z",
//         BlogViews: 600,
//         expert_details: {
//           name: "Dr. Michael Lee",
//           role: "Oncologist",
//           doctor_other_details: {
//             ProfilePhoto: "/images/doctor-michael.jpg",
//           },
//         },
//       },
//     },
//     {
//       blog_details: {
//         id: 5,
//         BlogSectionName: "similar",
//         BlogRanking: 5,
//         BlogTitle: "Healthy Eating for a Better Life",
//         BlogDateTime: "2024-08-15T08:45:00Z",
//         BlogViews: 280,
//         expert_details: {
//           name: "Dr. Sarah Brown",
//           role: "Nutritionist",
//           doctor_other_details: {
//             ProfilePhoto: "/images/doctor-sarah.jpg",
//           },
//         },
//       },
//     },
//   ],
//   total_pages: 1,
// };

const AllBlogsList = ({
  loading,
  initialLoading,
  current_page,
  setCurrent_Page,
  blogs,
  fetchAllBlogs,
  searchQuery,
}) => {
  const [rankingLoading, setRankingLoading] = useState(false);
  const [ranking, setRanking] = useState(1);
  const [section, setSection] = useState(" ");
  const [showModal, setShowModal] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState({});
  const [showBlogsModal, setShowBlogsModal] = useState(false);
  const [singleBlog, setSingleBlog] = useState({});

  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const handleAddRanking = async (id, isSimilar) => {
    if (!ranking || !section.trim()) {
      toast.error("Please select both ranking and section", {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      return;
    }
    const body = {
      ranking: Number(ranking),
      blog_section: section,
    };
    try {
      setRankingLoading(true);
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_RANK_FEATURED_BLOGS}${id}/?user_id=${userId}`,
        body
      );
      if (response?.data === "successfully ranked") {
        toast.success(`The blog has been ${response?.data}`, {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      } else if (response?.data === "Already have this ranking blog") {
        toast.error("Already have this ranking blog", {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      } else if (response?.data === "Please select a valid rank") {
        toast.error("You have exceeded the ranking limit for this section", {
          position: "top-center",
          theme: "colored",
          autoClose: 2500,
        });
      }
      setRanking(1);
      setSection("");
      setRankingLoading(false);
      setShowModal(false);
      fetchAllBlogs();
      {
        section === "Featured"
          ? fetchFeaturedBlogs()
          : section === "Top"
          ? fetchTopBlogs()
          : fetchTrendingBlogs();
      }
    } catch (err) {
      toast.error(`Already have this ranking blog`, {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
    }
  };

  const handleOpenRankingModal = (blog) => {
    setSelectedBlog(blog);
    setShowModal(true);
  };

  const handleViewBlogsModal = (item) => {
    setSingleBlog(item);
    setShowBlogsModal(true);
  };

  return (
    <>
      {showModal && (
        <RankingModal
          ranking={ranking}
          section={section}
          setSection={setSection}
          showModal={showModal}
          setShowModal={setShowModal}
          setRanking={setRanking}
          handleAddRanking={handleAddRanking}
          selectedBlog={selectedBlog}
          rankingLoading={rankingLoading}
          trending={true}
          isSimilar={true}
        />
      )}
      {showBlogsModal && (
        <ViewBlogsContentModal
          showBlogsModal={showBlogsModal}
          setShowBlogsModal={setShowBlogsModal}
          singleBlog={singleBlog}
        />
      )}
      <div className="bg-color">
        <div className="row">
          <div className="col-sm-12 fixTableHead">
            <table className="table mt-2 custon-table-featured">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      className={`fw-light text-center child-admin-list-heading ${
                        index === 4 ? "col-4" : "col"
                      }`}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="custom-border">
                {initialLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {blogs &&
                    Array.isArray(blogs?.items) &&
                    blogs?.items?.length === 0 ? (
                      <tr>
                        <td colSpan="12" className="text-center">
                            <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      blogs &&
                      Array.isArray(blogs?.items) &&
                      blogs?.items?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row align-baseline table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {(current_page - 1) * 10 + index + 1}
                            </td>

                            <td className="  col custom-font text-center">
                              <div className=" d-flex align-items-center">
                                <input
                                  className=" ms-5 form-check-input  blogs-content-input"
                                  type="checkbox"
                                  name="category"
                                  id="expertCategory"
                                  value="Expert Rejection"
                                  checked={
                                    blog?.blog_details?.BlogSectionName ===
                                      "similar" &&
                                    blog?.blog_details?.BlogRanking !== null
                                  }
                                  onChange={() =>
                                    handleOpenRankingModal(blog?.blog_details)
                                  }
                                />
                                <div className=" ms-2 ">
                                  {blog?.blog_details?.BlogSectionName ===
                                    "similar" &&
                                    blog?.blog_details?.BlogRanking !==
                                      null && (
                                      <span className="badge badge-success bg-success">
                                        {blog?.blog_details?.BlogSectionName} -{" "}
                                        {blog?.blog_details?.BlogRanking}
                                      </span>
                                    )}
                                </div>
                              </div>
                            </td>
                            <td className="col purple-content text-capitalize">
                              {blog?.blog_details?.expert_details
                                ?.doctor_other_details?.ProfilePhoto ? (
                                <Image
                                  src={`${blog?.blog_details?.expert_details?.doctor_other_details?.ProfilePhoto}`}
                                  alt={`Dr ${blog?.blog_details?.expert_details?.name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.blog_details?.expert_details?.name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(
                                blog?.blog_details?.expert_details?.name
                              )}
                            </td>
                            <td className="col custom-font text-center">
                              {blog?.blog_details?.BlogDateTime?.split("T")[0]}
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                              {highlightText(
                                blog?.blog_details?.BlogTitle,
                                searchQuery
                              )}
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {blog?.blog_details?.expert_details?.role}
                            </td>
                            <td className="text-center text-capitalize col custom-font">
                              {blog?.blog_details?.BlogViews}
                            </td>
                            <td className="text-center col">
                              <button
                                style={{ color: "#8107D1" }}
                                className="fw-semibold border-0 bg-white"
                                onClick={() =>
                                  handleViewBlogsModal(blog?.blog_details)
                                }
                              >
                                view
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
                {loading && renderPlaceholders("load")}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="d-flex justify-content-center align-items-center mt-3">
        <div className="d-none d-xl-block">
          <CustomPagination
            total_pages={blogs?.total_pages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </div>
      </div>
    </>
  );
};

export default AllBlogsList;
