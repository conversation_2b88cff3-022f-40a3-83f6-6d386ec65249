import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import ConsentFormModal from "./AllApprovalModals/ConsentFormModal.jsx";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import {
  capitalizeFullName,
  formatCustomDate,
  formatDate,
  skeletonLoader,
  timeDifference,
} from "../../../utils/helperfunction";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { toast } from "react-toastify";
import NoDataFound from "@/components/noDataFound/NoDataFound";
import CustomPagination from "@/components/CustomPagination/CustomPagination";

const AllConsentForm = () => {
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [allExpertsConsentForm, setAllExpertsConsentForm] = useState([]);
  const [noDtaError, setNoDtaError] = useState(false);
  const [consentFormData, setConsentFormData] = useState({});
  const [loading, setLoading] = useState(true);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  const fetchAllExpertsConsentForm = useCallback(async () => {
    try {
      setLoading(true);
      const allExpertsConsentFormResponse = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_EXPERT_CONSENT}all/?user_id=${admin_id}&page=${current_page}`
      );
      const data = allExpertsConsentFormResponse?.data?.data;
      const totalPages = allExpertsConsentFormResponse.data?.total_pages;
      setAllExpertsConsentForm(allExpertsConsentFormResponse?.data?.data);
      setTotalPages(allExpertsConsentFormResponse.data?.total_pages);
    } catch (error) {
      console.error(error);
      setNoDtaError(true);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, admin_id, current_page]);

  useEffect(() => {
    if (admin_id) {
      fetchAllExpertsConsentForm();
    }
  }, [fetchAllExpertsConsentForm, admin_id, current_page]);

  const handleExpertConsentFormApprovals = async (status, expert_id) => {
    if (status === "reject") {
      Swal.fire({
        title: "Reject Testimonial?",
        text: "Are you sure you want to reject this testimonial?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, reject it!",
        cancelButtonText: "No, cancel",
        confirmButtonColor: "#FF2E2E",
        cancelButtonColor: "#8107D1",
      }).then(async (result) => {
        if (result.isConfirmed) {
          const { value: reason } = await Swal.fire({
            title: "Reject Testimonial?",
            input: "textarea",
            inputLabel: "Reason for rejection",
            inputPlaceholder: "Type your reason here...",
            inputAttributes: {
              "aria-label": "Type your reason here",
            },
            showCancelButton: true,
            confirmButtonText: "Reject",
            cancelButtonText: "Cancel",
            confirmButtonColor: "#FF2E2E",
            cancelButtonColor: "#8107D1",
          });

          if (reason) {
            submitApproval(status, expert_id, reason);
          }
        }
      });
    } else {
      Swal.fire({
        title: "Approve Testimonial?",
        text: "Are you sure you want to approve this testimonial?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Approve",
        cancelButtonText: "Cancel",
        confirmButtonColor: "#8107D1",
        cancelButtonColor: "#FF2E2E",
      }).then(async (result) => {
        if (result.isConfirmed) {
          submitApproval(status, expert_id);
        }
      });
    }
  };

  const submitApproval = async (status, expert_id, reason) => {
    let consentFormStatus;

    if (status === "reject") {
      consentFormStatus = 0;
    } else if (status === "approve") {
      consentFormStatus = 1;
    }

    try {
      let dataToSend = { Status: consentFormStatus };

      if (status === "reject" && reason) {
        dataToSend.Reason = reason;
      }

      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_GET_DOCTORS_CONSENT_FORM}${expert_id}/?user_id=${admin_id}`,
        dataToSend
      );

      if (response.data?.message?.Status === 1) {
        toast.success("Consent Form Approved Successfully");
      } else if (response.data?.message?.Status === 0) {
        toast.success("Consent Form Rejected Successfully");
      }

      fetchAllExpertsConsentForm();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="overflow-hidden h-100">
        <div
          className={`${
            Array.isArray(allExpertsConsentForm) &&
            allExpertsConsentForm.length > 0
              ? "overflow-auto allApproval-tab-scroll "
              : "h-100 p-5 "
          }`}
        >
          <div className="row ">
            {loading ? (
              <div className="d-flex justify-content-between align-items-center">
                <div className="col-sm-6 mx-1">
                  {skeletonLoader("100px", "5px", 6)}
                </div>
                <div className="col-sm-6 mx-1">
                  {skeletonLoader("100px", "5px", 6)}
                </div>
              </div>
            ) : noDtaError ? (
              <div className="custom-margin-nodatafoud">
                <NoDataFound />
              </div>
            ) : allExpertsConsentForm &&
              Array.isArray(allExpertsConsentForm) &&
              allExpertsConsentForm.length > 0 ? (
              allExpertsConsentForm?.map((consentForm) => (

                <div key={consentForm.id} className="col-sm-6 mb-2">
                  <div className="consent-bg p-3">
                    <div className="row">
                      <div className="col-sm-1">
                        {consentForm?.expert_photo ? (
                          <>
                            <Image
                              src={`${consentForm?.expert_photo}`}
                              width={40}
                              height={40}
                              alt={consentForm.expert_name}
                              className="allApproval-image"
                            />
                          </>
                        ) : (
                          <>
                            <Image
                              src={dummyProfile}
                              width={40}
                              height={40}
                              alt="cu logo"
                              className="allApproval-image"
                            />
                          </>
                        )}
                      </div>
                      <div className="col-sm ">
                        <p className="allApproval-expert-name mb-0">
                          {consentForm.expert_name || "Unknown Name"}
                        </p>
                        <p className="allApproval-expert-role ">
                          {capitalizeFullName(
                            consentForm.expert_role
                              ? consentForm.expert_role
                              : "No Expert Role"
                          )}
                        </p>
                      </div>
                      <div className="col-sm">
                        <p className="allApproval-expert-para">
                          {consentForm?.para}
                        </p>
                      </div>
                      <div className="col-sm-1 gx-0">
                        <span
                          className="allApproval-view-btn"
                          onClick={() => {
                            setShowConsentModal(true);
                            setConsentFormData(consentForm);
                          }}
                        >
                          View
                        </span>
                      </div>
                    </div>
                    <div className=" d-flex justify-content-between align-items-center">
                      <div className="mx-5">
                        <p className="testimonial-formatedate mx-">
                          {consentForm?.DateOfConsentForm &&
                            formatCustomDate(consentForm?.DateOfConsentForm)}
                          &nbsp; &nbsp;
                          <span className="testimonal-time-difference">
                            (
                            {consentForm?.DateOfConsentForm &&
                              timeDifference(consentForm?.DateOfConsentForm)}
                            )
                          </span>
                        </p>
                      </div>
                      <div>
                        <button
                          type="button"
                          className="btn allApproval-reject-btn"
                          onClick={() =>
                            handleExpertConsentFormApprovals(
                              "reject",
                              consentForm?.DoctorId
                            )
                          }
                        >
                          Reject
                        </button>

                        <button
                          type="button"
                          className="btn allApproval-approve-btn ms-2"
                          onClick={() =>
                            handleExpertConsentFormApprovals(
                              "approve",
                              consentForm?.DoctorId
                            )
                          }
                        >
                          Approve
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className=" d-flex justify-content-center align-items-center  mt-5 p-5">
                <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                  No other consent forms available for approval.
                </h3>
              </div>
            )}
          </div>
        </div>
      </div>
      {totalPages > 1 && (
        <div>
          <CustomPagination
            total_pages={totalPages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </div>
      )}
      {showConsentModal && (
        <ConsentFormModal
          showConsentModal={showConsentModal}
          setShowConsentModal={setShowConsentModal}
          consentFormData={consentFormData}
        />
      )}
    </>
  );
};

export default AllConsentForm;
