import Image from "next/image";
import React, { useEffect, useState } from "react";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { formatDate, timeDifference } from "./db.js";
import NoDataFound from "../noDataFound/NoDataFound.jsx";

const TestimonialApproval = ({
  testimonials,
  noDtaError,
  testimonialsLoading,
  handleTestimonialModal,
  renderPlaceholders,
}) => {
  return (
    <>
      <div className="mt-2 ms-2 text-secondary feedback-note">
        <b style={{ color: "#8107d1" }}> Note:</b> Testimonials refers to
        feedbacks of patients after each appointments.
      </div>
      {testimonialsLoading === false ? (
        <>
          {noDtaError ? (
            <div className="custom-margin-nodatafoud">
              <NoDataFound />
            </div>
          ) : testimonials && testimonials?.length > 0 ? (
            <div className="overflow-hidden">
              <div className="overflow-auto allApproval-tab-scroll">
                <div className="row">
                  {testimonials?.map((item, index) => (
                    <div key={index} className="col-6 mb-2">
                      <div className="d-flex justify-content-between align-items-start bg-grey upload-reviews">
                        <div className="col-sm-1">
                          {item?.patient_photo ? (
                            <Image
                              src={item.patient_photo}
                              alt="feedback"
                              height={35}
                              width={35}
                              className="testimonial_image"
                              onError={(e) => {
                                e.target.src = "/images/profile.png"; // Set a fallback image when an error occurs
                              }}
                            />
                          ) : (
                            <Image
                              src="/images/profile.png" // Fallback image
                              alt="fallback"
                              height={35}
                              width={35}
                              className="testimonial_image"
                            />
                          )}
                        </div>
                        <div className="col-sm-2">
                          <p className="fw-semibold mb-0">
                            {formatDate(item?.CurrentTime)}
                          </p>
                          <p className="custom-transperent-btn">
                            {timeDifference(item?.CurrentTime)}
                          </p>
                        </div>
                        <div className="col-sm-7">
                          <p className="fw-semibold mb-0">
                            {item?.CancerTreatmentType}
                          </p>
                        </div>
                        <div className="col-sm-2">
                          <button
                            type="button"
                            className="btn btn-yellow"
                            onClick={() => handleTestimonialModal(item)}
                          >
                            Review
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No other testimonials available for approval.
              </h3>
            </div>
          )}
        </>
      ) : (
        renderPlaceholders()
      )}
    </>
  );
};

export default TestimonialApproval;
