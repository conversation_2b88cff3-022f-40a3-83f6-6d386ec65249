"use client";
import React, { useState, useEffect } from "react";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";

const NotificationPermission = ({
  permissionsList,
  childAdminId,
  admin_id,
  fetchGrantedUpdatePermissions,
}) => {
  const [permissions, setPermissions] = useState([]);
  const axiosAuth = useAxiosAuth();

  useEffect(() => {
    if (permissionsList) {
      setPermissions(
        permissionsList.map((permission) => ({
          ...permission,
          active: permission.Status === 1,
        }))
      );
    }
  }, [permissionsList]);

  const handleToggle = async (index) => {
    const updatedPermissions = permissions.map((permission, i) =>
      i === index ? { ...permission, active: !permission.active } : permission
    );

    const notification = updatedPermissions[index];
    const updatedStatus = notification.active ? 1 : 0;
    const body = {
      perms: [[notification.Codename, notification.Name, updatedStatus]],
    };

    try {
      await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_FETCH_EDIT_PERMISSIONS}${childAdminId}/?user_id=${admin_id}`,
        body
      );
      fetchGrantedUpdatePermissions();
      setPermissions(updatedPermissions);
    } catch (error) {
      console.error("Error updating notification status:", error);
      // Optionally, handle the error or revert the state update
      setPermissions(permissions);
    }
  };

  return (
    <div className="col-sm-12 gx-5">
      <div className="row">
        <div className="col-sm-10">
          <p className="p-s-subheading">Permission Settings</p>
        </div>
        <div className="col-sm-2">
          <p className="p-s-subheading">Status</p>
        </div>
      </div>
      <div className="overflow-hidden">
        <div className="content-scroll overflow-auto">
          <div className="notification-tab">
            <div className="">
              {permissions.map((permission, index) => (
                <div
                  key={permission?.id || index}
                  className="row mb-2 single-notification-tab me-3"
                >
                  <div className="col-sm-10">
                    <span className="notification-name">
                      {permission?.Name}
                    </span>
                  </div>
                  <div className="col-sm-2">
                    <div className="form-check form-switch">
                      <div
                        className={`toggle-container ${
                          permission.active ? "open" : "close"
                        }`}
                      >
                        <input
                          className="switch"
                          type="checkbox"
                          checked={permission.active}
                          onChange={() => handleToggle(index)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationPermission;
