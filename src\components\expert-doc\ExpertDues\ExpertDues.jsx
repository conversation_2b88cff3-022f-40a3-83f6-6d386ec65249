"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import TableHead from "../../experts/TableHead";
import Image from "react-bootstrap/Image";
import nodataFound from "../../../../public/images/nodata.png"; // update this path to your actual image path
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import { useParams } from "next/navigation";
import { BsFastForwardFill } from "react-icons/bs";
import { TbPlayerTrackPrevFilled } from "react-icons/tb";
import { FaDownload } from "react-icons/fa6";
import * as FileSaver from "file-saver";
import * as XLSX from "xlsx";
import "./expertdues.css";
import Loading from "../../Loading/PageLoading/Loading";
import CustomPagination from "../../CustomPagination/CustomPagination";
import { toast } from "react-toastify";
import { <PERSON><PERSON>, Overlay, Too<PERSON><PERSON> } from "react-bootstrap";
import NoDataFound from "@/components/noDataFound/NoDataFound";

const DrHeaderTitles = [
  "Sl No.",
  "Appointment ID",
  "Appointment Date",
  "Amount",
  "Doctor Consultation Fees",
  "Payment Status",
];

const getColor = (toBePaid) => {
  return toBePaid === "Y" ? "#FF971A" : "#FF2E2E";
};

const ExpertDues = () => {
  const [appointments, setAppointments] = useState([]);
  const [finalAmount, setFinalAmount] = useState(0);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [showOptions, setShowOptions] = useState(false);
  const [error, setError] = useState(false);
  const [downloadInProgress, setDownloadInProgress] = useState(false);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const params = useParams();
  const pageSize = 10;
  const cacheRef = useRef({});
  const targetRef = useRef(null);
  const overlayRef = useRef(null);
  const accumulatedAmountRef = useRef(0);

  const user_id = session?.user?.id;
  const expert_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";

  const fetchAppointments = useCallback(async () => {
    if (cacheRef.current[current_page]) {
      const { items, total_pages, total_items } =
        cacheRef.current[current_page];
      setAppointments(items);
      setTotalPages(total_pages);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_EXPERT_DUES_API_ENDPOINT}${expert_id}/?user_id=${user_id}&per_page=${pageSize}&page=${current_page}`
      );

      const items = response?.data?.items || [];
      const total_pages = response?.data?.total_pages || 0;
      const total_items = response?.data?.total_items || 0;

      setAppointments(items);
      setTotalPages(total_pages);
      cacheRef.current[current_page] = {
        items,
        total_pages,
        total_items,
      };

      const totalAmount = items.reduce((acc, item) => {
        if (item.to_be_paid === "Y") {
          acc += item.doctor_consultation_fees;
        }
        return acc;
      }, 0);

      accumulatedAmountRef.current += totalAmount;
      setFinalAmount(accumulatedAmountRef.current);
    } catch (err) {
      console.error("Error fetching data", err);
      setError(true);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, expert_id, user_id, current_page]);
  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  const handleDownloadAppRecord = async (downloadAll = false) => {
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
    const fileExtension = ".xlsx";
    let dataToExport;

    const downloadToastId = toast.loading(
      "Generating and downloading file...",
      {
        progress: 0,
        autoClose: false, // Keeps the toast open until download is complete
      }
    );

    try {
      setDownloadInProgress(true);
      let url = `${process.env.NEXT_PUBLIC_EXPERT_DUES_API_ENDPOINT}${expert_id}/?user_id=${user_id}`;

      if (downloadAll) {
        const response = await axiosAuth.get(url);
        dataToExport = response?.data?.items || [];
      } else {
        dataToExport = appointments;
      }

      // If no records are found, update toast and return
      if (!dataToExport || dataToExport?.length === 0) {
        toast.update(downloadToastId, {
          render: "No records found!",
          type: "warning",
          progress: 100,
        });
        toast.dismiss(downloadToastId);
        return;
      }

      // Mapping data for the download
      const mappedData = dataToExport?.map((appointment, index) => ({
        Sl_No: index + 1,
        Appointment_id: appointment?.appointmentId,
        Appointment_date: appointment?.appointment_date,
        Amount: appointment?.appointment_amount,
        Doctor_consultation_fees: appointment?.doctor_consultation_fees,
        Payment_status: appointment?.to_be_paid === "Y" ? "Paid" : "Unpaid",
      }));

      // Update progress to indicate file generation
      toast.update(downloadToastId, {
        render: "Generating file...",
        progress: 50,
      });

      // Generate the Excel file from the mapped data
      const ws = XLSX.utils.json_to_sheet(mappedData);
      const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const data = new Blob([excelBuffer], { type: fileType });

      // Construct file name
      const fileName = downloadAll
        ? `ExpertDues_AllData${fileExtension}`
        : `ExpertDues_Page${current_page}${fileExtension}`;

      // Update progress for download ready
      if (downloadToastId) {
        toast.update(downloadToastId, {
          render: "Download ready. Saving file...",
          progress: 80,
        });
      }

      // Trigger download of the file
      FileSaver.saveAs(data, fileName);

      // Final toast update for success
      if (downloadToastId) {
        toast.update(downloadToastId, {
          render: "Download complete!",
          progress: 100,
          type: "success",
          autoClose: 3000,
        });
        toast.dismiss(downloadToastId);
      }
    } catch (error) {
      console.error("Error downloading appointments", error);

      if (downloadToastId) {
        toast.update(downloadToastId, {
          render: "Failed to download. Please try again.",
          type: "error",
          progress: 100,
        });
        toast.dismiss(downloadToastId);
      }
    } finally {
      setDownloadInProgress(false);
      setShowOptions(false);
    }
  };

  const handleClickOutside = (e) => {
    if (
      targetRef.current &&
      !targetRef.current.contains(e.target) &&
      overlayRef.current &&
      !overlayRef.current.contains(e.target)
    ) {
      setShowOptions(false); // Close the tooltip if clicking outside
    }
  };

  useEffect(() => {
    // Attach the event listener to the document
    document.addEventListener("mousedown", handleClickOutside);

    // Cleanup the event listener when the component is unmounted or dependencies change
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className="expert-dues-container">
      {loading ? (
        <Loading />
      ) : (
        <div>
          <div className="total-expert-due-section p-3">
            <div>
              <p className="pb-0 mb-0">
                {" "}
                Total Amount to be paid:{" "}
                <span className="total-due-amount">$ {finalAmount}</span>
              </p>
            </div>
            {!error && appointments?.length > 0 && (
              <div className="total-expert-due-download" ref={targetRef}>
                <Button
                  onClick={() => setShowOptions(!showOptions)}
                  className=" bg-transparent border-0 text-black"
                >
                  Download Report&nbsp;{" "}
                  <FaDownload className="total-expert-due-download-btn" />
                </Button>
                <Overlay
                  target={targetRef.current}
                  show={showOptions}
                  placement="top"
                  className="bg-transparent"
                  ref={overlayRef}
                >
                  {(props) => (
                    <Tooltip
                      {...props}
                      className="expert-dues-tooltip bg-transparent d-flex justify-content-between align-content-center"
                    >
                      <Button
                        onClick={() => handleDownloadAppRecord(false)}
                        className="option-button border-0"
                        disabled={downloadInProgress}
                        style={{
                          pointerEvents: downloadInProgress ? "none" : "auto",
                          opacity: downloadInProgress ? 0.5 : 1,
                          backgroundColor: "#8107d1",
                        }}
                      >
                        Current Page
                      </Button>
                      &nbsp;&nbsp;
                      <Button
                        onClick={() => handleDownloadAppRecord(true)}
                        className="option-button border-0"
                        disabled={downloadInProgress}
                        style={{
                          pointerEvents: downloadInProgress ? "none" : "auto",
                          opacity: downloadInProgress ? 0.5 : 1,
                          backgroundColor: "#8107d1",
                        }}
                      >
                        All
                      </Button>
                    </Tooltip>
                  )}
                </Overlay>
              </div>
            )}
          </div>

          <table className="table experts-list-section mt-2">
            <TableHead headerTitles={DrHeaderTitles} />
            <tbody className="custom-border text-center">
              {appointments && appointments?.length > 0 ? (
                appointments.map((appointment, index) => {
                  const globalIndex = (current_page - 1) * pageSize + index + 1;
                  return (
                    <tr key={appointment.appointmentId} className="custom-row">
                      <td
                        className="custom-font text-center "
                        style={{ padding: "15px" }}
                      >
                        {globalIndex}
                      </td>
                      <td className="text-center purple-content">
                        {appointment.appointmentId}
                      </td>
                      <td className="custom-font text-center">
                        {appointment?.appointment_date}
                      </td>
                      <td className="text-center">
                        {appointment?.appointment_amount}
                      </td>
                      <td className="text-center">
                        {appointment?.doctor_consultation_fees}
                      </td>
                      <td
                        className="text-center fw-semibold"
                        style={{ color: getColor(appointment?.to_be_paid) }}
                      >
                        {appointment?.to_be_paid === "Y"
                          ? "To be paid"
                          : appointment?.to_be_paid === "N"
                          ? "Not to be paid"
                          : ""}
                      </td>
                    </tr>
                  );
                })
              ) : !error ? (
                <tr>
                  <td colSpan="6">
                    <div className="fs-4 d-flex flex-column align-items-center h-75 justify-content-center no-Patient-tickets-found">
                      <span className="no-expert-approval-record text-center">
                        <NoDataFound />
                      </span>
                    </div>
                  </td>
                </tr>
              ) : (
                <tr>
                  <td colSpan="6">
                    <div className="fs-4 d-flex flex-column align-items-center justify-content-center no-Patient-tickets-found">
                      <h3 className="text-secondary text-center">
                        No outstanding payments at the moment.
                      </h3>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
      {!loading && totalPages > 1 && (
        <div className="pagination-container">
          <CustomPagination
            total_pages={totalPages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </div>
      )}
    </div>
  );
};

export default ExpertDues;
