.custom-modal-header {
  display: flow-root;
}
/* .black-content {
  color: #5b5b5b;
  font-size: 12px;
  font-weight: 700;
} */
.custom-modal-width {
  max-width: 55%;
}
label.form-label.purple-content-2 {
  color: #713c8f;
  font-size: 12px;
  font-weight: 500;
}
th.purple-content {
  color: #8107d1;
  font-size: 16px;
  line-height: 17px;
  font-weight: 700;
}
.medication-heading {
  font-size: 28px;
  font-weight: 700;
  color: #8107d1;
}
.doc-sign {
  height: 20px;
  width: 130px;
}
.exist-treatment-container {
  margin-left: 0px;
  margin-right: 0px;
  border: 2px solid #f0eaea;
}
.purple-content {
  color: #8107d1;
  font-size: 16px;
  font-weight: 700;
}
.prescription-btn,
.prescription-btn:hover {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  border: none;
  color: white;
}
