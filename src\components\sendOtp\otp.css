.react-tel-input.phone-input {
    border: 1px solid #a14b9e;
    width: 100%;
    padding-left: 0px;
    cursor: pointer;
  }
  .country-container-input {
    outline: none !important;
  }
  .react-tel-input .form-control{
      cursor: pointer;
      width: 100% !important;
  }
   
  .phone-input:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: none;
  }
  .react-tel-input .form-control:focus{
      outline: none !important;
      box-shadow: none !important;
   
  }
   
  .react-tel-input.phone-input {
    padding-top: 0px;
    padding-bottom: 0px;
    border-radius: 0px;
  }

  .otp-val-error{
    font-size: 13px;
    color: orangered;
  }

  .admin-continue:disabled{
    background-color: #b384c1;
  }
  .admin-continue:hover{
    background-color: #961dba;
  }