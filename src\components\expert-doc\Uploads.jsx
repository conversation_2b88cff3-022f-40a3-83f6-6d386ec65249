"use client";
import React, { useState, useCallback, useEffect } from "react";
import PreviewArticles from "../../components/expert-doc/PreviewArticles";
import { toast } from "react-toastify";
import {
  useParams,
  usePathname,
  useSearchPara<PERSON>,
  useRouter,
} from "next/navigation";
import Swal from "sweetalert2";
import "./uploads.css";
import Loading from "../Loading/PageLoading/Loading";
import { Placeholder } from "react-bootstrap";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import PreviewIntroVideo from "./PreviewIntroVideo";
import PodcastPreview from "./PodcastPreview/PodcastPreview";
import PreviewReview from "./PreviewReview";
import FeedbackPreview from "./FeedbackPreview";
import PreviewConsent from "./PreviewConsent";
import TestimonialPreview from "./TestimonialPreview";
import { useSession } from "next-auth/react";
import AllReviews from "./UploadsComponent/AllReviews.jsx";
import RecentBlogs from "./UploadsComponent/RecentBlogs.jsx";
import AllDoctorsfeedback from "./UploadsComponent/AllDoctorsfeedback.jsx";
import AllPatientsTestimonial from "./UploadsComponent/AllPatientsTestimonial.jsx";
import IntroductionVideoUploads from "./UploadsComponent/IntroductionVideoUploads.jsx";
import ConsentFormUploads from "./UploadsComponent/ConsentFormUploads.jsx";
import PodcastRequest from "./UploadsComponent/PodcastRequest.jsx";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "50px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};

const Uploads = ({ doctorsConsent, doctorsData }) => {
  const { data: session } = useSession();
  const admin_id = session && session.user.id;

  const pathname = usePathname();
  const [selectedItem, setSelectedItem] = useState({});
  const searchParams = useSearchParams();
  const [podcastList, setPodcastList] = useState([]);
  const [selectedValue, setSelectedValue] = useState("all");
  const [loading, setLoading] = useState(true);
  const [articles, setArticles] = useState([]);
  const [testimonials, setTestimonials] = useState([]);
  const [articleLoading, setArticleLoading] = useState(true);
  const [showArticlesPreview, setShowArticlesPreview] = useState(false);
  const [showReviewPreview, setShowReviewPreview] = useState(false);
  const [showConsentPreview, setShowConsentPreview] = useState(false);
  const [feedbackPreview, setFeedbackPreview] = useState(false);
  const [testimonialPreview, setTestimonialPreview] = useState(false);
  const [selectedTestimonial, setSelectedTestimonial] = useState({});
  const [selectedConsent, setSelectedConsent] = useState({});
  const [reviews, setReviews] = useState([]);
  const [selectedPreview, setSelectedPreview] = useState({});
  const [selectedFeedback, setSelectedFeedback] = useState({});
  const [allFeedbacks, setAllFeedbacks] = useState([]);
  const initialTab = searchParams.get("tab");
  const initialSubtab = searchParams.get("subtab");
  const [activeSubTab, setActiveSubTab] = useState(initialSubtab || "blogs");
  const [showIntroVideoPreview, setShowIntroVideoPreview] = useState(false);
  const [showNoPreviewText, setShowNoPreviewText] = useState(true);
   const [current_page, setCurrent_Page] = useState(1);

  const router = useRouter();
  const [expertIntroVideo, setExpertIntroVideo] = useState({
    videoUrl: null,
    introVideoStatus: 0,
    expertName: "",
    expertRole: "",
  });
  const [selectedPodcastDetails, setSelectedPodcastDetails] = useState({
    id: 0,
    PodcastURL: null,
    PodcastTopic: "",
    PodcastStatus: 0,
    ExpertId: 0,
    expert_role: "",
    ThumbnailImage: "",
    PodcastDate: "",
    PodcastCategory: "",
    PodcastDescription: "",
    PodcastViews: 0,
  });
  const [selectedIntroVideo, setSelectedIntroVideo] = useState({
    url: null,
    index: 0,
  });
  const [showPodcastPreview, setShowPodcastPreview] = useState(false);
  const params = useParams();
  const { user_id } = params;
  const doctor_id = user_id[0];
  const doctor_email = decodeURIComponent(user_id[1]);

  const axiosAuth = useAxiosAuth();

  const handleTabChange = (subtab) => {
    // const updatedPath = `${pathname}?tab=${tab}`;
    const updatedPath = `${pathname}?tab=${initialTab}&subtab=${subtab}`;
    router.replace(updatedPath, { shallow: true });
    setActiveSubTab(subtab);
    // Reset all preview states
    setShowArticlesPreview(false);
    setShowReviewPreview(false);
    setFeedbackPreview(false);
    setTestimonialPreview(false);
    setShowIntroVideoPreview(false);
    setShowConsentPreview(false);
    setShowPodcastPreview(false);
    setSelectedItem(null);
    setSelectedPreview(null);
    setSelectedFeedback(null);
    setSelectedTestimonial(null);
    setSelectedIntroVideo(null);
    setSelectedConsent(null);
    setSelectedPodcastDetails(null);
  };

  const fetchArticles = useCallback(
    async (selectedValue) => {
      try {
        setLoading(true);
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_DOCTOR_ARTICLE_DATA}${doctor_id}/${selectedValue}/`
        );

        setArticles(response?.data);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
        setArticleLoading(false);
      }
    },
    [doctor_id, axiosAuth]
  );

  const fetchReviews = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS_EXTERNAL_REVIEWS}?expert_id=${doctor_id}`
      );

      setReviews(response?.data);
      // setReviews([]);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, doctor_id]);

  const fetchIntroVideo = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_DOCTOR_DATA}${doctor_email}/`
      );
      let introVideosStatus = [];
      let introVideos =
        response?.data?.user_data?.doctor_other_details?.IntVideoUrl;
      let introVideoStatus =
        response?.data?.user_data?.doctor_other_details?.IntroVideoStatus;
      if (
        introVideos &&
        introVideoStatus &&
        introVideos?.length === 1 &&
        introVideoStatus === 2
      ) {
        introVideosStatus[0] = 2;
      } else if (
        introVideos &&
        introVideoStatus &&
        introVideos?.length === 1 &&
        (introVideoStatus === 1 || introVideoStatus === 3)
      ) {
        introVideosStatus[0] = introVideoStatus;
      } else if (
        introVideos &&
        introVideoStatus &&
        introVideos?.length === 2 &&
        introVideoStatus === 2
      ) {
        introVideosStatus[0] = 2;
        introVideosStatus[1] = 2;
      } else if (
        introVideos &&
        introVideoStatus &&
        introVideos?.length === 2 &&
        (introVideoStatus === 1 || introVideoStatus === 3)
      ) {
        introVideosStatus[0] = 2;
        introVideosStatus[1] = introVideoStatus;
      }

      setExpertIntroVideo({
        videoUrl: introVideos,
        introVideoStatus: introVideosStatus,
        expertName: response?.data?.user_data?.name,
        expertRole: response?.data?.user_data?.role,
      });
    } catch (error) {
      console.error(error);
    }
  }, [doctor_email, axiosAuth]);

  const fetchPodcastList = useCallback(async () => {
    try {
      const statusParam = selectedValue !== "all" ? `&status=${selectedValue}` : "";
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_EXPERTS_PODCAST_REQUEST}${doctor_id}/?page=${current_page}&per_page=10${statusParam}`
      );

      setPodcastList(response?.data);
    } catch (error) {
      console.error(error);
    }
  }, [doctor_id, axiosAuth,current_page, selectedValue]);

  const fetchTestimonials = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_SHARE_UR_STORY}${doctor_id}/${selectedValue}/`
      );

      setTestimonials(response?.data);
    } catch (err) {
      toast.error("error in fetching testimonial");
    } finally {
      setLoading(false);
    }
  }, [doctor_id, axiosAuth, selectedValue]);

  const fetchFeedback = useCallback(async () => {
    try {
      const response = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_FEEDBACK}${doctor_id}/${selectedValue}/`
      );
      setAllFeedbacks(response?.data);
    } catch (err) {
      toast.error("error in fetching testimonial");
    }
  }, [doctor_id, axiosAuth, selectedValue]);

  useEffect(() => {
    if (doctor_email) {
      fetchIntroVideo();
    }

    if (doctor_id) {
      fetchPodcastList();
      fetchReviews();
      fetchTestimonials();
    }
    fetchFeedback();
    const subtab = searchParams.get("subtab");
    setActiveSubTab(subtab || "blogs");
  }, [
    fetchIntroVideo,
    doctor_email,
    fetchPodcastList,
    searchParams,
    doctor_id,
    fetchReviews,
    fetchTestimonials,
    fetchFeedback,
  ]);podcastList
  useEffect(() => {
    if (doctor_id) {
      fetchArticles(selectedValue);
    }
  }, [doctor_email, doctor_id, fetchArticles, selectedValue]);

  const handleView = (item) => {
    setSelectedItem(item);
  };

  const handlePreviewView = (item) => {
    setSelectedPreview(item);
    setShowConsentPreview(false);
    setShowReviewPreview(true);
    setShowIntroVideoPreview(false);
    setShowArticlesPreview(false);
    setShowPodcastPreview(false);
    setFeedbackPreview(false);
  };
  const handleFeedbackPreview = (item) => {
    setSelectedFeedback(item);
    setFeedbackPreview(true);
    setShowReviewPreview(false);
    setShowIntroVideoPreview(false);
    setShowArticlesPreview(false);
    setShowPodcastPreview(false);
    setShowConsentPreview(false);
    setTestimonialPreview(false);
  };
  const handleTestimonialPreview = (item) => {
    setSelectedTestimonial(item);
    setTestimonialPreview(true);
    setFeedbackPreview(false);
    setShowReviewPreview(false);
    setShowIntroVideoPreview(false);
    setShowArticlesPreview(false);
    setShowPodcastPreview(false);
    setShowConsentPreview(false);
  };
  const handleConsentPreview = (item) => {
    setSelectedConsent(item);
    setShowConsentPreview(true);
    setTestimonialPreview(false);
    setFeedbackPreview(false);
    setShowReviewPreview(false);
    setShowIntroVideoPreview(false);
    setShowArticlesPreview(false);
    setShowPodcastPreview(false);
  };

  const handleBlogDelete = async (blogId) => {
    const confirmResult = await Swal.fire({
      title: "Are you sure want to delete this Blog?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#9426B2",
      cancelButtonColor: "#B50000",
      confirmButtonText: "Yes",
    });

    if (confirmResult.isConfirmed) {
      const apiEndpoint = `${process.env.NEXT_PUBLIC_DOCTOR_UPDATE_BLOG_API_ENDPOINT}${blogId}/`;

      try {
        const response = await axiosAuth.delete(apiEndpoint);
        if (response?.data === "Content removed successfully") {
          toast.success("Blog Deleted successfully!");
          fetchArticles(selectedValue);
        } else {
          toast.error("Failed to delete Blogs");
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    }
  };
  const handleDeleteTestimonial = async (testimonialId) => {
    const confirmResult = await Swal.fire({
      title: "Are you sure want to delete this Testimonial?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#9426B2",
      cancelButtonColor: "#B50000",
      confirmButtonText: "Yes",
    });

    if (confirmResult.isConfirmed) {
      const apiEndpoint = `${process.env.NEXT_PUBLIC_APPROVE_REJECT_TESTIMONIAL}${testimonialId}/?user_id=${admin_id}`;

      try {
        const response = await axiosAuth.delete(apiEndpoint);
        if (response?.data === "Content removed successfully") {
          toast.success("Testimonial Deleted successfully!");
          fetchTestimonials();
        } else {
          toast.error("Failed to delete Testimonial");
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    }
  };
  const handleDeleteReview = async (reviewId) => {
    const confirmResult = await Swal.fire({
      title: "Are you sure want to delete this Review?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#9426B2",
      cancelButtonColor: "#B50000",
      confirmButtonText: "Yes",
    });

    if (confirmResult.isConfirmed) {
      const apiEndpoint = `${process.env.NEXT_PUBLIC_GET_EXPERTS_EXTERNAL_REVIEWS}${reviewId}/?user_id=${admin_id}`;

      try {
        const response = await axiosAuth.delete(apiEndpoint);
        if (response?.data === "Content removed successfully") {
          toast.success("Review Deleted successfully!");
          fetchReviews();
        } else {
          toast.error("Failed to delete Review");
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    }
  };
  const handleDeletePodcast = async (podcastId) => {
    const confirmResult = await Swal.fire({
      title: "Are you sure want to delete this Podcast?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#9426B2",
      cancelButtonColor: "#B50000",
      confirmButtonText: "Yes",
    });

    if (confirmResult.isConfirmed) {
      const apiEndpoint = `${process.env.NEXT_PUBLIC_PUBLISH_EXPERTS_PODCAST_REQUEST}${podcastId}/?user_id=${admin_id}`;

      try {
        const response = await axiosAuth.delete(apiEndpoint);
        if (response?.data === "Content removed successfully") {
          toast.success("Podcast Deleted successfully!");
          fetchPodcastList();
        } else {
          toast.error("Failed to delete Podcast");
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    }
  };

  const handleChange = (event) => {
    setSelectedValue(event.target.value);
  };

  const shouldRenderPreview = () => {
    if (!selectedItem || !expertIntroVideo) {
      return false;
    }

    if (selectedItem) {
      return true;
    }

    if (showIntroVideoPreview === true) {
      return false;
    }
    if (showReviewPreview === true) {
      return false;
    }

    return false;
  };

  const handlePodcastPreview = (podcast) => {
    setSelectedPodcastDetails({
      id: podcast.id,
      PodcastURL: podcast.PodcastURL,
      PodcastTopic: podcast.PodcastTopic,
      PodcastStatus: podcast.PodcastStatus,
      ExpertId: podcast.ExpertId,
      expert_role: podcast.expert_role,
      ThumbnailImage: podcast.ThumbnailImage,
      PodcastDate: podcast.PodcastDate,
      PodcastCategory: podcast.PodcastCategoryVal,
      PodcastDescription: podcast.PodcastDescription,
      PodcastViews: podcast.PodcastViews,
      Platforms: podcast.Platforms,
      PodcastSectionName: podcast.PodcastSectionName,
    });
    setShowPodcastPreview(true);
    setShowArticlesPreview(false);
    setShowIntroVideoPreview(false);
  };

  const handlePreviewEmpty = () => {
    setShowPodcastPreview(false);
    setShowArticlesPreview(false);
    setShowIntroVideoPreview(false);
    setShowReviewPreview(false);
    setShowConsentPreview(false);
    setFeedbackPreview(false);
    setTestimonialPreview(false);
    setShowNoPreviewText(true);
  };

  return (
    <>
      {loading ? (
        <>
          <Loading />
        </>
      ) : (
        <div className="payment-back">
          {/* -------------------------------------Buttons-------------------------- */}
          <div className="row">
            <p className="d-inline-flex gap-1 buttons-row mb-0 mb-0">
              <button
                className={`btn grey-btn ${
                  activeSubTab === "blogs" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("blogs");
                  handlePreviewEmpty();
                }}
              >
                Blogs
              </button>
              <button
                className={`btn grey-btn ${
                  activeSubTab === "reviews" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("reviews");
                  handlePreviewEmpty();
                }}
              >
                Reviews
              </button>
              <button
                className={`btn grey-btn ${
                  activeSubTab === "doctor" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("doctor");
                  handlePreviewEmpty();
                }}
              >
                Feedback
              </button>
              <button
                className={`btn grey-btn ${
                  activeSubTab === "testimonial" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("testimonial");
                  handlePreviewEmpty();
                }}
              >
                Testimonials
              </button>
              <button
                className={`btn grey-btn ${
                  activeSubTab === "consent" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("consent");
                  handlePreviewEmpty();
                }}
              >
                Consent Form
              </button>
              <button
                className={`btn grey-btn ${
                  activeSubTab === "introduction" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("introduction");
                  handlePreviewEmpty();
                }}
              >
                Introduction Videos
              </button>
              {/* <button
                className={`btn grey-btn ${
                  activeSubTab === "podcast" ? "activeExpertsTab" : ""
                }`}
                onClick={() => {
                  setSelectedValue("all");
                  handleTabChange("podcast");
                  handlePreviewEmpty();
                }}
              >
                Podcast
              </button> */}
            </p>
          </div>
          <div className=" overflow-hidden" style={{ minHeight: "550px" }}>
            <div className="user-management-scroll overflow-auto">
              <div className="row ">
                <div
                  className="col-sm-7 overflow-hidden"
                  style={{ minHeight: "550px" }}
                >
                  {/* -------------------------------------Buttons-------------------------- */}

                  {activeSubTab === "blogs" && (
                    <RecentBlogs
                      articles={articles}
                      setArticles={setArticles}
                      articleLoading={articleLoading}
                      renderPlaceholders={renderPlaceholders}
                      handleView={handleView}
                      setShowIntroVideoPreview={setShowIntroVideoPreview}
                      setShowArticlesPreview={setShowArticlesPreview}
                      setShowPodcastPreview={setShowPodcastPreview}
                      setShowConsentPreview={setShowConsentPreview}
                      setShowReviewPreview={setShowReviewPreview}
                      setFeedbackPreview={setFeedbackPreview}
                      handleBlogDelete={handleBlogDelete}
                      selectedValue={selectedValue}
                      handleChange={handleChange}
                      setShowNoPreviewText={setShowNoPreviewText}

                    />
                  )}
                  {activeSubTab === "reviews" && (
                    <AllReviews
                      reviews={reviews}
                      handlePreviewView={handlePreviewView}
                      loading={loading}
                      handleDeleteReview={handleDeleteReview}
                      selectedValue={selectedValue}
                      handleChange={handleChange}
                      setShowNoPreviewText={setShowNoPreviewText}
                    />
                  )}

                  {activeSubTab === "doctor" && (
                    <AllDoctorsfeedback
                      loading={loading}
                      allFeedbacks={allFeedbacks}
                      handleFeedbackPreview={handleFeedbackPreview}
                      selectedValue={selectedValue}
                      handleChange={handleChange}
                      setShowNoPreviewText={setShowNoPreviewText}
                    />
                  )}
                  {activeSubTab === "testimonial" && (
                    <AllPatientsTestimonial
                      testimonials={testimonials}
                      loading={loading}
                      handleTestimonialPreview={handleTestimonialPreview}
                      handleDeleteTestimonial={handleDeleteTestimonial}
                      selectedValue={selectedValue}
                      handleChange={handleChange}
                      setShowNoPreviewText={setShowNoPreviewText}
                    />
                  )}
                  {activeSubTab === "consent" && (
                    <ConsentFormUploads
                      doctorsConsent={doctorsConsent}
                      handleConsentPreview={handleConsentPreview}
                      selectedValue={selectedValue}
                      handleChange={handleChange}
                      setShowNoPreviewText={setShowNoPreviewText}
                    />
                  )}
                  {activeSubTab === "introduction" && (
                    <IntroductionVideoUploads
                      expertIntroVideo={expertIntroVideo}
                      setTestimonialPreview={setTestimonialPreview}
                      setFeedbackPreview={setFeedbackPreview}
                      setShowReviewPreview={setShowReviewPreview}
                      setShowIntroVideoPreview={setShowIntroVideoPreview}
                      setShowArticlesPreview={setShowArticlesPreview}
                      setShowPodcastPreview={setShowPodcastPreview}
                      setSelectedIntroVideo={setSelectedIntroVideo}
                      setShowNoPreviewText={setShowNoPreviewText}
                    />
                  )}
                  {/* {activeSubTab === "podcast" && (
                    <PodcastRequest
                      podcastList={podcastList}
                      articleLoading={articleLoading}
                      handlePodcastPreview={handlePodcastPreview}
                      handleDeletePodcast={handleDeletePodcast}
                      selectedValue={selectedValue}
                      handleChange={handleChange}
                      setShowNoPreviewText={setShowNoPreviewText}
                      setCurrent_Page={setCurrent_Page}
                      current_page={current_page}
                    />
                  )} */}
                </div>
                {/* ---------------------------------Preview------------------------------------------- */}
                <div className="col-sm-5">
                  <div className="preview-section">
                    <div className="overflow-hidden">
                      <div className="content-scroll-articles overflow-auto">
                        {showNoPreviewText && (
                          <>
                            <div className="row mt-2">
                              <div className="col-sm-8">
                                <p className="grey-text fw-bold ps-4 ">
                                  Preview
                                </p>
                              </div>
                            </div>

                            <div className="d-flex justify-content-center align-items-center">
                              <p className="no-preveiw fw-bold text-center">
                                No Preview
                                <br />
                                Click on view button to see preview
                              </p>
                            </div>
                          </>
                        )}
                        {showArticlesPreview && (
                          <PreviewArticles
                            selectedItem={selectedItem}
                            onClose={() => {
                              setSelectedItem(null);
                            }}
                          />
                        )}

                        {showReviewPreview && (
                          <PreviewReview
                            selectedPreview={selectedPreview}
                            onClose={() => {
                              setShowReviewPreview(null);
                            }}
                          />
                        )}

                        {feedbackPreview && (
                          <FeedbackPreview
                            selectedFeedback={selectedFeedback}
                            onClose={() => {
                              setFeedbackPreview(null);
                            }}
                          />
                        )}

                        {testimonialPreview && (
                          <TestimonialPreview
                            selectedTestimonial={selectedTestimonial}
                            onClose={() => {
                              setTestimonialPreview(null);
                            }}
                          />
                        )}

                        {showIntroVideoPreview && (
                          <PreviewIntroVideo
                            videoUrlItem={selectedIntroVideo}
                          />
                        )}

                        {showConsentPreview && (
                          <PreviewConsent
                            selectedConsent={selectedConsent}
                            doctorsData={doctorsData}
                          />
                        )}

                        {showPodcastPreview && (
                          <PodcastPreview
                            selectedPodcastDetails={selectedPodcastDetails}
                            fetchPodcastList={fetchPodcastList}
                            setShowPodcastPreview={setShowPodcastPreview}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Uploads;
