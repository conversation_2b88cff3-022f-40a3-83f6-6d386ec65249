'use client';
import React, { useState } from 'react';
import Image from "next/image";
import profile from '../../../public/assets/sampleprofilepay.png'
const SubscriptionExpire = () => {
    return (
        <>
        <h5 className='pay-history'>Refund Requests</h5>
            <div className='row row-payment'>
                <div className='col-sm-12 mb-3 refund-apply'>
                    <div className='row'>
                        <div className='col-sm-6 gx-0'>
                            <div className='row'>
                                <div className='col-sm-4 gx-0'>
                                <Image src={profile} alt="" className="profile-refund" />
                                </div>
                                <div className='col-sm-8 gx-0'>
                              <div className='patient-name-refund mt-3'>Katy <PERSON></div>
                              <span className='patient-represent'>Patient</span>
                            </div>
                        </div>
                       <span className='refund-amount'>Plus 75.99/month</span><br />
                    </div>
                        <div className='col-sm-6'>
                            <div className='row'>
                            <div className='col-sm-12 mt-2'>
                            <button type='button' className='btn btn-renew'>Renew</button>
                            </div>
                            <div className='col-sm-12 gx-0'>
                            <span className='doctor-name-payment'>Paid via Mastercard</span>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div className="form-group">
                        <label htmlFor="reason" className='reason-text'></label>
                        <textarea className="form-control expire-textarea mb-3 mt-2 pt-2" rows="2"></textarea>
                    </div>
                </div>
            </div>
        </>
    )
}
export default SubscriptionExpire;