import React from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";

const RankingModal = ({
  isTopExperts,
  showModal,
  setShowModal,
  setRanking,
  setSection,
  ranking,
  section,
  handleAddRanking,
  selectedBlog,
  rankingLoading,
  trending,
  isSimilar,
}) => {
  return (
    <div>
      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        // contentLabel={`Ranking Confirmation`}
        className="custom-deactivate-width"
        size="lg"
        centered
        backdrop="static"
      >
        <div className="modal-content modal-content-deactivate-modal">
          <Modal.Header>
            <Modal.Title className="custom-title-2">Select Ranking</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="mb-3">
              <label htmlFor="reasonSelect" className="form-label">
                Select Ranking:
              </label>

              <select
                className="form-select"
                id="categorySelect"
                value={ranking}
                onChange={(e) => setRanking(e.target.value)}
              >
                <option value="">Select a ranking number</option>
                <option value={1}>1</option>
                <option value={2}>2</option>
                <option value={3}>3</option>
                <option value={4}>4</option>
                <option value={5}>5</option>
                <option value={6}>6</option>
                <option value={7}>7</option>
              </select>
            </div>
            {!isTopExperts && (
              <div className="mb-3">
                <label htmlFor="reasonSelect" className="form-label">
                  Select Section:
                </label>

                <select
                  className="form-select"
                  id="categorySelect"
                  value={section}
                  onChange={(e) => setSection(e.target.value)}
                >
                  <option value="">Select a ranking section</option>

                  {/* {!isSimilar && (
                    <> */}
                  <option value="Featured">Featured</option>
                  <option value="Top">Top</option>
                  {trending && <option value="Trending">Trending</option>}
                  {/* </>
                  )} */}
                  {/* <option value="Similar">Similar Blog</option> */}
                </select>
              </div>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button
              className="btn-cancel-deact"
              variant="secondary"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </Button>
            {isTopExperts ? (
              <Button
                style={{ background: "#8107d1" }}
                className="btn-deact"
                onClick={() => {
                  handleAddRanking(selectedBlog?.expert_details?.id);
                }}
              >
                {rankingLoading ? "Loading.." : "Submit"}
              </Button>
            ) : (
              <Button
                style={{ background: "#8107d1" }}
                className="btn-deact"
                onClick={() => {
                  handleAddRanking(selectedBlog?.id, isSimilar);
                }}
              >
                {rankingLoading ? "Loading.." : "Submit"}
              </Button>
            )}
          </Modal.Footer>
        </div>
      </Modal>
    </div>
  );
};

export default RankingModal;
