import React from "react";
import Image from "next/image";
import TableHead from "./TableHead";
import Link from "next/link";
import {
  capitalizeFullName,
  highlightText,
} from "../../utils/helperfunction.js";
import Loading from "../Loading/PageLoading/Loading";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
import dummyProfile from "../../../public/images/dummy-avatar.jpg";
import NoDataFound from "../noDataFound/NoDataFound";

const ExpertsList = ({
  expertsList = [],
  headerTitles,
  expertiseListName,
  searchQuery,
  loading,
}) => {
  function getColor(status) {
    switch (status) {
      case "pending":
        return "orange";
      case "Approval_requested":
        return "blue";
      case "Approved":
        return "rgb(0, 204, 74)";
      case "Deactivated":
        return "gray";
      case "Rejected":
        return "red";
      case "self_deactivation":
        return "red";
      default:
        return "black";
    }
  }

  return (
    <>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        <div>
          <div className="row mt-2">
            <p className="heading mb-0">{expertiseListName}</p>
          </div>
          <table className="table experts-list-section mt-2 ">
            <TableHead headerTitles={headerTitles} />
            <tbody className="custom-border">
              {expertsList && expertsList?.length > 0 ? (
                expertsList?.map((expert, index) => {
                  return (
                    <tr key={index} className="custom-row">
                      <td className="text-start purple-content pr-4">
                        {expert?.profile_photo ? (
                          <>
                            <Image
                              src={`${expert?.profile_photo}`}
                              alt={`Dr ${expert?.name}`}
                              width={35}
                              height={35}
                              className="expert_image"
                            />
                          </>
                        ) : (
                          <>
                            <Image
                              src={dummyProfile}
                              alt={`Dr ${expert?.name}`}
                              width={35}
                              height={35}
                              className="expert_image"
                            />
                          </>
                        )}
                        {/* <span>{expert?.name}</span> */}
                        <Link
                          href={`/usermanagement/experts/experts-doctor/${expert.id}/${expert.email}/${expert.approval}`}
                          className="expert-purple-content text-decoration-none"
                        >
                          <span>
                            {highlightText(
                              capitalizeFullName(expert?.name),
                              searchQuery
                            )}
                          </span>
                        </Link>
                      </td>
                      <td className="custom-font text-center ">
                        {expert?.expertiseNames?.length > 0 ? (
                          <>
                            {expert?.expertiseNames?.length > 1 ? (
                              <OverlayTrigger
                                placement="top"
                                delay={{ show: 250, hide: 400 }}
                                overlay={
                                  <Tooltip id="tooltip">
                                    {expert?.expertiseNames.slice(1).join(", ")}
                                  </Tooltip>
                                }
                              >
                                <span>
                                  {expert?.expertiseNames?.[0] || ""}
                                  <span className="expertise_count">
                                    +{expert?.expertiseNames?.length - 1}
                                  </span>
                                </span>
                              </OverlayTrigger>
                            ) : (
                              <span>{expert?.expertiseNames?.[0]}</span>
                            )}
                          </>
                        ) : (
                          "No specialization"
                        )}
                      </td>
                      <td className="custom-font text-center">
                        {expert?.DateOfRegistration?.split("T")[0]}
                      </td>
                      <td className="custom-font text-center">
                        {expert?.date_of_activation
                          ? expert?.date_of_activation?.split("T")[0]
                          : "No activation date"}
                      </td>
                      <td
                        className="custom-font  text-center fw-semibold"
                        style={{ color: getColor(expert?.approval) }}
                      >
                        {expert?.approval === "pending"
                          ? "Pending"
                          : expert?.approval === "Approval_requested"
                          ? "Approval Requested"
                          : expert?.approval === "Approved"
                          ? "Approved"
                          : expert?.approval === "Deactivated"
                          ? "Deactivated"
                          : expert?.approval === "Rejected"
                          ? "Rejected"
                          : expert?.approval === "self_deactivation"
                          ? "Self Deactivated"
                          : expert?.approval}
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="5">
                    <NoDataFound />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </>
  );
};
export default ExpertsList;
