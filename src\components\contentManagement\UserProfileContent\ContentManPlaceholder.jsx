import React from "react";
import { Placeholder, Table, Button } from "react-bootstrap";
import { MdEdit, MdDelete } from "react-icons/md";

const ContentManPlaceholder = () => {
  return (
    <div className="row">
      <div className="col-sm-4">
        <form>
          <div className="mb-3">
            {/* Placeholder for Type of Cancer Label */}
            <div className="mb-3">
              <Placeholder as="p" animation="glow">
                <Placeholder
                  xs={12}
                  size={"lg"}
                  style={{ height: "43px", borderRadius: "4px" }}
                />
              </Placeholder>
              {/* Placeholder for Type of Cancer Input */}
              <Placeholder as="p" animation="glow">
                <Placeholder
                  xs={12}
                  size={"lg"}
                  style={{ height: "43px", borderRadius: "4px" }}
                />
              </Placeholder>
            </div>
            <Button variant="primary" className="purple-button" disabled>
              <Placeholder as="span" animation="glow">
                <Placeholder xs={6} />
              </Placeholder>
            </Button>
          </div>
          <div className="mb-3">
            <label className="">
              <Placeholder as="p" animation="glow">
                <Placeholder xs={6} />
              </Placeholder>
            </label>
            <Placeholder as="div" animation="glow" className="" />
          </div>
        </form>
      </div>
      <div className="col-sm-8">
        <div className="table-wrapper">
          <Table>
            <thead className="sticky-table-head">
              <tr>
                <th scope="col" style={{ width: "60px" }}>
                  <Placeholder as="p" animation="glow">
                    <Placeholder xs={6} />
                  </Placeholder>
                </th>
                <th scope="col">
                  <Placeholder as="p" animation="glow">
                    <Placeholder xs={6} />
                  </Placeholder>
                </th>
                <th scope="col">
                  <Placeholder as="p" animation="glow">
                    <Placeholder xs={6} />
                  </Placeholder>
                </th>
                <th scope="col" style={{ width: "100px" }}>
                  <Placeholder as="p" animation="glow">
                    <Placeholder xs={6} />
                  </Placeholder>
                </th>
                <th scope="col" style={{ width: "100px" }}>
                  <Placeholder as="p" animation="glow">
                    <Placeholder xs={6} />
                  </Placeholder>
                </th>
              </tr>
            </thead>
            <tbody>
              {[1, 2, 3].map((_, index) => (
                <tr key={index}>
                  <th scope="row">
                    <Placeholder as="p" animation="glow">
                      <Placeholder xs={6} />
                    </Placeholder>
                  </th>
                  <td>
                    <Placeholder as="p" animation="glow">
                      <Placeholder xs={6} />
                    </Placeholder>
                  </td>
                  <td>
                    <Placeholder as="p" animation="glow">
                      <Placeholder xs={6} />
                    </Placeholder>
                  </td>
                  <td>
                    <Button variant="primary" className="btn-sm" disabled>
                      <MdEdit />
                    </Button>
                  </td>
                  <td>
                    <Button variant="danger" className="btn-sm" disabled>
                      <MdDelete />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default ContentManPlaceholder;
