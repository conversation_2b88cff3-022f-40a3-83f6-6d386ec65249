import React, { useCallback, useEffect, useRef, useState } from "react";
import { IoCloseSharp, IoSearchSharp } from "react-icons/io5";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import Link from "next/link";
import { useSession } from "next-auth/react";
import SearchLoader from "./searchLoader/SearchLoader";
import _ from "lodash";
import { highlightText } from "../../utils/helperfunction";

const SearchAll = () => {
  const [isListVisible, setIsListVisible] = useState(false);
  const [selectedExpertise, setSelectedExpertise] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const { data: session } = useSession();
  const axiosAuth = useAxiosAuth();
  const searchMenu = useRef(null);
  const admin_id = session?.user?.id;

  const fetchHomeData = useCallback(
    _.debounce(async (searchTerm) => {
      try {
        setLoading(true);
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_HOME_SEARCH}name=${searchTerm}&user_id=${admin_id}`
        );
        setSelectedExpertise(response?.data);
        setLoading(false);
      } catch (err) {
        console.log("Error in getting the searched data", err);
      } finally {
        setLoading(false);
      }
    }, 500),
    [admin_id, axiosAuth]
  );

  const closeOpenSearch = (e) => {
    if (isListVisible && !searchMenu.current?.contains(e.target)) {
      setIsListVisible(false);
    }
  };
  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeOpenSearch);
  }

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    fetchHomeData(e.target.value);
    setIsListVisible(true);
  };
  const handleSelectUser = (user) => {
    setIsListVisible(false);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setSelectedExpertise([]);
    setIsListVisible(false);
  };


  return (
    <div className="col-sm-5 position-relative">
      <div className="expert-searchbar d-flex align-items-center">
        <IoSearchSharp
          className="mx-2 fw-bolder"
          style={{ fontSize: "25px", color: "#7B009C" }}
        />
        <input
          value={searchTerm}
          onChange={handleSearchChange}
          type="text"
          className="form-control allsearchinput"
          placeholder="search any user ..."
          aria-label="Search"
          aria-describedby="search-btn"
        />
        {!loading && searchTerm && (
          <IoCloseSharp
            className="clear-search-icon position-absolute"
            style={{
              fontSize: "20px",
              cursor: "pointer",
              right: "30px",
              color: "#7B009C",
            }}
            onClick={handleClearSearch}
          />
        )}
        {loading && (
          <div className=" me-2">
            <SearchLoader />
          </div>
        )}
        {isListVisible && selectedExpertise?.length > 0 && (
          <div
            className=" dropdown-search-home"
            style={{ maxHeight: "300px", overflowY: "auto" }}
            ref={searchMenu}
          >
            <ul className=" users-search-list">
              {selectedExpertise &&
                selectedExpertise?.map((user, index) => (
                  <li className=" list-unstyled searched-user" key={index}>
                    <Link
                      onClick={() => handleSelectUser(user)}
                      className="dropdown-item text-capitalize"
                      href={
                        user?.user_role === "patient"
                          ? `/usermanagement/patients/${user?.id}/${user?.email}/${user?.approval}/${user?.name}`
                          : `/usermanagement/experts/experts-doctor/${user.id}/${user.email}/${user.approval}`
                      }
                    >
                      {highlightText(user.name, searchTerm)}
                    </Link>
                  </li>
                ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchAll;
