"use client";
import React, { useState, useEffect, useCallback } from "react";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import DashboardStExperts from "./DashboardStExperts";
import TotalAppReqRate from "./TotalAppReqRate";
import ExpertAppRate from "./ExpertAppRate";
import TotalExpertAppts from "./TotalExpertAppts";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import Loading from "../Loading/PageLoading/Loading";
import { useRouter } from "next/navigation";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { countUserRoles } from "../../utils/helperfunction";
import AllApprovalsRequested from "./AllApprovalsRequested";

ChartJS.register(ArcElement, Tooltip, Legend);
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const ExpertsComp = () => {
  const axiosAuth = useAxiosAuth();
  const [activeTab, setActiveTab] = useState("medical");
  const [unApprovedExperts, setUnApprovedExperts] = useState();
  const [allExperts, setAllExperts] = useState();
  const [allExpertsAppointmentsDetails, setAllExpertsAppointmentsDetails] =
    useState({});
  const [loading, setLoading] = useState(true);

  const [expertStatistics, setExpertStatistics] = useState({
    requests_approved: 0,
    under_review: 0,
    requests_pending: 0,
    requests_approved: 0,
    requests_deactivated: 0,
  });

  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();

  const { data: session } = useSession();
  const router = useRouter();

  const user_id = session?.user?.id;

  useEffect(() => {
    setActiveTab("medical");
  }, []);

  const approveExperts = useCallback(async () => {
    try {
      setLoading(true);
      const data = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_EXPERTS}all/all/?user_id=${user_id}`
      );
      setAllExpertsAppointmentsDetails(data?.data?.appointments_requests);

      const approvedExperts = data?.data?.experts_data?.filter(
        (user) => user.approval === "Approved"
      );
      setAllExperts(approvedExperts);

      const requestApprovalExpertsData = data?.data?.experts_data?.filter(
        (user) => user.approval === "Approval_requested"
      );
      const expertStatisticsData = data.data.doctor_no;

      setExpertStatistics({
        requests_approved: expertStatisticsData?.["total doctor number"],
        under_review: expertStatisticsData?.pending,
        requests_rejected: expertStatisticsData?.rejected,
        requests_approval: expertStatisticsData?.approved,
        requests_deactivated:
          expertStatisticsData?.deactivated +
          expertStatisticsData?.self_deactivated,
        requests_deleted: expertStatisticsData?.deleted,
      });
      const reversedExpertsData = requestApprovalExpertsData
        ?.slice()
        ?.reverse();

      const unApprovedDoctorsData =
        data?.data?.approval_requested_data?.doctor_approval_requests;
      const influencerRequests =
        data?.data?.approval_requested_data?.influencer_requests;
      const researcherRequests =
        data?.data?.approval_requested_data?.researcher_requests;

      // Combine all the arrays into a single array
      const allRequests = [
        ...unApprovedDoctorsData,
        ...influencerRequests,
        ...researcherRequests,
      ];

      setUnApprovedExperts(allRequests);
    } catch (err) {
      console.log("error in fetching ", err);
    } finally {
      setLoading(false);
    }
  }, [user_id, axiosAuth]);

  const countTotal = countUserRoles(allExperts);

  useEffect(() => {
    if (session && session?.user && session?.user?.id) {
      approveExperts();
    }
  }, [user_id, approveExperts, session]);

  const handleDateChange = (event, dateType) => {
    if (dateType === "startDate") {
      setStartDate(event.target.value);
    } else if (dateType === "endDate") {
      setEndDate(event.target.value);
    }
  };
  return (
    <>
      {loading == true ? (
        <>
          <Loading />
        </>
      ) : (
        // <div className="overflow-hidden">
        // <div className="expert-content-scroll overflow-auto">
        <div className="bg-color">
          <div className=" overflow-hidden">
            <div className="user-management-scroll overflow-auto">
              <div className="row">
                <div className="col-sm-4">
                  <div className="row mb-2">
                    <div className="col-sm-4">
                      <p className="heading">Experts Data</p>
                    </div>
                    <div className="col-sm-8">
                      <div className="d-flex expert-count-filter">
                        <div className="input-group mb-0 custom-form-select">
                          <input
                            type="date"
                            className="form-control search-input-focus d-flex justify-content-center align-content-center"
                            aria-label="Sizing example input"
                            aria-describedby="inputGroup-sizing-sm"
                            onChange={(event) =>
                              handleDateChange(event, "startDate")
                            }
                            value={startDate || "all"}
                          />
                        </div>

                        <div className="input-group mb-0 custom-form-select">
                          <input
                            type="date"
                            className="form-control search-input-focus d-flex justify-content-center align-content-center"
                            aria-label="Sizing example input"
                            aria-describedby="inputGroup-sizing-sm"
                            onChange={(event) =>
                              handleDateChange(event, "endDate")
                            }
                            value={endDate || "all"}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <DashboardStExperts
                    startDate={startDate}
                    endDate={endDate}
                    loading={loading}
                    countTotal={countTotal}
                  />
                  <TotalAppReqRate
                    allExpertsAppointmentsDetails={
                      allExpertsAppointmentsDetails
                    }
                  />
                  <ExpertAppRate chartDatas={expertStatistics} />
                </div>
                {/* *********************************************Total Expert Appointments********************************************** */}
                <TotalExpertAppts
                  allExpertsAppointmentsDetails={allExpertsAppointmentsDetails}
                />
                {/* **********************************************Buttons***************************************************************** */}
                <AllApprovalsRequested
                  unApprovedExperts={unApprovedExperts}
                  router={router}
                />
              </div>
            </div>
          </div>
        </div>
        //   </div>
        // </div>
      )}
    </>
  );
};

export default ExpertsComp;
