"use client";
import React, { useState } from "react";
import Setting from "../AllHead/SettingAndNotify";
import AdminAndUser from "../AllHead/AdimAndUser";
import NewAdmin from "../AllHead/NewAdmin";

const ChildAdminHead = () => {
  return (
    <>
      <div className="row mt-4">
        <div className="col-sm-8">
          <p className="purple-text">User Management - Child Admin</p>
        </div>
        <div className="col-sm-3">
          <div className="row">
            <div className="col-sm-6">
              <AdminAndUser />
            </div>
            <div className="col-sm-6 ms-auto">
              <NewAdmin />
            </div>
          </div>
        </div>
        <div className="col-sm-1">
          <Setting />
        </div>
      </div>
    </>
  );
};
export default ChildAdminHead;
