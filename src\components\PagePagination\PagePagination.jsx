// PagePagination.jsx

import React, { useState, useEffect } from "react";
import { BsFillCircleFill, BsFastForwardFill } from "react-icons/bs";
import { IoPlayBackSharp } from "react-icons/io5";
import "./pagepagination.css";

const PagePagination = ({
  setCurrentPage,
  currentPage,
  itemsPerPage,
  items,
  onPageChange,
}) => {
  const [totalPages, setTotalPages] = useState(1);

  const handleCircleClick = (page) => {
    onPageChange((prevPage) => {
      const newPage = page === -1 ? prevPage - 1 : prevPage + page;
      return newPage > 0 ? newPage : 1;
    });
  };

  useEffect(() => {
    if (itemsPerPage > 0 && items > 0) {
      setTotalPages(Math.ceil(items / itemsPerPage));
    }
  }, [items, itemsPerPage]);

  const prevPage = () => {
    const newPage = Math.max(currentPage - 1, 1);
    setCurrentPage(newPage);
  };

  const nextPage = () => {
    const newPage = Math.min(currentPage + 1, totalPages);
    setCurrentPage(newPage);
  };

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  return (
    <ul className="pagination justify-content-center custom-pagination">
      <li className="page-item">
        <span
          style={{ cursor: "pointer" }}
          className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
          onClick={prevPage}
        >
          <span className={`page-count ${currentPage === 1 ? "disabled" : ""}`}>
            Prev
          </span>
          <IoPlayBackSharp
            size={18}
            style={{
              color: "#96969C",
              fontSize: "10px",
              cursor: "pointer",
              fontWeight: "bold",
            }}
          />
        </span>
      </li>
      {totalPages >= 0 &&
        Array.from({ length: totalPages }).map((_, index) => (
          <li
            key={index}
            className={`page-item ${
              currentPage === index + 1 ? "active active-page" : ""
            }`}
            onClick={() => paginate(index + 1)}
          >
            <a className="page-link custom-page-link active-index" href="#">
              {index + 1}
            </a>
          </li>
        ))}

      <li className="page-item">
        <span
          className="page-link custom-page-link"
          onClick={nextPage}
        >
          <BsFastForwardFill style={{ color: "#96969C" }} /> &nbsp;
          <span className="page-count">Next</span>
        </span>
      </li>
    </ul>
  );
};

export default PagePagination;
