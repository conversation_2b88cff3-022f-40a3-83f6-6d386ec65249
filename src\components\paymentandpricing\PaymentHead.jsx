"use client";
import React, { useState } from "react";
import { FiSettings } from "react-icons/fi";
import { LuBell } from "react-icons/lu";
import AdminAndUser from "../AllHead/AdimAndUser";
import NewAdmin from "../AllHead/NewAdmin";
import Link from "next/link";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import { useNotification } from "../../Context/NotificationContext/NotificationContext";
import { useSession } from "next-auth/react";
import { Badge, Placeholder } from "react-bootstrap";
import BellIconNotification from "../BellIconNotification/BellIconNotification";

const PaymentHead = ({ heading }) => {
  const { isAdminChildAdmin } = useAdminContext();
  const { totalNotifications, notificationLoading } = useNotification();
  const { data: session } = useSession();
  const authenticated = session?.status === "authenticated";
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const toggleNotificationPopup = () => {
    setIsNotificationOpen((prevState) => !prevState); // Toggle the visibility of the notification popup
  };
  return (
    <>
      <div className="row mt-3">
        <div className="col-sm-8">
          <p className="main-purple-text">{heading}</p>
        </div>
        <div className=" col-sm-3">
          <div className="row">
            <div className="col-sm-6 ms-auto">
              {!isAdminChildAdmin && <NewAdmin />}
            </div>
            <div className="col-sm-6">
              <AdminAndUser />
            </div>
          </div>
        </div>
        <div className="col-sm-1 d-flex justify-content-around align-items-center">
          <Link className=" text-decoration-none" href={"/profilesetting"}>
            <FiSettings className="icon-setting" />
          </Link>

          <span className="nav-item d-none d-xl-block">
            <span
              onClick={(e) => {
                e.stopPropagation(); // Stop the propagation of the click event
                toggleNotificationPopup(); // Toggle the notification popup
              }}
              id="notification-link"
              style={{
                position: "relative",
                display: "inline-block",
              }}
            >
              <span className="btn btn-bell">
                <LuBell className="icon-bell" cursor={"pointer"} />
                <span className="start-100 translate-middle badge rounded-pill text-center">
                  {totalNotifications ? totalNotifications : 0}
                  <span className="visually-hidden">unread messages</span>
                </span>
              </span>

              {authenticated && (
                <>
                  {notificationLoading ? (
                    <Placeholder
                      as="div"
                      animation="glow"
                      className="notification-badge-placeholder"
                      style={{ width: "20px" }}
                    >
                      <Placeholder
                        xs={12}
                        size={"sm"}
                        style={{
                          height: "20px",
                          borderRadius: "5px",
                        }}
                      />
                    </Placeholder>
                  ) : (
                    <Badge bg="danger" className="notification-badge">
                      {/* {totalNotifications > 100 ? "100+" : totalNotifications} */}
                      {totalNotifications ? totalNotifications : 0}
                    </Badge>
                  )}
                </>
              )}
            </span>
          </span>
          {isNotificationOpen && (
            <BellIconNotification
              isNotificationOpen={isNotificationOpen}
              onClose={toggleNotificationPopup}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default PaymentHead;
