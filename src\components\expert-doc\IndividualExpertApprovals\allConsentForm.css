.viewConsent-modal-width {
  max-width: 70%;
}
.model_title_doctor_consent > div {
  color: #9d2ba5;
}


.consentform-custom-overflow {
  max-height: 900px;
  overflow-x: hidden;
}
.consentform-custom-overflow::-webkit-scrollbar {
  display: none;
}
.docPat-consent-form-box {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 12px;
  background-color: white;
  /* border: 1px solid red; */
  width: 98%;
}
.bg-heading {
  background: transparent linear-gradient(180deg, #9d2ba5 0%, #ca00d9 100%) 0%
    0% no-repeat padding-box;
  border-radius: 5px;
}

.heading {
  color: white;
  font-size: 17px;
}

.consent-custom-overflow {
  max-height: 600px;
}

.content-scroll::-webkit-scrollbar {
  display: none;
}
.consent-addContent {
  background-color: #fcebfc;
  padding: 30px;
}
.form-control.custom-form-placeholder::placeholder {
  font-weight: 500;
  font-size: 14px;
  color: black;
}
.custom-form-label {
  letter-spacing: -0.15px;
  color: #4a2f91;
  font-size: 17px;
  font-weight: 400;
}
input.form-control.custom-form-control {
  border-radius: 5px;
  background-color: #f2f2f2;
  border: none;
}
input.form-control.custom-form-control:focus {
  border-radius: 5px;
  background-color: #f2f2f2;
  border: none;
  box-shadow: none;
}
.custom-para {
  letter-spacing: -0.13px;
  font-size: 14px;
}
button.btn.orange-btn,
button.btn.orange-btn:hover,
button.btn.orange-btn:active,
button.btn.orange-btn:focus {
  background-color: #f37721;
  border-radius: 16px;
  color: white;
  font-size: 12px;
  padding-right: 20px;
  padding-left: 20px;
  border: 1px solid #f37721;
}
button.btn.orange-btn:disabled {
  opacity: 0.5;
}

.user_signature {
  pointer-events: none;
}

@media screen and (min-width: 576px) {
}
@media screen and (max-width: 768px) {
}
@media screen and (min-width: 768px) {
}
@media screen and (max-width: 1200px) {
  .viewConsent-modal-width {
    max-width: 100%;
  }
  .bg-color {
    background-color: transparent;
    padding: 0px;
  }
  .bg-heading {
    background: transparent linear-gradient(180deg, #fdf5ff 0%, #fcfcfc 100%) 0%
      0% no-repeat padding-box;
    border-radius: 0px 0px 5px 5px;
    padding: 10px;
  }

  .heading {
    letter-spacing: 0px;
    color: #a100d3;
    font-size: 14px;
    font-weight: 500;
  }
  .custom-form-label {
    color: #5c0092;
    font-size: 16px;
    font-weight: 500;
  }
}

/* ================ modal================== */
.allApproval-modal-heading {
  font-size: 18px;
  color: #5b5b5b;
  font-weight: 600;
}
