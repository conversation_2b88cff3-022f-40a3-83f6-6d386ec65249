"use client";

import React, { useEffect, useState } from "react";
import ProgressBar from "react-bootstrap/ProgressBar";
import "./count.css";
import { IoIosInformationCircle } from "react-icons/io";
import { Tooltip } from "react-tooltip";

const CountdownProgressBar = ({
  starttime,
  meetingStatus,
  current_status,
  status,
  hasPatientQuery,
  hasDoctorReply,
  appointmentCancelReason,
}) => {
  const cancelledApp = status === "C";
  const cancelledAppPending = status === "C_P";
  const cancelledAppRejected = status === "C_R";
  const appRescheduled = status === "R";
  const [time, setTime] = useState({
    total: 0,
    days: 0,
    hours: 0,
    minutes: 0,
  });

  useEffect(() => {
    if (meetingStatus === 2) {
      const endtime = new Date(Date.parse(starttime) + 3 * 24 * 60 * 60 * 1000);
      const totalDuration = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds

      const interval = setInterval(() => {
        const t = Date.parse(endtime) - Date.parse(new Date());
        if (t <= 0) {
          clearInterval(interval);
          setTime({
            total: 0,
            days: 0,
            hours: 0,
            minutes: 0,
          });
        } else {
          const minutes = Math.floor((t / 1000 / 60) % 60);
          const hours = Math.floor((t / (1000 * 60 * 60)) % 24);
          const days = Math.min(Math.floor(t / (1000 * 60 * 60 * 24)), 3);
          setTime({
            total: t,
            days: days,
            hours: hours,
            minutes: minutes,
          });
        }
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setTime({
        total: 0,
        days: 0,
        hours: 0,
        minutes: 0,
      });
    }
  }, [starttime, meetingStatus]);

  const totalDuration = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds
  // const progress = (totalDuration - time.total) / totalDuration * 100;
  const progress = (time.total / totalDuration) * 100;

  const label =
    meetingStatus === 2
      ? time.total <= 0 || hasPatientQuery
        ? "Completed"
        : `${time.days}D, ${time.hours}H, ${time.minutes}M Left`
      : cancelledApp
      ? "Cancelled"
      : cancelledAppPending
      ? "Cancellation Pending"
      : cancelledAppRejected
      ? "Cancellation Rejected"
      : appRescheduled && current_status === "Upcoming"
      ? "Upcoming/Rescheduled"
      : current_status === "Unattended"
      ? "Unattended"
      : current_status;
  // const label =
  //   meetingStatus === 2
  //     ? time.total <= 0 || hasPatientQuery
  //       ? "Completed"
  //       : // ? '0D, 0H, 0M remaining'
  //         `${time.days}D, ${time.hours}H, ${time.minutes}M Left`
  //     : cancelledApp
  //     ? "Cancelled"
  //     : current_status;

  return (
    <div className="progress-bar-container">
      {meetingStatus === 2 && label !== "Completed" ? (
        <>
          <ProgressBar
            animated
            now={progress}
            className={` activate-progress-bar ${
              label === "Completed"
                ? "colored-completed-btn"
                : label === "Ongoing"
                ? "colored-ongoing-btn"
                : label === "Upcoming"
                ? "colored-upcoming-btn"
                : label === "Cancelled"
                ? "colored-cancelled-btn"
                : ""
            }`}
          />
          <div className="progress-bar-label">{label}</div>
        </>
      ) : (
        <div
          className={`progress-button-status ${
            label === "Completed"
              ? "colored-completed-btn"
              : label === "Ongoing"
              ? "colored-ongoing-btn"
              : label === "Upcoming"
              ? "colored-upcoming-btn"
              : label === "Expired"
              ? "colored-expired-btn"
              : label?.includes("Cancellation") ||
                label === "Cancelled" ||
                label === "Cancellation Pending" ||
                label === "Cancellation Rejected"
              ? "colored-cancelled-btn"
              : label === "Upcoming/Rescheduled"
              ? "colored-rescheduled-btn"
              : label === "Unattended"
              ? "colored-rescheduled-btn"
              : ""
          }`}
        >
          {label}
          {label === "Cancellation Rejected" && (
            <span>
              <a
                data-tooltip-id="my-tooltip"
                data-tooltip-content={appointmentCancelReason}
              >
                <IoIosInformationCircle />
              </a>
              <Tooltip
                style={{
                  width: "300px",
                  boxShadow: "0px 3px 6px #00000018",
                  padding: "20px",
                  lineHeight: "25px",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
                id="my-tooltip"
              />
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default CountdownProgressBar;
