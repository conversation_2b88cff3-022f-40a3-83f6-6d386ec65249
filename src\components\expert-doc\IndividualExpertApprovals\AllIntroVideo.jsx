import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import ReactPlayer from "react-player";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import nodataFound from "../../../../public/images/nodata.png";
import Swal from "sweetalert2";
import { Button, Modal } from "react-bootstrap";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useParams } from "next/navigation";
import { debounce } from "lodash";

const renderLabel = (index = 1) => {
  if (index === 1) {
    return <span className="badge bg-warning">Pending</span>;
  } else if (index === 2) {
    return <span className="badge bg-success">Approved</span>;
  } else if (index === 3) {
    return <span className="badge bg-danger">Rejected</span>;
  }
};

const AllIntroVideo = () => {
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [enableTextBox, setEnableTextBox] = useState(false);
  const [doctorDetails, setDoctorDetails] = useState({});

  const params = useParams();
  const doctor_id =
    params && params?.user_id?.length > 0 && params?.user_id[0]
      ? params?.user_id[0]
      : "";
  const doctor_email1 =
    params && params?.user_id?.length > 0 && params?.user_id[1]
      ? params?.user_id[1]
      : "";

  const doctor_email = decodeURIComponent(doctor_email1);

  const axiosAuth = useAxiosAuth();

  const handleEnableTextbox = () => {
    setEnableTextBox(true);
  };

  const fetchDoctorDetails = useCallback(
    async (doctor_email) => {
      try {
        const response = await axiosAuth.get(
          `${process.env.NEXT_PUBLIC_GET_DOCTOR_DATA}${doctor_email}/`
        );
        setDoctorDetails(response?.data?.user_data);
      } catch (error) {
        console.log(error);
      }
    },
    [axiosAuth]
  );

  const handleIntroVideoStatus = async (status) => {
    let intro_Status;

    if (status === "reject") {
      intro_Status = 3;
    } else if (status === "approve") {
      intro_Status = 2;
    }

    try {
      setIntroVideoRejAppLoading(true);
      let dataToSend = { IntroVideoStatus: intro_Status };

      // Include reason only if status is reject
      if (status === "reject") {
        dataToSend.IntroVideo_Reason = videoRejectReason;
      }

      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CU_DOCTOR_INTRO_VIDEO_STATUS_BY_ADMIN}${doctor_id}/?user_id=${admin_id}`,
        dataToSend
      );

      const { message } = response.data;

      if (message?.IntroVideoStatus === 2) {
        toast.success("Doctor Intro Video Approved Successfully", {
          theme: "colored",
          position: "top-center",
          autoClose: 3500,
        });
        setShowArticleModal(false);
      } else if (message?.IntroVideoStatus === 3) {
        toast.success("Doctor Intro Video Rejected Successfully", {
          theme: "colored",
          autoClose: 3500,
          position: "top-center",
        });
        setShowArticleModal(false);
      } else if (
        message === "Only pending videos can be approved or rejected"
      ) {
        toast.info(message, {
          theme: "colored",
          autoClose: 3500,
          position: "top-center",
        });
      } else {
        toast.info(response.data, {
          theme: "colored",
          autoClose: 3500,
          position: "top-center",
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIntroVideoRejAppLoading(false);
      fetchDoctorDetails(doctor_email);
      setEnableTextBox(false);
    }
  };

  const debouncedHandleIntroVideoStatus = debounce(
    handleIntroVideoStatus,
    1000
  );

  const handleVideoRejection = () => {
    debouncedHandleIntroVideoStatus("reject");
  };

  useEffect(() => {
    if (doctor_email) {
      fetchDoctorDetails(doctor_email);
    }
  }, [doctor_email, fetchDoctorDetails]);

  const introVideos = doctorDetails?.doctor_other_details?.IntVideoUrl || [];

  const introVideoStatus =
    doctorDetails?.doctor_other_details?.IntroVideoStatus;
  return (
    <>
      <div>

        <form>
            <div className="intro-video pe-0">
              <div className="d-flex justify-content-around align-items-center mt-4">
                {introVideos?.length > 0 &&
                  introVideoStatus >= 1 &&
                  introVideoStatus <= 3 && (
                    <>
                    <div className="row">
                    <div className="col-sm-6">
                      <div>
                        <input
                          type="checkbox"
                          checked={selectedVideo === 0}
                          onChange={() => {
                            if (selectedVideo === 0) {
                              setSelectedVideo(-1); // Unselect if already selected
                            } else {
                              setSelectedVideo(0); // Select if not selected
                            }
                          }}
                          disabled={
                            introVideos?.length == 1
                              ? false
                              : true || introVideoStatus == 2
                              ? false
                              : true
                          }
                        />
                        &nbsp;
                        <label>
                          Intro Video 1{" "}
                          {renderLabel(
                            introVideos?.length == 1 && introVideoStatus == 1
                              ? 1
                              : introVideos?.length == 1 &&
                                introVideoStatus == 2
                              ? 2
                              : introVideos?.length == 1 &&
                                introVideoStatus == 3
                              ? 3
                              : (introVideos?.length == 2 &&
                                  introVideoStatus == 1) ||
                                introVideoStatus == 2 ||
                                introVideoStatus == 3
                              ? 2
                              : 1
                          )}
                        </label>
                        <div
                          style={{
                            position: "relative",
                            width: "100%",
                            height: "auto",
                          }}
                        >
                          <ReactPlayer
                            url={introVideos[0]}
                            controls
                            width="400px"
                            height={"254px"}
                          />
                          <Image
                            src={CU_logoImage}
                            width={90}
                            height={50}
                            alt="cu logo"
                            style={{
                              position: "absolute",
                              top: "15px",
                              left: "8px",
                              zIndex: 1,
                            }}
                          />
                        </div>
                      </div>
                      &nbsp;
                      <div>
                        {introVideos.length > 1 && (
                          <>
                            <input
                              type="checkbox"
                              checked={selectedVideo === 1}
                              onChange={() => {
                                if (selectedVideo === 1) {
                                  setSelectedVideo(-1); // Unselect if already selected
                                } else {
                                  setSelectedVideo(1); // Select if not selected
                                }
                              }}
                              disabled={
                                selectedVideo === null &&
                                selectedVideo === -1 &&
                                introVideoStatus === 2
                              }
                            />
                            &nbsp;
                            <label>
                              Intro Video 2 {renderLabel(introVideoStatus)}
                            </label>
                            <div
                              style={{
                                position: "relative",
                                width: "100%",
                                height: "auto",
                              }}
                            >
                              <ReactPlayer
                                url={introVideos[1]}
                                controls
                                width="400px"
                                height={"254px"}
                              />
                              <Image
                                src={CU_logoImage}
                                width={90}
                                height={50}
                                alt="cu logo"
                                style={{
                                  position: "absolute",
                                  top: "15px",
                                  left: "8px",
                                  zIndex: 1,
                                }}
                              />
                            </div>
                            {/* <ReactPlayer
                                      url={introVideos[1]}
                                      controls
                                      width="100%"
                                    /> */}
                          </>
                        )}
                      </div>
                      </div>
                      </div>
                    </>
                  )}
              </div>
            </div>
        
        </form>
      </div>
      {enableTextBox && (
        <Modal
          show={enableTextBox}
          onHide={() => {
            setEnableTextBox(false);
          }}
          size="lg"
          centered
        >
          <Modal.Header closeButton>
            <Modal.Title style={{ color: "#8107d1", fontWeight: 600 }}>
              Reject Intro Video {selectedVideo + 1}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="row p-3">
              <label className="light-grey-text">Reason</label>
              <textarea
                className="text-area"
                value={videoRejectReason}
                disabled={!enableTextBox}
                onChange={(event) => setVideoRejectReason(event.target.value)}
                style={{ height: calculateTextAreaHeight() }}
              />
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              variant="secondary"
              onClick={() => {
                setEnableTextBox(false);
              }}
            >
              Close
            </Button>
            <button
              type="submit"
              className="btn btn-purple"
              disabled={!enableTextBox}
              onClick={(e) => {
                e.preventDefault();
                handleVideoRejection();
              }}
            >
              {introVideoRejAppLoading ? "Submitting.." : "Submit"}
            </button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

export default AllIntroVideo;
