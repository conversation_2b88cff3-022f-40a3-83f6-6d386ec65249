// ApprovalComponent.js
import React from "react";

const ApprovalComponent = () => {
  return (
    <div className="container padding-bottom-3x mb-1">
      <div className="card custom-card">
        <div className="card-body">
          <div className="steps d-flex flex-wrap flex-sm-nowrap justify-content-between padding-top-2x padding-bottom-1x">
            {/* Create individual step components */}
            <div className="step completed">
              <div className="step-icon-wrap">
                <div className="step-icon">1</div>
              </div>
              <h4 className="step-title">Profile Verification</h4>
            </div>

            <div className="line"></div>
            <div className="step completed">
              <div className="step-icon-wrap">
                <div className="step-icon">2</div>
              </div>
              <h4 className="step-title">Processing</h4>
            </div>
            <div className="line"></div>
            <div className="step completed">
              <div className="step-icon-wrap">
                <div className="step-icon">3</div>
              </div>
              <h4 className="step-title">Pre Approval</h4>
            </div>
            <div className="line"></div>
            <div className="step">
              <div className="step-icon-wrap">
                <div className="step-icon">4</div>
              </div>
              <h4 className="step-title">Approved</h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApprovalComponent;
