import Image from "next/image";
import React from "react";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { formatDate, timeDifference } from "./db.js";
import NoDataFound from "../noDataFound/NoDataFound.jsx";

const ConsentFormApprovals = ({
  doctorConsentLoading,
  doctorsConsent,
  renderPlaceholders,
  doctorsData,
  noDtaError,
  handleConsentFormModal,
  setshowConsentFormModal,

}) => {
  return (
    <>
      {doctorConsentLoading === false ? (
        <>
          {noDtaError ? (
            <div className="custom-margin-nodatafoud">
              <NoDataFound />
            </div>
          ) : doctorsConsent &&
            Array.isArray(doctorsConsent?.data) &&
            doctorsConsent?.data?.length > 0 ? (
            <div className="overflow-hidden">
              <div className="overflow-auto allApproval-tab-scroll">
                <div className="row">
                  <div className="col-6">
                    <div className="d-flex justify-content-between align-items-start bg-grey upload-reviews upload-reviews">
                      <div className="col-sm-1">
                        {doctorsData?.doctor_other_details?.ProfilePhoto ? (
                          <Image
                            src={
                              doctorsData?.doctor_other_details?.ProfilePhoto
                            }
                            alt="feedback"
                            height={35}
                            width={35}
                            className="testimonial_image"
                          />
                        ) : (
                          <Image
                            src="/images/profile.png"
                            alt="fallback"
                            height={35}
                            width={35}
                            className="testimonial_image"
                          />
                        )}
                      </div>
                      <div className="col-sm-2">
                        <p className="fw-semibold mb-0">
                          {formatDate(
                            doctorsConsent?.data?.[0]?.DateOfConsentForm
                          )}
                        </p>
                        <p className="custom-transperent-btn">
                          {timeDifference(
                            doctorsConsent?.data?.[0]?.DateOfConsentForm
                          )}
                        </p>
                      </div>
                      <div className="col-sm-9">
                        <button
                          type="button"
                          className="btn btn-yellow"
                          onClick={() => handleConsentFormModal()}
                        >
                          Review
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
              <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                No consent forms available for approval.
              </h3>
            </div>
          )}
        </>
      ) : (
        renderPlaceholders()
      )}
    </>
  );
};

export default ConsentFormApprovals;
