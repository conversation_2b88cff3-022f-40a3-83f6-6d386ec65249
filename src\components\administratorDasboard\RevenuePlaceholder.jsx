import React from "react";
import { Placeholder } from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";

const RevenuePlaceholder = () => {
  const selectStyle = { height: "30px", width: "100%", borderRadius: "8px" };
  const chartStyle = { height: "300px", width: "100%", borderRadius: "8px" };

  return (
    <div className="">
      <div className="year-navigation mt-2 ">
        {/* Placeholder for the Total Revenue title */}
        <Placeholder as="div" animation="glow">
          <Placeholder
            className="fs-5"
            style={{ width: "150px", height: "30px",borderRadius:'8px' }}
          />
        </Placeholder>

        <div className="year-select-chart">
          {/* Placeholder for the select input */}
          <Placeholder as="div" animation="glow">
            <Placeholder style={selectStyle} />
          </Placeholder>
        </div>
      </div>

      <div className="p-5">
        {/* Placeholder for the chart */}
        <Placeholder as="div" animation="glow">
          <Placeholder style={chartStyle} />
        </Placeholder>
      </div>
    </div>
  );
};

export default RevenuePlaceholder;
