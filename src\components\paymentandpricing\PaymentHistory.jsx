"use client";
import React, { useCallback, useEffect, useState } from "react";
import InvoiceModal from "./InvoiceModal";
import RefundRequest from "../paymentandpricing/RefundRequest";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import PaymentHistoryBody from "./PaymentHistoryBody";
import CustomPagination from "../CustomPagination/CustomPagination";
import { toast } from "react-toastify";

const PaymentHistory = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [allPayments, setAllPayments] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState({});
  const [loading, setLoading] = useState(false);
  const axiosAuth = useAxiosAuth();

  const fetchPayments = useCallback(
    async (pageNumber) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_FETCH_ALL_PAYMENTS}all/?page=${currentPage}&per_page=8`;

        if (pageNumber) {
          url += `&page=${pageNumber}`;
        }
        const response = await axiosAuth.get(url);
        setAllPayments(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (err) {
        toast.error("error in fetching payments");
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [axiosAuth, currentPage]
  );

  useEffect(() => {
    fetchPayments(currentPage);
  }, [fetchPayments, currentPage]);

  const handleViewInvoiceModal = (payment) => {
    setSelectedPayment(payment);
    setShowInvoiceModal(true);
  };

  return (
    <>
      <InvoiceModal
        showInvoiceModal={showInvoiceModal}
        setShowInvoiceModal={setShowInvoiceModal}
        selectedPayment={selectedPayment}
      />
      <div className="">
        <div className="row">
          <div className="col-sm-8">
            {/* <h3 className="pay-history">Payments history</h3>
            <hr className="line-horizontal" /> */}
            <div className="row">
              <div className="col-sm-12">
                <table className="table table-containern mt-3">
                  <thead className="sticky-header">
                    <tr className="header-color">
                      <th>Name</th>
                      <th>Appointment Id</th>
                      <th>Status</th>
                      <th>Amount</th>
                      <th>Date</th>
                      <th>View Invoice</th>
                    </tr>
                  </thead>
                  <PaymentHistoryBody
                    initialLoading={initialLoading}
                    loading={loading}
                    allPayments={allPayments}
                    handleViewInvoiceModal={handleViewInvoiceModal}
                  />
                </table>
                {allPayments?.total_pages !== 1 && (
                  <div className="pb-2">
                    <CustomPagination
                      total_pages={allPayments?.total_pages}
                      current_page={currentPage}
                      setCurrent_Page={setCurrentPage}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="col-sm-4 gx-5">
            <RefundRequest />
          </div>
        </div>
      </div>
    </>
  );
};
export default PaymentHistory;
