button.btn.btn-preview {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  color: #8107d1;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
}
.preview-content {
  background: #fbfbfb;
  border-radius: 3px;
  width: 100%;
  min-height: 692px;
  height: 100%;
  /* display: flex;
    justify-content: center;
    align-items: center;
    text-align: center; */
  padding: 10px;
}

.nopreview {
  color: #dfdfdf;
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  /* Optionally, you can use text-align for horizontal centering */
}

button.btn.btn-preview:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.purple-text {
  color: #8107d1;
  /* font-size: 21px; */
  font-weight: 600;
}

button.btn.btn-newuser {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffffff;
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
button.btn.btn-newuser:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffffff;
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

button.btn.btn-newadmin {
  background: #ff7700 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  width: 100%;
  color: #ffff;
}
button.btn.btn-newadmin:focus {
  background: #ff7700 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  width: 100%;
  color: #ffff;
}

button.btn.btn-back {
  color: #8107d1;
  font-size: 18px;
  font-weight: 500;
  border: none;
}

button.btn.btn-back:focus {
  color: #8107d1;
  font-size: 18px;
  font-weight: 500;
  border: none;
}

.icon-setting {
  font-size: 35px;
  color: #8107d1;
}

.icon-bell {
  stroke-width: 2px;
  font-size: 37px;
  color: #8107d1;
}

button.btn.btn-bell {
  position: relative;
}

span.start-100.translate-middle.badge.rounded-pill {
  border: 2px solid #6d00b5;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 2px 10px #ababab4a;
  position: absolute;
  margin-top: 6px;
  margin-left: -21px;
  top: 0;
  padding-left: 3px;
  height: 61%;
  color: #7100bb;
  width: 50%;
  padding-top: 8px;
  padding-right: -38px;
}

button.btn.btn-arrows {
  border: 0;
  background: none;
  color: #7d8185;
}

button.btn.btn-arrows:focus {
  border: 0;
  background: none;
}

button.btn.btn-arrows:hover {
  border: 0;
  background: none;
}

button.btn.btn-arrows.active {
  border: 0;
  background: none;
}

span.rbc-toolbar-label.month-color {
  color: #343434;
  font-size: 16px;
  font-weight: bold;
}

.yaer-cal {
  color: #343434;
  font-size: 16px;
  font-weight: bold;
}

.year-annual {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029, 0px 3px 6px #00000029;
  border: 1px solid #8107d1;
  border-radius: 3px;
  color: #ffff;
  text-align: center;
  padding: 6px 34px 6px 31px;
}

span.year-flow {
  position: relative;
}

.selector-calendar {
  border: 1px solid #ededed;
  padding: 5px 30px 5px 30px;
  border-radius: 10px;
}

.rbc-toolbar {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
  font-size: 16px;
}

.rbc-header {
  padding: 0 3px;
  height: 62px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 200;
  font-size: 14px;
  min-height: 0;
  color: #6b6a6a;
}

.rbc-month-view {
  border: 1px solid #e7e7e7;
  border-radius: 12px 12px 0 0;
}

.rbc-header {
  padding: 0 3px;
}

.rbc-toolbar {
  display: contents;
  justify-content: flex-start;
  align-items: start;
}

.year-col {
  flex: 0 0 auto;
  width: 33.33333333%;
  display: flex;
  justify-content: flex-end;
}

.calendar-back {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
  padding: 0 30px 10px 30px;
}

.color-blue {
  color: #100db1;
}

.color-yellow {
  color: #ff971a;
}

.color-purple {
  color: #763cef;
}

.color-red {
  color: #f80d38;
}

.color-identification-back {
  height: 70px;
  border-radius: 0 0 12px 12px;
  max-height: 100%;
  background: #f7f7f7 0% 0% no-repeat padding-box;
  font-size: 12px;
  color: #656565;
  display: flex;
  justify-content: flex-start;
  text-align: center;
  align-items: center;
}

.rbc-event,
.rbc-day-slot .rbc-background-event {
  margin: 0;
  padding: 2px 5px;
  background: #763cef 0% 0% no-repeat padding-box;
  border: 1px solid #e7e7e7;
  background-color: transparent;
  border-radius: 5px;
  color: #fff;
}

.rbc-button-link {
  padding: 14px 20px 0 0;
}

.rbc-day-bg.rbc-today {
  background-color: #ffff;
}

button.rbc-button-link {
  color: #343434;
  padding: 15px 20px;
}

.rbc-day-bg.rbc-off-range-bg {
  background: #f7f7f7 0% 0% no-repeat padding-box;
}

.bOPcbp:not(.using-icon).active {
  background: #ff971a;
  border: transparent;
}
.TimelinePointWrapper-sc-12rz3g8-0.egilaT.right {
  align-items: stretch; /* or simply remove this line */
}
.cfyYup:not(.using-icon).active {
  background: #ff971a;
  border: transparent;
}

img.profile-refund {
  width: 92%;
  height: 96%;
  object-fit: contain;
  padding: 8px 0 0 18px;
}

.row.row-payment {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 3px;
  padding: 18px;
  /* height: 95%; */
}

.refund-apply {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  /* height: 211px; */
}

.patient-name-refund {
  color: #343434;
  font-size: 16px;
  font-weight: bold;
}

.patient-represent {
  color: #100db1;
  font-size: 16px;
  font-weight: 400;
}

.refund-amount {
  color: #343434;
  font-size: 14px;
  font-weight: bold;
  padding-left: 12px;
}

button.btn.btn-renew {
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  width: 100%;
}

button.btn.btn-renew:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: #ffff;
  width: 100%;
}

span.doctor-name-payment {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  /* top: 10px; */
  margin-top: 20px;
}

textarea.form-control.reason-textarea {
  border: none;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  height: 70px;
}
textarea.form-control.reason-textarea:focus {
  box-shadow: none;
}

label.reason-text {
  position: absolute;
  padding: 5px;
}

textarea.form-control.expire-textarea {
  border: none;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  height: 100px;
}
textarea.form-control.expire-textarea:focus {
  box-shadow: none;
}
/* ***********No preview************* */

.inner-content {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  border-radius: 5px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 15px 0px 15px 0px;
  cursor: pointer;
}
.card-name {
  letter-spacing: 0px;
  color: #414146;
  font-size: 14px;
  font-weight: 600;
}
.expert-type {
  letter-spacing: 0px;
  color: #100db1;
  opacity: 1;
  font-size: 14px;
}
.type-patient {
  letter-spacing: 0px;
  color: #96969c;
  opacity: 1;
  font-size: 14px;
}
img.profile-img-expert {
  object-fit: cover;
  border-radius: 50%;
  /* width: 40%; */
  /* height: 31%; */
  margin-left: 10%;
  margin-right: 4%;
}

.active-approval {
  float: right;
}

.rbc-overlay {
  overflow-y: auto;
  max-height: 200px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.rbc-overlay::-webkit-scrollbar {
  display: none;
}
