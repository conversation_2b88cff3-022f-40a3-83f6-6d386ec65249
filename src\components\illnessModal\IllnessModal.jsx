import React from "react";
import { Modal } from "react-bootstrap";

const IllnessModal = ({
  showInnerModal,
  setShowInnerModal,
  prescription_data,
}) => {
  return (
    <Modal
      show={showInnerModal}
      onHide={() => setShowInnerModal(false)}
      dialogClassName="modal-dialog modal-lg modal-dialog-scrollable "
    >
      <Modal.Header>
        <h1 className="modal-title fs-5 purple-content">Existing Treatment</h1>
        <button
          type="button"
          className="btn-close"
          onClick={() => setShowInnerModal(false)}
        />
      </Modal.Header>
      <Modal.Body>
        <textarea
          id="editable-paragraph"
          className="form-control"
          rows="100"
          placeholder="Existing Treatment"
          value={prescription_data}
        ></textarea>
      </Modal.Body>
      {/* <Modal.Footer>
        <button
          type="button"
          className="btn btn-secondary"
          onClick={() => setShowInnerModal(false)}>
          close
        </button>
      </Modal.Footer> */}
    </Modal>
  );
};

export default IllnessModal;
