import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import ReactPlayer from "react-player";
import CU_logoImage from "../../../../public/images/adminbglogo.png";
import nodataFound from "../../../../public/images/nodata.png";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import Swal from "sweetalert2";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { capitalizeFullName } from "../../../utils/helperfunction";
import IntroVideoPlaceholder from "./IntroVideoPlaceholder";
import NoDataFound from "@/components/noDataFound/NoDataFound";
import { FaVideoSlash } from "react-icons/fa6";
import CustomPagination from "@/components/CustomPagination/CustomPagination";

const AllIntroVideo = () => {
  const [expertsIntroVideo, setExpertsIntroVideo] = useState([]);
  const [loading, setLoading] = useState(true);
  const axiosAuth = useAxiosAuth();
  const [noDtaError, setNoDtaError] = useState(false);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { data: session } = useSession();
  const admin_id = session?.user?.id;

  const fetchIntroVideo = useCallback(async () => {
    try {
      setLoading(true);
      const exportIntroVideoResponse = await axiosAuth.get(
        `${process.env.NEXT_PUBLIC_GET_ALL_EXPERT_INTRO_VIDEO}all/?user_id=${admin_id}&page=${current_page}`
      );
      setExpertsIntroVideo(exportIntroVideoResponse?.data?.items);
      setTotalPages(exportIntroVideoResponse?.data?.total_pages);
    } catch (error) {
      console.error(error);
      setNoDtaError(true);
    } finally {
      setLoading(false);
    }
  }, [axiosAuth, admin_id, current_page]);

  useEffect(() => {
    if (admin_id) {
      fetchIntroVideo();
    }
  }, [fetchIntroVideo, admin_id, current_page]); // Remove extra fetchIntroVideo call

  // useEffect(() => {
  //   if (admin_id) {
  //     fetchIntroVideo();
  //   }
  // }, [fetchIntroVideo, admin_id, current_page]);

  const handleIntroVideoStatus = async (status, doctor_id) => {
    let intro_Status;

    if (status === "reject") {
      intro_Status = 3;
    } else if (status === "approve") {
      intro_Status = 2;
    }

    const confirmationMessage =
      status === "reject"
        ? "Are you sure you want to reject this Intro Video"
        : "Do you want to approve this Intro Video?";

    const result = await Swal.fire({
      title: "Are you sure?",
      text: confirmationMessage,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText:
        status === "reject" ? "Yes, reject it!" : "Yes, approve it!",
      cancelButtonText: "Cancel",
      confirmButtonColor: "#8107D1",
      cancelButtonColor: "#FF2E2E",
    });

    if (!result.isConfirmed) {
      return;
    }

    let videoRejectReason = "";
    if (status === "reject") {
      const { value: reason } = await Swal.fire({
        title: "Enter Reason for Rejection",
        input: "textarea",
        inputPlaceholder: "Enter the reason for rejection...",
        showCancelButton: true,
        confirmButtonText: "Submit",
        cancelButtonText: "Cancel",
      });

      if (!reason) {
        Swal.fire(
          "Rejection Cancelled",
          "You need to provide a reason to reject the video.",
          "info"
        );
        return;
      }

      videoRejectReason = reason;
    }

    try {
      let dataToSend = { IntroVideoStatus: intro_Status };

      if (status === "reject") {
        dataToSend.IntroVideo_Reason = videoRejectReason;
      }

      let response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CU_DOCTOR_INTRO_VIDEO_STATUS_BY_ADMIN}${doctor_id}/?user_id=${admin_id}`,
        dataToSend
      );

      const { message } = response.data;
      if (message?.IntroVideoStatus === 2) {
        toast.success("Expert Intro Video Approved Successfully");
      } else if (message?.IntroVideoStatus === 3) {
        toast.success("Expert Intro Video Rejected Successfully");
      } else if (
        message === "Only pending videos can be approved or rejected"
      ) {
        toast.info(message);
      } else {
        toast.info(response.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      fetchIntroVideo();
    }
  };

  if (loading) {
    return <IntroVideoPlaceholder />;
  }
  return (
    <>
      <div className="row ">
        {noDtaError ? (
          <div className=" custom-margin-nodatafoud">
            <NoDataFound />
          </div>
        ) : expertsIntroVideo &&
          Array.isArray(expertsIntroVideo) &&
          expertsIntroVideo?.length > 0 ? (
          expertsIntroVideo.map((video, index) => (
            <div key={index} className="col-sm-6 px-4 py-2">
              <div className="introvideo-bg p-3">
                <div className="row">
                  <div className="col-sm-6">
                    <div
                      style={{
                        position: "relative",
                        width: "100%",
                        height: "100%",
                        borderRadius: "15px",
                        overflow: "hidden",
                        borderBottomLeftRadius: "15px",
                        borderBottomRightRadius: "15px",
                        borderTopLeftRadius: "15px",
                        borderTopRightRadius: "15px",
                      }}
                    >
                      {video?.IntVideoUrl?.length == 1 &&
                        video.IntroVideoStatus == 1 && (
                          <ReactPlayer
                            url={video.IntVideoUrl[0]}
                            controls
                            width="100%"
                            height="200px"
                          />
                        )}
                      {video?.IntVideoUrl?.length == 2 &&
                        video.IntroVideoStatus == 1 && (
                          <ReactPlayer
                            url={video.IntVideoUrl[1]}
                            controls
                            width="100%"
                            height="200px"
                          />
                        )}
                      <Image
                        src={CU_logoImage}
                        width={90}
                        height={50}
                        alt="cu logo"
                        style={{
                          position: "absolute",
                          top: "15px",
                          left: "8px",
                          zIndex: 1,
                        }}
                      />
                    </div>
                  </div>
                  <div className="col-sm-6">
                    <p className="allApproval-heading">Introduction Video</p>
                    <div className="row">
                      <div className="col-sm-auto">
                        {video?.ProfilePhoto ? (
                          <Image
                            src={video?.ProfilePhoto}
                            width={50}
                            height={50}
                            alt="profile-image"
                            className="allApproval-image"
                          />
                        ) : (
                          <Image
                            src={dummyProfile}
                            alt={`Dr ${video?.name}`}
                            width={35}
                            height={35}
                            className="expert_image"
                          />
                        )}
                      </div>
                      <div className="col-sm-auto p-0">
                        <p className="allApproval-expert-name mb-0">
                          {video?.expert_name || "No Expert Name Available"}
                        </p>
                        <p className="allApproval-expert-role">
                          {capitalizeFullName(
                            video?.expert_role
                              ? video?.expert_role
                              : "Unknown name"
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="modal-footer mt-5">
                      <button
                        type="button"
                        className="btn allApproval-reject-btn"
                        onClick={() =>
                          handleIntroVideoStatus("reject", video?.DoctorId)
                        }
                      >
                        Reject
                      </button>
                      <button
                        type="button"
                        className="btn allApproval-approve-btn ms-2"
                        onClick={() =>
                          handleIntroVideoStatus("approve", video?.DoctorId)
                        }
                      >
                        Approve
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className=" d-flex justify-content-center align-items-center mt-5 p-5">
            <p className="mt-2 ms-2 text-secondary testimonial-note">
              <span style={{ color: "#8107d1" }}>
                <FaVideoSlash style={{ fontSize: "20px" }} />
              </span>{" "}
              No other introduction videos available for approval. .
            </p>
          </div>
        )}
      </div>
      <div>
        {totalPages !== 1 && (
          <CustomPagination
            total_pages={totalPages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        )}
      </div>
    </>
  );
};

export default AllIntroVideo;
