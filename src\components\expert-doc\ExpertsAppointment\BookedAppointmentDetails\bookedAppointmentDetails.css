.modal-title-section {
  font-size: 21px;
  font-weight: 700;
}
.AppointmentModal-heading-A,
.event-status-A {
  color: #763cef;
}
.AppointmentModal-heading-B,
.event-status-B {
  color: #ff7700;
}
.AppointmentModal-heading-C,
.event-status-C {
  color: #b50000;
}
.AppointmentModal-heading-P,
.event-status-P {
  color: #ff971a;
}

.AppointmentModal-heading-R,
.event-status-R {
  color: #f80d38;
}

.PatientDetail-heading {
  font-size: 18px;
  font-weight: 600;
  
}
.AppointmentModal-label {
  font-size: 14px;
  font-weight: 500;
  color: #223645;
}
.AppointmentModal-input {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 2px solid #eeeeee;
  border-radius: 3px;
  font-size: 14px;
  font-weight: 500;
  color: #223645;
}
/* .closeButton {
  background: #8007d0 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
}
.closeButton:hover {
  background: #8007d0 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
} */
.cancelAppointmentButton {
  background: #b50000 0% 0% no-repeat padding-box !important;
  border: 1px solid #e3e3e3 !important;
  border-radius: 5px !important;
}
.cancelAppointmentButton:hover,
.cancelAppointmentButton:active {
  background: #b50000 0% 0% no-repeat padding-box !important;
  border: 1px solid #e3e3e3 !important;
  border-radius: 5px !important;
}
.rescheduleAppointmentButton {
  background: #f37721 0% 0% no-repeat padding-box !important;
  border: 1px solid #e3e3e3 !important;
  border-radius: 5px !important;
}
.rescheduleAppointmentButton:hover,
.rescheduleAppointmentButton:active {
  background: #f37721 0% 0% no-repeat padding-box !important;
  border: 1px solid #e3e3e3 !important;
  border-radius: 5px !important;
}
.joinMeetButton {
  background: #8007d0 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
}
.joinMeetButton:disabled {
  background: #8007d0 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 0.6;
}
.joinMeetButton:hover,
.joinMeetButton:active {
  background: #8007d0 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
}
