"use client";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import Image from "next/image";
import profile from "../../../public/assets/sampleprofilepay.png";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { Placeholder } from "react-bootstrap";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import CustomPagination from "../CustomPagination/CustomPagination";
import { FaTimes } from "react-icons/fa";
import _ from "lodash";
import { highlightText } from "../../utils/helperfunction";
const RefundRequest = () => {
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [initialLoading, setInitialLoading] = useState(true);
  const [refunds, setRefunds] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const axiosAuth = useAxiosAuth();
  const [error, setError] = useState(false);

  const { data: session } = useSession();
  const userId = session?.user?.id;

  const renderPlaceholders = (value) => {
    const placeholders = Array.from(
      { length: value === "initialLoad" ? 7 : 1 },
      (_, index) => (
        <div className="placeholder-container row" key={index}>
          <div className="placeholder-left col-sm-12">
            <Placeholder as="div" animation="glow">
              <Placeholder
                className="rounded placeholder-item mb-4"
                style={{
                  width: "100%",
                  height: "120px",
                }}
              />
            </Placeholder>
          </div>
        </div>
      )
    );

    return <div className="placeholders-wrapper">{placeholders}</div>;
  };

  const handleSearchQuery = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleClearQueryFilter = () => {
    setSearchQuery("");
  };

  const fetchRefunds = useCallback(
    async (pageNumber, query) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_FETCH_ALL_REFUNDS}all/?user_id=${userId}`;

        if (pageNumber) {
          url += `&page=${pageNumber}`;
        }
        if (query) {
          url += `&name=${query}`;
        }

        const response = await axiosAuth.get(url);
        setRefunds(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (err) {
        console.log("error in fetching payments", err);
        setError(true);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [axiosAuth, userId]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((page, query) => {
      fetchRefunds(page, query);
    }, 400);
  }, [fetchRefunds]);

  useEffect(() => {
    debouncedFetchData(currentPage, searchQuery);
  }, [searchQuery, debouncedFetchData, currentPage]);

  useEffect(() => {
    fetchRefunds(currentPage);
  }, [fetchRefunds, currentPage]);

  if (initialLoading) {
    return renderPlaceholders("initialLoad");
  }

  return (
    <>
      <div className="mt-3 mb-3 d-flex justify-content-between align-items-center">
        <h5 className="pay-history mb-0">Refunds</h5>
        <div className="row mx-0">
          <div className="">
            <div>
              <input
                type="text"
                className="form-control  expert-search-bar"
                placeholder="Search Name"
                aria-label="Recipient's username"
                aria-describedby="button-addon2"
                value={searchQuery}
                onChange={handleSearchQuery}
              />
              <span
                style={{ zIndex: 9999 }}
                className="cancel-expert-search-btn"
              >
                {!loading && searchQuery && (
                  <FaTimes
                    style={{ marginTop: "11px" }}
                    className=" cross-icon-calendar"
                    onClick={handleClearQueryFilter}
                  />
                )}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="row row-payment overflow-auto refund-profiles-container">
        {refunds?.refund_data?.length > 0 ? (
          <>
            {refunds?.refund_data?.map((request) => (
              <div
                key={request?.["Appointment ID"]}
                className="mb-3 py-2 ps-2 border rounded refund-apply"
              >
                <div className="">
                  <div className="col-sm-12 mb-2 d-flex">
                    <strong className="patient-name-payment">
                      Patient Name:
                    </strong>{" "}
                    <span className=" ms-auto">
                      {highlightText(request?.["Patient name"], searchQuery)}
                    </span>
                  </div>
                  <div className="col-sm-12 mb-2 d-flex">
                    <strong className="patient-name-payment">
                      Refunded Amount:
                    </strong>{" "}
                    <span className=" ms-auto">
                      ${request?.["Refunded amount"].toFixed(2)}
                    </span>
                  </div>
                  <div className="col-sm-12 mb-2 d-flex">
                    <strong className="patient-name-payment">
                      Refund Date:
                    </strong>{" "}
                    <span className=" ms-auto">{request?.["Refund Date"]}</span>
                  </div>
                  <div className="col-sm-12 mb-2 d-flex">
                    <strong className="patient-name-payment">
                      Appointment ID:
                    </strong>{" "}
                    <span className=" ms-auto">
                      {request?.["Appointment ID"]}
                    </span>
                  </div>
                </div>
              </div>
            ))}
            {loading && renderPlaceholders("load")}
          </>
        ) : (
          <div className="fs-4 d-flex align-items-center justify-content-center my-auto w-100">
            <PiFolderNotchOpenFill color={"#8107d1"} size={30} />
            &nbsp; No Records Found
          </div>
        )}
        {refunds?.total_pages !== 1 && (
          <CustomPagination
            total_pages={refunds?.total_pages}
            current_page={currentPage}
            setCurrent_Page={setCurrentPage}
          />
        )}
      </div>
    </>
  );
};
export default RefundRequest;
