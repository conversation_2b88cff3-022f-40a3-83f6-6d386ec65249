import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import ModalImage from "react-modal-image";
import { AiFillEye } from "react-icons/ai";
import { formatDate } from "../../../utils/helperfunction";
import { FaImage } from "react-icons/fa";
const ArticleViewModal = ({ show, handleClose, blogDetails }) => {
  return (
    <Modal
      show={show}
      onHide={() => handleClose(false)}
      size="xl"
      centered
      scrollable
    >
      <Modal.Header>
        <Modal.Title className="d-flex justify-content-between align-items-center w-100">
          <div className="blog-title-section">
            <span className="blog-title-span">Blog Title: </span>
            <span className="blog-title">
              {blogDetails?.BlogTitle || "No title available"}
            </span>
          </div>
          <div className="float-end">
            <div className="me-4 fs-6 d-flex justify-content-center align-items-center fw-bold mb-0">
              <AiFillEye color="#8107d1" className="fs-5" /> &nbsp;
              {blogDetails?.BlogViews ?? "No views available"} &nbsp; Views
            </div>
          </div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="d-flex justify-content-between align-items-center w-100">
          <div className="mb-4">
            <span className="blog-category fs-5">Category: </span>
            <span className="blog-title fw-bold">
              {blogDetails?.BlogCategoryVal || "No category available"}
            </span>
          </div>
          <div className="mb-4">
            <span className="blog-status"> Blog Status : &nbsp;</span>
            <strong>
              {blogDetails?.BlogStatus === 1
                ? "Approved"
                : blogDetails?.BlogStatus === 2
                ? "Rejected"
                : "Pending"}
            </strong>
          </div>
        </div>

        <div className="overflow-hidden">
          {blogDetails?.BlogBody ? (
            <div
              dangerouslySetInnerHTML={{
                __html: blogDetails.BlogBody,
              }}
              className="blog-body"
            />
          ) : (
            "No blog details available"
          )}
        </div>

        <p className="blog-related-images fs-5">Blog Related Images</p>

        <div className="blog-images-view-container d-flex justify-content-between align-items-center flex-wrap">
          {blogDetails?.BlogBannerImage ? (
            <div className="viewblog-image-wrapper mb-4">
              <p className="fw-bold">Blog Banner Image</p>
              <ModalImage
                small={blogDetails.BlogBannerImage}
                large={blogDetails.BlogBannerImage}
                alt="Blog Banner"
                hideDownload={true}
                hideZoom={true}
                className="custom-blog-image-size-for-small"
              />
            </div>
          ) : (
            <p className="fw-bold">
              {" "}
              <FaImage color="#9426b2" size={25} /> &nbsp; No blog banner image
              available
            </p>
          )}

          {/* {blogDetails?.BlogFeatureImage ? (
            <div className="viewblog-image-wrapper mb-4">
              <p className="fw-bold">Blog Feature Image</p>
              <ModalImage
                small={blogDetails.BlogFeatureImage}
                large={blogDetails.BlogFeatureImage}
                alt="Blog Feature"
                hideDownload={true}
                hideZoom={true}
                className="custom-blog-image-size-for-small"
              />
            </div>
          ) : (
            <p className="fw-bold">
              <FaImage color="#9426b2" size={25} /> &nbsp; No blog feature image
              available
            </p>
          )} */}

          {/* {blogDetails?.BlogImages?.length > 0 ? (
            blogDetails.BlogImages.map((blogImage, index) => (
              <div key={index} className="viewblog-image-wrapper mb-4">
                <p className="fw-bold">Blog Main Image</p>
                <ModalImage
                  small={blogImage}
                  large={blogImage}
                  alt={`blog-image-${index}`}
                  hideDownload={true}
                  hideZoom={true}
                  className="custom-blog-image-size-for-small"
                />
              </div>
            ))
          ) : (
            <p className="fw-bold">
              <FaImage color="#9426b2" size={25} /> &nbsp; No blog images
              available
            </p>
          )} */}
        </div>
      </Modal.Body>
      <Modal.Footer className="d-flex justify-content-between align-items-center">
        <div>
          <p>
            <span className="blog-time-section">Uploaded Date: </span>
            <span className="blog-time fw-bold">
              {blogDetails?.BlogDateTime
                ? formatDate(blogDetails.BlogDateTime)
                : "No time available"}
            </span>
          </p>
        </div>
        <Button
          className="blog-modal-close-button"
          onClick={() => {
            handleClose(false);
          }}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ArticleViewModal;
