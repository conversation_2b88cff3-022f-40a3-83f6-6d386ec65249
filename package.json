{"name": "cu-super-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/core": "^7.24.4", "@babel/runtime": "^7.24.4", "@faker-js/faker": "^8.4.1", "@firebase/auth": "^1.8.2", "axios": "^1.6.8", "bootstrap": "^5.3.3", "chart.js": "^4.4.2", "coordinate_to_country": "^1.1.0", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "debug": "^4.3.4", "faker": "^6.6.6", "file-saver": "^2.0.5", "firebase": "^11.2.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.10.60", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "next": "14.2.2", "next-auth": "^4.24.7", "peacock": "^1.0.0", "quill": "^2.0.2", "react": "^18", "react-big-calendar": "^1.11.6", "react-bootstrap": "^2.9.0-beta.1", "react-calendar": "^4.8.0", "react-chartjs-2": "^5.2.0", "react-chrono": "^2.6.1", "react-date-picker": "^10.6.0", "react-date-range": "^1.4.0", "react-datepicker": "^6.9.0", "react-documents": "^1.2.1", "react-dom": "^18", "react-drag-drop-files": "^2.3.10", "react-geolocated": "^4.3.0", "react-icons": "^5.1.0", "react-infinite-scroll-component": "^6.1.0", "react-modal": "^3.16.1", "react-modal-image": "^2.6.0", "react-pdf": "^9.1.1", "react-phone-input-2": "^2.15.1", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-select": "^5.8.0", "react-toastify": "^10.0.5", "react-tooltip": "^5.26.4", "react-tracking": "^9.3.2", "sweetalert2": "^11.10.8", "swiper": "^11.1.1", "swr": "^2.2.5", "use-debounce": "^10.0.1", "vercel": "^39.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "20.12.7", "@types/react": "18.3.3", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.2.2", "next-transpile-modules": "^10.0.1", "typescript": "5.4.5"}}