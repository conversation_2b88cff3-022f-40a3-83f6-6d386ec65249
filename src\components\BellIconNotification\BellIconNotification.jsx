import React, { useEffect, useState } from "react";
import "./bellIconNotification.css";
import { Placeholder } from "react-bootstrap";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { formatNotificationTime } from "../../utils/helperfunction"; // Uncomment if needed
import { useNotification } from "../../Context/NotificationContext/NotificationContext";
import NotificationPlaceholder from "./NotificationPlaceholder";

const BellIconNotification = ({ onClose, isNotificationOpen }) => {
  const [clickedNotificationId, setClickedNotificationId] = useState(null);
  // Added state
  const router = useRouter();
  const {
    totalNotifications,
    notificationLoading,
    notifications,
    updateNotificationStatus,
  } = useNotification();

  useEffect(() => {
    const handleClickOutside = (event) => {
      const notificationPopup = document.querySelector(
        ".notification-popup-cu"
      );
      if (
        isNotificationOpen &&
        !event.target.closest("#notification-link") &&
        notificationPopup &&
        !notificationPopup.contains(event.target)
      ) {
        onClose();
      }
    };
    if (typeof window !== "undefined") {
      document.body.addEventListener("click", handleClickOutside);
      return () => {
        document.body.removeEventListener("click", handleClickOutside);
      };
    }
  }, [isNotificationOpen, onClose]);

  const handleNotificationClick = async (notification) => {
    updateNotificationStatus(notification?.id);
    setClickedNotificationId(notification?.id);
    // setTimeout(() => {
    //   router.push(notifications?.Link);
    // }, [1500]);
  };

  const closeNotification = async (notification) => {
    updateNotificationStatus(notification?.id);
    setClickedNotificationId(notification?.id);
  };

  return (
    <div
      className={`notification-popup-cu ${
        isNotificationOpen ? "open" : "closing"
      }`}
    >
      <div className="notification-header">
        <button className="buttons-in-notifications dismiss-text">
          Dismiss all notifications
        </button>
        <button onClick={onClose} className="close-btn">
          &times;
        </button>
      </div>
      <div className="notification-list overflow-auto">
        {notificationLoading ? (
          <NotificationPlaceholder />
        ) : (
          <>
            {totalNotifications === 0 ? (
              <p>No notifications</p>
            ) : (
              notifications &&
              notifications?.map((notification) => (
                <div key={notification?.id} className="notification-item">
                  <div className="pt-1 d-flex justify-content-between align-items-center">
                    <p className={`notification-text mb-0`}>
                      {notification?.Title}
                    </p>
                    <button
                      onClick={() => closeNotification(notification)}
                      className={`close-btn hover-close-btn ${
                        clickedNotificationId === notification?.id ? "exit" : ""
                      }`}
                    >
                      &times;
                    </button>
                  </div>
                  <Link
                    key={notification?.id}
                    href={notification?.Link || "/"}
                    style={{
                      textDecoration: "none",
                      color: "inherit",
                      display: "block",
                      width: "100%",
                    }}
                    className={` ${
                      clickedNotificationId === notification?.id ? "exit" : ""
                    }`}
                    onClick={() => {
                      handleNotificationClick(notification);
                    }}
                  >
                    <p className="notification-desc">{notification?.Body}</p>
                    <p className="notification-time">
                      {formatNotificationTime(notification?.NotificationTime)}
                    </p>
                  </Link>
                </div>
              ))
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default BellIconNotification;
