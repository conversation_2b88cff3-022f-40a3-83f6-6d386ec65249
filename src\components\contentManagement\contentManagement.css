.contentManage-custom-scroll {
  max-height: 607px;
  padding: 12px;
}

.single-podcast-timestamps {
  color: #707070;
  font-size: 15px;
}

span.time-stamp {
  color: #0e79f3;
  cursor: pointer;
}

.custom-label {
  font-weight: bold;
  color: #333;
}

.fixTableHead::-webkit-scrollbar {
  display: none;
}

.form-control.custom-form-control {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0.5rem;
  font-size: 12px;
}

.grey-bg {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  background-color: #f6f6f6;
  padding: 15px;
}

.form-control.custom-form-control::placeholder {
  font-size: 12px;
}

.form-control.custom-form-control:focus {
  border-color: #ccc;
  box-shadow: none;
}

.th.custom-font-size {
  font-size: 12px;
}

.td.custom-font-size {
  font-size: 12px;
}

.purple-button,
.purple-button:hover,
.purple-button:active .purple-button:focus {
  background-color: #8107d1;
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 5px;
  font-size: 12px;
}

.table-wrapper {
  max-height: 300px;
  overflow-y: auto;
}

.sticky-table-head {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  border: 1px solid #ddd;
  padding: 0.7rem 0.5rem;
}

/* .table th {
  background-color: #f2f2f2;
} */

.edit-update-button,
.edit-update-button:hover {
  background-color: #8107d1;
  border: none;
  color: white;
}

.cancellationRefund-Rescheduling {
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancellation-desclaimer-section,
.rescheduling-desclaimer-section {
  font-size: small;
  color: #8107d1;
}

/* Hide the spinners */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.custom-small-image {
  width: 100px;
  /* or any size you want */
  height: 100px;
  /* or any size you want */
}

.custom-small-image img {
  pointer-events: none;
}

.placeHolder_loading {
  margin-bottom: 2%;
  border-radius: 8px;
}

.statusFilter {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 44px;
}

.statusFilter:focus {
  border: 1px solid #e3e3e3;
  box-shadow: 0px 3px 6px #00000029;
}

.custom-form-select {
  border: none;
  box-shadow: 0px 3px 6px #00000029;
  font-size: 14px;
}

.custom-form-select:focus {
  border: none;
  box-shadow: 0px 3px 6px #00000029;
  font-size: 14px;
}

.selectDate {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 44px;
}

.selectDate :focus {
  border: 0;
  border-radius: 0;
  box-shadow: none;
}

.cross-container {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cross-symbol {
  border-radius: 50%;
  color: #8107d1;
  padding: 3px;
}

.custom-blog-overflow {
  max-height: 270px;
}

.no_blogs_found {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 3%;
  background-color: #fbf8f8;
}

.no_blogs_found_image {
  object-fit: contain;
}

.blog-list-bg {
  background: #f7f4f8;
  padding: 10px 15px 10px 15px;
}

.blog-article-title {
  letter-spacing: 0px;
  color: #8107d1;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0px;
}

.upload-date-border {
  border-right: 1px solid #e3e3e3;
  border-left: 1px solid #e3e3e3;
}

.blog-title-section {
  width: 85%;
}

.blog-title-span,
.blog-category,
.blog-related-images,
.blog-time-section,
.blog-status {
  color: #8107d1;
  font-weight: 600;
}

.blog-title {
  font-size: 1.1em;
  font-weight: 600;
}

.custom-blog-image-size-for-small {
  width: 100px;
  height: 100px;
  border-radius: 5px;
}

.profile-img-expert {
  object-fit: cover;
  border-radius: 50%;
}

.blog-images-small-size {
  width: 300px;
  height: 300px;
}

.custom-button {
  background: #ff2e2e;
  color: white;
}

.delete-ranking-exp-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.contentManage-custom-scroll-blog {
  max-height: 500px;
  padding: 12px;
}

.videos-thumbnail-image {
  width: 300px;
  height: 200px;
  border-radius: 5px;
}

