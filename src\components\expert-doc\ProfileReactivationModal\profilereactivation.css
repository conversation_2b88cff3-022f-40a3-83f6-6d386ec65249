.reactivation-title {
  color: #8107d1;
  font: 700;
}

.reactivation-footer-section {
  display: flex;
  justify-content: space-between;
}

.reactivation-approve-button,
.reactivation-approve-button:hover {
  background-color: #04ab20;
  color: white;
  border: none;
}

.reactivation-reject-button,
.reactivation-reject-button:hover {
  background-color: red;
  color: white;
  border: none;
}

.reject-reason-container {
  animation: ease-in-out 0.5s;
}

.reject-reason-textarea {
  width: 100%;
  height: 100px;
  margin: 10px 0;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

@keyframes ease-in-out {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reason-for-rejecting-profile {
  margin-top: 10%;
  color: #8107d1;
  font: bold;
}

.reactivation-cancel-button,
.reactivation-cancel-button:hover {
  background-color: #af0000;
  color: white;
  border: none;
}

.reactivation-approvals-section {
  display: flex;
  justify-content: space-around;
}

.activation-model-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #8107d1;
  font-weight: 600;
  width: 100%;
}

.activation-requested-date {
  font-size: 16px;
}
.activation-requested-date-value {
  color: #5b5b5b;
  font: 600;
}
