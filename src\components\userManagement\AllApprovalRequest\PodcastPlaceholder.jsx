import React from 'react';
import { Placeholder } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import { FaFile } from 'react-icons/fa';

const PodcastPlaceholder = () => {
  const placeholders = Array.from({ length: 14 }).map((_, index) => (
    <div key={index} className="col-sm-6 p-2">
      <div className="introvideo-bg p-3">
        <div className="row upload-reviews">
          <div className="col-sm-1">
            <Placeholder as={FaFile} animation="glow" style={{ fontSize: '30px', color: '#8107D1' }} />
          </div>
          <div className="col-sm-3">
            <Placeholder as="p" animation="glow" style={{ fontSize: '13px' }} className="mb-0">
              <Placeholder xs={7} />
            </Placeholder>
            <div style={{ fontSize: '12px' }}>
              <Placeholder as="p" animation="glow" className="custom-transperent-btn">
                <Placeholder xs={4} />
              </Placeholder>
            </div>
          </div>
          <div className="fw-semibold col-sm-8 mb-0">
            <Placeholder as="p" animation="glow">
              <Placeholder xs={12} />
            </Placeholder>
          </div>
        </div>
      </div>
    </div>
  ));

  return <div className="row">{placeholders}</div>;
};

export default PodcastPlaceholder;
