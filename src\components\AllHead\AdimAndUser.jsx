"use client";
import React, { useState } from "react";
import { Modal, Button, Row, Col, Form, Spinner } from "react-bootstrap";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useAxiosAuth from "@/lib/hooks/useAxiosAuth";
import { useAdminContext } from "@/Context/AdminContext/AdminContext";
import { isValidPhoneNumber } from "libphonenumber-js";
import { getPhoneNumberWithoutCountryCode } from "@/utils/helperfunction";

const AdminAndUser = () => {
  const [selectedRole, setSelectedRole] = useState("doctor");
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    country_code: "us",
  });
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone: "",
  });
  const [loading, setLoading] = useState(false);
  const axiosAuth = useAxiosAuth();
  const { session } = useAdminContext();
  const admin_id = session?.user?.id;

  const validateForm = () => {
    let valid = true;
    const newErrors = {
      name: "",
      email: "",
      phone: "",
    };

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
      valid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      valid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
      valid = false;
    }

    if (!formData.phone) {
      newErrors.phone = "Phone number is required";
      valid = false;
    } else if (formData.phone.replace(/\D/g, "").length < 6) {
      newErrors.phone = "Please enter a valid phone number";
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const validatePhoneNumber = (phone, country) => {
    const internationalValue = `+${phone}`;

    const isValid = isValidPhoneNumber(
      internationalValue,
      country?.countryCode
    );

    if (!isValid) {
      setErrors((prev) => ({
        ...prev,
        phone: `Invalid phone number for ${
          country?.name || "selected country"
        }`,
      }));
      return false;
    }

    setErrors((prev) => ({
      ...prev,
      phone: "", // Clear phone error
    }));

    return true;
  };

  const handleRoleChange = (event) => {
    setSelectedRole(event.target.value);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }
  };

  const handlePhoneChange = (value, country) => {
    setFormData((prev) => ({
      ...prev,
      phone: value,
      country_code: country.countryCode.toLowerCase(),
    }));
    // validatePhoneNumber(value, country);
    // if (errors.phone) {
    //   setErrors({ ...errors, phone: "" });
    // }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const isFormValid = validateForm();
    const isPhoneValid = validatePhoneNumber(formData.phone, {
      countryCode: formData.country_code,
    });

    if (!isFormValid || !isPhoneValid) {
      return;
    }

    setLoading(true);
    const phoneNo = getPhoneNumberWithoutCountryCode(formData.phone);

    try {
      const payload = {
        name: formData.name,
        email: formData.email,
        phone: phoneNo,
        country_code: `+${formData.phone.split(" ")[0]}`,
        user_role: selectedRole,
      };

      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_CHILD_ADMIN_API}?user_id=${admin_id}`,
        payload
      );

      if (response.status === 201 || response.status === 200) {
        toast.success("User created successfully!", {
          autoClose: 1000,
          position: "top-center",
        });
        handleClose();
        setFormData({ name: "", email: "", phone: "", country_code: "us" });
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Error creating user");
    } finally {
      setLoading(false);
    }
  };

  const [showModalUser, setShowModalUser] = useState(false);
  const handleShowUser = () => setShowModalUser(true);
  const handleClose = () => {
    setShowModalUser(false);
    setErrors({ name: "", email: "", phone: "" });
  };

  return (
    <>
      <button
        type="button"
        className="btn btn-newuser"
        onClick={handleShowUser}
      >
        New User
      </button>

      <Modal
        show={showModalUser}
        centered
        dialogClassName="custom-modal-dialog"
        onHide={handleClose}
        size="lg"
      >
        <div className="modal-content py-4">
          <Modal.Header className="modal-header-allhead d-flex justify-content-between align-items-center">
            <Modal.Title className="title-edit">Add New User</Modal.Title>
            <button
              type="button"
              className="btn btn-cancle-plan"
              onClick={handleClose}
            >
              Dismiss
            </button>
          </Modal.Header>
          <Modal.Body>
            <div className="row">
              <div className="col-sm-12">
                <Form onSubmit={handleSubmit}>
                  <Row>
                    <h6 className="edit-plans-pricing">Select User</h6>
                  </Row>
                  <Row>
                    <Col xs={4}>
                      <label className="label-select input-radio-style mt-1">
                        <input
                          type="radio"
                          value="doctor"
                          className="mx-2"
                          checked={selectedRole === "doctor"}
                          onChange={handleRoleChange}
                        />
                        Doctor
                      </label>
                    </Col>
                    <Col xs={4}>
                      <label className="label-select input-radio-style mt-1">
                        <input
                          type="radio"
                          value="researcher"
                          className="mx-2"
                          checked={selectedRole === "researcher"}
                          onChange={handleRoleChange}
                        />
                        Researcher
                      </label>
                    </Col>
                    <Col xs={4}>
                      <label className="label-select input-radio-style mt-1">
                        <input
                          type="radio"
                          value="influencer"
                          className="mx-2"
                          checked={selectedRole === "influencer"}
                          onChange={handleRoleChange}
                        />
                        Influencer
                      </label>
                    </Col>
                  </Row>

                  <Form.Group className="mt-3">
                    <Form.Label className="label-select">Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="input-form-modal"
                      isInvalid={!!errors.name}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.name}
                    </Form.Control.Feedback>
                  </Form.Group>

                  <Form.Group className="mt-3">
                    <Form.Label className="label-select">Email</Form.Label>
                    <Form.Control
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="input-form-modal"
                      isInvalid={!!errors.email}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.email}
                    </Form.Control.Feedback>
                  </Form.Group>

                  <Form.Group className="mt-3 w-100">
                    <Form.Label className="label-select">Phone</Form.Label>
                    <PhoneInput
                      className="w-100 input-form-modal"
                      country={formData.country_code}
                      value={formData.phone}
                      onChange={handlePhoneChange}
                      inputProps={{
                        // required: true,
                        name: "phone",
                      }}
                      containerClass="phone-input-container"
                      inputClass={`phone-input ${
                        errors.phone ? "is-invalid" : ""
                      }`}
                      buttonClass="phone-button"
                    />
                    {errors.phone && (
                      <div className="invalid-feedback d-block">
                        {errors.phone}
                      </div>
                    )}
                  </Form.Group>

                  <Button
                    variant="primary"
                    type="submit"
                    disabled={loading}
                    className="btn purple-modal-btn mt-5 w-100"
                  >
                    {loading ? (
                      <>
                        <Spinner
                          animation="border"
                          size="sm"
                          className="me-2"
                        />
                        Creating...
                      </>
                    ) : (
                      "Create User"
                    )}
                  </Button>
                </Form>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>

      <style jsx>{`
        :global(.react-tel-input) {
          width: 100% !important;
        }

        :global(.react-tel-input .form-control) {
          width: 100% !important;
          height: 48px !important;
          padding-left: 48px !important;
          background: #ffffff 0% 0% no-repeat padding-box !important;
          box-shadow: 0px 3px 6px #00000029 !important;
          border: 1px solid #e3e3e3 !important;
          border-radius: 3px !important;
          font-size: 1rem;
          outline: none !important;
          box-sizing: border-box;
        }

        :global(.react-tel-input .form-control:focus) {
          border-color: #e3e3e3 !important;
          box-shadow: 0px 3px 6px #00000029 !important;
        }

        :global(.react-tel-input .flag-dropdown) {
          background: #ffffff !important;
          border: 1px solid #e3e3e3 !important;
          border-right: none !important;
          border-radius: 3px 0 0 3px !important;
          height: 48px !important;
        }

        :global(.react-tel-input .flag-dropdown:focus) {
          outline: none !important;
          box-shadow: none !important;
        }

        .invalid-feedback {
          color: #dc3545;
          font-size: 0.875rem;
          margin-top: 0.25rem;
        }
      `}</style>
    </>
  );
};

export default AdminAndUser;
