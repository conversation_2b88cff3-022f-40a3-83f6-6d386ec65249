"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Placeholder } from "react-bootstrap";
import _ from "lodash";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import CustomPagination from "../CustomPagination/CustomPagination";
import { FaTimes } from "react-icons/fa";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { highlightInteger } from "../../utils/helperfunction";
import NoDataFound from "../noDataFound/NoDataFound";

const PatientRefund = ({ id }) => {
  const axiosAuth = useAxiosAuth();
  const [refunds, setRefunds] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState(false);
  const [pageLimit, setPageLimit] = useState();
  const { data: session } = useSession();
  const userId = session && session?.user?.id;

  const renderPlaceholders = (value) => {
    const placeholders = Array.from(
      { length: value === "initialLoad" ? 9 : 1 },
      (_, index) => (
        <div key={index} className="placeHolder_loading">
          <Placeholder as="p" animation="glow">
            <Placeholder
              xs={12}
              size={"lg"}
              style={{ height: "43px", borderRadius: "4px" }}
            />
          </Placeholder>
        </div>
      )
    );

    return placeholders;
  };

  const getRefunds = useCallback(
    async (pageNumber, perPage, query) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_FETCH_ALL_REFUNDS}${id}/?user_id=${userId}`;

        if (pageNumber && id) {
          url += `&page=${pageNumber}`;
        }
        if (query && id) {
          url += `&id=${query}`;
        }
        if (perPage && id) {
          url += `&per_page=${perPage}`;
        }
        const response = await axiosAuth.get(url);
        setRefunds(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (err) {
        console.log("error in getting the refunds ", err);
        setError(true);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [id, axiosAuth, userId]
  );

  const debouncedFetchData = useMemo(() => {
    return _.debounce((page, perPage, query) => {
      getRefunds(page, perPage, query);
    }, 400);
  }, [getRefunds]);

  useEffect(() => {
    debouncedFetchData(currentPage, pageLimit, searchQuery);
  }, [searchQuery, debouncedFetchData, currentPage, pageLimit]);

  const handleClearQueryFilter = () => {
    setSearchQuery("");
    setCurrentPage(1);
  };

  const handlePageLimit = (e) => {
    const status = e.target.value;
    setPageLimit(status);
  };

  const handleSearchQuery = (e) => {
    setSearchQuery(e.target.value);
  };

  return (
    <>
      <div className="custom-width mb-3">
        <div className="row">
          <div className="col-md-4 col-xl-2">
            <div className="filters">
              <div className="row">
                <div className="col-sm-12">
                  <p className="custom-heading">Filters</p>
                </div>
              </div>
              <div className="row mx-0">
                <div className="mb-3 expert-search-bar p-0 ps-0">
                  <div
                    className=" position-relative"
                    style={{ height: "45px" }}
                  >
                    <input
                      type="text"
                      className="form-control app-search-filter"
                      style={{
                        outline: "none",
                        border: "0px",
                        borderRadius: "3px",
                      }}
                      placeholder="Appointment ID..."
                      aria-label="Recipient's username"
                      aria-describedby="button-addon2"
                      value={searchQuery}
                      disabled={refunds?.refund_data?.length == 0 || loading}
                      onChange={handleSearchQuery}
                    />
                    <span
                      style={{ zIndex: 9999 }}
                      className="cancel-expert-search-btn"
                    >
                      {!loading && searchQuery && (
                        <FaTimes
                          style={{ marginTop: "11px" }}
                          className=" cross-icon-calendar"
                          onClick={handleClearQueryFilter}
                        />
                      )}
                    </span>
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="col-sm-12">
                  <select
                    style={{ height: "45px" }}
                    className="form-select form-select-sm custom-select"
                    aria-label="Select Prescription"
                    name="statusFilter"
                    value={pageLimit}
                    disabled={refunds?.refund_data?.length == 0 || loading}
                    onChange={handlePageLimit}
                  >
                    <option value="">Items Per Page</option>
                    <option value="10">10</option>
                    <option value="15">15</option>
                    <option value="20">20</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="col-md-8 col-xl-10 mt-5">
            <div className="row hide-on-mobile ">
              <div className="col-sm-12">
                <div className="row ">
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Appointment Id</p>
                  </div>
                  <div className="col-sm-3 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Refund Date</p>
                  </div>
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Payment Status</p>
                  </div>
                  <div className="col-sm-2 d-flex justify-content-center align-items-center">
                    <p className="title mb-0"> Refund Amount</p>
                  </div>
                  <div className="col-sm-3 d-flex justify-content-center align-items-center">
                    <p className="title mb-0">Currency</p>
                  </div>
                </div>
              </div>
            </div>
            <hr />
            <div className="custom-overflow-2 overflow-auto  px-lg-3">
              {/* <div className="overflow-auto custom-overflow-2 px-3"> */}
              {initialLoading ? (
                renderPlaceholders("initialLoad")
              ) : refunds?.refund_data?.length > 0 ? (
                <>
                  {refunds &&
                    refunds?.refund_data?.map((refund, index) => (
                      <div key={refund?.AppointmentId} className="row mb-3">
                        <div className="col-sm-12">
                          <div className="content-holder-2 p-2">
                            <div className="row ">
                              <div className="col-sm-2 d-flex justify-content-center align-items-center">
                                <p className="mb-0">
                                  {highlightInteger(
                                    refund?.["Appointment ID"],
                                    searchQuery
                                  )}
                                </p>
                              </div>
                              <div className="col-sm-3 d-flex justify-content-center align-items-center">
                                <p className="mb-0">
                                  {refund?.["Refund Date"]}
                                </p>
                              </div>
                              <div className="col-sm-2 d-flex justify-content-center align-items-center ">
                                <p className="mb-0 rounded-4 payments-label text-white payment-success-bg">
                                  success
                                </p>
                              </div>
                              <div className="col-sm-2 d-flex justify-content-center align-items-center">
                                <p className="mb-0">
                                  {refund?.["Refunded amount"]} $
                                </p>
                              </div>
                              <div className="col-sm-3 d-flex justify-content-center align-items-center">
                                <p className="mb-0">{refund?.Currency} $</p>
                              </div>
                            </div>
                            {/* *******************************************************mobile design**************************************************** */}
                          </div>
                        </div>
                      </div>
                    ))}
                  <div className="">
                    {loading && (
                      <div>
                        <Placeholder as="p" animation="glow">
                          <Placeholder
                            xs={12}
                            size={"lg"}
                            style={{ height: "43px", borderRadius: "4px" }}
                          />
                        </Placeholder>
                      </div>
                    )}
                  </div>
                </>
              ) : error ? (
                <NoDataFound />
              ) : (
                <>
                  <h3 className="text-secondary text-center my-5">
                    No refunds have been initiated yet.
                  </h3>
                </>
              )}
            </div>
            {refunds?.total_pages !== 1 && (
              <CustomPagination
                total_pages={refunds?.total_pages}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default PatientRefund;
