import React from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import dynamic from "next/dynamic";

const PdfViewer = dynamic(() => import("./PdfViewer"), {
  ssr: false,
});

const PdfViewerModal = ({ show, handleClosePdfModal, document }) => {
  return (
    <Modal
      show={show}
      onHide={handleClosePdfModal}
      centered
      scrollable
      backdrop="static"
      size="lg"
      className="custom-pdf-document"
    >
      <Modal.Header closeButton>
        <Modal.Title>View Transaction Receipt</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <PdfViewer document={document} />
      </Modal.Body>
    
    </Modal>
  );
};

export default PdfViewerModal;
