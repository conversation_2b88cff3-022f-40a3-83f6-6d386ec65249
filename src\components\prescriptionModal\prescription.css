.edit-button-pres {
  background-color: #f37721;
  font-size: 15px;
  color: white;
}
.edit-button-pres:hover {
  background-color: #f37721 !important;
  color: white;
}
label.form-label.purple-content-2 {
  color: #713c8f;
  font-size: 12px;
  font-weight: 500;
}
th.purple-content {
  color: #8d4f9f;
  font-size: 16px;
  line-height: 17px;
  font-weight: 700;
}
.doc-sign {
  height: 20px;
  width: 130px;
}
.exist-treatment-container {
  margin-left: 0px;
  margin-right: 0px;
  border: 2px solid #f0eaea;
}
.purple-content {
  color: #8d4f9f;
  font-size: 16px;
}

.medication-heading {
  font-size: 22px;
  font-weight: 700;
  color: #8d4f9f;
}

.black-content {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-size: 12px;
}
img.headerImage1 {
  width: 90px;
  height: 41px;
  object-fit: contain;
}
