"use client";
import React, { useState } from "react";
import SubscriptionExpire from "../paymentandpricing/SubscriptionExpire";
import SubscriptionPlans from "../paymentandpricing/SubscriptionPlans";

const PriceAndSubscription = () => {
  return (
    <>
      <div className="payment-back">
        <div className="row">
          <div className="col-sm-9">
            <h3 className="pay-history">Overview</h3>
            <hr className="line-horizontal" />
            <div className="row">
              <div className="col-sm-12 payment-method-background">
                <div className="row">
                  <div className="col-sm-6 borde-right-style">
                    <span className="method-use">Most Popular Plan</span>
                    <div className="row">
                      <div className="col-sm-4">
                        <div className="starter mt-3">
                          <h6 className="starter-paln">Starter</h6>
                          <span className="month-plan">35.99/month</span>
                        </div>
                      </div>
                      <div className="col-sm-8 mt-3">
                        <span className="all-plans">Till Expiry</span>
                        <span className="days-of-expire">13 days</span>

                        <div className="progress progress-back">
                          <div
                            className="progress-bar custom-progress"
                            role="progressbar"
                            aria-valuenow="50"
                            aria-valuemin="0"
                            aria-valuemax="100"
                          ></div>
                        </div>

                        <div className="row">
                          <div className="col-sm-2 mt-3">
                            <h6 className="all-plans">Users</h6>
                            <span className="all-users">2212</span>
                          </div>
                          <div className="col-sm-10 mt-3">
                            <h6 className="all-plans">
                              Payment Method most used
                            </h6>
                            <span className="all-users">Mastercard</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="col-sm-6">
                    <span className="method-use">Least Used Plan</span>
                    <div className="row">
                      <div className="col-sm-4">
                        <div className="starter mt-3">
                          <h6 className="starter-paln">Premium</h6>
                          <span className="month-plan">109.99/month</span>
                        </div>
                      </div>
                      <div className="col-sm-8 mt-3">
                        <span className="all-plans">Till Expiry</span>
                        <span className="days-of-expire">13 days</span>

                        <div className="progress progress-back">
                          <div
                            className="progress-bar custom-progress-2"
                            role="progressbar"
                            aria-valuenow="80"
                            aria-valuemin="0"
                            aria-valuemax="100"
                          ></div>
                        </div>
                        <div className="row">
                          <div className="col-sm-2 mt-3">
                            <h6 className="all-plans">Users</h6>
                            <span className="all-users">152</span>
                          </div>
                          <div className="col-sm-10 mt-3">
                            <h6 className="all-plans">
                              Payment Method most used
                            </h6>
                            <span className="all-users">paypal</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-sm-12">
                <SubscriptionPlans />
              </div>
            </div>
          </div>
          <div className="col-sm-3 gx-5">
            <SubscriptionExpire />
          </div>
        </div>
      </div>
    </>
  );
};
export default PriceAndSubscription;
