import React from "react";
import ReactPlayer from "react-player";

const VideoSection = ({
  doctorDetails,
  enableTextBox,
  handleIntroVideoStatus,
  setVideoRejectReason,
  calculateTextAreaHeight,
  handleDisableTextbox,
  handleVideoRejection,
  introVideoRejAppLoading,
  videoRejectReason,
  handleEnableTextbox,
}) => {
 
  return (
    <>
      <div className="col-sm-6">
        <form>
          <div className="row">
            <div className="col-sm-5 pe-0">
              <div className=" d-flex justify-content-center align-items-center mt-4">
                <ReactPlayer
                  url={introVideos}
                  controls
                  width="100%"
                  height="100%"
                />
              </div>
            </div>
            
          </div>
        </form>
      </div>
    </>
  );
};

export default VideoSection;
