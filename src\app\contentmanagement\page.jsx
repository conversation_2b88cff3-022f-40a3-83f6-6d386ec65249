"use client";

import React from "react";
import Navbar from "../../components/navbar/Navbar";
import Home from "../../components/administratorDasboard/Home";
import "../../components/userManagement/usermanagement.css";
import "../../components/contentManagement/contentManagement.css";
import AddExperts from "../../components/contentManagement/AddExperts";
import withAuth from "../../withAuth/withAuth";
import ContentManagementTabs from "../../components/ContentManagementParent/contentManagementTabs/ContentManagementTabs";

const ContentManagementPage = () => {
  return (
    <ContentManagementTabs />
  );
};

export default withAuth(ContentManagementPage);
