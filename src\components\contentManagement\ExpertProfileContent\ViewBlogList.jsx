import React, { useState, useEffect, useCallback } from "react";
import { Modal, ListGroup, Button, Placeholder } from "react-bootstrap";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import No_Blogs_uploaded_yet from "../../../../public/images/No Blogs uploaded yet.png";
import Image from "next/image";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { formatCustomDateAndTime, formatDate } from "../../../utils/helperfunction";
import ArticleEditModal from "./ArticleEditModal";
import ArticleViewModal from "./ArticleViewModal";
import Swal from "sweetalert2";
import { useSession } from "next-auth/react";

const renderPlaceholders = () => {
  const placeholders = Array.from({ length: 8 }, (_, index) => (
    <div key={index} className="placeHolder_loading">
      <Placeholder as="p" animation="glow">
        <Placeholder
          xs={12}
          size={"lg"}
          style={{ height: "150px", borderRadius: "10px" }}
        />
      </Placeholder>
    </div>
  ));

  return placeholders;
};
const ViewBlogList = ({ showBlogModal, setShowBlogModal, viewExpertBlog }) => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState("");
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const admin_id = session && session?.user?.id;
  const expert_id = viewExpertBlog && viewExpertBlog?.id;

  const fetchBlogs = useCallback(async () => {
    try {
      if (expert_id) {
        // Create the API endpoint with the filter
        let apiEndpoint = `${process.env.NEXT_PUBLIC_GET_POST_BLOG_FOR_SELECTED_EXPERT}${expert_id}`;

        // Add status filter if selected
        if (statusFilter !== "") {
          apiEndpoint += `/${statusFilter}`;
        } else {
          apiEndpoint += `/all`;
        }

        const response = await axiosAuth.get(apiEndpoint);
        setBlogs(response?.data);
      }
    } catch (error) {
      console.error("Error fetching Blogs: ", error);
      toast.error("Failed to fetch blogs", {
        position: "top-center",
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  }, [expert_id, statusFilter, axiosAuth]);

  useEffect(() => {
    fetchBlogs();
  }, [fetchBlogs]);

  const handleStatusChange = (event) => {
    setStatusFilter(event.target.value);
  };

  const clearSelectedStatus = () => {
    setStatusFilter("");
  };

  const handleDelete = async (blogId) => {
    const confirmResult = await Swal.fire({
      title: "Are you sure want to delete this Blog?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#B50000",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirmResult.isConfirmed) {
      const apiEndpoint = `${process.env.NEXT_PUBLIC_DOCTOR_UPDATE_BLOG_API_ENDPOINT}${blogId}/?user_id=${admin_id}`;

      try {
        const response = await axiosAuth.delete(apiEndpoint);
        if (
          response?.statusText === "Content removed successfully" ||
          response?.status == 200
        ) {
          toast.success("Blog Deleted successfully!");
          fetchBlogs();
        } else {
          console.error("Failed to delete Blog");
          toast.error("");
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    }
  };


  return (
    <>
      <Modal
        show={showBlogModal}
        onHide={() => setShowBlogModal(false)}
        backdrop="static"
        keyboard={false}
        centered
        scrollable
        size="xl"
      >
        <Modal.Header className="d-flex justify-content-between align-items-center">
          <Modal.Title>
            Blogs list for the selected expert :{" "}
            <span className="fw-bold">
              {viewExpertBlog.name || "Expert Name"}
            </span>
          </Modal.Title>
          <div className="float-end">
            {" "}
            <Button
              className="border-0 bg-transparent text-danger fw-bold"
              onClick={() => setShowBlogModal(false)}
            >
              Close
            </Button>
          </div>
        </Modal.Header>
        <Modal.Body>
          {loading ? (
            renderPlaceholders()
          ) : (
            <>
              <div className="row mb-3">
                <div className="col-sm-12 d-flex justify-content-end align-items-end">
                  <div className="input-group selectDate">
                    <select
                      className="form-select statusFilter"
                      aria-label="Select Blog Status"
                      value={statusFilter}
                      onChange={handleStatusChange}
                    >
                      <option value="" defaultValue={"Select Blog Status"}>
                        Select Blog Status
                      </option>
                      <option value="1">Approved</option>
                      <option value="0">Pending</option>
                      <option value="2">Rejected</option>
                    </select>
                    {statusFilter && (
                      <span
                        className="cross-container mx-2"
                        onClick={clearSelectedStatus}
                      >
                        <span className="cross-symbol">&#10006;</span>
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {blogs && blogs.length === 0 ? (
                <div className="no_blogs_found text-center">
                  <Image
                    src={No_Blogs_uploaded_yet}
                    alt="no blogs found"
                    width={300}
                    height={250}
                    className="no_blogs_found_image"
                  />
                </div>
              ) : (
                <ListGroup>
                  {blogs &&
                    Array.isArray(blogs) &&
                    blogs?.map((blog) => (
                      <ListGroup.Item key={blog?.blog_details?.id}>
                        <h5>
                          <strong>Blog Title :</strong>{" "}
                          {blog?.blog_details?.BlogTitle}
                        </h5>
                        <p>
                          Uploaded:{" "}
                          {formatCustomDateAndTime(
                            blog?.blog_details?.BlogDateTime
                          )}
                        </p>
                        <p>
                          <strong>Blog Category :</strong>{" "}
                          {blog?.blog_details?.BlogCategoryVal ||
                            "No Category Available"}
                        </p>
                        <p>
                          Status:{" "}
                          {blog?.blog_details?.BlogStatus === 1
                            ? "Approved"
                            : blog?.blog_details?.BlogStatus === 0
                            ? "Pending"
                            : "Rejected"}
                        </p>
                        <div className="d-flex justify-content-end">
                          <Button
                            variant="info"
                            onClick={() => {
                              setSelectedBlog(blog?.blog_details);
                              setShowViewModal(true);
                            }}
                            className="me-2"
                          >
                            View
                          </Button>
                          <Button
                            variant="warning"
                            onClick={() => {
                              setSelectedBlog(blog);
                              setShowEditModal(true);
                            }}
                            className="me-2"
                          >
                            Edit
                          </Button>
                          <Button
                            variant="danger"
                            onClick={() => handleDelete(blog?.blog_details?.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </ListGroup.Item>
                    ))}
                </ListGroup>
              )}
            </>
          )}
        </Modal.Body>
      </Modal>

      {showViewModal && (
        <ArticleViewModal
          show={showViewModal}
          handleClose={() => setShowViewModal(false)}
          blogDetails={selectedBlog}
        />
      )}

      {showEditModal && selectedBlog && (
        <ArticleEditModal
          show={showEditModal}
          handleClose={() => setShowEditModal(false)}
          blogDetails={selectedBlog}
          fetchBlogs={fetchBlogs}
        />
      )}
    </>
  );
};

export default ViewBlogList;
