.grey-text {
  letter-spacing: 0px;
  color: #5b5b5b;
  opacity: 1;
  font-weight: 700;
}
.user-management-scroll {
  max-height: 618px;
  padding: 12px;
}
.communication-scroll {
  max-height: 545px;
}

.video-clip {
  background: #dfdfdf;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  opacity: 1;
  /* border: 1px solid red; */
  /* height: 100%; */
  width: 345px;
  height: 260px;
}
/* .article-modal-cont {
  width: 65%;
  margin-right: auto;
  margin-left: auto;
  margin-top: 5%;
  z-index: 9999px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 10px #00000029;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
  padding: 15px;
} */

.custom-modal-content-article {
  z-index: 9999px;
}
.allApproval-container {
  background: #fff 0 0 no-repeat padding-box;
  border: 2px solid #dfdfdf;
  border-radius: 8px;
  min-height: 620px;
  padding: 10px;
}
.bg-grey {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border-radius: 5px;
  opacity: 1;
  font-size: 14px;
}
.upload-reviews {
  padding: 8px 15px 0px;
}
.upload-reviews-video {
  padding: 15px;
}
textarea.text-area {
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  opacity: 1;
}
.custom-transperent-btn {
  color: #ff971a;
  font-size: smaller;
  font-weight: 600;
}
button.btn.custom-reject-btn {
  border-radius: 3px;
  background-color: red;
  color: white;
}
button.btn.btn-green {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  background-color: #04ab20;
  color: white;
  float: right;
  font-size: 12px;
}
button.btn.btn-yellow {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
  background-color: #ff971a;
  color: white;
  float: right;
  font-size: 12px;
}
button.btn.btn-purple {
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  background-color: #8107d1;
  color: white;
}
button.btn.btn-red {
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 3px;
  background-color: #b50000;
  color: white;
}

.light-grey-text {
  letter-spacing: 0px;
  color: #96969c;
}
.red-text {
  color: #ff2e2e;
}
.preview-section {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  background-color: #f6f6f6;
  /* padding: 15px; */
  height: 100%;
}
.video-player {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  height: 293px;
  background-color: #000000;
  /* width: 626px; */
}
.transcript-sec {
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  background-color: white;
}
span.blue-text {
  letter-spacing: 0px;
  color: #0793d1;
}
/* ******************8communication*************************** */
.payment-back {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 0px 5px 5px 5px;
}
.queries-background {
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  height: 650px;
}
.tickets-background {
  border: 1px solid #e3e3e3;
  border-radius: 5px;
  opacity: 1;
  background-color: #fdf5ff;
  height: 790px;
  padding: 20px;
}
.box-color {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: inset 0px 2px 3px #00000029;
  border: 1px solid #e3e3e3;
  height: 41px;
  width: 169px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
}
.box-color-2 {
  height: 41px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #5b5b5b;
  width: 450px;
  padding: 20px;
  background-color: #eeeeee;
  border-radius: 5px;
}
.query-border {
  border-left: 3px solid #707070;
}
.ticket-border {
  border-left: 5px solid #707070;
}
.ticket-no-color-1 {
  color: #5b5b5b;
  display: flex;
  margin-left: 315px;
}
.ticket-no-color-2 {
  color: #5b5b5b;
  display: flex;
  margin-left: 331px;
}
.ticket-no-color-3 {
  color: #5b5b5b;
  /* display: flex; */
  margin-left: 259px;
}
.ticket-no-color-4 {
  color: #5b5b5b;
  display: flex;
  margin-left: 325px;
}

.queries-no-color-1 {
  color: #8107d1;
  display: flex;
  margin-left: 54px;
}

.queries-no-color-2 {
  color: #5b5b5b;
  display: flex;
  margin-left: 80px;
}

.queries-no-color-3 {
  color: #04ab20;
  display: flex;
  margin-left: 91px;
}

.queries-no-color-4 {
  color: #ff971a;
  display: flex;
  margin-left: 64px;
}
.queiries-lines {
  background: #ffffff 0% 0% no-repeat padding-box;
  /* height: 790px; */
  padding: 10px;
  max-height: 800px;
  overflow: auto;
}
.line-color {
  /* justify-content: center; */
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  height: 41px;
  display: flex;
  cursor: pointer;
  color: #5b5b5b;
  font-size: 14px;
  font-weight: normal;
  /* justify-items: center; */
}

.remove-btn-style {
  border: none;
  box-shadow: none;
  background-color: transparent;
  border-right: 1px solid #dfdfdf;
  width: 3%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
}
span.urgent-query-2 {
  width: 15%;
  display: flex;
  justify-content: center;
  align-items: center;
}
span.line-of-query-2 {
  width: 75%;
  display: flex;
  align-items: center;
  margin-left: 15px;
  /* border: 1px solid red; */
}
span.line-of-query {
  width: 75%;
  display: flex;
  align-items: center;
  margin-left: 15px;
  /* border: 1px solid red; */
}

span.query-time {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align-last: right;
  width: 25%;
  /* border: 1px solid red; */
}
.query-border-1 {
  border-left: 3px solid #8107d1;
  /* Adjust the color and width as needed */
}

.query-border-2 {
  border-left: 3px solid #96969c;
  /* Adjust the color and width as needed */
}

.query-border-3 {
  border-left: 3px solid #04ab20;
  /* Adjust the color and width as needed */
}

.query-border-4 {
  border-left: 3px solid #ff971a;
  /* Adjust the color and width as needed */
}
.queries-no-color-5 {
  color: #ff2e2e;
  display: flex;
  margin-left: 50%;
  font-weight: bold;
}

.article-query {
  color: #5b5b5b;
  line-height: 28px;
  font-weight: 14px;
  font-weight: bold;
}
.flag-advance {
  float: right;
}
.article-lines {
  color: #5b5b5b;
  line-height: 28px;
  font-size: 15px;
  font-weight: normal;
}
.text-advance {
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 0%;
  /* margin-top: 170px; */
}

.text-advance:focus {
  box-shadow: none;
  background: #fbfbfb 0% 0% no-repeat padding-box;
  border: 1px solid #e3e3e3;
  border-radius: 0%;
}
.attach-button {
  height: 72px;
  font-size: 14px;
  font-weight: bold;
  background: #ffffff 0% 0% no-repeat padding-box;
  border-left: 1px solid #e3e3e3;
  border-right: 1px solid #e3e3e3;
  border-bottom: 1px solid #e3e3e3;
}
label.label-attach {
  color: #b5b2b2;
}
.icon-attach {
  font-size: large;
  margin: 26px 12px 26px 16px;
}

button.btn.btn-reply-msg.btn.btn-primary {
  width: 159px;
  height: 48px;
  float: right;
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  margin: 10px 10px 10px 10px;
}
button.btn.btn-transperent {
  font-size: 14px;
}
.custom-font-size-recents {
  font-size: 12px;
}
.badge.rounded-pill.custom-bg-light {
  box-shadow: inset 0px 3px 6px #00000029;
  background-color: #dfdfdf;
  padding: 10px;
}
.recent-video {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 5px;
  width: 182px;
  height: 128px;
  background-color: black;
}
.content-scroll-profile {
  max-height: 590px;
  padding: 5px;
}
.content-scroll-profile::-webkit-scrollbar {
  display: none;
}
.content-scroll-3 {
  /* max-height: 255px; */
  max-height: 139px;
  /* overflow-y: scroll; */
  padding: 5px;
}
/* 
.content-scroll-3::-webkit-scrollbar {
  display: none;
} */
.form-control.form-fildes-read1:focus {
  box-shadow: none;
  border-color: #dfdfdf;
}
.bg-color-white {
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  background-color: white;
  padding: 5px;
}
.purple-text {
  font-size: 12px;
}
button.btn.btn-transparent.purple-btn-transparent {
  font-size: 14px;
  font-weight: 600;
}
button.btn.btn-transparent.purple-btn-transparent:active {
  border-color: transparent;
}
.single-line {
  white-space: nowrap; /* Prevents text wrapping */
  overflow: hidden; /* Ensures no overflow issues */
  text-overflow: ellipsis; /* Adds "..." if the text is too long */
}
.ratings-div {
  background: #fef4e8 0% 0% no-repeat padding-box;
  border-radius: 3px;
  padding: 0px;
  height: 90%;
}
p.no-preveiw {
  letter-spacing: 0px;
  color: #dfdfdf;
  font-size: 26px;
  margin-top: 255px;
}
.custom-shadow {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 3px;
}
.side-border {
  border-right: 2px solid #f6f6f6;
  opacity: 1;
}

.react-modal-custom {
  position: absolute;
  top: 50%;
  left: 50%;
  right: auto;
  bottom: auto;
  transform: translate(-50%, -50%);
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* .modal-body-custom {
  width: 100%;
} */
.href_link {
  text-decoration: none;
  color: #8107d1;
}

.testimonial_image {
  border-radius: 50%;
}

.custom-approved-btn {
  width: 150px;
  height: 40px;
  float: right;
  background: #04ab20 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  color: white;
}

.custom-rejected-btn {
  width: 150px;
  height: 40px;
  float: right;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  border: none;
  color: white;
}

.custom-small-image {
  width: 100px; /* or any size you want */
  height: 100px; /* or any size you want */
}
.custom-small-image img {
  pointer-events: none;
}

.experts-no-articles-found {
  color: #8107d1;
  opacity: 1;
  font-size: 25px;
  font-weight: 500;
  min-height: 100px;
}
.expert-image-section {
  position: relative;
}

svg.expert-editprofile {
  position: absolute;
  bottom: 0px;
  /* z-index: 1; */
  height: 35px;
  width: 35px;
  padding: 6px;
  border-radius: 50%;
  border: 50%;
  color: #8107d1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 2px solid #dfdfdf;
  font-size: 25px;
  right: 0px;
  cursor: "pointer";
}
button.btn.expert-btn-save-changes-color-change {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border: none;
  height: 48px;
  float: right;
  color: white;
}

.introPreview-heading {
  font-size: 18px;
  font-weight: 600;
  color: #9426b2;
}

.expertCertScroll {
  max-height: 55px;
}

.review-icon {
  color: #8107d1;
  font-size: 30px;
}
.class-span {
  display: flex;
  margin-top: 17px;
  justify-content: center;
}
.single-line {
  white-space: nowrap;
  display: inline-block;
}
.search-input-focus:focus {
  border-color: transparent;
  box-shadow: none;
}
.form-select.select-font {
  font-size: 13px !important;
  box-shadow: 0px 3px 6px #00000029;
  height: 37px;
  border: none;
  border-radius: 0px;
}
.form-select.select-font:focus {
  border: none;
  box-shadow: 0px 3px 6px #00000029;
}

.bg-escalated {
  background-color: #ff7700;
}

.ticket-status {
  cursor: pointer;
}

.query-status-text-badge {
  border-radius: 20px;
  padding: 2px 4px 2px 4px;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

/* Hide the scrollbar but allow scrolling */
.queiries-lines {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  overflow-y: scroll; /* Hide scrollbar but allow scrolling */
}

/* Hide scrollbar for Webkit browsers (Chrome, Safari) */
.queiries-lines::-webkit-scrollbar {
  display: none;
}
