import { useSession } from "next-auth/react";
import React, { useCallback, useEffect, useState } from "react";
import { toast } from "react-toastify";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Loader from "../../loader/Loader";
import { MdDelete, MdEdit } from "react-icons/md";
import { Button, Form, Modal } from "react-bootstrap";
import Swal from "sweetalert2";
import ContentManPlaceholder from "./ContentManPlaceholder";

const CancellationRefund = () => {
  const [hours, setHours] = useState("");
  const [percentage, setPercentage] = useState("");
  const [loading, setLoading] = useState(false);
  const [listOfCancellationPolicy, setListOfCancellationPolicy] = useState([]);
  const [showCancellationEditModal, setShowCancellationEditModal] =
    useState(false);
  const [currentPolicy, setCurrentPolicy] = useState(null);
  const { data: session } = useSession();
  const user_id = session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const hoursOptions = [
    0,
    ...Array.from({ length: 12 }, (_, i) => (i + 1) * 12),
  ];
  const percentageOptions = Array.from({ length: 21 }, (_, i) => i * 5);

  const getListOfCancellationPolicy = useCallback(async () => {
    try {
      if (user_id) {
        const response = await axiosAuth.get(
          `${
            process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE
          }all/?Category=${encodeURIComponent(
            "Cancellation Refund Policy"
          )}&user_id=${user_id}`
        );
        setListOfCancellationPolicy(response?.data);
      }
    } catch (err) {
      console.log("Error in fetching ", err);
    }
  }, [axiosAuth, user_id]);

  const handleCancellationRefundRequest = async () => {
    if (!hours || !percentage) {
      const message = !hours ? "hours" : "percentage";
      toast.error(`Please enter a valid ${message} value.`, {
        autoClose: 3500,
        position: "top-center",
      });
      return;
    }
    try {
      setLoading(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_CREATE_CONTENT_TYPE}?user_id=${user_id}`,
        {
          Content: {
            Time: hours,
            Refund: percentage,
          },
          Category: "Cancellation Refund Policy",
        }
      );
      if (response?.data == "Data already exists") {
        toast.error(response?.data, {
          theme: "colored",
          autoClose: 3500,
          position: "top-center",
        });
      } else if (response?.data) {
        toast.success(`Cancellation refund policy added.`, {
          theme: "colored",
          autoClose: 3500,
          position: "top-center",
        });
        getListOfCancellationPolicy();
      }
    } catch (error) {
      console.log("Error in adding cancellation refund policy.", error);
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 3500,
        position: "top-center",
      });
    } finally {
      setLoading(false);
      setHours("");
      setPercentage("");
    }
  };

  const handleEditClick = (policy) => {
    setCurrentPolicy(policy);
    setShowCancellationEditModal(true);
  };

  const handleEditSubmit = async () => {
    try {
      setLoading(true);
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${currentPolicy.id}/?user_id=${user_id}`,
        {
          Content: {
            Time: currentPolicy.Content.Time,
            Refund: currentPolicy.Content.Refund,
          },
          Category: "Cancellation Refund Policy",
        }
      );
      if (response?.data) {
        toast.success(`Cancellation refund policy edited successfully.`, {
          theme: "colored",
          autoClose: 2500,
          position: "top-center",
        });
        getListOfCancellationPolicy();
      }
    } catch (error) {
      toast.error(`Something Went Wrong!`, {
        theme: "colored",
        autoClose: 2500,
        position: "top-center",
      });
    } finally {
      setLoading(false);
      setShowCancellationEditModal(false);
      setCurrentPolicy(null);
    }
  };

  //   const handleChange = (e) => {
  //     const { name, value } = e.target;
  //     setCurrentPolicy({
  //       ...currentPolicy,
  //       Content: {
  //         ...currentPolicy.Content,
  //         [name]: Number(value),
  //       },
  //     });
  //   };
  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "Time" && !Number.isInteger(Number(value))) {
      toast.error(`Please enter a valid whole number for hours.`, {
        autoClose: 3500,
        position: "top-center",
      });
      return;
    }
    setCurrentPolicy({
      ...currentPolicy,
      Content: {
        ...currentPolicy.Content,
        [name]: Number(value),
      },
    });
  };

  const handleDeleteClick = async (policyId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_CRUD_CONTENT_TYPE}${policyId}/?user_id=${user_id}`
          );
          if (response?.status) {
            toast.success(`Cancellation policy deleted successfully`, {
              theme: "colored",
              autoClose: 2500,
              position: "top-center",
            });
            getListOfCancellationPolicy();
          }
        } catch (error) {
          console.log("Error in deleting the cancellation policy", error);
          toast.error(`Something Went Wrong!`, {
            theme: "colored",
            autoClose: 2500,
            position: "top-center",
          });
        }
      }
    });
  };

  useEffect(() => {
    getListOfCancellationPolicy();
  }, [getListOfCancellationPolicy]);

  if (loading) {
    return <ContentManPlaceholder />;
  }

  return (
    <div>
      <h5 className=" mb-3 admin-add-blog-list-header fw-semibold">
        Add Cancellation Policy
      </h5>
      <label htmlFor="CancellationRefund" className="form-label custom-label">
        Cancellation Refund
      </label>

      <div className="row">
        <div className="col-4">
          <div className="cancellation-desclaimer-section">
            <strong>Disclaimer:</strong> The refund percentage, which represents
            the amount deducted, is calculated based on the time remaining
            before the scheduled meeting.
          </div>
          <div className="row mb-3">
            <div className="col">
              <label htmlFor="hoursInput" className="form-label custom-label">
                Hours
              </label>
              <input
                type="number"
                id="hoursInput"
                className="form-control"
                placeholder="Enter Number of Hours"
                value={hours}
                // onChange={(e) => setHours(Number(e.target.value))}
                onChange={(e) => {
                  const value = Number(e.target.value);
                  if (Number.isInteger(value) && value >= 0) {
                    setHours(value);
                  } else {
                    toast.error(
                      `Please enter a valid whole number for hours.`,
                      {
                        autoClose: 3500,
                        position: "top-center",
                      }
                    );
                  }
                }}
              />
            </div>
            {/* <div className="col">
              <label htmlFor="hoursSelect" className="form-label custom-label">
                Hours Select
              </label>
              <select
                id="hoursSelect"
                className="form-select"
                value={hours}
                onChange={(e) => setHours(Number(e.target.value))}
              >
                {hoursOptions.map((option) => (
                  <option key={option} value={option}>
                    {option} hours
                  </option>
                ))}
              </select>
            </div> */}
          </div>

          <div className="row mb-3">
            <div className="col">
              <label
                htmlFor="percentageInput"
                className="form-label custom-label"
              >
                Percentage
              </label>
              <input
                type="number"
                id="percentageInput"
                className="form-control"
                value={percentage}
                placeholder="Enter Number of Percentage"
                onChange={(e) => setPercentage(Number(e.target.value))}
              />
            </div>
            {/* <div className="col">
              <label
                htmlFor="percentageSelect"
                className="form-label custom-label"
              >
                Percentage Select
              </label>
              <select
                id="percentageSelect"
                className="form-select"
                value={percentage}
                onChange={(e) => setPercentage(Number(e.target.value))}
              >
                {percentageOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}%
                  </option>
                ))}
              </select>
            </div> */}
          </div>
          <button
            disabled={loading}
            type="submit"
            onClick={handleCancellationRefundRequest}
            className="btn purple-button"
          >
            {loading ? "Updating..." : "Update Refund Policy"}
          </button>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th scope="col" style={{ width: "60px" }}>
                    Slno
                  </th>
                  <th scope="col">Timings in hours (hrs)</th>
                  <th scope="col">Refund Deduction Percentage (%)</th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              {/* {loading ? (
                <Loader />
              ) : ( */}
              <tbody>
                {listOfCancellationPolicy &&
                  Array.isArray(listOfCancellationPolicy) &&
                  listOfCancellationPolicy
                    .slice() // Create a copy of the array to avoid mutating the original
                    .sort((a, b) => a.Content.Time - b.Content.Time) // Sort the array based on Time
                    .map((item, index) => (
                      <tr key={index}>
                        <th scope="row" className="custom-font-size">
                          {index + 1}
                        </th>
                        <td className="custom-font-size">
                          {item?.Content?.Time}
                        </td>
                        <td className="custom-font-size">
                          {item?.Content?.Refund} %
                        </td>
                        <td>
                          <button
                            className="btn btn-primary btn-sm purple-button"
                            onClick={() => handleEditClick(item)}
                          >
                            <MdEdit className="custom-font-size" />
                          </button>
                        </td>
                        <td>
                          <button
                            className="btn btn-danger btn-sm"
                            onClick={() => handleDeleteClick(item.id)}
                          >
                            <MdDelete className="custom-font-size" />
                          </button>
                        </td>
                      </tr>
                    ))}
              </tbody>
              {/* )} */}
            </table>
          </div>
        </div>
      </div>
      <Modal
        show={showCancellationEditModal}
        onHide={() => setShowCancellationEditModal(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Edit Cancellation Refund Policy</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentPolicy && (
            <Form>
              <Form.Group controlId="formEditHours">
                <Form.Label>Hours</Form.Label>
                <Form.Control
                  type="number"
                  name="Time"
                  value={currentPolicy.Content.Time}
                  onChange={handleChange}
                />
              </Form.Group>
              <Form.Group controlId="formEditPercentage" className="mt-3">
                <Form.Label>Refund Deduction Percentage</Form.Label>
                <Form.Control
                  type="number"
                  name="Refund"
                  value={currentPolicy.Content.Refund}
                  onChange={handleChange}
                />
              </Form.Group>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={() => setShowCancellationEditModal(false)}
          >
            Cancel
          </Button>
          <Button variant="primary" onClick={handleEditSubmit}>
            Update
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default CancellationRefund;
