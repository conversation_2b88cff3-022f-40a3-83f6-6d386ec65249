import { useRef, useState } from "react";
import { toast } from "react-toastify";
import Modal from "react-bootstrap/Modal";
import Form from "react-bootstrap/Form";
import InputGroup from "react-bootstrap/InputGroup";
import Button from "react-bootstrap/Button";
import ReactPlayer from "react-player";
import ModalImage from "react-modal-image";
import useAxiosAuth from "@/lib/hooks/useAxiosAuth";

const VideoEditModal = ({
  showEditModal,
  setShowEditModal,
  selectedVideo,
  setSelectedVideo,
  fetchVideo,
  userId,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [thumbnailPreview, setThumbnailPreview] = useState(
    selectedVideo?.thumbnail_image || ""
  );

  const formRef = useRef(null);
  const axiosAuth = useAxiosAuth();

  // Handle thumbnail preview update
  // const handleThumbnailChange = (event) => {
  //   const file = event.target.files[0];
  //   if (file) {
  //     const previewURL = URL.createObjectURL(file);
  //     setThumbnailPreview(previewURL);
  //   }
  // };

  const handleThumbnailChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const previewURL = URL.createObjectURL(file);
      setThumbnailPreview(previewURL);
      setSelectedVideo((prev) => ({
        ...prev,
        thumbnail_image: file,
      }));
    }
  };

  const handleUpdate = async (event) => {
    event.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("video_title", selectedVideo.video_title || "");

    const videoFileInput = event.target.elements.video_file;
    const thumbnailFileInput = event.target.elements.thumbnail_image;
    console.log(thumbnailFileInput, "thumbnailFileInput");

    const videoFile = videoFileInput?.files?.[0] || null;
    const thumbnailFile = thumbnailFileInput?.files?.[0] || null;

    // Video File
    if (videoFile) {
      formData.append("video_file", videoFile);
      formData.append("isUrl", "False");
    } else if (selectedVideo.video_file) {
      formData.append("video_file", selectedVideo.video_file);
      formData.append("isUrl", "True");
    }
    console.log(thumbnailFile, "thumbnailFile");

    // Only append thumbnail if it is changed
    // if (thumbnailFile) {
    //   formData.append("thumbnail_image", thumbnailFile);
    // } else if (selectedVideo.thumbnail_image instanceof File) {
    //   // Check if thumbnail is a File instance and append it
    //   formData.append("thumbnail_image", selectedVideo.thumbnail_image);
    // }

    if (selectedVideo.thumbnail_image instanceof File) {
      formData.append("thumbnail_image", selectedVideo.thumbnail_image);
    }

    try {
      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_VIDEO}${selectedVideo?.id}/?user_id=${userId}`,
        formData,
        { headers: { "Content-Type": "multipart/form-data" } }
      );

      if (response.data.isSuccess) {
        toast.success(response.data.message);
        fetchVideo();
        setShowEditModal(false);
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to update video. Try again later...");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      backdrop="static"
      size="lg"
      centered
      show={showEditModal}
      scrollable
      onHide={() => setShowEditModal(false)}
    >
      <Modal.Header closeButton>
        <Modal.Title>Edit</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleUpdate} ref={formRef}>
          <InputGroup className="mb-3">
            <InputGroup.Text>Title</InputGroup.Text>
            <Form.Control
              type="text"
              name="video_title"
              value={selectedVideo?.video_title || ""}
              onChange={(e) =>
                setSelectedVideo({
                  ...selectedVideo,
                  video_title: e.target.value,
                })
              }
            />
          </InputGroup>

          <div className="border p-2 rounded">
            <InputGroup className="mb-3">
              <InputGroup.Text>Video URL</InputGroup.Text>
              <Form.Control
                placeholder="Video URL"
                name="video_file"
                value={selectedVideo.video_file || ""}
                onChange={(e) =>
                  setSelectedVideo({
                    ...selectedVideo,
                    video_file: e.target.value,
                  })
                }
              />
            </InputGroup>
            <p className="text-center">or</p>
            <InputGroup className="mb-3">
              <Form.Control type="file" name="video_file" accept="video/*" />
              <InputGroup.Text>Change Video</InputGroup.Text>
            </InputGroup>
          </div>

          <div className="border p-2 rounded mt-2">
            <InputGroup className="mb-3">
              <InputGroup.Text>Thumbnail</InputGroup.Text>
              <Form.Control
                placeholder="Enter Thumbnail URL"
                name="thumbnail_image"
                value={selectedVideo.thumbnail_image || ""}
                disabled
              />
            </InputGroup>
            <p className="text-start fw-bold">Change Thumbnail Image </p>
            <InputGroup className="mb-3">
              <Form.Control
                type="file"
                name="thumbnail_image"
                accept="image/*"
                onChange={handleThumbnailChange} // Handle preview update
              />
              {/* <InputGroup.Text>Change Thumbnail</InputGroup.Text> */}
            </InputGroup>
          </div>

          {/* Thumbnail preview */}
          <div className="d-flex justify-content-between align-items-center">
            {thumbnailPreview && (
              <div className="mb-2">
                <p>Thumbnail Preview</p>
                <ModalImage
                  small={thumbnailPreview}
                  large={thumbnailPreview}
                  alt="Thumbnail Image"
                  className="videos-thumbnail-image rounded border"
                />
              </div>
            )}
            {selectedVideo.video_file && (
              <div>
                <p>Preview Video</p>
                <div className="rounded border">
                  <ReactPlayer
                    url={selectedVideo.video_file}
                    controls
                    width="300px"
                    height="200px"
                    className="rounded"
                  />
                </div>
              </div>
            )}
          </div>

          <Button
            type="submit"
            className="my-3 bgColor-purple float-end"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Updating..." : "Update Video"}
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default VideoEditModal;
