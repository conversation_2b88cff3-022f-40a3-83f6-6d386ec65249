import Image from "next/image";
import React, { useState } from "react";
import { useEffect } from "react";
import { PiFolderNotchOpenFill } from "react-icons/pi";
import { formatDate, timeDifference } from "./db.js";
import NoDataFound from "../noDataFound/NoDataFound.jsx";
import { toast } from "react-toastify";
import { useSession } from "next-auth/react";
import Swal from "sweetalert2";
import useAxiosAuth from "@/lib/hooks/useAxiosAuth.js";

const FeedbackApprovals = ({
  UnApprovedFeedback,
  unApprovedFeedbackLoading,
  renderPlaceholders,
  noDtaError,
  fetchFeedback,
}) => {
  const [loading, setLoading] = useState(false);
  const { data: session } = useSession();
  const userId = session && session?.user?.id;
  const axiosAuth = useAxiosAuth();

  const handleFeedbackApprovals = async (status, blog_id) => {
    Swal.fire({
      title: `Are you sure you want to ${status} this feedback?`,
      text: "This action cannot be undone!",
      icon: status === "approve" ? "success" : "warning",
      showCancelButton: true,
      confirmButtonText: status === "approve" ? "Approve" : "Reject",
      cancelButtonText: "Cancel",
      confirmButtonColor: status === "approve" ? "#28a745" : "#dc3545",
      cancelButtonColor: "red",
    }).then(async (result) => {
      if (result.isConfirmed) {
        let blog_status = status === "approve" ? 1 : 0;
        try {
          setLoading(true);
          const response = await axiosAuth.put(
            `${process.env.NEXT_PUBLIC_APPROVE_REJECT_FEEDBACK}${blog_id}/?user_id=${userId}`,
            { status: blog_status }
          );

          if (response?.data?.status === 1) {
            toast.success("Feedback Approved.");
          } else if (response?.data?.status === 0) {
            toast.error("Feedback Rejected.");
          }
          fetchFeedback();
        } catch (error) {
          console.error(error);
          toast.error("An error occurred. Please try again.");
        } finally {
          setLoading(false);
        }
      }
    });
  };

  return (
    <>
      <div className="mt-2 ms-2 text-secondary feedback-note">
        <b style={{ color: "#8107d1" }}> Note:</b> Feedback refers to opinions
        or insights shared by experts on the expert platform.
      </div>
      <div className="overflow-hidden">
        <div className="overflow-auto allApproval-tab-scroll">
          <div className="row">
            {unApprovedFeedbackLoading === false ? (
              <>
                {noDtaError ? (
                  <div className="custom-margin-nodatafoud">
                    <NoDataFound />
                  </div>
                ) : UnApprovedFeedback &&
                  Array.isArray(UnApprovedFeedback) &&
                  UnApprovedFeedback.length > 0 ? (
                  UnApprovedFeedback.map((item, index) => (
                    <div key={index} className="col-6 mb-2">
                      <div className="row bg-grey upload-reviews">
                        {/* Feedback Date and Time */}
                        <div className="w-100 d-flex justify-content-between align-items-center">
                          <div>
                            <p className="fw-semibold mb-0">
                              {formatDate(item.CurrentTime)}
                            </p>
                          </div>
                          <div>
                            <p className="custom-transperent-btn mb-0">
                              {timeDifference(item.CurrentTime)}
                            </p>
                          </div>
                        </div>

                        <div className="row px-1">
                          <p className="allApproval-expert-para text-justify">
                            {item.Feedback}
                          </p>
                        </div>

                        <div className="modal-footer px-2 mb-3">
                          <button
                            type="button"
                            className="btn allApproval-reject-btn"
                            disabled={loading}
                            onClick={() =>
                              handleFeedbackApprovals("reject", item.id)
                            }
                          >
                            Reject
                          </button>
                          <button
                            type="button"
                            className="btn allApproval-approve-btn ms-2"
                            disabled={loading}
                            onClick={() =>
                              handleFeedbackApprovals("approve", item.id)
                            }
                          >
                            Approve
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="d-flex justify-content-center align-items-center mt-5 p-5">
                    <h3 className="mt-2 ms-2 text-secondary testimonial-note">
                      No other feedbacks available for approval.
                    </h3>
                  </div>
                )}
              </>
            ) : (
              renderPlaceholders()
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default FeedbackApprovals;
