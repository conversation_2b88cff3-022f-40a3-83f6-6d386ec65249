.top-continer {
    margin-top: 95px;
}

.admin-custom-overflow {
    max-height: 640px;
}

.profile-bg-profilesettings {
    padding: 5px 40px;
    background: transparent linear-gradient(180deg, #f6e6ff 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
    /* width: 100%;
  height: 78px; */
}

p.profile-heading {
    letter-spacing: 0px;
    color: #223645;
    opacity: 1;
    font-size: 21px;
    font-weight: 600;
}

.back-form-con {
    background-color: #fcf2ff;
    margin-bottom: 50px;
}

.pic-holder {
    text-align: center;
    position: relative;
    /* border-radius: 50%; */
    width: 150px;
    height: 150px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-bottom: 20px; */
}

.voilet-text-custom-margin {
    margin-top: 65px;
}

.edit-react-icon {
    background-color: white;
}

.pic {
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center;
    object-position: center;
}

.pic-holder .upload-file-block,
.pic-holder .upload-loader {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(90, 92, 105, 0.7);
    color: #f8f9fc;
    font-size: 12px;
    font-weight: 600;
    opacity: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.pic-holder .upload-file-block {
    cursor: pointer;
}

.pic-holder:hover .upload-file-block,
.uploadProfileInput:focus~.upload-file-block {
    opacity: 1;
}

.pic-holder.uploadInProgress .upload-file-block {
    display: none;
}

.pic-holder.uploadInProgress .upload-loader {
    opacity: 1;
}

/* Snackbar css */
.snackbar {
    visibility: hidden;
    min-width: 250px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 2px;
    padding: 16px;
    position: fixed;
    z-index: 1;
    left: 50%;
    bottom: 30px;
    font-size: 14px;
    transform: translateX(-50%);
}

.snackbar.show {
    visibility: visible;
    -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
    animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

.save-changes-btn {
    width: 15%;
    margin-right: 40px;
}

.edit-icon-container {
    display: flex;
    align-items: center;
    position: absolute;
    right: 10px;
    top: -3px;
}

.pic-edit-mode {
    object-fit: cover;
    background-size: cover;
    width: 50%;
}

@-webkit-keyframes fadein {
    from {
        bottom: 0;
        opacity: 0;
    }

    to {
        bottom: 30px;
        opacity: 1;
    }
}

@keyframes fadein {
    from {
        bottom: 0;
        opacity: 0;
    }

    to {
        bottom: 30px;
        opacity: 1;
    }
}

@-webkit-keyframes fadeout {
    from {
        bottom: 30px;
        opacity: 1;
    }

    to {
        bottom: 0;
        opacity: 0;
    }
}

@keyframes fadeout {
    from {
        bottom: 30px;
        opacity: 1;
    }

    to {
        bottom: 0;
        opacity: 0;
    }
}

.profile-address {
    /* height: 151px;  */
    resize: vertical;
}

.error-msg-medicalrec {
    font-size: 14px;
}

.profile-d-back {
    background-color: white;
}

.profile-d-btn {
    /* width: 32%; */
    border-color: #97012f !important;
    border-width: 2px;
    letter-spacing: 0px;
    color: #97012f !important;
    border-radius: 0;
    font-size: 15px;
}

.profile-d-btn:hover {
    /* width: 32%; */
    border-color: #97012f;
    border-width: 2px;
    letter-spacing: 0px;
    color: #97012f;
    border-radius: 0;
}

.profile-d-btn-1 {
    width: 100%;
    background-color: #8d4f9f;
    color: #ffffff;
    border-radius: 0;
    border: 0;
    font-size: 15px;
    padding: 10px;
}

.profile-d-btn-1:hover {
    background-color: #8d4f9f;
    color: #ffffff;
    border-radius: 0;
    border: 0;
}

.btn.profile-d-btn-1:disabled {
    background-color: #787479;
    color: #ffffff;
    border-radius: 0;
    border: 0;
}

p.purple-text {
    letter-spacing: 0px;
    color: #8d4f9f;
    opacity: 1;
    font-size: 15px;
}

.voilet-text {
    letter-spacing: 0px;
    color: #690062;
    opacity: 1;
    font-size: 18px;
    font-weight: 500;
}

label.purple-text {
    letter-spacing: 0px;
    color: #9426b2;
    opacity: 1;
    font-size: 15px;
    font-weight: 500;
}

.profile-notes {
    height: 100px;
}

.form-control.custom-inpu-p {
    border-color: #e3e3e3;
    box-shadow: 0px 3px 6px #00000029;
    height: 30px;
    font-size: 12px;
}

.form-control.custom-inpu-p:focus {
    border-color: #e3e3e3;
    box-shadow: 0px 3px 6px #00000029;
}

/* SWAL STYLING  */
.swal-confirm-button-class {
    float: inline-end;
    background-color: #4caf50 !important;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 16px;
    margin-right: auto;
    /* This will push the button to the right */
    margin-left: 15px;
}

.swal-confirm-button-class:focus-visible {
    outline: none;
}

.swal-cancel-button-class {
    background-color: #ef5f12;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 16px;
    margin-right: 15px;
}

.swal2-actions {
    width: 100%;
}

.form-control.category-input:disabled {
    background-color: white !important;
    opacity: 1;
}

button.signature-pad-modal-btn {
    border: none;
    border-radius: 0px;
    background: transparent;
}

.custom-col-height {
    height: 90px;
}

span.purple-text {
    letter-spacing: 0px;
    color: #9426b2;
    font-size: 14px;
}

.signature-pad-row {
    background: #ffffff 0% 0% no-repeat padding-box;
    border-radius: 3px;
    width: 50%;
    background: #f9f6f9 0% 0% no-repeat padding-box;
    border-radius: 5px;
    padding: 5px;
    margin-left: 219px;
}

.signature-container {
    text-align: end;
}

.upload-file-block:hover {
    border-radius: 50%;
}

/* ------signature pad modal-------------- */

.custom-signmodal-width {
    max-width: 44%;
}

.signButton {
    font-size: 14px;
    color: #ffffff;
    background: #ed05ed 0% 0% no-repeat padding-box;
    border-radius: 30px;
    border: none;
    box-shadow: 0px 3px 6px #00000029;
    padding: 5px;
}

.sign-heading {
    font-size: 18px;
    font-weight: 600;
    color: #9426b2;
}

.draw-btn {
    background-color: transparent;
    color: black;
    border: none;
    font-size: 18px;
    font-weight: 500;
    padding: 0px;
    border-radius: 0px;
    margin-right: 20px;
    border: none;
    border-bottom: 3px solid transparent;
}

.draw-btn:hover,
.draw-btn:active,
.draw-btn:focus {
    background-color: transparent !important;
    color: black !important;
    border: none !important;
    border-bottom: 3px solid #7b009c !important;
}

.signature-pad {
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 1px solid #7b009c;
    border-radius: 3px;
}

.import-signature-pad {
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 1px solid #7b009c;
    border-radius: 3px;
    margin-top: 10px;
    height: 400px;
}

#videoInput {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

#videoAttachLabelbox {
    position: relative;
    font-weight: 600;
    font-size: 14px;
    width: auto;
    padding: 10px;
    color: #ffffff;
    cursor: pointer;
    background: #9426b2 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
}

.signpara {
    font-size: 15px;
    font-weight: 600;
    color: #223645;
}

.signpad-btn {
    font-size: 14px;
    background: #f37721 0% 0% no-repeat padding-box;
    box-shadow: inset 0px 3px 6px #00000029;
    border: 1px solid #f37721;
    border-radius: 5px;
    color: #ffffff;
}

.upload-sign-btn {
    font-size: 14px;
    background: #7b009c 0% 0% no-repeat padding-box;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    color: #ffffff;
}

.upload-sign-btn:active,
.upload-sign-btn:hover,
.upload-sign-btn:focus {
    background: #7b009c 0% 0% no-repeat padding-box;
    border: 1px solid #e3e3e3;
}

.signpad-cancel-btn {
    font-size: 14px;
    background: #cdcdd4 0% 0% no-repeat padding-box;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    color: #ffffff;
}

.signpad-cancel-btn:active,
.signpad-cancel-btn:hover,
.signpad-cancel-btn:focus {
    background: #cdcdd4 0% 0% no-repeat padding-box !important;
    border: 1px solid #e3e3e3 !important;
}

/* -------------------admin profile settings css--------------------- */
.main-contanier {
    background: #ffffff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 0px 5px 5px 5px;
    padding: 10px;
}

.profile-setting-heading {
    letter-spacing: 0px;
    color: #8107d1;
    text-decoration: none;
    font-size: 21px;
    font-weight: 500;
    cursor: pointer;
}

.form-control.profile-address {
    height: 115px;
    resize: vertical;
    box-shadow: 0px 3px 6px #00000029;
    opacity: 1;
    border: none;
    font-size: 12px;
}

.form-control.profile-address:focus {
    border-color: #e3e3e3;
    box-shadow: 0px 3px 6px #00000029;
}

.profile-pic-wrapper {
    /* width: 100%; */
    position: relative;
    display: flex;
    align-items: center;
}


.form-control.custom-input-control {
    box-shadow: 0px 3px 6px #00000029;
    opacity: 1;
    border: none;
    /* background: #f1f1f1 0% 0% no-repeat padding-box; */
    font-size: 12px;
    border: 1px solid #e3e3e3;
}

.form-select.custom-input-control {
    box-shadow: 0px 3px 6px #00000029;
    opacity: 1;
    border: none;
    /* background: #f1f1f1 0% 0% no-repeat padding-box; */
    font-size: 12px;
    border: 1px solid #e3e3e3;
}

.form-control.custom-input-control:focus {
    box-shadow: 0px 3px 6px #00000029;
    font-size: 12px;
    border: 1px solid #e3e3e3;
}

.form-select.custom-input-control:focus {
    box-shadow: 0px 3px 6px #00000029;
    font-size: 12px;
    border: 1px solid #e3e3e3;
}

.form-control.input-form-modal-phone:focus {
    box-shadow: none;
}

.form-control:disabled {
    opacity: 1;
    color: grey;
}

.p-s-subheading {
    letter-spacing: 0px;
    color: #5b5b5b;
    font-size: 16px;
    font-weight: 700;
}

.notification-tab {
    /* width: 65%;
  height: 131px; */
    overflow-y: auto;
    overflow-x: hidden;
}

.single-notification-tab {
    background: #f6f6f6 0% 0% no-repeat padding-box;
    /* border-radius: 5px 21px 21px 5px; */
    padding: 4px;
}

span.notification-name {
    letter-spacing: 0px;
    color: #223645;
    font-size: 14px;
    font-weight: 500;
}

button.profile-submit-btn {
    background: #f37721;
    color: white;
    box-shadow: 0px 3px 50px #00000014;
    border-radius: 5px;
    opacity: 1;
    border: none;
    font-size: 14px;
    padding: 10px 20px 10px 20px;
}

/* Scrollbar css */
.notification-tab::-webkit-scrollbar {
    width: 8px;
}

.notification-tab::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notification-tab::-webkit-scrollbar-thumb {
    background-color: #8107d1;
    border-radius: 4px;
}

.notification-tab::-webkit-scrollbar-thumb:hover {
    background-color: #a242e1;
}

/* child admin css */

.content-scroll {
    max-height: 375px;
}

.content-scroll::-webkit-scrollbar {
    display: none;
}

.forgot-pwd-link {
    letter-spacing: 0px;
    color: #f37721;
    font-size: 16px;
    font-weight: 600;
}

/* input[type="date"]::before {
  content: "dd/mm/yyyy";
  color: #aaa;
  display: inline-block;
  position: absolute;
  pointer-events: none;
  padding-left: 5px;
}
input[type="date"].has-value::before {
  display: none;
} */

.profile-sub-btn {
    /* width: 32%; */
    border-color: #04ab20 !important;
    border-width: 2px;
    letter-spacing: 0px;
    color: #04ab20 !important;
    border-radius: 0;
    font-size: 15px;
}

.password-eye {
    border: none;
    background-color: transparent;
}

.tick-mark-userpermissions {
    color: green;
}

.admin-content-scroll {
    max-height: 760px;
}

.phone-number-change-button {
    font-size: 13px !important;
    color: #713c8f !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    z-index: 999px;
    background-color: white !important;
    padding: 3.7px !important;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px !important;
    border-top: 1px solid rgb(227, 227, 227) !important;
    border-bottom: 1px solid rgb(227, 227, 227) !important;
    border-right: 1px solid rgb(227, 227, 227) !important;
    right: 0px !important;
}

.childadmin-content-scroll {
    max-height: 730px;
    padding: 12px;
}