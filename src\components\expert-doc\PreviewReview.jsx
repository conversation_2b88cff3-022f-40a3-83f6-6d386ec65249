import React from "react";

const PreviewReview = ({ selectedPreview }) => {
  return (
    <>
      <div className="row">
        <p className="m-3 introPreview-heading">Review Preview</p>
        <div className="col-sm-10 mx-auto">
          <p className="fw-semibold mt-4">
            {" "}
            Patient Email : {selectedPreview?.PatientEmail[0]}
          </p>
          <p className="">Description</p>
          <p
            className=""
            dangerouslySetInnerHTML={{
              __html: selectedPreview.Review,
            }}
          ></p>
        </div>
      </div>
    </>
  );
};

export default PreviewReview;
