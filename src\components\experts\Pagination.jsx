import React from "react";
import { BsFastForwardFill } from "react-icons/bs";
import { TbPlayerTrackPrevFilled } from "react-icons/tb";
import "../userManagement/usermanagement.css";

const Pagination = ({ currentPage, setCurrentPage, totalPages }) => {
  const prevPage = () => {
    const newPage = Math.max(currentPage - 1, 1);
    setCurrentPage(newPage);
  };

  const nextPage = () => {
    const newPage = Math.min(currentPage + 1, totalPages);
    setCurrentPage(newPage);
  };

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  const renderPagination = () => {
    const maxPagesToShow = 12;
    const pages = [];

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <li
            key={i}
            onClick={() => paginate(i)}
            className={`page-item ${
              currentPage === i ? "active active-page" : ""
            }`}
          >
            <a className="page-link custom-page-link active-index" href="#">
              {i}
            </a>
          </li>
        );
      }
    } else {
      const startPage = Math.max(
        1,
        currentPage - Math.floor(maxPagesToShow / 2)
      );
      const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

      if (startPage > 1) {
        pages.push(
          <li key={1} onClick={() => paginate(1)} className="page-item">
            <a className="page-link custom-page-link active-index" href="#">
              1
            </a>
          </li>
        );
        pages.push(
          <li key="ellipsis-start" className="page-item disabled">
            <span className="page-link">...</span>
          </li>
        );
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(
          <li
            key={i}
            onClick={() => paginate(i)}
            className={`page-item ${
              currentPage === i ? "active active-page" : ""
            }`}
          >
            <a className="page-link custom-page-link active-index" href="#">
              {i}
            </a>
          </li>
        );
      }

      if (endPage < totalPages) {
        pages.push(
          <li key="ellipsis-end" className="page-item disabled">
            <span className="page-link">...</span>
          </li>
        );
        pages.push(
          <li
            key={totalPages}
            onClick={() => paginate(totalPages)}
            className={`page-item ${
              currentPage === totalPages ? "active active-page" : ""
            }`}
          >
            <a className="page-link custom-page-link active-index" href="#">
              {totalPages}
            </a>
          </li>
        );
      }
    }

    return pages;
  };

  return (
    <div>
      <div>
        <nav aria-label="Page navigation example">
          <ul className="pagination justify-content-center custom-pagination">
            <li
              onClick={prevPage}
              className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
            >
              <a
                className="page-link custom-page-link"
                href="#"
                tabIndex="-1"
                aria-disabled="true"
              >
                <span className="page-count">
                  {currentPage === 1 ? 1 : "Prev"}
                </span>
                <TbPlayerTrackPrevFilled style={{ color: "#96969C" }} />
              </a>
            </li>
            {renderPagination()}
            <li
              onClick={nextPage}
              className={`page-item ${
                currentPage === totalPages ? "disabled" : ""
              }`}
            >
              <a className="page-link custom-page-link" href="#">
                <BsFastForwardFill style={{ color: "#96969C" }} />
                <span className="page-count"> Next </span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default Pagination;
