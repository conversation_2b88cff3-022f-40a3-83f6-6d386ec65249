a/* -----------------------Profile dropdown----------------- */
.dropdown-menu {
  --bs-dropdown-min-width: 13rem;
}

.modal-content-deactivate-modal {
  /* background: #ffffff 0% 0% no-repeat padding-box; */
  box-shadow: 0px 3px 10px #00000029;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
}

/* .custom-deactivate-width {
  margin-top: 10%;
} */
.custom-title-2 {
  font-size: 20px;
  color: #9426b2;
  font-weight: 500;
  margin-bottom: 0px !important;
}
textarea.custom-textarea {
  width: 100%;
  height: 90px;
  /* background-color: grey; */
  border: 1px solid lightgrey;
}
textarea.custom-textarea:focus-visible {
  outline: none;
  border: 1px solid lightgrey;
}
.btn-deact {
  background-color: #8107d1;
  color: white;
  border: none;
}
.btn-deact:hover {
  background-color: #9a40d5;
  color: white;
  border: none;
}
.btn-cancel-deact {
  background-color: #414146;
  color: white;
  border: none;
}

.dropdown-item.actions.dropdown-item.actions {
  color: #1c2126;
  background-color: transparent;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-bottom: -13px;
}
.dropdown-item.actions.active,
.dropdown-item.actions:active {
  color: #1c2126;
  background-color: transparent;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-bottom: -13px;
}
.dropdown-item.actions-1.dropdown-item.actions-1 {
  color: #ff2e2e;
  background-color: transparent;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}
.dropdown-item.actions-1.active,
.dropdown-item.actions-1:active {
  color: #ff2e2e;
  background-color: transparent;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}
.dropdown-toggle::after {
  content: none;
}
.modal-delete {
  display: flex;
  height: 50px;
  top: 0;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 5px 5px 0px 0px;
}
p.delete-confirm {
  text-align: center;
  margin-top: 50px;
  font-weight: bold;
  font-size: 16px;
  letter-spacing: 0.5px;
  color: #333333;
}
p.delete-confirm-sure {
  text-align: center;
  margin-top: -8px;
  color: #333333;
  font-weight: normal;
  font-size: 16px;
  letter-spacing: 0.5px;
}
button.btn.btn-delete {
  height: 48px;
  float: right;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #fff;
  border: none;
  width: 159px;
}
button.btn.btn-delete:focus {
  height: 48px;
  float: right;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #fff;
  border: none;
  width: 159px;
}
h4.title-edit {
  font-weight: bold;
  font-size: 18px;
  color: #5b5b5b;
}
.main-purple-text {
  color: #8107d1;
  font-size: 21px;
  font-weight: 600;
}

/* -------New User button--------- */
button.btn.btn-newuser {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffffff;
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
button.btn.btn-newuser:focus {
  background: transparent linear-gradient(180deg, #8107d1 0%, #e20ed4 100%) 0%
    0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 5px;
  color: #ffffff;
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* -----------------New Admmin button----------------- */
button.btn.btn-newadmin {
  background: #ff7700 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  width: 100%;
  color: #ffff;
}
button.btn.btn-newadmin:focus {
  background: transparent linear-gradient(180deg, #ff971a 0%, #e23a0e 100%) 0%
    0% no-repeat padding-box;
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  width: 100%;
  color: #ffff;
}
/* -------------------Setting amd Notification---------------------------------- */
.icon-setting {
  font-size: 35px;
  color: #8107d1;
}

.icon-bell {
  stroke-width: 2px;
  font-size: 37px;
  color: #8107d1;
}

button.btn.btn-bell {
  position: relative;
}
button.btn.btn-bell {
  position: relative;
}

span.start-100.translate-middle.badge.rounded-pill {
  border: 2px solid #6d00b5;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 2px 10px #ababab4a;
  position: absolute;
  margin-top: 6px;
  margin-left: -21px;
  top: 0;
  padding-left: 3px;
  height: 61%;
  color: #7100bb;
  width: 50%;
  padding-top: 8px;
  padding-right: -38px;
}
/* --------------------------profile Dropdown---------------------------- */
button.btn.btn-profile {
  width: 80%;
  height: 38px;
  border: none;
  color: #fff;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  border-radius: 3px;
}
button.btn.btn-profile:focus {
  width: 80%;
  height: 38px;
  border: none;
  color: #fff;
  background: #ff2e2e 0% 0% no-repeat padding-box;
  border-radius: 3px;
}
.custom-modal-content-user {
  width: 600px;
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 10px #00000029;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
  height: 800px;
}
.modal-header-allhead {
  height: 57px;
  background: #f6f6f6 0% 0% no-repeat padding-box;
  border: 1px solid #dfdfdf;
  border-radius: 5px 5px 0px 0px;
}
.title-edit.modal-title.h4 {
  font-size: 18px;
  font-weight: bold;
  color: #5b5b5b;
}
button.btn.btn-cancle-plan {
  font-size: 18px;
  font-weight: 500;
  color: #ff2e2e;
  border: none;
  float: right;
}
button.btn.btn-cancle-plan:focus {
  font-size: 18px;
  font-weight: 500;
  color: #ff2e2e;
  border: none;
}

label.label-edit.form-label {
  font-weight: bold;
  color: #333333;
  font-size: 16px;
  margin-left: 1px;
}
label.label-select {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

/* ------------------select radio button and Its Forms----------------------- */
label.label-select.input-radio-style {
  width: 100%;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}

label.label-select.input-radio-style:active,
label.label-select.input-radio-style:hover {
  width: 100%;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  background: #fdf5ff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}
.input-form-modal-phone:disabled,
.gender-select:disabled,
.expert-select-timezone:disabled,
.form-select:disabled {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: auto;
  background-color: #ffffff 0% 0% no-repeat padding-box;
  font-size: 14px;
}
.icon-size {
  font-size: 14px; /* Adjust size as needed */
}

.timezone-icon {
  color: #8107d1;
  font-size: 34px;
  /* position: absolute;
  display: flex;
  margin-top: 8px; */
}
input.form-control.input-form-modal {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
}
input.form-control.input-form-modal-1 {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
  padding-left: 190px;
}
select.input-form-modal {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
}
span.handle-text {
  position: absolute;
  margin: 7px 35px 34px;
  font-size: 14px;
  font-weight: 300;
  width: 143px;
  padding: 8px;
  border-right: 1px solid #dfdfdf;
  color: #5b5b5b;
}
select.input-form-modal:focus {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  height: 48px;
}
textarea.input-form-modal {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}
textarea.input-form-modal:focus {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}
button.btn.purple-modal-btn {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffff;
  border: none;
  width: 100%;
  margin-top: 42px;
  height: 48px;
}
button.btn.purple-modal-btn:hover {
  background: #9b33e0 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffff;
  border: none;
  width: 100%;
  margin-top: 42px;
  height: 48px;
}
button.btn.purple-modal-btn:focus {
  background: #8107d1 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 50px #00000014;
  border-radius: 5px;
  color: #ffff;
  border: none;
  width: 100%;
  margin-top: 42px;
  height: 48px;
}
.all-icon {
  color: #8107d1;
  font-size: 25px;
  position: absolute;
  margin-top: 11px;
  margin-left: 11px;
  padding: -6px;
}
/* --------------------------------New Admin Modal ------------------------------------------- */
.custom-modal-content-admin.modal-for-newadmin {
  width: 900px;
}
.form-check-input:checked[type="checkbox"] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%238107D1' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input {
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
  background-color: #dfdfdf;
  box-shadow: inset 0px 3px 6px #00000029;
  border: none;
  border-radius: 3px;
}
.form-check.newadminlabel .form-check-input:checked {
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
  background-color: #dfdfdf;
  box-shadow: inset 0px 3px 6px #00000029;
  border: none;
  border-radius: 3px;
}
.form-check.newadminlabel-1 .form-check-input:checked {
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
  background-color: #8107d1;
  box-shadow: inset 0px 3px 6px #00000029;
  border: none;
  border-radius: 3px;
}
.form-check-input:focus {
  border-color: #ffff;
  outline: 0;
  box-shadow: none;
}
.form-check.newadminlabel {
  width: 175px;
  height: 36px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}
.form-check.newadminlabel .form-check-input {
  float: left;
  margin-left: -17px;
}
.form-check.newadminlabel-1 .form-check-input {
  float: left;
  margin-left: -17px;
}

label.form-check-label {
  display: inline-block;
  margin-left: 16px;
  margin-top: 0px;
  font-size: 14px;
}
.form-check.newadminlabel-1 {
  width: 105px;
  height: 36px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
}

.form-check.newadminlabel-1 .form-check-input:checked[type="checkbox"] {
  background-image: none !important;
}

.href_link {
  text-decoration: none;
  color: #8107d1;
}
.input-form-modal-phone {
  width: 100%;
}

.new-user-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
