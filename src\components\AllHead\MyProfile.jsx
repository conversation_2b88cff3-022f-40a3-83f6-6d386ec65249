"use client";

import Cookies from "js-cookie";
import { use<PERSON>ara<PERSON>, usePathname, useRouter } from "next/navigation";
import React, { useContext, useEffect, useState } from "react";
import Dropdown from "react-bootstrap/Dropdown";
import { toast } from "react-toastify";
import "./allhead.css";
import ExpertProfileStatus from "../Modals/ExpertProfileStatus";
import useAxiosAuth from "../../lib/hooks/useAxiosAuth";
import { useAdminContext } from "../../Context/AdminContext/AdminContext";
import Swal from "sweetalert2";

const MyProfile = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [isProfileRestricted, setIsProfileRestricted] = useState(false);
  const [deactivationReason, setDeactivationReason] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [reasonCategory, setReasonCategory] = useState("");
  const [userProfileStatus, setUserProfileStatus] = useState("");
  const { isAdminChildAdmin, userPermissions, session } = useAdminContext();
  const isPermissible = userPermissions?.includes("cu_app.deactivate_user");
  const hasDeletePermission = userPermissions?.includes("cu_app.delete_cuuser");

  const router = useRouter();
  const axiosAuth = useAxiosAuth();
  const pathname = usePathname();

  const admin_id = session && session?.user?.id;
  const params = useParams();
  const { user_id } = params;
  const user_Id = user_id[0];
  const user_email = decodeURIComponent(user_id[1]);
  const user_Status = user_id[2];

  useEffect(() => {
    if (user_Status) {
      setUserProfileStatus(user_Status);
    }
  }, [user_Status]);

  const handleDropdownToggle = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const handleOpenModal = (action) => {
    if (isAdminChildAdmin && !hasDeletePermission) {
      toast.info(`You do not have Permission for this`, {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
    } else {
      setSelectedAction(action);
      setShowModal(true);
    }
  };

  const handleProfileApprovals = async (status, reason = null) => {
    try {
      let body = { approval: status };
      if (status === "Deactivated") {
        body.StatusChangeReason = deactivationReason;
        body.ReasonType = "Deactivation";
        body.ReasonCategory = reasonCategory;
      } else if (status === "Rejected") {
        body.StatusChangeReason = rejectionReason;
        body.ReasonType = "Rejection";
        body.ReasonCategory = reasonCategory;
      }

      const response = await axiosAuth.put(
        `${process.env.NEXT_PUBLIC_UPDATE_EXPERT_STATUS}${user_Id}/?user_id=${admin_id}`,
        body
      );
      if (response?.data?.data?.approval === status) {
        toast.success(`The Profile has been ${status}`);
        if (pathname.startsWith("/usermanagement/patients/")) {
          router.push(
            `/usermanagement/patients/${user_Id}/${user_email}/${response?.data?.approval}/${response?.data?.data?.name}`
          );
        } else if (pathname.startsWith("/usermanagement/experts/")) {
          router.push(
            `/usermanagement/experts/experts-doctor/${user_Id}/${user_email}/${response?.data?.approval}`
          );
        }

        setUserProfileStatus(response?.data?.approval); // Update the status state
      } else {
        toast.error(response?.data?.data);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteProfile = async (user_email) => {
    if (isAdminChildAdmin && !hasDeletePermission) {
      toast.info(`You do not have permission for this.`, {
        position: "top-center",
        theme: "colored",
        autoClose: 2500,
      });
      return;
    }

    Swal.fire({
      title: "Are you sure you want to delete this profile?",
      text: "Deleting this profile will prevent the user from accessing the application, but the data will be stored in the database for 6 months.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#8107d1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete this profile.",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.put(
            `${process.env.NEXT_PUBLIC_DELETE_USER_PROFILE}${user_email}/?user_id=${admin_id}`,
            {
              approval: "Deleted",
            }
          );
          if (response?.status === 200) {
            toast.success("User profile deleted successfully");
            if (pathname.startsWith("/usermanagement/patients/")) {
              router.push(
                `/usermanagement/patients/${user_Id}/${user_email}/${response?.data?.approval}/${response?.data?.name}`
              );
            } else if (pathname.startsWith("/usermanagement/experts/")) {
              router.push(
                `/usermanagement/experts/experts-doctor/${user_Id}/${user_email}/${response?.data?.approval}`
              );
            }

            setUserProfileStatus(response?.data?.approval); // Update the status state
          } else {
            toast.error("Failed to delete profile.");
          }
        } catch (error) {
          console.error("Error deleting profile: ", error);
          toast.error("Something went wrong!");
        }
      }
    });
  };

  return (
    <>
      <Dropdown show={dropdownOpen} onToggle={handleDropdownToggle}>
        <Dropdown.Toggle variant="profile" id="dropdown-profile">
          {selectedAction ? `${selectedAction}` : "Profile Actions"}
        </Dropdown.Toggle>
        <Dropdown.Menu>
          {isProfileRestricted ? (
            <Dropdown.Item className="actions">
              Remove Restriction
              <hr />
            </Dropdown.Item>
          ) : (
            <>
              {userProfileStatus === "Approved" ? null : (
                <Dropdown.Item
                  className="actions text-justify"
                  onClick={() => handleProfileApprovals("Approved")}
                >
                  Approve Profile
                  <hr />
                </Dropdown.Item>
              )}
              {userProfileStatus === "Approval_requested" &&
                userProfileStatus !== "Approved" &&
                userProfileStatus !== "Rejected" &&
                userProfileStatus !== "Deactivated" && (
                  <Dropdown.Item
                    className="actions text-justify"
                    onClick={() => handleOpenModal("Reject Profile")}
                  >
                    Reject Profile
                    <hr />
                  </Dropdown.Item>
                )}
            </>
          )}
          {userProfileStatus === "Approved" &&
            userProfileStatus !== "Deactivated" && (
              <Dropdown.Item
                className="actions-1 mb-1"
                onClick={() => handleOpenModal("Deactivate Profile")}
              >
                Deactivate Profile
                <hr />
              </Dropdown.Item>
            )}
          {userProfileStatus !== "Deleted" && (
            <Dropdown.Item
              className="actions-1 mb-1"
              onClick={() => handleDeleteProfile(user_email)}
            >
              Delete Profile
            </Dropdown.Item>
          )}
        </Dropdown.Menu>
      </Dropdown>

      {showModal && (
        <ExpertProfileStatus
          selectedAction={selectedAction}
          setSelectedAction={setSelectedAction}
          showModal={showModal}
          setShowModal={setShowModal}
          setDeactivationReason={setDeactivationReason}
          setRejectionReason={setRejectionReason}
          handleProfileApprovals={handleProfileApprovals}
          deactivationReason={deactivationReason}
          rejectionReason={rejectionReason}
          reasonCategory={reasonCategory}
          setReasonCategory={setReasonCategory}
        />
      )}
    </>
  );
};

export default MyProfile;
