/* .bg-color {
  background-color: #f0f0f5;
  padding: 20px;
} */
.consent-form-box {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 12px;
  background-color: white;
}
.bg-heading {
  background: transparent linear-gradient(180deg, #9d2ba5 0%, #ca00d9 100%) 0%
    0% no-repeat padding-box;
  border-radius: 5px;
}
.consent-addContent {
  padding: 30px;
  height: 200px;
}

.doctors-consent-text{
    background-color: #fcebfc;
    padding: 10px;
    text-align: justify;
}

.heading {
  color: white;
  font-size: 17px;
}
.content-scroll {
  max-height: 600px;
}

.content-scroll::-webkit-scrollbar {
  display: none;
}
.form-control.custom-form-placeholder::placeholder {
  font-weight: 500;
  font-size: 14px;
  color: black;
}
.custom-form-label {
  letter-spacing: -0.15px;
  color: #4a2f91;
  font-size: 17px;
  font-weight: 400;
}
input.form-control.custom-form-control {
  border-radius: 5px;
  background-color: white;
  border: none;
}
input.form-control.custom-form-control:focus {
  border-color: transparent;
  background-color: white !important;
}
.custom-para {
  letter-spacing: -0.13px;
  font-size: 14px;
}
button.btn.orange-btn {
  background-color: #f37721;
  border-radius: 10px;
  color: white;
  font-size: 12px;
  padding-right: 20px;
  padding-left: 20px;
}

button.btn-orange {
  background-color: #f37721;
  border-radius: 16px;
  color: white;
  font-size: 12px;
  min-height: 50px;
  padding: 8px 20px;
  border: 0px;
  font-size: 15px;
}

