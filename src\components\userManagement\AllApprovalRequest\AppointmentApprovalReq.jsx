import Image from "next/image";
import Link from "next/link";
import React, { useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { capitalizeFullName } from "../../expert-doc/db";
import CustomPagination from "../../CustomPagination/CustomPagination";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import Swal from "sweetalert2";
import { toast } from "react-toastify";
import PatientApprovalPlaceholder from "./PatientApprovalPlaceholder";
import NoDataFound from "@/components/noDataFound/NoDataFound";

const AppointmentApprovalReq = ({ userId }) => {
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [singleApp, setSingleApp] = useState({});
  const [loading, setLoading] = useState(true);
  const [patientAppointmentReqs, setpatientAppointmentReqs] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [current_page, setCurrent_Page] = useState(1);
  const [initialLoading, setInitialLoading] = useState(true);
  const [approveLoading, setApproveLoading] = useState(false); // Loading state for approve button
  const [rejectLoading, setRejectLoading] = useState(false);

  const axiosAuth = useAxiosAuth();

  const fetchAllPatientAppointmentReqs = useCallback(
    async (pageNumber) => {
      try {
        setLoading(true);
        if (pageNumber === 1) setInitialLoading(true);
        let url = `${process.env.NEXT_PUBLIC_GET_APPOINTMENTS_FILTER}?app_status=Cancellation Pending&user_id=${userId}`;

        if (pageNumber) {
          url += `&page=${pageNumber}`;
        }
        const response = await axiosAuth.get(url);
        setpatientAppointmentReqs(response?.data);
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
        if (pageNumber === 1) setInitialLoading(false);
      }
    },
    [userId, axiosAuth]
  );

  useEffect(() => {
    fetchAllPatientAppointmentReqs(current_page);
  }, [fetchAllPatientAppointmentReqs, current_page]);

  const handleApproveRejectCancellation = async (isApproved) => {
    const shouldEdit = await Swal.fire({
      title:
        isApproved === "C"
          ? "Do You Want to Approve the Cancellation ?"
          : "Do You Want to Reject the Cancellation ?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancel",
      confirmButtonColor: "#9426B2",
      cancelButtonColor: "#B50000",
      backdrop: "rgba(0,0,0,0.4)",
    });
    if (!shouldEdit.isConfirmed) {
      return;
    }

    const bodyData = { status: isApproved };
    try {
      if (isApproved) {
        setApproveLoading(true); // Set loading state for approve button
      } else {
        setRejectLoading(true); // Set loading state for reject button
      }
      if (isApproved) {
        const response = await axiosAuth.put(
          `${process.env.NEXT_PUBLIC_CANCEL_APPOINTMENT}${singleApp?.app_data?.id}/?user_id=${userId}`,
          bodyData
        );
        if (response?.data?.message?.status === "C") {
          toast.success("Cancellation approved Successfully!", {
            autoClose: 3000,
            theme: "colored",
            position: "top-center",
          });
        } else if (response?.data?.message?.status === "C_R") {
          toast.success("Cancellation rejected Successfully!", {
            autoClose: 3000,
            theme: "colored",
            position: "top-center",
          });
        }
      }
      setShowReasonModal(false);
      await fetchAllPatientAppointmentReqs(1);
    } catch (error) {
      console.error("Error Approving/Rejecting:", error);
      toast.error("Error in Approving/Rejecting the Cancellation", {
        autoClose: 3000,
        theme: "colored",
        position: "top-center",
      });
      // Handle the error (e.g., show an error message to the user)
    } finally {
      setApproveLoading(false); // Reset loading state for approve button
      setRejectLoading(false);
    }
  };
  const handleOpenModal = (appointment) => {
    setSingleApp(appointment);
    setShowReasonModal(true);
  };
  return (
    <div className="col-sm-6">
      <p className="allApproval-subHeading mt-2">
        Appointment Cancellation Request
      </p>
      <table className="table mt-2">
        <thead className="allApproval-patient-head">
          <tr className="">
            <th scope="col" className="text-center allApproval-heading">
              Patient Name
            </th>
            <th scope="col" className="text-center allApproval-heading">
              Date of Appointment
            </th>
            <th scope="col" className="text-center allApproval-heading">
              Status
            </th>
            <th scope="col" className="text-center allApproval-heading">
              Action
            </th>
          </tr>
        </thead>
        <tbody className="allApproval-patient-head">
          {patientAppointmentReqs &&
          patientAppointmentReqs?.items?.length === 0 ? (
            <tr>
              <td colSpan="4">
                <div className=" mt-5">
                  <NoDataFound />
                </div>
              </td>
            </tr>
          ) : loading ? (
            <PatientApprovalPlaceholder />
          ) : (
            patientAppointmentReqs &&
            patientAppointmentReqs?.items?.map((appointment, index) => (
              <tr key={index} className="allApproval-patient-custom-row">
                <td className="text-center allApproval-patient-content d-flex align-items-center pr-4">
                  {appointment?.patient_photo ? (
                    <>
                      <Image
                        src={appointment?.patient_photo}
                        alt={`Dr ${appointment?.patient_details?.name}`}
                        width={35}
                        height={35}
                        className="expert_image"
                      />
                    </>
                  ) : (
                    <>
                      <Image
                        src={dummyProfile}
                        alt={`Dr ${appointment?.patient_details?.name}`}
                        width={35}
                        height={35}
                        className="expert_image"
                      />
                    </>
                  )}
                  <Link href="" className="text-decoration-none">
                    <span className="allApproval-patient-content">
                      {capitalizeFullName(appointment?.patient_details?.name)}
                    </span>
                  </Link>
                </td>
                <td className="text-center">
                  {
                    appointment?.slot_details?.schedule_start_time?.split(
                      "T"
                    )[0]
                  }
                </td>
                <td
                  className="text-center"
                  style={{ color: "orange", cursor: "pointer" }}
                >
                  Cancellation_requested
                </td>
                <td
                  className="text-center fw-semibold"
                  style={{ color: "#8107d1", cursor: "pointer" }}
                  onClick={() => {
                    handleOpenModal(appointment);
                  }}
                >
                  View
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
      <div className="d-flex justify-content-center align-items-center">
        <div className="d-none d-xl-block">
          {patientAppointmentReqs?.total_pages !== 1 && (
            <CustomPagination
              total_pages={patientAppointmentReqs?.total_pages}
              current_page={current_page}
              setCurrent_Page={setCurrent_Page}
            />
          )}
        </div>
      </div>
      <Modal
        show={showReasonModal}
        onHide={() => {
          setShowReasonModal(false);
        }}
        centered
      >
        <Modal.Header>
          <Modal.Title className="allApproval-subHeading">
            Reason for Appointment Cancellation
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>{singleApp?.appointment_cancel_reason}</p>
        </Modal.Body>

        <Modal.Footer>
          <button
            type="button"
            className="btn allApproval-reject-btn"
            onClick={() => handleApproveRejectCancellation("C_R")}
          >
            Reject
          </button>

          <button
            onClick={() => handleApproveRejectCancellation("C")}
            type="button"
            className="btn allApproval-approve-btn ms-2"
          >
            Approve
          </button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AppointmentApprovalReq;
