import React, { useState } from "react";
import Image from "next/image";
import dummyProfile from "../../../../public/images/dummy-avatar.jpg";
import { highlightText } from "../../../utils/helperfunction";
import noDataFound from "../../../../public/assets/noDataFound.png";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import { Placeholder } from "react-bootstrap";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import ViewTestimonialModal from "./ViewTestimonialModal";
import RankingModal from "../BlogsContent/RankingModal";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import NoDataFound from "@/components/noDataFound/NoDataFound";

const renderPlaceholders = (value) => {
  const placeholders = Array.from(
    { length: value === "initialLoad" ? 7 : 1 },
    (_, index) => (
      <tr key={index}>
        <td colSpan="12">
          <div key={index} className="">
            <Placeholder as="p" animation="glow">
              <Placeholder
                xs={12}
                size={"lg"}
                style={{ height: "50px", borderRadius: "4px" }}
              />
            </Placeholder>
          </div>
        </td>
      </tr>
    )
  );

  return placeholders;
};

const headers = [
  "S No",
  "Select Testimonial",
  "Expert Name",
  "Date of Upload",
  "Expert Role",
  "View Testimonial",
  "Delete Ranking",
];

const ExpertTestimonialsList = ({
  loading,
  initialLoading,
  current_page,
  setCurrent_Page,
  expertTestimonials,
  searchQuery,
  handleDeleteRanking,
  handleAddRanking,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState({});
  const [showTestimonialModal, setShowTestimonialsModal] = useState(false);
  const [singleTestimonial, setSingleTestimonial] = useState({});
  const [deletingPodcastId, setDeletingPodcastId] = useState(null);

  const { data: session } = useSession();

  const handleViewTestimonialModal = (item) => {
    setSingleTestimonial(item);
    setShowTestimonialsModal(true);
  };

  const handleDeletePodcastRanking = async (id) => {
    try {
      setDeletingPodcastId(id);
      await handleDeleteRanking(id, "expert");
    } catch (err) {
      toast.error("Error in delete ranking", err);
    } finally {
      setDeletingPodcastId(null);
    }
  };

  return (
    <>
      {showTestimonialModal && (
        <ViewTestimonialModal
          showTestimonialModal={showTestimonialModal}
          setShowTestimonialsModal={setShowTestimonialsModal}
          id={singleTestimonial.id}
          name={singleTestimonial.expert_name}
          feedback={singleTestimonial.Feedback}
          time={singleTestimonial.CurrentTime}
          role={singleTestimonial.FeedbackCategory}
        />
      )}
      <div className="">
        <div className="row">
          <div className="col-sm-12 fixTableHead">
            <table className="table mt-2 custon-table-featured">
              <thead className="custom-border">
                <tr className="custom-name">
                  {headers?.map((header, index) => (
                    <th
                      key={header}
                      scope="col"
                      // className={`fw-semibold text-center child-admin-list-heading ${
                      //   index === 4 ? "col-4" : "col"
                      // }`}
                    className="fw-semibold text-center child-admin-list-heading"
                      style={{fontSize: "14px"}}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="custom-border">
                {initialLoading ? (
                  renderPlaceholders("initialLoad")
                ) : (
                  <>
                    {expertTestimonials &&
                    Array.isArray(expertTestimonials.items) &&
                    expertTestimonials?.items?.length === 0 ? (
                      <tr>
                        <td colSpan="5" className="text-center">
                          <NoDataFound />
                        </td>
                      </tr>
                    ) : (
                      expertTestimonials &&
                      Array.isArray(expertTestimonials.items) &&
                      expertTestimonials?.items?.map((blog, index) => {
                        return (
                          <tr
                            key={index}
                            className="custom-row table-content-blogs"
                          >
                            <td className="col custom-font text-center">
                              {(current_page - 1) * 10 + index + 1}
                            </td>

                            <td className="  col custom-font text-center">
                              <div className=" d-flex align-items-center">
                                <input
                                  className=" ms-5 form-check-input  blogs-content-input"
                                  type="checkbox"
                                  name="category"
                                  id="expertCategory"
                                  value="Expert Rejection"
                                  checked={blog?.selected_content_id !== null}
                                  onChange={() =>
                                    handleAddRanking(
                                      blog.id,
                                      "expert",
                                      blog.ExpertId
                                    )
                                  }
                                />
                                <div className=" ms-2 ">
                                  {blog?.status !== null && (
                                    <span className="badge badge-success bg-success">
                                      Selected
                                    </span>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="col purple-content text-capitalize">
                            <div className=" d-flex align-items-center">
                              {blog?.expert_photo ? (
                                <Image
                                  src={`${blog?.expert_photo}`}
                                  alt={`Dr ${blog?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              ) : (
                                <Image
                                  src={dummyProfile}
                                  alt={`Dr ${blog?.expert_name}`}
                                  width={35}
                                  height={35}
                                  className="expert_image_blogs_list"
                                />
                              )}
                              {highlightText(blog?.expert_name, searchQuery)}
                              </div>
                            </td>
                            <td className="col custom-font">
                            <div className=" d-flex justify-content-center align-items-center">
                              {blog?.CurrentTime?.split("T")[0]}
                              </div>
                            </td>
                            <td className=" text-capitalize fw-medium col-4 custom-font">
                            <div className=" d-flex align-items-center">
                              {blog?.expert_role}
                              </div>
                            </td>
                            <td className="text-center fw-semibold col">
                            <div className=" d-flex align-items-center">
                              <button
                                style={{ color: "#8107D1" }}
                                className=" border-0 bg-white"
                                onClick={() => handleViewTestimonialModal(blog)}
                              >
                                view
                              </button>
                              </div>
                            </td>
                            <td className="text-center fw-semibold col">
                            <div className=" d-flex align-items-center">
                              <button
                                style={{ color: "red", fontSize: "15px" }}
                                className=" border-0 bg-white"
                                onClick={() =>
                                  handleDeletePodcastRanking(
                                    blog?.selected_content_id
                                  )
                                }
                                disabled={deletingPodcastId === blog?.id}
                              >
                                {deletingPodcastId === blog?.id
                                  ? "Deleting..."
                                  : "Delete"}
                              </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </>
                )}
                {loading && renderPlaceholders("load")}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="d-flex justify-content-center align-items-center mt-3">
        <div className="d-none d-xl-block">
          <CustomPagination
            total_pages={expertTestimonials?.total_pages}
            current_page={current_page}
            setCurrent_Page={setCurrent_Page}
          />
        </div>
      </div>
    </>
  );
};

export default ExpertTestimonialsList;
