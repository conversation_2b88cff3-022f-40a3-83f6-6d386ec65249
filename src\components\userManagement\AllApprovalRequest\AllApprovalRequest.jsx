"use client";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import AllIntroVideo from "./AllIntroVideo.jsx";
import AllTestimonials from "./AllTestimonials.jsx";
import AllConsentForm from "./AllConsentForm.jsx";
import AllFeedback from "./AllFeedback.jsx";
import AllBlogs from "./AllBlogs.jsx";
import AllPodcats from "./AllPodcasts.jsx";
import AllPatientApprovals from "./AllpatientApprovals.jsx";
import "./allApprovalRequest.css";
import "./allConsentForm.css";
import { useSession } from "next-auth/react";
import Cookies from "js-cookie";

const AllApprovalRequest = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const initialTab = searchParams.get("tab");
  //   const { session } = useContext(AdminDetailsContext);
  const [activeTab, setActiveTab] = useState(initialTab || "introVideo");

  const { data: session } = useSession();
  const userId = Cookies.get("userId") || session?.user?.id;

  const handleTabChange = (tab) => {
    const updatedPath = `${pathname}?tab=${tab}`;
    router.replace(updatedPath, { shallow: true });
    setActiveTab(tab);
  };
  return (
    <div className="row">
      <div className="allApproval-container">
        <>
          <p className="d-inline-flex gap-1 buttons-row mb-0 mt-3 mb-0">
            <button
              className={`btn approvals-grey-btn ${
                activeTab === "introVideo" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("introVideo")}
            >
              Intro Videos
            </button>
            <button
              className={`btn approvals-grey-btn ${
                activeTab === "testimonials" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("testimonials")}
            >
              Testimonials
            </button>
            <button
              className={`btn approvals-grey-btn ${
                activeTab === "consentForm" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("consentForm")}
            >
              Consent Form
            </button>
            <button
              className={`btn approvals-grey-btn ${
                activeTab === "feedback" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("feedback")}
            >
              Feedback
            </button>
            <button
              className={`btn approvals-grey-btn ${
                activeTab === "blogs" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("blogs")}
            >
              Blogs
            </button>
            {/* <button
              className={`btn approvals-grey-btn ${
                activeTab === "podcasts" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("podcasts")}
            >
              Podcasts
            </button> */}
            <button
              className={`btn approvals-grey-btn ${
                activeTab === "patientApprovals" ? "activeApprovalsTab" : ""
              }`}
              onClick={() => handleTabChange("patientApprovals")}
            >
              Patient Approvals
            </button>
          </p>
          {activeTab === "introVideo" && <AllIntroVideo />}
          {activeTab === "testimonials" && <AllTestimonials />}
          {activeTab === "consentForm" && <AllConsentForm />}
          {activeTab === "feedback" && <AllFeedback userId={userId} />}
          {activeTab === "blogs" && <AllBlogs userId={userId} />}
          {/* {activeTab === "podcasts" && <AllPodcats />} */}
          {activeTab === "patientApprovals" && (
            <AllPatientApprovals userId={userId} />
          )}
        </>
      </div>
    </div>
  );
};

export default AllApprovalRequest;
