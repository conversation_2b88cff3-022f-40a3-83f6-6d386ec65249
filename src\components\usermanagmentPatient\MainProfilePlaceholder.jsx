import React from "react";
import { Placeholder, Row, Col } from "react-bootstrap";

const PlaceholderItem = () => (
  <Placeholder as="p" className="mb-5" animation="glow">
    <Placeholder
      size="lg"
      style={{ height: "43px", borderRadius: "3px" }}
      xs={12}
    />
  </Placeholder>
);

const MainProfilePlaceholder = () => (
  <>
    <div className="row">
      <div className="col-sm-6">
        <div className="form-group form-back">
          <div className="row">
            <div className="col-sm-6 mb-4" style={{ marginTop: "70px" }}>
              <Placeholder as="div" animation="glow">
                <Placeholder
                  className="rounded-circle"
                  style={{ width: "150px", height: "150px" }}
                />
              </Placeholder>
            </div>
            <div className="col-sm-6">
              <Placeholder as="div" animation="glow">
                <Placeholder
                  className="rounded"
                  style={{ width: "100%", height: "100px", marginTop: "70px" }}
                />
              </Placeholder>
            </div>
          </div>
          <Row>
            <Col sm={6}>
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
            </Col>
            <Col sm={6}>
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
            </Col>
          </Row>
          <div className="mb-2">
            <PlaceholderItem />
          </div>
        </div>
      </div>
      <div className="col-sm-6">
        <div className="form-group form-back">
          <Row className=" mt-5">
            <Col sm={6}>
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
            </Col>
            <Col sm={6}>
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
              <PlaceholderItem />
            </Col>
          </Row>
          <div className="mb-2">
            <PlaceholderItem />
          </div>
        </div>
      </div>
    </div>
  </>
);

export default MainProfilePlaceholder;
