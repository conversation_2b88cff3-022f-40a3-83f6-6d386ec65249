"use client";
import ReportType from "./ReportType";
import ExpertExpertise from "./UserProfileContent/ExpertExpertise";
import ReschedulingMeetingUpdate from "./UserProfileContent/ReschedulingMeetingUpdate";
import CancellationRefund from "./UserProfileContent/CancellationRefund";
import AllFAQsSection from "./UserProfileContent/AllFAQsSection";
import AllVideosByAdmin from "./UserProfileContent/AllVideosByAdmin";
import AddCategory from "./UserProfileContent/AddCategory";
import AddUpdates from "./Updates/AddUpdates";
import CommonTopics from "../ContentManagementParent/contentManagementTabs/commonTopics/CommonTopics";
import EmbeddedExperts from "../ContentManagementParent/contentManagementTabs/embeddedExperts/EmbeddedExperts";
const AddExperts = () => {
  return (
    <>
      <div className="overflow-hidden">
        <div className=" overflow-auto contentManage-custom-scroll">
        <div className="bg-color mb-4 p-2">
            <EmbeddedExperts />
          </div>
          <div className="bg-color mb-4 p-2">
            <CommonTopics />
          </div>
          <div className="bg-color mb-4 p-2">
            <AddUpdates />
          </div>
          <div className="bg-color mb-4 p-2">
            <ExpertExpertise />
          </div>
          <div className="bg-color mb-4 p-2">
            <ReportType />
          </div>
          {/* <div className="bg-color mb-4">
            <AddChildAdminDesignation />
          </div> */}
          {/* <div className="bg-color mb-4">
            <UserRejectingCategory />
          </div>
          <div className="bg-color mb-4">
            <UserDeactivatingCategory />
          </div> */}
          
          <div className=" bg-color mb-4 p-2">
            <CancellationRefund />
          </div>

          <div className=" bg-color mb-4 p-2">
            <ReschedulingMeetingUpdate />
          </div>

          {/* <div className=" bg-color mb-4">
            <AddPodcast />
          </div> */}
          <div className=" bg-color mb-4 p-2">
            <AllFAQsSection />
          </div>
          {/* <div className=" bg-color mb-4">
            <BlogByAdmin />
          </div> */}
           <div className=" bg-color mb-4 p-2">
            <AllVideosByAdmin />
          </div>
        </div>
      </div>
    </>
  );
};

export default AddExperts;
