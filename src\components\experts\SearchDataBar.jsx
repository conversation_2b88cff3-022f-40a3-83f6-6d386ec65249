import React, { useEffect, useRef, useState } from "react";
import Buttons from "../userManagement/Buttons";
import { FaCalendar, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { GrSearch } from "react-icons/gr";

const SearchDataBar = ({
  heading,
  onStatusChange,
  selectedStatus,
  searchQuery,
  endDate,
  startDate,
  setStartDate,
  setEndDate,
  setSearchQuery,
  totalExperts,
  expert,
  setCurrent_Page,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const searchMenu = useRef(null);

  const handleStatusChange = (e) => {
    const status = e.target.value;
    onStatusChange(status);
  };

  const handleCalendarClick = () => {
    setShowPicker(!showPicker);
  };
  const handleClearFilter = (e) => {
    e.stopPropagation();
    handleDateFilterChange(null, null);
    setShowPicker(false);
  };
  const handleDateFilterChange = (start, end) => {
    setCurrent_Page(1);
    setStartDate(start);
    setEndDate(end);
    if (start && end) {
      setShowPicker(false);
    }
  };
  const handleClearQueryFilter = () => {
    setSearchQuery("");
  };
  const closeDatePicker = (e) => {
    if (showPicker && !searchMenu.current?.contains(e.target)) {
      setShowPicker(false);
    }
  };

  if (typeof window !== "undefined") {
    document.addEventListener("mousedown", closeDatePicker);
  }
  useEffect(() => {
    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", closeDatePicker);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", closeDatePicker);
      }
    };
  }, []);

  return (
    <div className="row ">
      <div className="col-sm-2">
        <p className="expert-heading">{heading}</p>
        <p className="expert-total">
          Total {expert} : {totalExperts}
        </p>
      </div>
      <div className="col-sm-2 ">
        <select
          className="form-select filterwidth select-font"
          aria-label=".form-select-sm example"
          onChange={handleStatusChange}
          value={selectedStatus}
        >
          <option value="all">By Status </option>
          <option value="1">Approved</option>
          <option value="0">Pending</option>
          <option value="2">Rejected</option>
          <option value="3">Deactivated</option>
          <option value="6">Deleted</option>
          <option value="4">Approval Requested</option>
          <option value="5">Self Deactivation</option>
        </select>
      </div>
      <div className="col-sm-2">
        <div ref={searchMenu} className=" calender-filter-container">
          <span className="date-filter-expert" onClick={handleCalendarClick}>
            {startDate
              ? `${startDate.toLocaleDateString()} - ${
                  endDate ? endDate.toLocaleDateString() : ""
                }`
              : "Select Date"}
            <span style={{ zIndex: 1 }} className="calendar-icon-expert">
              {startDate ? (
                <FaTimes
                  className=" cross-icon-calendar"
                  onClick={handleClearFilter}
                />
              ) : (
                <FaCalendar className=" calender-icon-calendar" />
              )}
            </span>
          </span>

          {showPicker && (
            <div style={{ position: "absolute", zIndex: 1 }}>
              <DatePicker
                selected={startDate}
                startDate={startDate}
                endDate={endDate}
                selectsRange
                inline
                showTimeSelect={false} // Disable time selection
                onChange={(dates) => handleDateFilterChange(dates[0], dates[1])}
              />
            </div>
          )}
        </div>
      </div>

      <div className="col-sm-2">
        <div className=" expert-search-bar  position-relative input-group">
          <input
            type="text"
            className="form-control"
            style={{
              boxShadow: "none",
              height: "40px",
              border: "0px",
              borderRadius: "3px",
              fontSize: "13px",
            }}
            placeholder="Search for Expert"
            aria-label="Recipient's username"
            aria-describedby="button-addon2"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrent_Page(1);
            }}
            // onChange={(e) => onSearchChange(e.target.value)}
          />
          <span
            className="input-group-text custom-search-icon"
            id="button-addon2"
            style={{borderRadius: "5px"}}
          >
            <GrSearch style={{ color: "#8107d1" }} />
          </span>
          <span style={{ zIndex: 9999 }} className="cancel-expert-search-btn">
            {searchQuery && (
              <FaTimes
                className=" cross-icon-calendar"
                onClick={handleClearQueryFilter}
              />
            )}
          </span>
        </div>
      </div>
      <div className="col-sm-4">
        <Buttons />
      </div>
    </div>
  );
};

export default SearchDataBar;
