import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { MdEdit, MdDelete } from "react-icons/md";
import { Button, Modal, Form, InputGroup } from "react-bootstrap";
import useAxiosAuth from "../../../lib/hooks/useAxiosAuth";
import ContentManPlaceholder from "./ContentManPlaceholder";
import { FaEye } from "react-icons/fa";
import Swal from "sweetalert2";
import { toast } from "react-toastify";
import { useSession } from "next-auth/react";
import ReactPlayer from "react-player";
import { BsFillInfoCircleFill, BsSearch } from "react-icons/bs";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { debounce } from "lodash";
import { IoCloseSharp } from "react-icons/io5";
import { GrSearch } from "react-icons/gr";
import { highlightText } from "@/utils/helperfunction";
import VideoEditModal from "./VideoEditModal";

const AllVideosByAdmin = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [videosList, setVideosList] = useState([]);
  const [error, setError] = useState(false);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [current_page, setCurrent_Page] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [showEditModal, setShowEditModal] = useState(false);
 
  const axiosAuth = useAxiosAuth();
  const { data: session } = useSession();
  const userId = session && session?.user?.id;
  const formRef = useRef();

  const fetchVideo = useCallback(async () => {
    try {
      setIsLoading(true);
      const url = `${process.env.NEXT_PUBLIC_GET_VIDEOS}?${
        debouncedSearchTerm
          ? `search=${debouncedSearchTerm}`
          : `page=${current_page}`
      }`;

      const response = await axiosAuth.get(url);
      if (response.data.isSuccess) {
        const items = response.data.items || [];
        const total_pages = response?.data?.total_pages;
        setVideosList(items);
        setTotalPages(total_pages);
      } else {
        setVideosList([]);
      }
    } catch (error) {
      console.error(error);
      setError(true);
      toast.error("Something went wrong! Please refresh the page...");
    } finally {
      setIsLoading(false);
    }
  }, [axiosAuth, current_page, debouncedSearchTerm]);

  const debouncedSetSearchTerm = useMemo(
    () => debounce(setDebouncedSearchTerm, 500),
    []
  );

  useEffect(() => {
    debouncedSetSearchTerm(searchTerm);
  }, [searchTerm, debouncedSetSearchTerm]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  useEffect(() => {
    fetchVideo();
  }, [fetchVideo, debouncedSearchTerm]);

  const handleSubmit = async (event) => {
    event.preventDefault();

    const formData = new FormData();
    const isUrl = event.target.videoUrl.value !== "";
    const thumbnailImage = event.target.thumbnailImage.files[0];
    const videoUrl = event.target.videoUrl.value;
    const videoFile = event.target.videoFile.files[0];
    const videoTitle = event.target.title.value;

    if (!videoTitle) {
      toast.error("Please add video title");
      return;
    }
    if (!videoFile && !videoUrl) {
      toast.error("Please add video URL or video file");
      return;
    }
    if (!thumbnailImage) {
      toast.error("Please add thumbnail image");
      return;
    }

    formData.append("isUrl", isUrl ? "True" : "False");
    thumbnailImage && formData.append("thumbnail_image", thumbnailImage);
    formData.append("video_title", videoTitle);
    formData.append("video_file", isUrl ? videoUrl : videoFile);

    try {
      setIsSubmitting(true);
      const response = await axiosAuth.post(
        `${process.env.NEXT_PUBLIC_ADD_VIDEO}?user_id=${userId}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response.data.isSuccess) {
        toast.success(response.data.message);
        fetchVideo();
        formRef.current.reset();
      }
    } catch (error) {
      console.log(error);
      Swal.fire({
        icon: "error",
        title: "Oops...",
        text: "Something went wrong! Please try again later.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (videoId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      confirmButtonColor: "#8107d1",
      cancelButtonText: "Cancel",
      cancelButtonColor: "#d33",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axiosAuth.delete(
            `${process.env.NEXT_PUBLIC_ADD_VIDEO}${videoId}/?user_id=${userId}`
          );
          if (response?.data?.isSuccess) {
            toast.success(response.data.message);
            fetchVideo();
          }
        } catch (error) {
          console.log(error);
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Something went wrong! Please try again later.",
          });
        }
      }
    });
  };

  const handleView = (videoUrl) => {
    setSelectedVideo(videoUrl);
    setShowVideoPlayer(true);
  };

  useEffect(() => {
    fetchVideo();
  }, [fetchVideo]);

  const handleEdit = (video) => {
    setSelectedVideo(video);
    setShowEditModal(true);
  };
  return (
    <>
      <div className="row">
        <div className=" d-flex justify-content-between align-items-center">
          <h5 className="mb-3 admin-add-blog-list-header fw-semibold">
            Add Videos
          </h5>
          <div
            className={` col-sm-3 d-flex justify-content-end align-items-center `}
          >
            <div className="input-group search-input-patient calender-filter-container">
              <input
                type="text"
                style={{
                  borderRadius: "3px",
                  border: "none",
                }}
                className="form-control search-input-focus"
                placeholder={"Search Videos..."}
                aria-label="search videos..."
                aria-describedby="button-addon2"
                onChange={handleSearchChange}
                value={searchTerm}
              />

              <span
                className="input-group-text custom-search-icon"
                id="button-addon2"
                style={{ borderRadius: "5px" }}
              >
                {!searchTerm && (
                  <GrSearch
                    style={{
                      color: "#7B009C",
                    }}
                  />
                )}
              </span>

              <style jsx>{`
                ::placeholder {
                  color: #212529;
                }
              `}</style>
              {!isLoading && searchTerm && (
                <IoCloseSharp
                  className="clear-search-icon position-absolute"
                  style={{
                    fontSize: "20px",
                    cursor: "pointer",
                    right: "6px",
                    top: "10px",
                    color: "#7B009C",
                  }}
                  onClick={() => {
                    setSearchTerm("");
                  }}
                />
              )}
            </div>
          </div>
        </div>

        <div className="col-sm-4">
          <form onSubmit={handleSubmit} ref={formRef}>
            <div className="mb-1">
              <label htmlFor="title" className="form-label custom-label">
                Add Video Title
              </label>
              <input
                type="text"
                className="form-control custom-form-control"
                id="title"
                name="title"
                placeholder="Enter here"
                required
              />
            </div>
            <div className="mb-1">
              <label
                htmlFor="thumbnailImage"
                className="form-label custom-label"
              >
                Add Thumbnail Image
              </label>
              <input
                type="file"
                className="form-control custom-form-control"
                id="thumbnailImage"
                name="thumbnailImage"
                accept="image/*"
              />
            </div>
            <div className="rounded border p-1">
              <div className="">
                <label htmlFor="videoUrl" className="form-label custom-label">
                  Add Video URL
                </label>
                <input
                  type="url"
                  className="form-control custom-form-control"
                  id="videoUrl"
                  name="videoUrl"
                  placeholder="Enter video URL"
                />
              </div>
              <p className="text-center mb-0">or</p>
              <div>
                <label htmlFor="videoFile " className="form-label custom-label">
                  Add Video File
                </label>
                <input
                  type="file"
                  className="form-control custom-form-control"
                  id="videoFile"
                  name="videoFile"
                  accept="video/*"
                />
              </div>
            </div>
            <button
              disabled={isSubmitting}
              type="submit"
              className="btn purple-button mt-1"
            >
              {isSubmitting ? "Uploading.." : "Upload Video"}
            </button>
          </form>
        </div>
        <div className="col-sm-8">
          <div className="table-wrapper">
            <table className="table">
              <thead className="sticky-table-head">
                <tr>
                  <th
                    scope="col"
                    className="text-center"
                    style={{ width: "60px" }}
                  >
                    Sl No
                  </th>
                  <th scope="col">Video Title</th>
                  <th scope="col">
                    Video URL &nbsp;
                    <span
                      style={{ fontSize: "12px" }}
                      className="text-muted text-center"
                    >
                      <BsFillInfoCircleFill color="#8107d1" />
                      &nbsp; Click URL to watch video
                    </span>
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Edit
                  </th>
                  <th scope="col" style={{ width: "100px" }}>
                    Delete
                  </th>
                </tr>
              </thead>
              <tbody>
                {videosList?.length == 0 && !error && (
                  <tr>
                    <td colSpan="6" className="text-center">
                      No videos uploaded yet.
                    </td>
                  </tr>
                )}
                {Array.isArray(videosList) &&
                  videosList?.map((item, index) => (
                    <tr key={item.id}>
                      <th scope="row" className="text-center custom-font-size">
                        {(current_page - 1) * 10 + index + 1}
                      </th>
                      <td className="custom-font-size w-25">
                        {highlightText(item.video_title, searchTerm)}
                      </td>
                      <td
                        className="custom-font-size"
                        style={{ cursor: "pointer" }}
                        onClick={() => handleView(item)}
                      >
                        {item.video_file}
                      </td>
                      <td>
                        <button
                          className="btn btn-primary btn-sm purple-button"
                          onClick={() => handleEdit(item)}
                        >
                          <MdEdit className="custom-font-size" />
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDelete(item.id)}
                        >
                          <MdDelete className="custom-font-size" />
                        </button>
                      </td>
                    </tr>
                  ))}
                {error && videosList.length == 0 && (
                  <tr>
                    <td colSpan="6" className="text-center">
                      No Data Found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          {totalPages > 1 && (
            <CustomPagination
              total_pages={totalPages}
              current_page={current_page}
              setCurrent_Page={setCurrent_Page}
            />
          )}
        </div>
      </div>

      {showVideoPlayer && (
        <Modal
          show={showVideoPlayer}
          onHide={() => setShowVideoPlayer(false)}
          centered
          size="lg"
        >
          <Modal.Body className="bg-transparent p-0 rounded">
            <ReactPlayer
              url={selectedVideo?.video_file}
              controls
              width="100%"
              className="rounded"
              light={
                selectedVideo.thumbnail_image
                  ? selectedVideo.thumbnail_image
                  : true
              }
            />
          </Modal.Body>
        </Modal>
      )}
      {showEditModal && (
        <VideoEditModal
          showEditModal={showEditModal}
          setShowEditModal={setShowEditModal}
          selectedVideo={selectedVideo}
          setSelectedVideo={setSelectedVideo}
          fetchVideo={fetchVideo}
          userId={userId}
        />
      )}
    </>
  );
};

export default AllVideosByAdmin;
