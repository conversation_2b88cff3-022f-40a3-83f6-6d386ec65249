'use client'

import PaymentPriceToggle from "../../components/paymentandpricing/PaymentPriceToggle";

import "../../components/AllHead/allhead.css";
import "../../components/paymentandpricing/paymentandprice.css";
import Navbar from "../../components/navbar/Navbar";
import withAuth from "../../withAuth/withAuth";

const Homepage = () => {
  return (
    <main>
      <div className="container-fluid">
        <div className="row ">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <PaymentPriceToggle />
          </div>
        </div>
      </div>
    </main>
  );
};

export default withAuth(Homepage);
