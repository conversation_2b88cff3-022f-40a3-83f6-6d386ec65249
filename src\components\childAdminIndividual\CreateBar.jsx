// BarGraph.js
import { useEffect } from 'react';
import * as d3 from 'd3';

const BarGraph = () => {
  const data = [
    { label: 'Jan', value: 20 },
    { label: 'Feb', value: 50 },
    { label: 'Mar', value: 90 },
    { label: 'Apr', value: 20 },
    { label: 'May', value: 50 },
    { label: 'Jun', value: 30 },
    { label: 'Jul', value: 20 },
    { label: 'Aug', value: 80 },
    { label: 'Sep', value: 90 },
    { label: 'Oct', value: 20 },
    { label: 'Nov', value: 70 },
    { label: 'Dec', value: 30 },
    // Add more data points as needed
  ];

  const targetValue = 40; // Adjust this value to set the horizontal line position

  useEffect(() => {
    // Set up margin and dimensions
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };
    const width = 640 - margin.left - margin.right;
    const height = 310- margin.top - margin.bottom;

    // Create x and y scales
    const x = d3.scaleBand().range([0, width]).padding(0.4);
    const y = d3.scaleLinear().range([height, 0]);

    // Set domains for x and y scales
    x.domain(data.map((d) => d.label));
    y.domain([0, d3.max(data, (d) => d.value)]);

    // Create SVG element
    const svg = d3.select('#bar-graph');

    // Clear existing content
    svg.selectAll('*').remove();

    const gradient = svg
      .append('defs')
      .append('linearGradient')
      .attr('id', 'bar-gradient')
      .attr('x1', '0%')
      .attr('y1', '0%')
      .attr('x2', '0%')
      .attr('y2', '100%');

    gradient
      .append('stop')
      .attr('offset', '0%')
      .style('stop-color', '#F7D8FF');

    gradient
      .append('stop')
      .attr('offset', '100%')
      .style('stop-color', '#D18AFF');

    // Add x-axis with thicker gray line and values
    svg
      .append('g')
      .attr('transform', `translate(${margin.left},${height + margin.top + 5})`)
      .call(
        d3.axisBottom(x)
          .tickSize(0)
          .tickPadding(10)
      )
      .select('.domain')
      .attr('stroke', '#D3D3D3')
      .attr('stroke-width', 2);

    // Add horizontal dotted line
    // svg
    //   .append('line')
    //   .attr('x1', margin.left)
    //   .attr('y1', y(targetValue) + margin.top)
    //   .attr('x2', width + margin.left)
    //   .attr('y2', y(targetValue) + margin.top)
    //   .attr('stroke', '#D3D3D3')
    //   .attr('stroke-width', 2)
     

    // Add bars with space between bars and x-axis line
    svg
      .selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d) => x(d.label) + margin.left)
      .attr('y', (d) => y(d.value) + margin.top)
      .attr('width', x.bandwidth() - 5)
      .attr('height', (d) => height - y(d.value))
      .attr('fill', 'url(#bar-gradient)')
      .attr('stroke', '#fff')
      .attr('stroke-width', 1);
  }, [data]);

  return (
    <svg id="bar-graph" width={640} height={310}>
      
    </svg>
  );
};

export default BarGraph;
