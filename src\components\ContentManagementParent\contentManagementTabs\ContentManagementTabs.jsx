"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Tab from "react-bootstrap/Tab";
import Tabs from "react-bootstrap/Tabs";
import Home from "../../administratorDasboard/Home";
import "../../../components/userManagement/usermanagement.css";
import "../../../components/administratorDasboard/home.css";
import Navbar from "../../navbar/Navbar";
import AddExperts from "../../../components/contentManagement/AddExperts";
import BlogsContent from "../BlogsContent/BlogsContent";
import PodcastContent from "../PodcastContent/PodcastContent";
import ManageUsersTab from "../contentManagementTabs/ManageUsersTab";
import TestimonialsParent from "../../ContentManagementParent/testimonialTabs/TestimonialsParent";

const ContentManagementTabs = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("overview");

  // Use searchParams to get the 'tab' parameter.
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    setActiveTab(tabParam || "overview");
  }, [searchParams]);

  const handleSelect = (key) => {
    router.push(`?tab=${key}`); // Update query parameter on tab select.
  };

  return (
    <main>
      <div className="container-fluid">
        <div className="row">
          <div className="col-sm-1 navbar-parent">
            <Navbar />
          </div>
          <div className="col-sm-11">
            <div className="row">
              <div className="col-sm-12">
                <div className="row mt-3">
                  <div className="col-sm-8">
                    <p className="main-purple-text">Content Management</p>
                  </div>
                  <Home />
                </div>
              </div>
            </div>
            <Tabs
              // defaultActiveKey="overview"
              activeKey={activeTab}
              onSelect={handleSelect}
              id="uncontrolled-tab-example"
              className=" custom-content-tab"
            >
              <Tab className="" eventKey="overview" title="Overview">
                <AddExperts />
              </Tab>
              <Tab className="" eventKey="blogs" title="Blogs">
                <BlogsContent />
              </Tab>
              {/* <Tab className="" eventKey="podcast" title="Podcast">
                <PodcastContent />
              </Tab> */}

              <Tab
                className=""
                eventKey="askruchikaexperts"
                title="Manage Users"
              >
                <ManageUsersTab />
              </Tab>
              <Tab
                className=""
                eventKey="testimonials"
                title="Manage Testimonials"
              >
                <TestimonialsParent />
              </Tab>
            </Tabs>
          </div>
        </div>
      </div>
    </main>
  );
};

export default ContentManagementTabs;
