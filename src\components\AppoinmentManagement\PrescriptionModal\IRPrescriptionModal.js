import React from "react";
import "./prescriptionModal.css";
import Image from "next/image";
import img1 from "../../../../public/assets/cancer_unwired_logo.png";
import Modal from "react-bootstrap/Modal";
import { Button } from "react-bootstrap";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { useState } from "react";
import Cookies from "js-cookie";

const IRPrescriptionModal = ({ show, onClose, data }) => {
  return (
    <Modal show={show} onHide={onClose} dialogClassName="custom-modal-width">
      <Modal.Header className="custom-modal-header">
        {/* <Button variant="primary" onClick={handleDownload}>
        Download
      </Button> */}
        <div className="row">
          <div className="col-sm-9">
            <p className="purple-content mb-1 fw-semibold">
              {data?.doctor_details?.name &&
                data.doctor_details.name.charAt(0).toUpperCase() +
                  data.doctor_details.name.slice(1)}
            </p>

            <p className="purple-content fs-6 mb-0">
              Dept. of {data?.doctor_details?.doctor_other_details?.Dept}
            </p>
          </div>

          <div className="col-sm-3">
            <Image className="headerImage1" src={img1} alt="header" />
          </div>
        </div>

        <div className="row">
          <div className="col-sm-9">
            <p className="black-content mb-1">MD license: 12-136547</p>

            <p className="black-content mb-1">
              Doctor ID: {data?.doctor_details?.id}
            </p>

            <p className="black-content fw-medium mb-0">24th June, 2023</p>
          </div>

          <div className="col-sm-3">
            <p className="purple-content mb-1 fw-semibold">Cancer Hospital</p>

            <p className="black-content mb-0">
              <a className="link-opacity-100" href="#">
                www.cancerhospital.com
              </a>
            </p>
          </div>
        </div>
      </Modal.Header>
      <Modal.Body>
        <form className="row g-3">
          <div className="col-md-2 mt-0">
            <label
              htmlFor="inputId"
              className="form-label purple-content-2 fw-medium mb-1"
            >
              Id
            </label>
            <input
              type="text"
              className="form-control custom-form-control"
              id="inputId"
              value={data?.patient_details?.id}
              readOnly
            />
          </div>
          <div className="col-md-3 mt-0">
            <label
              htmlFor="inputName"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Name
            </label>
            <input
              type="name"
              className="form-control custom-form-control"
              id="inputName"
              value={data?.patient_details?.name}
              readOnly
            />
          </div>
          <div className="col-md-3 mt-0">
            <label
              htmlFor="inputEmail"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Consultation Id
            </label>
            <input
              type="mail"
              className="form-control custom-form-control"
              id="inputEmail"
              value={data?.prescription_data?.AppointmentId}
              readOnly
            />
          </div>
          <div className="col-md-2 mt-0">
            <label
              htmlFor="inputEmail"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Sex
            </label>
            <input
              type="mail"
              className="form-control custom-form-control"
              id="inputEmail"
              value={data?.patient_details?.sex}
              readOnly
            />
          </div>
          <div className="col-md-2 mt-0">
            <label
              htmlFor="inputEmail"
              className="form-label  purple-content-2 fw-medium mb-1"
            >
              Age
            </label>
            <input
              type="mail"
              className="form-control custom-form-control"
              id="inputEmail"
              value={data?.patient_details?.age}
              readOnly
            />
          </div>

          <div className="input-group mb-0">
            <span className="input-group-text purple-content" id="basic-addon1">
              Current Diagnosis
            </span>
            <input
              type="text"
              className="form-control custom-form-control"
              aria-label="Username"
              aria-describedby="basic-addon1"
              value={data?.prescription_data?.CurrentDiagnosis}
              readOnly
            />
          </div>

          <div className="form-floating">
            <textarea
              className="form-control"
              placeholder="Leave a comment here"
              id="floatingTextarea"
              style={{ height: "100px" }}
              value={data?.prescription_data?.ConsultationSummary}
            />
            <label htmlFor="floatingTextarea" className="purple-content">
              ConsultationSummary
            </label>
          </div>

          <div className="form-floating">
            <textarea
              className="form-control"
              placeholder="Leave a comment here"
              id="floatingTextarea"
              style={{ height: "100px" }}
              value={data?.prescription_data?.Remarks}
            />
            <label htmlFor="floatingTextarea" className="purple-content">
              Remarks
            </label>
          </div>

          <div className="form-floating">
            <textarea
              className="form-control"
              placeholder="Leave a comment here"
              id="floatingTextarea"
              style={{ height: "100px" }}
              value={data?.prescription_data?.SpecialInstructions}
            />
            <label htmlFor="floatingTextarea" className="purple-content">
              Special Instructions
            </label>
          </div>

          <div className="input-group mb-0 mt-2">
            <span className="input-group-text purple-content" id="basic-addon1">
              Follow-up Appointment
            </span>
            <input
              type="text"
              className="form-control custom-form-control"
              aria-label="Username"
              aria-describedby="basic-addon1"
              value={data?.prescription_data?.FollowUp}
              readOnly
            />
          </div>

          <div className="col-sm-3 offset-sm-9 border">
            <p className="purple-signature">Digital Signature</p>
            <Image
              src={data?.prescription_data?.DoctorSignature}
              alt=""
              className="ms-5 mb-2"
              width={100}
              height={70}
            />
          </div>
          {/* <div className="col-sm-1 mx-auto">
            <Button className="prescription-btn" onClick={handleDownload}>
              Download
            </Button>
          </div> */}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default IRPrescriptionModal;
